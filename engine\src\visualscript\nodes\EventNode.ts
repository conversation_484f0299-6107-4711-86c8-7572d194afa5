/**
 * 视觉脚本事件节点
 * 事件节点是视觉脚本的入口点，用于响应各种事件
 */
import { Node, NodeCategory, NodeOptions, NodeType, SocketType, SocketDirection } from './Node';

/**
 * 事件类型枚举
 */
export enum EventType {
  START = 'start',
  UPDATE = 'update',
  FIXED_UPDATE = 'fixedUpdate',
  LATE_UPDATE = 'lateUpdate',
  MOUSE_DOWN = 'mouseDown',
  MOUSE_UP = 'mouseUp',
  MOUSE_MOVE = 'mouseMove',
  MOUSE_ENTER = 'mouseEnter',
  MOUSE_LEAVE = 'mouseLeave',
  MOUSE_WHEEL = 'mouseWheel',
  MOUSE_CLICK = 'mouseClick',
  MOUSE_DOUBLE_CLICK = 'mouseDoubleClick',
  KEY_DOWN = 'keyDown',
  KEY_UP = 'keyUp',
  KEY_PRESS = 'keyPress',
  TOUCH_START = 'touchStart',
  TOUCH_END = 'touchEnd',
  TOUCH_MOVE = 'touchMove',
  COLLISION_ENTER = 'collisionEnter',
  COLLISION_EXIT = 'collisionExit',
  TRIGGER_ENTER = 'triggerEnter',
  TRIGGER_EXIT = 'triggerExit',
  TIMER = 'timer',
  CUSTOM = 'custom'
}

/**
 * 事件优先级枚举
 */
export enum EventPriority {
  HIGHEST = 0,
  HIGH = 1,
  MEDIUM = 2,
  LOW = 3,
  LOWEST = 4
}

/**
 * 事件过滤器类型
 */
export type EventFilter = (eventData: any) => boolean;

/**
 * 事件节点选项
 */
export interface EventNodeOptions extends NodeOptions {
  /** 事件名称 */
  eventName?: string;
  /** 事件优先级 */
  priority?: EventPriority;
  /** 事件过滤器 */
  filter?: EventFilter;
  /** 是否启用事件监听 */
  enabled?: boolean;
  /** 事件节流间隔（毫秒） */
  throttleInterval?: number;
  /** 事件防抖延迟（毫秒） */
  debounceDelay?: number;
  /** 是否只触发一次 */
  triggerOnce?: boolean;
}

/**
 * 事件节点基类
 */
export class EventNode extends Node {
  /** 节点类型 */
  public readonly nodeType: NodeType = NodeType.EVENT;

  /** 节点类别 */
  public readonly category: NodeCategory = NodeCategory.EVENT;

  /** 事件名称 */
  protected eventName: string;

  /** 事件优先级 */
  protected priority: EventPriority;

  /** 事件过滤器 */
  protected filter?: EventFilter;

  /** 是否启用事件监听 */
  protected enabled: boolean;

  /** 事件节流间隔 */
  protected throttleInterval: number;

  /** 事件防抖延迟 */
  protected debounceDelay: number;

  /** 是否只触发一次 */
  protected triggerOnce: boolean;

  /** 是否已触发过（用于once模式） */
  protected hasTriggered: boolean = false;

  /** 最后触发时间（用于节流） */
  protected lastTriggerTime: number = 0;

  /** 防抖定时器 */
  protected debounceTimer?: number;

  /**
   * 创建事件节点
   * @param options 节点选项
   */
  constructor(options: EventNodeOptions) {
    super(options);

    this.eventName = options.eventName || '';
    this.priority = options.priority || EventPriority.MEDIUM;
    this.filter = options.filter;
    this.enabled = options.enabled !== false;
    this.throttleInterval = options.throttleInterval || 0;
    this.debounceDelay = options.debounceDelay || 0;
    this.triggerOnce = options.triggerOnce || false;
  }
  
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 事件节点只有输出流程插槽，没有输入流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      description: '当事件触发时执行'
    });
  }
  
  /**
   * 初始化节点
   * 在视觉脚本引擎启动时调用
   */
  public initialize(): void {
    // 确保插槽已初始化
    if (this.outputs.size === 0) {
      this.initializeSockets();
    }

    // 子类可以重写此方法来添加自定义初始化逻辑
  }

  /**
   * 当视觉脚本开始执行时调用
   */
  public onStart(): void {
    // 子类实现具体的启动逻辑
  }

  /**
   * 当视觉脚本停止执行时调用
   */
  public onStop(): void {
    // 子类实现具体的停止逻辑
  }
  
  /**
   * 当视觉脚本更新时调用
   * @param _deltaTime 帧间隔时间（秒）
   */
  public onUpdate(_deltaTime: number): void {
    // 子类实现具体的更新逻辑
  }

  /**
   * 获取事件名称
   * @returns 事件名称
   */
  public getEventName(): string {
    return this.eventName;
  }

  /**
   * 设置事件名称
   * @param eventName 事件名称
   */
  public setEventName(eventName: string): void {
    this.eventName = eventName;
  }

  /**
   * 启用事件监听
   */
  public enable(): void {
    this.enabled = true;
    this.emit('enabled');
  }

  /**
   * 禁用事件监听
   */
  public disable(): void {
    this.enabled = false;
    this.emit('disabled');
  }

  /**
   * 检查是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 设置事件过滤器
   * @param filter 过滤器函数
   */
  public setFilter(filter?: EventFilter): void {
    this.filter = filter;
  }

  /**
   * 设置事件优先级
   * @param priority 优先级
   */
  public setPriority(priority: EventPriority): void {
    this.priority = priority;
  }

  /**
   * 获取事件优先级
   */
  public getPriority(): EventPriority {
    return this.priority;
  }

  /**
   * 重置触发状态（用于once模式）
   */
  public resetTriggerState(): void {
    this.hasTriggered = false;
  }

  /**
   * 检查事件是否应该被触发
   * @param eventData 事件数据
   */
  protected shouldTrigger(eventData?: any): boolean {
    // 检查是否启用
    if (!this.enabled) {
      return false;
    }

    // 检查是否已触发过（once模式）
    if (this.triggerOnce && this.hasTriggered) {
      return false;
    }

    // 检查过滤器
    if (this.filter && !this.filter(eventData)) {
      return false;
    }

    // 检查节流
    if (this.throttleInterval > 0) {
      const now = Date.now();
      if (now - this.lastTriggerTime < this.throttleInterval) {
        return false;
      }
    }

    return true;
  }

  /**
   * 触发事件
   * @param args 事件参数
   */
  protected trigger(...args: any[]): void {
    // 检查是否应该触发
    if (!this.shouldTrigger(args[0])) {
      return;
    }

    // 处理防抖
    if (this.debounceDelay > 0) {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      this.debounceTimer = window.setTimeout(() => {
        this.executeTrigger(...args);
      }, this.debounceDelay);

      return;
    }

    // 直接触发
    this.executeTrigger(...args);
  }

  /**
   * 执行触发逻辑
   * @param args 事件参数
   */
  private executeTrigger(...args: any[]): void {
    try {
      // 更新最后触发时间
      this.lastTriggerTime = Date.now();

      // 标记已触发（用于once模式）
      if (this.triggerOnce) {
        this.hasTriggered = true;
      }

      // 设置输出参数
      if (args.length > 0 && this.outputs.size > 1) {
        const outputNames = Array.from(this.outputs.keys())
          .filter(name => name !== 'flow')
          .sort(); // 确保输出顺序的一致性

        for (let i = 0; i < Math.min(args.length, outputNames.length); i++) {
          this.setOutputValue(outputNames[i], args[i]);
        }
      }

      // 发射事件
      this.emit('triggered', ...args);

      // 触发流程
      this.triggerFlow('flow');
    } catch (error) {
      console.error('触发事件节点时出错:', error);
      this.emit('error', error);

      // 即使出错也要尝试触发流程，以免中断执行
      try {
        this.triggerFlow('flow');
      } catch (flowError) {
        console.error('触发事件节点流程时出错:', flowError);
      }
    }
  }
}

/**
 * 开始事件节点
 * 当视觉脚本开始执行时触发
 */
export class StartEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.START
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '开始';
    }
    if (!this.metadata.description) {
      this.metadata.description = '当视觉脚本开始执行时触发';
    }
  }

  public onStart(): void {
    // 在脚本开始时立即触发
    this.trigger();
  }
}

/**
 * 更新事件节点
 * 每帧更新时触发
 */
export class UpdateEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.UPDATE
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '更新';
    }
    if (!this.metadata.description) {
      this.metadata.description = '每帧更新时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加deltaTime输出
    this.addOutput({
      name: 'deltaTime',
      type: SocketType.DATA,
      dataType: 'number',
      description: '帧间隔时间（秒）'
    });
  }

  public onUpdate(deltaTime: number): void {
    this.trigger(deltaTime);
  }
}

/**
 * 固定更新事件节点
 * 固定时间步长更新时触发
 */
export class FixedUpdateEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.FIXED_UPDATE
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '固定更新';
    }
    if (!this.metadata.description) {
      this.metadata.description = '固定时间步长更新时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加fixedDeltaTime输出
    this.addOutput({
      name: 'fixedDeltaTime',
      type: SocketType.DATA,
      dataType: 'number',
      description: '固定帧间隔时间（秒）'
    });
  }

  public onFixedUpdate(fixedDeltaTime: number): void {
    this.trigger(fixedDeltaTime);
  }

  /**
   * 当视觉脚本固定更新时调用
   * @param fixedDeltaTime 固定帧间隔时间（秒）
   */
  public onUpdate(fixedDeltaTime: number): void {
    this.onFixedUpdate(fixedDeltaTime);
  }
}

/**
 * 后更新事件节点
 * 后更新时触发
 */
export class LateUpdateEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.LATE_UPDATE
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '后更新';
    }
    if (!this.metadata.description) {
      this.metadata.description = '后更新时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加deltaTime输出
    this.addOutput({
      name: 'deltaTime',
      type: SocketType.DATA,
      dataType: 'number',
      description: '帧间隔时间（秒）'
    });
  }

  public onLateUpdate(deltaTime: number): void {
    this.trigger(deltaTime);
  }
}

/**
 * 鼠标按下事件节点
 */
export class MouseDownEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.MOUSE_DOWN
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '鼠标按下';
    }
    if (!this.metadata.description) {
      this.metadata.description = '鼠标按下时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加鼠标位置输出
    this.addOutput({
      name: 'mouseX',
      type: SocketType.DATA,
      dataType: 'number',
      description: '鼠标X坐标'
    });

    this.addOutput({
      name: 'mouseY',
      type: SocketType.DATA,
      dataType: 'number',
      description: '鼠标Y坐标'
    });

    this.addOutput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'number',
      description: '鼠标按键（0=左键，1=中键，2=右键）'
    });
  }

  public onMouseDown(event: MouseEvent): void {
    this.trigger(event.clientX, event.clientY, event.button);
  }
}

/**
 * 鼠标抬起事件节点
 */
export class MouseUpEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.MOUSE_UP
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '鼠标抬起';
    }
    if (!this.metadata.description) {
      this.metadata.description = '鼠标抬起时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加鼠标位置输出
    this.addOutput({
      name: 'mouseX',
      type: SocketType.DATA,
      dataType: 'number',
      description: '鼠标X坐标'
    });

    this.addOutput({
      name: 'mouseY',
      type: SocketType.DATA,
      dataType: 'number',
      description: '鼠标Y坐标'
    });

    this.addOutput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'number',
      description: '鼠标按键（0=左键，1=中键，2=右键）'
    });
  }

  public onMouseUp(event: MouseEvent): void {
    this.trigger(event.clientX, event.clientY, event.button);
  }
}

/**
 * 鼠标移动事件节点
 */
export class MouseMoveEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.MOUSE_MOVE
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '鼠标移动';
    }
    if (!this.metadata.description) {
      this.metadata.description = '鼠标移动时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加鼠标位置输出
    this.addOutput({
      name: 'mouseX',
      type: SocketType.DATA,
      dataType: 'number',
      description: '鼠标X坐标'
    });

    this.addOutput({
      name: 'mouseY',
      type: SocketType.DATA,
      dataType: 'number',
      description: '鼠标Y坐标'
    });

    this.addOutput({
      name: 'deltaX',
      type: SocketType.DATA,
      dataType: 'number',
      description: '鼠标X轴移动距离'
    });

    this.addOutput({
      name: 'deltaY',
      type: SocketType.DATA,
      dataType: 'number',
      description: '鼠标Y轴移动距离'
    });
  }

  public onMouseMove(event: MouseEvent): void {
    this.trigger(event.clientX, event.clientY, event.movementX, event.movementY);
  }
}

/**
 * 鼠标进入事件节点
 */
export class MouseEnterEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.MOUSE_ENTER
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '鼠标进入';
    }
    if (!this.metadata.description) {
      this.metadata.description = '鼠标进入元素时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加鼠标位置输出
    this.addOutput({
      name: 'mouseX',
      type: SocketType.DATA,
      dataType: 'number',
      description: '鼠标X坐标'
    });

    this.addOutput({
      name: 'mouseY',
      type: SocketType.DATA,
      dataType: 'number',
      description: '鼠标Y坐标'
    });

    this.addOutput({
      name: 'target',
      type: SocketType.DATA,
      dataType: 'element',
      description: '目标元素'
    });
  }

  public onMouseEnter(event: MouseEvent): void {
    this.trigger(event.clientX, event.clientY, event.target);
  }
}

/**
 * 鼠标离开事件节点
 */
export class MouseLeaveEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.MOUSE_LEAVE
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '鼠标离开';
    }
    if (!this.metadata.description) {
      this.metadata.description = '鼠标离开元素时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加鼠标位置输出
    this.addOutput({
      name: 'mouseX',
      type: SocketType.DATA,
      dataType: 'number',
      description: '鼠标X坐标'
    });

    this.addOutput({
      name: 'mouseY',
      type: SocketType.DATA,
      dataType: 'number',
      description: '鼠标Y坐标'
    });

    this.addOutput({
      name: 'target',
      type: SocketType.DATA,
      dataType: 'element',
      description: '目标元素'
    });
  }

  public onMouseLeave(event: MouseEvent): void {
    this.trigger(event.clientX, event.clientY, event.target);
  }
}

/**
 * 鼠标滚轮事件节点
 */
export class MouseWheelEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.MOUSE_WHEEL
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '鼠标滚轮';
    }
    if (!this.metadata.description) {
      this.metadata.description = '鼠标滚轮滚动时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加滚轮信息输出
    this.addOutput({
      name: 'deltaX',
      type: SocketType.DATA,
      dataType: 'number',
      description: '水平滚动距离'
    });

    this.addOutput({
      name: 'deltaY',
      type: SocketType.DATA,
      dataType: 'number',
      description: '垂直滚动距离'
    });

    this.addOutput({
      name: 'deltaZ',
      type: SocketType.DATA,
      dataType: 'number',
      description: 'Z轴滚动距离'
    });

    this.addOutput({
      name: 'deltaMode',
      type: SocketType.DATA,
      dataType: 'number',
      description: '滚动模式'
    });
  }

  public onMouseWheel(event: WheelEvent): void {
    this.trigger(event.deltaX, event.deltaY, event.deltaZ, event.deltaMode);
  }
}

/**
 * 键盘按下事件节点
 */
export class KeyDownEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.KEY_DOWN
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '键盘按下';
    }
    if (!this.metadata.description) {
      this.metadata.description = '键盘按下时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加键盘信息输出
    this.addOutput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      description: '按下的键'
    });

    this.addOutput({
      name: 'code',
      type: SocketType.DATA,
      dataType: 'string',
      description: '键码'
    });

    this.addOutput({
      name: 'ctrlKey',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否按下Ctrl键'
    });

    this.addOutput({
      name: 'shiftKey',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否按下Shift键'
    });

    this.addOutput({
      name: 'altKey',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否按下Alt键'
    });
  }

  public onKeyDown(event: KeyboardEvent): void {
    this.trigger(event.key, event.code, event.ctrlKey, event.shiftKey, event.altKey);
  }
}

/**
 * 键盘抬起事件节点
 */
export class KeyUpEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.KEY_UP
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '键盘抬起';
    }
    if (!this.metadata.description) {
      this.metadata.description = '键盘抬起时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加键盘信息输出
    this.addOutput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      description: '抬起的键'
    });

    this.addOutput({
      name: 'code',
      type: SocketType.DATA,
      dataType: 'string',
      description: '键码'
    });

    this.addOutput({
      name: 'ctrlKey',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否按下Ctrl键'
    });

    this.addOutput({
      name: 'shiftKey',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否按下Shift键'
    });

    this.addOutput({
      name: 'altKey',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否按下Alt键'
    });
  }

  public onKeyUp(event: KeyboardEvent): void {
    this.trigger(event.key, event.code, event.ctrlKey, event.shiftKey, event.altKey);
  }
}

/**
 * 碰撞进入事件节点
 */
export class CollisionEnterEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.COLLISION_ENTER
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '碰撞进入';
    }
    if (!this.metadata.description) {
      this.metadata.description = '物体碰撞进入时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加碰撞信息输出
    this.addOutput({
      name: 'otherEntity',
      type: SocketType.DATA,
      dataType: 'entity',
      description: '碰撞的其他实体'
    });

    this.addOutput({
      name: 'contactPoint',
      type: SocketType.DATA,
      dataType: 'vector3',
      description: '碰撞点'
    });

    this.addOutput({
      name: 'contactNormal',
      type: SocketType.DATA,
      dataType: 'vector3',
      description: '碰撞法线'
    });

    this.addOutput({
      name: 'impulse',
      type: SocketType.DATA,
      dataType: 'number',
      description: '碰撞冲量'
    });
  }

  public onCollisionEnter(otherEntity: any, contactPoint: any, contactNormal: any, impulse: number): void {
    this.trigger(otherEntity, contactPoint, contactNormal, impulse);
  }
}

/**
 * 碰撞退出事件节点
 */
export class CollisionExitEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.COLLISION_EXIT
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '碰撞退出';
    }
    if (!this.metadata.description) {
      this.metadata.description = '物体碰撞退出时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加碰撞信息输出
    this.addOutput({
      name: 'otherEntity',
      type: SocketType.DATA,
      dataType: 'entity',
      description: '碰撞的其他实体'
    });
  }

  public onCollisionExit(otherEntity: any): void {
    this.trigger(otherEntity);
  }
}

/**
 * 触发器进入事件节点
 */
export class TriggerEnterEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.TRIGGER_ENTER
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '触发器进入';
    }
    if (!this.metadata.description) {
      this.metadata.description = '物体进入触发器时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加触发器信息输出
    this.addOutput({
      name: 'otherEntity',
      type: SocketType.DATA,
      dataType: 'entity',
      description: '进入触发器的实体'
    });

    this.addOutput({
      name: 'triggerEntity',
      type: SocketType.DATA,
      dataType: 'entity',
      description: '触发器实体'
    });
  }

  public onTriggerEnter(otherEntity: any, triggerEntity: any): void {
    this.trigger(otherEntity, triggerEntity);
  }
}

/**
 * 触发器退出事件节点
 */
export class TriggerExitEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.TRIGGER_EXIT
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '触发器退出';
    }
    if (!this.metadata.description) {
      this.metadata.description = '物体退出触发器时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加触发器信息输出
    this.addOutput({
      name: 'otherEntity',
      type: SocketType.DATA,
      dataType: 'entity',
      description: '退出触发器的实体'
    });

    this.addOutput({
      name: 'triggerEntity',
      type: SocketType.DATA,
      dataType: 'entity',
      description: '触发器实体'
    });
  }

  public onTriggerExit(otherEntity: any, triggerEntity: any): void {
    this.trigger(otherEntity, triggerEntity);
  }
}

/**
 * 定时器事件节点
 */
export class TimerEventNode extends EventNode {
  private timerId?: number;
  private interval: number;
  private repeat: boolean;

  constructor(options: EventNodeOptions & { interval?: number; repeat?: boolean }) {
    super({
      ...options,
      eventName: EventType.TIMER
    });

    this.interval = options.interval || 1000;
    this.repeat = options.repeat !== false;

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '定时器';
    }
    if (!this.metadata.description) {
      this.metadata.description = `定时器事件，间隔${this.interval}ms${this.repeat ? '（重复）' : '（单次）'}`;
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加定时器信息输出
    this.addOutput({
      name: 'timestamp',
      type: SocketType.DATA,
      dataType: 'number',
      description: '触发时间戳'
    });

    this.addOutput({
      name: 'count',
      type: SocketType.DATA,
      dataType: 'number',
      description: '触发次数'
    });

    // 添加间隔输入
    this.addInput({
      name: 'interval',
      type: SocketType.DATA,
      dataType: 'number',
      description: '定时器间隔（毫秒）',
      defaultValue: this.interval
    });
  }

  private triggerCount: number = 0;

  public startTimer(): void {
    this.stopTimer();

    const triggerTimer = () => {
      this.triggerCount++;
      this.trigger(Date.now(), this.triggerCount);

      if (this.repeat) {
        this.timerId = window.setTimeout(triggerTimer, this.interval);
      }
    };

    this.timerId = window.setTimeout(triggerTimer, this.interval);
  }

  public stopTimer(): void {
    if (this.timerId) {
      clearTimeout(this.timerId);
      this.timerId = undefined;
    }
  }

  public resetTimer(): void {
    this.triggerCount = 0;
    this.stopTimer();
  }

  public onStart(): void {
    super.onStart();
    this.startTimer();
  }

  public onStop(): void {
    super.onStop();
    this.stopTimer();
  }
}

/**
 * 自定义事件节点
 */
export class CustomEventNode extends EventNode {
  private customEventName: string;

  constructor(options: EventNodeOptions & { customEventName: string }) {
    super({
      ...options,
      eventName: EventType.CUSTOM
    });

    this.customEventName = options.customEventName;

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = `自定义事件: ${this.customEventName}`;
    }
    if (!this.metadata.description) {
      this.metadata.description = `自定义事件 "${this.customEventName}" 触发时执行`;
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加通用数据输出
    this.addOutput({
      name: 'eventData',
      type: SocketType.DATA,
      dataType: 'any',
      description: '事件数据'
    });
  }

  public onCustomEvent(eventData: any): void {
    this.trigger(eventData);
  }

  public getCustomEventName(): string {
    return this.customEventName;
  }
}

/**
 * 事件节点工厂
 */
export class EventNodeFactory {
  /**
   * 创建事件节点
   * @param eventType 事件类型
   * @param options 节点选项
   * @returns 事件节点实例
   */
  public static createEventNode(eventType: EventType, options: EventNodeOptions): EventNode {
    switch (eventType) {
      case EventType.START:
        return new StartEventNode(options);

      case EventType.UPDATE:
        return new UpdateEventNode(options);

      case EventType.FIXED_UPDATE:
        return new FixedUpdateEventNode(options);

      case EventType.LATE_UPDATE:
        return new LateUpdateEventNode(options);

      case EventType.MOUSE_DOWN:
        return new MouseDownEventNode(options);

      case EventType.MOUSE_UP:
        return new MouseUpEventNode(options);

      case EventType.MOUSE_MOVE:
        return new MouseMoveEventNode(options);

      case EventType.MOUSE_ENTER:
        return new MouseEnterEventNode(options);

      case EventType.MOUSE_LEAVE:
        return new MouseLeaveEventNode(options);

      case EventType.MOUSE_WHEEL:
        return new MouseWheelEventNode(options);

      case EventType.KEY_DOWN:
        return new KeyDownEventNode(options);

      case EventType.KEY_UP:
        return new KeyUpEventNode(options);

      case EventType.TIMER:
        return new TimerEventNode(options);

      case EventType.COLLISION_ENTER:
        return new CollisionEnterEventNode(options);

      case EventType.COLLISION_EXIT:
        return new CollisionExitEventNode(options);

      case EventType.TRIGGER_ENTER:
        return new TriggerEnterEventNode(options);

      case EventType.TRIGGER_EXIT:
        return new TriggerExitEventNode(options);

      case EventType.CUSTOM:
        // 自定义事件需要额外的参数
        throw new Error('自定义事件节点需要使用 createCustomEventNode 方法创建');

      default:
        throw new Error(`不支持的事件类型: ${eventType}`);
    }
  }

  /**
   * 创建自定义事件节点
   * @param customEventName 自定义事件名称
   * @param options 节点选项
   * @returns 自定义事件节点实例
   */
  public static createCustomEventNode(customEventName: string, options: EventNodeOptions): CustomEventNode {
    return new CustomEventNode({
      ...options,
      customEventName
    });
  }

  /**
   * 获取所有支持的事件类型
   * @returns 事件类型数组
   */
  public static getSupportedEventTypes(): EventType[] {
    return Object.values(EventType);
  }

  /**
   * 获取事件类型的显示名称
   * @param eventType 事件类型
   * @returns 显示名称
   */
  public static getEventTypeDisplayName(eventType: EventType): string {
    const displayNames: Record<EventType, string> = {
      [EventType.START]: '开始',
      [EventType.UPDATE]: '更新',
      [EventType.FIXED_UPDATE]: '固定更新',
      [EventType.LATE_UPDATE]: '后更新',
      [EventType.MOUSE_DOWN]: '鼠标按下',
      [EventType.MOUSE_UP]: '鼠标抬起',
      [EventType.MOUSE_MOVE]: '鼠标移动',
      [EventType.MOUSE_ENTER]: '鼠标进入',
      [EventType.MOUSE_LEAVE]: '鼠标离开',
      [EventType.MOUSE_WHEEL]: '鼠标滚轮',
      [EventType.MOUSE_CLICK]: '鼠标点击',
      [EventType.MOUSE_DOUBLE_CLICK]: '鼠标双击',
      [EventType.KEY_DOWN]: '键盘按下',
      [EventType.KEY_UP]: '键盘抬起',
      [EventType.KEY_PRESS]: '键盘按键',
      [EventType.TOUCH_START]: '触摸开始',
      [EventType.TOUCH_END]: '触摸结束',
      [EventType.TOUCH_MOVE]: '触摸移动',
      [EventType.COLLISION_ENTER]: '碰撞进入',
      [EventType.COLLISION_EXIT]: '碰撞退出',
      [EventType.TRIGGER_ENTER]: '触发器进入',
      [EventType.TRIGGER_EXIT]: '触发器退出',
      [EventType.TIMER]: '定时器',
      [EventType.CUSTOM]: '自定义事件'
    };

    return displayNames[eventType] || eventType;
  }

  /**
   * 获取事件类型的描述
   * @param eventType 事件类型
   * @returns 描述
   */
  public static getEventTypeDescription(eventType: EventType): string {
    const descriptions: Record<EventType, string> = {
      [EventType.START]: '当视觉脚本开始执行时触发',
      [EventType.UPDATE]: '每帧更新时触发',
      [EventType.FIXED_UPDATE]: '固定时间步长更新时触发',
      [EventType.LATE_UPDATE]: '后更新时触发',
      [EventType.MOUSE_DOWN]: '鼠标按下时触发',
      [EventType.MOUSE_UP]: '鼠标抬起时触发',
      [EventType.MOUSE_MOVE]: '鼠标移动时触发',
      [EventType.MOUSE_ENTER]: '鼠标进入元素时触发',
      [EventType.MOUSE_LEAVE]: '鼠标离开元素时触发',
      [EventType.MOUSE_WHEEL]: '鼠标滚轮滚动时触发',
      [EventType.MOUSE_CLICK]: '鼠标点击时触发',
      [EventType.MOUSE_DOUBLE_CLICK]: '鼠标双击时触发',
      [EventType.KEY_DOWN]: '键盘按下时触发',
      [EventType.KEY_UP]: '键盘抬起时触发',
      [EventType.KEY_PRESS]: '键盘按键时触发',
      [EventType.TOUCH_START]: '触摸开始时触发',
      [EventType.TOUCH_END]: '触摸结束时触发',
      [EventType.TOUCH_MOVE]: '触摸移动时触发',
      [EventType.COLLISION_ENTER]: '物体碰撞进入时触发',
      [EventType.COLLISION_EXIT]: '物体碰撞退出时触发',
      [EventType.TRIGGER_ENTER]: '物体进入触发器时触发',
      [EventType.TRIGGER_EXIT]: '物体退出触发器时触发',
      [EventType.TIMER]: '定时器到期时触发',
      [EventType.CUSTOM]: '自定义事件触发时执行'
    };

    return descriptions[eventType] || '未知事件类型';
  }
}

/**
 * 事件节点管理器
 * 统一管理所有事件节点的生命周期和事件分发
 */
export class EventNodeManager {
  /** 事件节点映射 */
  private eventNodes: Map<string, EventNode[]> = new Map();

  /** 全局事件过滤器 */
  private globalFilters: Map<EventType, EventFilter[]> = new Map();

  /** 事件统计 */
  private eventStats: Map<EventType, { count: number; lastTriggered: number }> = new Map();

  /**
   * 注册事件节点
   * @param eventType 事件类型
   * @param node 事件节点
   */
  public registerEventNode(eventType: EventType, node: EventNode): void {
    if (!this.eventNodes.has(eventType)) {
      this.eventNodes.set(eventType, []);
    }

    this.eventNodes.get(eventType)!.push(node);

    // 按优先级排序
    this.eventNodes.get(eventType)!.sort((a, b) => a.getPriority() - b.getPriority());
  }

  /**
   * 注销事件节点
   * @param eventType 事件类型
   * @param node 事件节点
   */
  public unregisterEventNode(eventType: EventType, node: EventNode): void {
    const nodes = this.eventNodes.get(eventType);
    if (nodes) {
      const index = nodes.indexOf(node);
      if (index !== -1) {
        nodes.splice(index, 1);
      }
    }
  }

  /**
   * 添加全局事件过滤器
   * @param eventType 事件类型
   * @param filter 过滤器
   */
  public addGlobalFilter(eventType: EventType, filter: EventFilter): void {
    if (!this.globalFilters.has(eventType)) {
      this.globalFilters.set(eventType, []);
    }

    this.globalFilters.get(eventType)!.push(filter);
  }

  /**
   * 移除全局事件过滤器
   * @param eventType 事件类型
   * @param filter 过滤器
   */
  public removeGlobalFilter(eventType: EventType, filter: EventFilter): void {
    const filters = this.globalFilters.get(eventType);
    if (filters) {
      const index = filters.indexOf(filter);
      if (index !== -1) {
        filters.splice(index, 1);
      }
    }
  }

  /**
   * 分发事件到相关节点
   * @param eventType 事件类型
   * @param eventData 事件数据
   */
  public dispatchEvent(eventType: EventType, eventData: any): void {
    // 检查全局过滤器
    const globalFilters = this.globalFilters.get(eventType) || [];
    for (const filter of globalFilters) {
      if (!filter(eventData)) {
        return; // 被全局过滤器拦截
      }
    }

    // 更新事件统计
    this.updateEventStats(eventType);

    // 获取相关事件节点
    const nodes = this.eventNodes.get(eventType) || [];

    // 分发到各个节点
    for (const node of nodes) {
      if (node.isEnabled()) {
        try {
          // 根据事件类型调用相应的处理方法
          this.callNodeHandler(node, eventType, eventData);
        } catch (error) {
          console.error(`事件节点处理错误 [${eventType}]:`, error);
        }
      }
    }
  }

  /**
   * 调用节点处理方法
   * @param node 事件节点
   * @param eventType 事件类型
   * @param eventData 事件数据
   */
  private callNodeHandler(node: EventNode, eventType: EventType, eventData: any): void {
    switch (eventType) {
      case EventType.START:
        (node as StartEventNode).onStart();
        break;
      case EventType.UPDATE:
        (node as UpdateEventNode).onUpdate(eventData);
        break;
      case EventType.MOUSE_DOWN:
        (node as MouseDownEventNode).onMouseDown(eventData);
        break;
      case EventType.MOUSE_UP:
        (node as MouseUpEventNode).onMouseUp(eventData);
        break;
      case EventType.MOUSE_MOVE:
        (node as MouseMoveEventNode).onMouseMove(eventData);
        break;
      case EventType.MOUSE_ENTER:
        (node as MouseEnterEventNode).onMouseEnter(eventData);
        break;
      case EventType.MOUSE_LEAVE:
        (node as MouseLeaveEventNode).onMouseLeave(eventData);
        break;
      case EventType.MOUSE_WHEEL:
        (node as MouseWheelEventNode).onMouseWheel(eventData);
        break;
      case EventType.KEY_DOWN:
        (node as KeyDownEventNode).onKeyDown(eventData);
        break;
      case EventType.KEY_UP:
        (node as KeyUpEventNode).onKeyUp(eventData);
        break;
      case EventType.COLLISION_ENTER:
        (node as CollisionEnterEventNode).onCollisionEnter(
          eventData.otherEntity,
          eventData.contactPoint,
          eventData.contactNormal,
          eventData.impulse
        );
        break;
      case EventType.COLLISION_EXIT:
        (node as CollisionExitEventNode).onCollisionExit(eventData.otherEntity);
        break;
      case EventType.TRIGGER_ENTER:
        (node as TriggerEnterEventNode).onTriggerEnter(
          eventData.otherEntity,
          eventData.triggerEntity
        );
        break;
      case EventType.TRIGGER_EXIT:
        (node as TriggerExitEventNode).onTriggerExit(
          eventData.otherEntity,
          eventData.triggerEntity
        );
        break;
      case EventType.CUSTOM:
        (node as CustomEventNode).onCustomEvent(eventData);
        break;
      default:
        console.warn(`未处理的事件类型: ${eventType}`);
    }
  }

  /**
   * 更新事件统计
   * @param eventType 事件类型
   */
  private updateEventStats(eventType: EventType): void {
    const stats = this.eventStats.get(eventType) || { count: 0, lastTriggered: 0 };
    stats.count++;
    stats.lastTriggered = Date.now();
    this.eventStats.set(eventType, stats);
  }

  /**
   * 获取事件统计
   * @param eventType 事件类型
   */
  public getEventStats(eventType?: EventType): Map<EventType, { count: number; lastTriggered: number }> | { count: number; lastTriggered: number } | undefined {
    if (eventType) {
      return this.eventStats.get(eventType);
    }
    return this.eventStats;
  }

  /**
   * 清空事件统计
   */
  public clearEventStats(): void {
    this.eventStats.clear();
  }

  /**
   * 启用所有事件节点
   */
  public enableAllNodes(): void {
    for (const nodes of this.eventNodes.values()) {
      for (const node of nodes) {
        node.enable();
      }
    }
  }

  /**
   * 禁用所有事件节点
   */
  public disableAllNodes(): void {
    for (const nodes of this.eventNodes.values()) {
      for (const node of nodes) {
        node.disable();
      }
    }
  }

  /**
   * 获取指定类型的事件节点数量
   * @param eventType 事件类型
   */
  public getNodeCount(eventType?: EventType): number {
    if (eventType) {
      return this.eventNodes.get(eventType)?.length || 0;
    }

    let total = 0;
    for (const nodes of this.eventNodes.values()) {
      total += nodes.length;
    }
    return total;
  }
}
