/**
 * 冲突解决器
 * 处理多用户交互冲突和优先级管理
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import type { Entity } from '../../core/Entity';

/**
 * 冲突类型
 */
export enum ConflictType {
  OBJECT_CONTENTION = 'object_contention',
  SPACE_COLLISION = 'space_collision',
  PERMISSION_VIOLATION = 'permission_violation',
  RESOURCE_CONFLICT = 'resource_conflict',
  TEMPORAL_CONFLICT = 'temporal_conflict'
}

/**
 * 冲突信息
 */
export interface ConflictInfo {
  id: string;
  type: ConflictType;
  participants: string[];
  targetObject?: Entity;
  severity: ConflictSeverity;
  timestamp: number;
  data: any;
}

/**
 * 冲突严重程度
 */
export enum ConflictSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * 解决策略
 */
export enum ResolutionStrategy {
  PRIORITY_BASED = 'priority_based',
  FIRST_COME_FIRST_SERVE = 'first_come_first_serve',
  COLLABORATIVE = 'collaborative',
  ADMIN_OVERRIDE = 'admin_override',
  RANDOM = 'random'
}

/**
 * 解决结果
 */
export interface ResolutionResult {
  conflictId: string;
  strategy: ResolutionStrategy;
  winner?: string;
  action: ResolutionAction;
  participants: { [userId: string]: ResolutionAction };
  timestamp: number;
}

/**
 * 解决动作
 */
export enum ResolutionAction {
  ALLOW = 'allow',
  DENY = 'deny',
  QUEUE = 'queue',
  SHARE = 'share',
  REDIRECT = 'redirect'
}

/**
 * 冲突解决器
 */
export class ConflictResolver extends EventEmitter {
  private config: any;
  private activeConflicts: Map<string, ConflictInfo> = new Map();
  private resolutionHistory: ResolutionResult[] = [];
  private userPriorities: Map<string, number> = new Map();
  private objectLocks: Map<string, string> = new Map(); // objectId -> userId
  private interactionQueues: Map<string, string[]> = new Map(); // objectId -> userIds

  constructor(config: any) {
    super();
    this.config = config;
  }

  /**
   * 检查交互冲突
   */
  public checkInteractionConflicts(
    userEntity: Entity,
    interactionData: any,
    allUsers: any[]
  ): void {
    const userId = userEntity.id;

    // 检查物体争夺冲突
    this.checkObjectContentionConflicts(userId, interactionData, allUsers);

    // 检查空间碰撞冲突
    this.checkSpaceCollisionConflicts(userId, userEntity, allUsers);

    // 检查权限违规冲突
    this.checkPermissionViolationConflicts(userId, interactionData, allUsers);

    // 检查资源冲突
    this.checkResourceConflicts(userId, interactionData, allUsers);
  }

  /**
   * 检查物体争夺冲突
   */
  private checkObjectContentionConflicts(
    userId: string,
    interactionData: any,
    allUsers: any[]
  ): void {
    // 检查是否多个用户试图抓取同一物体
    if (interactionData.leftAdvancedGesture?.type === 'grab' || 
        interactionData.rightAdvancedGesture?.type === 'grab') {
      
      // 找到目标物体
      const targetObject = this.findTargetObject(userId, interactionData);
      if (!targetObject) return;

      // 检查其他用户是否也在尝试抓取
      const competingUsers = this.findCompetingUsers(targetObject, allUsers, userId);
      
      if (competingUsers.length > 0) {
        const conflict: ConflictInfo = {
          id: this.generateConflictId(),
          type: ConflictType.OBJECT_CONTENTION,
          participants: [userId, ...competingUsers],
          targetObject,
          severity: this.calculateSeverity(competingUsers.length),
          timestamp: Date.now(),
          data: {
            object: targetObject.id,
            gesture: interactionData.leftAdvancedGesture?.type || interactionData.rightAdvancedGesture?.type
          }
        };

        this.registerConflict(conflict);
      }
    }
  }

  /**
   * 检查空间碰撞冲突
   */
  private checkSpaceCollisionConflicts(
    userId: string,
    userEntity: Entity,
    allUsers: any[]
  ): void {
    const userTransform = userEntity.getComponent('Transform');
    if (!userTransform) return;

    const collisionThreshold = 0.5; // 0.5米碰撞阈值

    for (const otherUser of allUsers) {
      if (otherUser.id === userId || !otherUser.entity) continue;

      const otherTransform = otherUser.entity.getComponent('Transform');
      if (!otherTransform) continue;

      // 获取位置属性
      const userPos = (userTransform as any).position || (userTransform as any).getPosition?.() || { x: 0, y: 0, z: 0 };
      const otherPos = (otherTransform as any).position || (otherTransform as any).getPosition?.() || { x: 0, y: 0, z: 0 };

      const distance = Math.sqrt(
        Math.pow(userPos.x - otherPos.x, 2) +
        Math.pow(userPos.y - otherPos.y, 2) +
        Math.pow(userPos.z - otherPos.z, 2)
      );

      if (distance < collisionThreshold) {
        const conflict: ConflictInfo = {
          id: this.generateConflictId(),
          type: ConflictType.SPACE_COLLISION,
          participants: [userId, otherUser.id],
          severity: ConflictSeverity.MEDIUM,
          timestamp: Date.now(),
          data: {
            distance,
            threshold: collisionThreshold,
            positions: {
              [userId]: userPos,
              [otherUser.id]: otherPos
            }
          }
        };

        this.registerConflict(conflict);
      }
    }
  }

  /**
   * 检查权限违规冲突
   */
  private checkPermissionViolationConflicts(
    userId: string,
    interactionData: any,
    allUsers: any[]
  ): void {
    const user = allUsers.find(u => u.id === userId);
    if (!user) return;

    // 检查交互权限
    if ((interactionData.leftAdvancedGesture?.type === 'grab' || 
         interactionData.rightAdvancedGesture?.type === 'grab') &&
        !user.permissions.canInteract) {
      
      const conflict: ConflictInfo = {
        id: this.generateConflictId(),
        type: ConflictType.PERMISSION_VIOLATION,
        participants: [userId],
        severity: ConflictSeverity.HIGH,
        timestamp: Date.now(),
        data: {
          action: 'grab',
          permission: 'canInteract',
          userRole: user.role
        }
      };

      this.registerConflict(conflict);
    }
  }

  /**
   * 检查资源冲突
   */
  private checkResourceConflicts(
    userId: string,
    _interactionData: any,
    allUsers: any[]
  ): void {
    // 检查是否超出用户的交互距离限制
    const user = allUsers.find(u => u.id === userId);
    if (!user) return;

    // 这里应该检查用户是否试图与超出距离的物体交互
    // 简化实现，使用 user.permissions.maxInteractionDistance
  }

  /**
   * 注册冲突
   */
  private registerConflict(conflict: ConflictInfo): void {
    // 检查是否已存在相似冲突
    const existingConflict = this.findSimilarConflict(conflict);
    if (existingConflict) {
      // 更新现有冲突
      existingConflict.timestamp = conflict.timestamp;
      return;
    }

    this.activeConflicts.set(conflict.id, conflict);
    
    Debug.log('ConflictResolver', '检测到冲突', conflict);
    this.emit('conflictDetected', conflict);

    // 自动解决冲突
    setTimeout(() => {
      this.resolveConflict(conflict.id);
    }, 100); // 100ms后解决
  }

  /**
   * 解决冲突
   */
  public resolveConflict(conflictId: string): ResolutionResult | null {
    const conflict = this.activeConflicts.get(conflictId);
    if (!conflict) return null;

    const strategy = this.selectResolutionStrategy(conflict);
    const result = this.applyResolutionStrategy(conflict, strategy);

    // 记录解决结果
    this.resolutionHistory.push(result);
    
    // 限制历史记录大小
    if (this.resolutionHistory.length > 100) {
      this.resolutionHistory.shift();
    }

    // 移除已解决的冲突
    this.activeConflicts.delete(conflictId);

    Debug.log('ConflictResolver', '冲突已解决', result);
    this.emit('conflictResolved', result);

    return result;
  }

  /**
   * 选择解决策略
   */
  private selectResolutionStrategy(conflict: ConflictInfo): ResolutionStrategy {
    switch (conflict.type) {
      case ConflictType.OBJECT_CONTENTION:
        return ResolutionStrategy.PRIORITY_BASED;
      
      case ConflictType.SPACE_COLLISION:
        return ResolutionStrategy.COLLABORATIVE;
      
      case ConflictType.PERMISSION_VIOLATION:
        return ResolutionStrategy.ADMIN_OVERRIDE;
      
      case ConflictType.RESOURCE_CONFLICT:
        return ResolutionStrategy.FIRST_COME_FIRST_SERVE;
      
      default:
        return ResolutionStrategy.PRIORITY_BASED;
    }
  }

  /**
   * 应用解决策略
   */
  private applyResolutionStrategy(
    conflict: ConflictInfo,
    strategy: ResolutionStrategy
  ): ResolutionResult {
    const result: ResolutionResult = {
      conflictId: conflict.id,
      strategy,
      action: ResolutionAction.ALLOW,
      participants: {},
      timestamp: Date.now()
    };

    switch (strategy) {
      case ResolutionStrategy.PRIORITY_BASED:
        this.applyPriorityBasedResolution(conflict, result);
        break;
      
      case ResolutionStrategy.FIRST_COME_FIRST_SERVE:
        this.applyFirstComeFirstServeResolution(conflict, result);
        break;
      
      case ResolutionStrategy.COLLABORATIVE:
        this.applyCollaborativeResolution(conflict, result);
        break;
      
      case ResolutionStrategy.ADMIN_OVERRIDE:
        this.applyAdminOverrideResolution(conflict, result);
        break;
      
      case ResolutionStrategy.RANDOM:
        this.applyRandomResolution(conflict, result);
        break;
    }

    return result;
  }

  /**
   * 应用基于优先级的解决方案
   */
  private applyPriorityBasedResolution(conflict: ConflictInfo, result: ResolutionResult): void {
    const participants = conflict.participants;
    const priorities = participants.map(userId => ({
      userId,
      priority: this.getUserPriority(userId)
    }));

    // 按优先级排序
    priorities.sort((a, b) => b.priority - a.priority);

    // 最高优先级用户获胜
    result.winner = priorities[0].userId;
    
    for (const participant of participants) {
      if (participant === result.winner) {
        result.participants[participant] = ResolutionAction.ALLOW;
      } else {
        result.participants[participant] = ResolutionAction.QUEUE;
        this.addToQueue(conflict.targetObject?.id || 'unknown', participant);
      }
    }
  }

  /**
   * 应用先到先得解决方案
   */
  private applyFirstComeFirstServeResolution(conflict: ConflictInfo, result: ResolutionResult): void {
    // 第一个参与者获胜
    result.winner = conflict.participants[0];
    
    for (let i = 0; i < conflict.participants.length; i++) {
      const participant = conflict.participants[i];
      if (i === 0) {
        result.participants[participant] = ResolutionAction.ALLOW;
      } else {
        result.participants[participant] = ResolutionAction.QUEUE;
      }
    }
  }

  /**
   * 应用协作解决方案
   */
  private applyCollaborativeResolution(conflict: ConflictInfo, result: ResolutionResult): void {
    // 允许所有参与者协作
    for (const participant of conflict.participants) {
      result.participants[participant] = ResolutionAction.SHARE;
    }
  }

  /**
   * 应用管理员覆盖解决方案
   */
  private applyAdminOverrideResolution(conflict: ConflictInfo, result: ResolutionResult): void {
    // 拒绝所有非管理员用户
    for (const participant of conflict.participants) {
      const userPriority = this.getUserPriority(participant);
      if (userPriority >= 100) { // 管理员优先级
        result.participants[participant] = ResolutionAction.ALLOW;
        result.winner = participant;
      } else {
        result.participants[participant] = ResolutionAction.DENY;
      }
    }
  }

  /**
   * 应用随机解决方案
   */
  private applyRandomResolution(conflict: ConflictInfo, result: ResolutionResult): void {
    const randomIndex = Math.floor(Math.random() * conflict.participants.length);
    result.winner = conflict.participants[randomIndex];
    
    for (let i = 0; i < conflict.participants.length; i++) {
      const participant = conflict.participants[i];
      if (i === randomIndex) {
        result.participants[participant] = ResolutionAction.ALLOW;
      } else {
        result.participants[participant] = ResolutionAction.DENY;
      }
    }
  }

  /**
   * 查找目标物体
   */
  private findTargetObject(_userId: string, _interactionData: any): Entity | null {
    // 简化实现，实际应该根据手势位置和方向找到目标物体
    return null;
  }

  /**
   * 查找竞争用户
   */
  private findCompetingUsers(_targetObject: Entity, _allUsers: any[], _excludeUserId: string): string[] {
    // 简化实现，实际应该检查其他用户是否也在尝试抓取同一物体
    return [];
  }

  /**
   * 查找相似冲突
   */
  private findSimilarConflict(conflict: ConflictInfo): ConflictInfo | null {
    for (const existingConflict of this.activeConflicts.values()) {
      if (existingConflict.type === conflict.type &&
          this.arraysEqual(existingConflict.participants, conflict.participants)) {
        return existingConflict;
      }
    }
    return null;
  }

  /**
   * 计算严重程度
   */
  private calculateSeverity(participantCount: number): ConflictSeverity {
    if (participantCount >= 4) return ConflictSeverity.CRITICAL;
    if (participantCount >= 3) return ConflictSeverity.HIGH;
    if (participantCount >= 2) return ConflictSeverity.MEDIUM;
    return ConflictSeverity.LOW;
  }

  /**
   * 获取用户优先级
   */
  private getUserPriority(userId: string): number {
    return this.userPriorities.get(userId) || 50; // 默认优先级
  }

  /**
   * 设置用户优先级
   */
  public setUserPriority(userId: string, priority: number): void {
    this.userPriorities.set(userId, priority);
  }

  /**
   * 添加到队列
   */
  private addToQueue(objectId: string, userId: string): void {
    if (!this.interactionQueues.has(objectId)) {
      this.interactionQueues.set(objectId, []);
    }
    
    const queue = this.interactionQueues.get(objectId)!;
    if (!queue.includes(userId)) {
      queue.push(userId);
    }
  }

  /**
   * 数组相等比较
   */
  private arraysEqual(arr1: string[], arr2: string[]): boolean {
    if (arr1.length !== arr2.length) return false;
    const sorted1 = [...arr1].sort();
    const sorted2 = [...arr2].sort();
    return sorted1.every((val, index) => val === sorted2[index]);
  }

  /**
   * 生成冲突ID
   */
  private generateConflictId(): string {
    return `conflict_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 获取活跃冲突
   */
  public getActiveConflicts(): ConflictInfo[] {
    return Array.from(this.activeConflicts.values());
  }

  /**
   * 获取解决历史
   */
  public getResolutionHistory(): ResolutionResult[] {
    return [...this.resolutionHistory];
  }

  /**
   * 获取统计信息
   */
  public getStats(): any {
    return {
      activeConflicts: this.activeConflicts.size,
      totalResolutions: this.resolutionHistory.length,
      conflictTypes: this.getConflictTypeStats(),
      resolutionStrategies: this.getResolutionStrategyStats()
    };
  }

  /**
   * 获取冲突类型统计
   */
  private getConflictTypeStats(): any {
    const stats: any = {};
    
    for (const conflict of this.activeConflicts.values()) {
      stats[conflict.type] = (stats[conflict.type] || 0) + 1;
    }
    
    return stats;
  }

  /**
   * 获取解决策略统计
   */
  private getResolutionStrategyStats(): any {
    const stats: any = {};
    
    for (const result of this.resolutionHistory) {
      stats[result.strategy] = (stats[result.strategy] || 0) + 1;
    }
    
    return stats;
  }

  /**
   * 销毁解决器
   */
  public destroy(): void {
    this.activeConflicts.clear();
    this.resolutionHistory = [];
    this.userPriorities.clear();
    this.objectLocks.clear();
    this.interactionQueues.clear();
    this.removeAllListeners();
  }
}
