/**
 * 移动端同步API控制器
 * 
 * 提供移动端专用的数据同步API接口
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Headers,
  HttpStatus,
  HttpException,
  UseGuards,
  Logger,
  Req,
  Res
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiHeader,
  ApiBearerAuth
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { MobileSyncService } from '../services/mobile-sync.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { MobileDeviceGuard } from '../guards/mobile-device.guard';

/**
 * 数据变更DTO
 */
export class DataChangeDto {
  id: string;
  type: 'create' | 'update' | 'delete' | 'move';
  entityType: 'node' | 'tree' | 'config';
  entityId: string;
  data: any;
  timestamp: number;
  deviceId: string;
  userId: string;
  version: number;
}

/**
 * 同步上传DTO
 */
export class SyncUploadDto {
  changes: DataChangeDto[];
  deviceId: string;
  timestamp: number;
  checksum?: string;
}

/**
 * 同步下载查询DTO
 */
export class SyncDownloadQueryDto {
  lastSyncTime?: number;
  entityTypes?: string[];
  limit?: number = 100;
  includeDeleted?: boolean = false;
}

/**
 * 冲突解决DTO
 */
export class ConflictResolutionDto {
  conflictId: string;
  resolution: 'accept_local' | 'accept_remote' | 'merge';
  mergedData?: any;
}

/**
 * 移动端同步控制器
 */
@ApiTags('移动端同步')
@Controller('api/mobile/sync')
@UseGuards(JwtAuthGuard, MobileDeviceGuard)
@ApiBearerAuth()
export class MobileSyncController {
  private readonly logger = new Logger(MobileSyncController.name);

  constructor(
    private readonly mobileSyncService: MobileSyncService
  ) {}

  /**
   * 上传数据变更
   */
  @Post('upload')
  @ApiOperation({ summary: '上传数据变更' })
  @ApiHeader({ name: 'X-Device-ID', description: '设备ID' })
  @ApiHeader({ name: 'X-User-ID', description: '用户ID' })
  @ApiResponse({ status: 200, description: '上传成功' })
  @ApiResponse({ status: 409, description: '存在冲突' })
  async uploadChanges(
    @Body() uploadDto: SyncUploadDto,
    @Headers('X-Device-ID') deviceId: string,
    @Headers('X-User-ID') userId: string,
    @Req() req: Request
  ) {
    try {
      // 验证设备ID和用户ID
      if (!deviceId || !userId) {
        throw new HttpException('缺少设备ID或用户ID', HttpStatus.BAD_REQUEST);
      }
      
      if (uploadDto.deviceId !== deviceId) {
        throw new HttpException('设备ID不匹配', HttpStatus.BAD_REQUEST);
      }
      
      // 验证数据完整性
      if (uploadDto.checksum) {
        const calculatedChecksum = this.calculateChecksum(uploadDto.changes);
        if (calculatedChecksum !== uploadDto.checksum) {
          throw new HttpException('数据校验失败', HttpStatus.BAD_REQUEST);
        }
      }
      
      // 处理上传的变更
      const result = await this.mobileSyncService.processUploadedChanges(
        userId,
        deviceId,
        uploadDto.changes
      );
      
      this.logger.log(`用户 ${userId} 设备 ${deviceId} 上传了 ${uploadDto.changes.length} 个变更`);
      
      return {
        success: true,
        data: {
          accepted: result.acceptedChanges,
          conflicts: result.conflicts,
          rejected: result.rejectedChanges,
          timestamp: Date.now()
        }
      };
      
    } catch (error) {
      this.logger.error('上传数据变更失败:', error);
      throw new HttpException(
        error.message || '上传数据变更失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 下载数据变更
   */
  @Get('download')
  @ApiOperation({ summary: '下载数据变更' })
  @ApiHeader({ name: 'X-Device-ID', description: '设备ID' })
  @ApiHeader({ name: 'X-User-ID', description: '用户ID' })
  @ApiHeader({ name: 'X-Last-Sync', description: '上次同步时间戳' })
  @ApiQuery({ name: 'lastSyncTime', required: false, description: '上次同步时间' })
  @ApiQuery({ name: 'entityTypes', required: false, description: '实体类型过滤' })
  @ApiQuery({ name: 'limit', required: false, description: '限制数量' })
  @ApiQuery({ name: 'includeDeleted', required: false, description: '包含已删除项' })
  @ApiResponse({ status: 200, description: '下载成功' })
  async downloadChanges(
    @Query() queryDto: SyncDownloadQueryDto,
    @Headers('X-Device-ID') deviceId: string,
    @Headers('X-User-ID') userId: string,
    @Headers('X-Last-Sync') lastSyncHeader?: string
  ) {
    try {
      // 验证设备ID和用户ID
      if (!deviceId || !userId) {
        throw new HttpException('缺少设备ID或用户ID', HttpStatus.BAD_REQUEST);
      }
      
      // 获取上次同步时间
      const lastSyncTime = queryDto.lastSyncTime || 
        (lastSyncHeader ? parseInt(lastSyncHeader) : 0);
      
      // 获取变更数据
      const changes = await this.mobileSyncService.getChangesForDevice(
        userId,
        deviceId,
        lastSyncTime,
        {
          entityTypes: queryDto.entityTypes,
          limit: queryDto.limit,
          includeDeleted: queryDto.includeDeleted
        }
      );
      
      // 获取同步统计
      const stats = await this.mobileSyncService.getSyncStats(userId, deviceId);
      
      this.logger.log(`用户 ${userId} 设备 ${deviceId} 下载了 ${changes.length} 个变更`);
      
      return {
        success: true,
        data: {
          changes,
          stats,
          timestamp: Date.now(),
          hasMore: changes.length === queryDto.limit
        }
      };
      
    } catch (error) {
      this.logger.error('下载数据变更失败:', error);
      throw new HttpException(
        error.message || '下载数据变更失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取同步状态
   */
  @Get('status')
  @ApiOperation({ summary: '获取同步状态' })
  @ApiHeader({ name: 'X-Device-ID', description: '设备ID' })
  @ApiHeader({ name: 'X-User-ID', description: '用户ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getSyncStatus(
    @Headers('X-Device-ID') deviceId: string,
    @Headers('X-User-ID') userId: string
  ) {
    try {
      if (!deviceId || !userId) {
        throw new HttpException('缺少设备ID或用户ID', HttpStatus.BAD_REQUEST);
      }
      
      const status = await this.mobileSyncService.getDeviceStatus(userId, deviceId);
      const stats = await this.mobileSyncService.getSyncStats(userId, deviceId);
      const conflicts = await this.mobileSyncService.getConflicts(userId, deviceId);
      
      return {
        success: true,
        data: {
          status,
          stats,
          conflicts: conflicts.length,
          timestamp: Date.now()
        }
      };
      
    } catch (error) {
      this.logger.error('获取同步状态失败:', error);
      throw new HttpException(
        error.message || '获取同步状态失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 解决冲突
   */
  @Post('resolve-conflict')
  @ApiOperation({ summary: '解决同步冲突' })
  @ApiHeader({ name: 'X-Device-ID', description: '设备ID' })
  @ApiHeader({ name: 'X-User-ID', description: '用户ID' })
  @ApiResponse({ status: 200, description: '冲突解决成功' })
  async resolveConflict(
    @Body() resolutionDto: ConflictResolutionDto,
    @Headers('X-Device-ID') deviceId: string,
    @Headers('X-User-ID') userId: string
  ) {
    try {
      if (!deviceId || !userId) {
        throw new HttpException('缺少设备ID或用户ID', HttpStatus.BAD_REQUEST);
      }
      
      const result = await this.mobileSyncService.resolveConflict(
        userId,
        deviceId,
        resolutionDto.conflictId,
        resolutionDto.resolution,
        resolutionDto.mergedData
      );
      
      this.logger.log(`用户 ${userId} 设备 ${deviceId} 解决了冲突 ${resolutionDto.conflictId}`);
      
      return {
        success: true,
        data: result,
        message: '冲突解决成功'
      };
      
    } catch (error) {
      this.logger.error('解决冲突失败:', error);
      throw new HttpException(
        error.message || '解决冲突失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取冲突列表
   */
  @Get('conflicts')
  @ApiOperation({ summary: '获取冲突列表' })
  @ApiHeader({ name: 'X-Device-ID', description: '设备ID' })
  @ApiHeader({ name: 'X-User-ID', description: '用户ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getConflicts(
    @Headers('X-Device-ID') deviceId: string,
    @Headers('X-User-ID') userId: string
  ) {
    try {
      if (!deviceId || !userId) {
        throw new HttpException('缺少设备ID或用户ID', HttpStatus.BAD_REQUEST);
      }
      
      const conflicts = await this.mobileSyncService.getConflicts(userId, deviceId);
      
      return {
        success: true,
        data: {
          conflicts,
          count: conflicts.length,
          timestamp: Date.now()
        }
      };
      
    } catch (error) {
      this.logger.error('获取冲突列表失败:', error);
      throw new HttpException(
        error.message || '获取冲突列表失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 强制同步
   */
  @Post('force-sync')
  @ApiOperation({ summary: '强制同步' })
  @ApiHeader({ name: 'X-Device-ID', description: '设备ID' })
  @ApiHeader({ name: 'X-User-ID', description: '用户ID' })
  @ApiResponse({ status: 200, description: '同步成功' })
  async forceSync(
    @Headers('X-Device-ID') deviceId: string,
    @Headers('X-User-ID') userId: string,
    @Body() options?: {
      resetLocal?: boolean;
      resetRemote?: boolean;
      conflictResolution?: 'client_wins' | 'server_wins';
    }
  ) {
    try {
      if (!deviceId || !userId) {
        throw new HttpException('缺少设备ID或用户ID', HttpStatus.BAD_REQUEST);
      }
      
      const result = await this.mobileSyncService.forceSync(
        userId,
        deviceId,
        options || {}
      );
      
      this.logger.log(`用户 ${userId} 设备 ${deviceId} 执行了强制同步`);
      
      return {
        success: true,
        data: result,
        message: '强制同步完成'
      };
      
    } catch (error) {
      this.logger.error('强制同步失败:', error);
      throw new HttpException(
        error.message || '强制同步失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 重置设备数据
   */
  @Delete('reset/:deviceId')
  @ApiOperation({ summary: '重置设备数据' })
  @ApiParam({ name: 'deviceId', description: '设备ID' })
  @ApiHeader({ name: 'X-User-ID', description: '用户ID' })
  @ApiResponse({ status: 200, description: '重置成功' })
  async resetDeviceData(
    @Param('deviceId') deviceId: string,
    @Headers('X-User-ID') userId: string
  ) {
    try {
      if (!deviceId || !userId) {
        throw new HttpException('缺少设备ID或用户ID', HttpStatus.BAD_REQUEST);
      }
      
      await this.mobileSyncService.resetDeviceData(userId, deviceId);
      
      this.logger.log(`用户 ${userId} 重置了设备 ${deviceId} 的数据`);
      
      return {
        success: true,
        message: '设备数据已重置'
      };
      
    } catch (error) {
      this.logger.error('重置设备数据失败:', error);
      throw new HttpException(
        error.message || '重置设备数据失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取设备列表
   */
  @Get('devices')
  @ApiOperation({ summary: '获取用户设备列表' })
  @ApiHeader({ name: 'X-User-ID', description: '用户ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getUserDevices(
    @Headers('X-User-ID') userId: string
  ) {
    try {
      if (!userId) {
        throw new HttpException('缺少用户ID', HttpStatus.BAD_REQUEST);
      }
      
      const devices = await this.mobileSyncService.getUserDevices(userId);
      
      return {
        success: true,
        data: {
          devices,
          count: devices.length,
          timestamp: Date.now()
        }
      };
      
    } catch (error) {
      this.logger.error('获取设备列表失败:', error);
      throw new HttpException(
        error.message || '获取设备列表失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 注册设备
   */
  @Post('register-device')
  @ApiOperation({ summary: '注册移动设备' })
  @ApiResponse({ status: 200, description: '注册成功' })
  async registerDevice(
    @Body() deviceInfo: {
      deviceId: string;
      deviceName: string;
      platform: string;
      version: string;
      capabilities: string[];
    },
    @Headers('X-User-ID') userId: string
  ) {
    try {
      if (!userId) {
        throw new HttpException('缺少用户ID', HttpStatus.BAD_REQUEST);
      }
      
      const device = await this.mobileSyncService.registerDevice(
        userId,
        deviceInfo
      );
      
      this.logger.log(`用户 ${userId} 注册了设备 ${deviceInfo.deviceId}`);
      
      return {
        success: true,
        data: device,
        message: '设备注册成功'
      };
      
    } catch (error) {
      this.logger.error('注册设备失败:', error);
      throw new HttpException(
        error.message || '注册设备失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取同步统计
   */
  @Get('stats')
  @ApiOperation({ summary: '获取同步统计' })
  @ApiHeader({ name: 'X-Device-ID', description: '设备ID' })
  @ApiHeader({ name: 'X-User-ID', description: '用户ID' })
  @ApiQuery({ name: 'period', required: false, description: '统计周期' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getSyncStats(
    @Headers('X-Device-ID') deviceId: string,
    @Headers('X-User-ID') userId: string,
    @Query('period') period: string = '24h'
  ) {
    try {
      if (!deviceId || !userId) {
        throw new HttpException('缺少设备ID或用户ID', HttpStatus.BAD_REQUEST);
      }
      
      const stats = await this.mobileSyncService.getDetailedStats(
        userId,
        deviceId,
        period
      );
      
      return {
        success: true,
        data: {
          stats,
          period,
          timestamp: Date.now()
        }
      };
      
    } catch (error) {
      this.logger.error('获取同步统计失败:', error);
      throw new HttpException(
        error.message || '获取同步统计失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 健康检查
   */
  @Get('health')
  @ApiOperation({ summary: '移动端服务健康检查' })
  @ApiResponse({ status: 200, description: '服务正常' })
  async healthCheck() {
    try {
      const health = await this.mobileSyncService.getServiceHealth();
      
      return {
        success: true,
        data: {
          status: 'healthy',
          timestamp: Date.now(),
          ...health
        }
      };
      
    } catch (error) {
      this.logger.error('健康检查失败:', error);
      throw new HttpException(
        '服务不可用',
        HttpStatus.SERVICE_UNAVAILABLE
      );
    }
  }

  /**
   * 计算校验和
   */
  private calculateChecksum(data: any): string {
    // 简化的校验和计算
    const str = JSON.stringify(data);
    let hash = 0;
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    return Math.abs(hash).toString(16);
  }
}
