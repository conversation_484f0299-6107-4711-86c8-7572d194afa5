import { Injectable, BadRequestException } from '@nestjs/common';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as pdfParse from 'pdf-parse';
import * as mammoth from 'mammoth';
import * as XLSX from 'xlsx';

export interface ProcessedDocument {
  text: string;
  chunks: TextChunk[];
  metadata: {
    title?: string;
    author?: string;
    pageCount?: number;
    wordCount?: number;
    language?: string;
    processingTime?: number;
  };
}

export interface TextChunk {
  id: string;
  text: string;
  startIndex: number;
  endIndex: number;
  metadata?: Record<string, any>;
}

@Injectable()
export class DocumentProcessorService {
  /**
   * 处理文档
   */
  async processDocument(file: Express.Multer.File): Promise<ProcessedDocument> {
    const startTime = Date.now();
    const extension = path.extname(file.originalname).toLowerCase();

    let result: ProcessedDocument;

    try {
      switch (extension) {
        case '.pdf':
          result = await this.processPDF(file);
          break;
        case '.docx':
          result = await this.processDocx(file);
          break;
        case '.pptx':
          result = await this.processPPTX(file);
          break;
        case '.xlsx':
        case '.xls':
          result = await this.processExcel(file);
          break;
        case '.txt':
        case '.md':
          result = await this.processText(file);
          break;
        default:
          throw new BadRequestException(`不支持的文件类型: ${extension}`);
      }

      // 添加处理时间
      result.metadata.processingTime = Date.now() - startTime;

      return result;
    } catch (error) {
      throw new BadRequestException(`文档处理失败: ${error.message}`);
    }
  }

  /**
   * 处理PDF文件
   */
  private async processPDF(file: Express.Multer.File): Promise<ProcessedDocument> {
    const buffer = await fs.readFile(file.path);
    const data = await pdfParse(buffer);

    const text = this.cleanText(data.text);
    const chunks = this.chunkText(text);

    return {
      text,
      chunks,
      metadata: {
        title: data.info?.Title || path.basename(file.originalname, '.pdf'),
        author: data.info?.Author,
        pageCount: data.numpages,
        wordCount: this.countWords(text),
        language: this.detectLanguage(text),
      },
    };
  }

  /**
   * 处理Word文档
   */
  private async processDocx(file: Express.Multer.File): Promise<ProcessedDocument> {
    const buffer = await fs.readFile(file.path);
    const result = await mammoth.extractRawText({ buffer });

    const text = this.cleanText(result.value);
    const chunks = this.chunkText(text);

    return {
      text,
      chunks,
      metadata: {
        title: path.basename(file.originalname, '.docx'),
        wordCount: this.countWords(text),
        language: this.detectLanguage(text),
      },
    };
  }

  /**
   * 处理PowerPoint文件
   */
  private async processPPTX(file: Express.Multer.File): Promise<ProcessedDocument> {
    // 这里需要使用专门的PPTX解析库
    // 暂时使用简单的文本提取
    const text = await this.extractTextFromPPTX(file);
    const cleanedText = this.cleanText(text);
    const chunks = this.chunkText(cleanedText);

    return {
      text: cleanedText,
      chunks,
      metadata: {
        title: path.basename(file.originalname, '.pptx'),
        wordCount: this.countWords(cleanedText),
        language: this.detectLanguage(cleanedText),
      },
    };
  }

  /**
   * 处理Excel文件
   */
  private async processExcel(file: Express.Multer.File): Promise<ProcessedDocument> {
    const workbook = XLSX.readFile(file.path);
    let text = '';

    // 遍历所有工作表
    workbook.SheetNames.forEach(sheetName => {
      const worksheet = workbook.Sheets[sheetName];
      const sheetText = XLSX.utils.sheet_to_txt(worksheet);
      text += `\n=== ${sheetName} ===\n${sheetText}\n`;
    });

    const cleanedText = this.cleanText(text);
    const chunks = this.chunkText(cleanedText);

    return {
      text: cleanedText,
      chunks,
      metadata: {
        title: path.basename(file.originalname, path.extname(file.originalname)),
        wordCount: this.countWords(cleanedText),
        language: this.detectLanguage(cleanedText),
      },
    };
  }

  /**
   * 处理文本文件
   */
  private async processText(file: Express.Multer.File): Promise<ProcessedDocument> {
    const text = await fs.readFile(file.path, 'utf-8');
    const cleanedText = this.cleanText(text);
    const chunks = this.chunkText(cleanedText);

    return {
      text: cleanedText,
      chunks,
      metadata: {
        title: path.basename(file.originalname, path.extname(file.originalname)),
        wordCount: this.countWords(cleanedText),
        language: this.detectLanguage(cleanedText),
      },
    };
  }

  /**
   * 从PPTX提取文本（简化实现）
   */
  private async extractTextFromPPTX(file: Express.Multer.File): Promise<string> {
    // 这里应该使用专门的PPTX解析库
    // 暂时返回文件名作为占位符
    return `PowerPoint文档: ${file.originalname}`;
  }

  /**
   * 清理文本
   */
  private cleanText(text: string): string {
    return text
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\n{3,}/g, '\n\n')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 文本分块
   */
  private chunkText(text: string, chunkSize: number = 1000, overlap: number = 200): TextChunk[] {
    const chunks: TextChunk[] = [];
    const sentences = this.splitIntoSentences(text);
    
    let currentChunk = '';
    let chunkIndex = 0;
    let startIndex = 0;

    for (let i = 0; i < sentences.length; i++) {
      const sentence = sentences[i];
      
      if (currentChunk.length + sentence.length > chunkSize && currentChunk.length > 0) {
        // 创建当前块
        chunks.push({
          id: `chunk_${chunkIndex++}`,
          text: currentChunk.trim(),
          startIndex,
          endIndex: startIndex + currentChunk.length,
        });

        // 计算重叠部分
        const overlapText = this.getOverlapText(currentChunk, overlap);
        currentChunk = overlapText + sentence;
        startIndex = text.indexOf(currentChunk);
      } else {
        currentChunk += sentence;
      }
    }

    // 添加最后一个块
    if (currentChunk.trim()) {
      chunks.push({
        id: `chunk_${chunkIndex}`,
        text: currentChunk.trim(),
        startIndex,
        endIndex: startIndex + currentChunk.length,
      });
    }

    return chunks;
  }

  /**
   * 分割句子
   */
  private splitIntoSentences(text: string): string[] {
    // 简单的句子分割，可以根据需要改进
    return text.split(/[。！？.!?]+/).filter(s => s.trim().length > 0);
  }

  /**
   * 获取重叠文本
   */
  private getOverlapText(text: string, overlapSize: number): string {
    if (text.length <= overlapSize) {
      return text;
    }
    return text.slice(-overlapSize);
  }

  /**
   * 统计词数
   */
  private countWords(text: string): number {
    // 中文按字符数，英文按单词数
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    return chineseChars + englishWords;
  }

  /**
   * 检测语言
   */
  private detectLanguage(text: string): string {
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const totalChars = text.length;
    
    if (chineseChars / totalChars > 0.3) {
      return 'zh-CN';
    } else {
      return 'en';
    }
  }
}
