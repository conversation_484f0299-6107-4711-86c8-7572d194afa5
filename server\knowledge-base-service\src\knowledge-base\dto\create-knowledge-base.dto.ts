import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsUUID, IsObject, IsEnum, IsNumber, Min, Max } from 'class-validator';
import { KnowledgeBaseStatus } from '../entities/knowledge-base.entity';

export class CreateKnowledgeBaseDto {
  @ApiProperty({ description: '知识库名称', example: '医疗展厅知识库' })
  @IsString()
  name: string;

  @ApiProperty({ description: '知识库描述', required: false, example: '包含医疗设备和健康知识的综合知识库' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '关联场景ID', required: false, example: 'scene_123' })
  @IsOptional()
  @IsUUID()
  sceneId?: string;

  @ApiProperty({ description: '知识库状态', enum: KnowledgeBaseStatus, required: false })
  @IsOptional()
  @IsEnum(KnowledgeBaseStatus)
  status?: KnowledgeBaseStatus;

  @ApiProperty({ 
    description: '配置信息', 
    required: false,
    example: {
      chunkSize: 1000,
      chunkOverlap: 200,
      embeddingModel: 'text-embedding-ada-002',
      searchThreshold: 0.7,
      maxResults: 10
    }
  })
  @IsOptional()
  @IsObject()
  config?: {
    chunkSize?: number;
    chunkOverlap?: number;
    embeddingModel?: string;
    searchThreshold?: number;
    maxResults?: number;
  };
}

export class UpdateKnowledgeBaseDto {
  @ApiProperty({ description: '知识库名称', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: '知识库描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '关联场景ID', required: false })
  @IsOptional()
  @IsUUID()
  sceneId?: string;

  @ApiProperty({ description: '知识库状态', enum: KnowledgeBaseStatus, required: false })
  @IsOptional()
  @IsEnum(KnowledgeBaseStatus)
  status?: KnowledgeBaseStatus;

  @ApiProperty({ description: '配置信息', required: false })
  @IsOptional()
  @IsObject()
  config?: {
    chunkSize?: number;
    chunkOverlap?: number;
    embeddingModel?: string;
    searchThreshold?: number;
    maxResults?: number;
  };
}

export class SearchKnowledgeBaseDto {
  @ApiProperty({ description: '搜索查询', example: '什么是高血压？' })
  @IsString()
  query: string;

  @ApiProperty({ description: '返回结果数量', required: false, minimum: 1, maximum: 50, default: 5 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  topK?: number = 5;

  @ApiProperty({ description: '相似度阈值', required: false, minimum: 0, maximum: 1, default: 0.7 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  threshold?: number = 0.7;

  @ApiProperty({ description: '过滤条件', required: false })
  @IsOptional()
  @IsObject()
  filter?: Record<string, any>;
}
