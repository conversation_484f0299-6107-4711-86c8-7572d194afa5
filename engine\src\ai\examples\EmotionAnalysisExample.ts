/**
 * 情感分析系统使用示例
 */
import { 
  AIEmotionAnalysisSystem, 
  EmotionType, 
  AnalysisMethod,
  EmotionAnalysisOptions 
} from '../AIEmotionAnalysisSystem';

/**
 * 情感分析示例类
 */
export class EmotionAnalysisExample {
  private emotionSystem: AIEmotionAnalysisSystem;

  constructor() {
    // 创建情感分析系统实例
    this.emotionSystem = new AIEmotionAnalysisSystem({
      debug: true,
      analysisMethod: AnalysisMethod.HYBRID,
      enableHistory: true,
      maxHistoryLength: 50,
      language: 'zh',
      enableEmoticonAnalysis: true,
      enableToneAnalysis: true
    });
  }

  /**
   * 运行基础示例
   */
  public async runBasicExample(): Promise<void> {
    console.log('=== 基础情感分析示例 ===');

    // 测试文本
    const testTexts = [
      '我今天非常开心！😊',
      '这让我感到很失望和难过',
      '我对这个结果感到愤怒',
      '这真是太令人惊讶了！',
      '我有点担心这个决定',
      '这个味道让我觉得恶心',
      '我感到很平静和放松',
      '我爱你 ❤️',
      '我讨厌这种情况',
      '我对此感到困惑'
    ];

    for (const text of testTexts) {
      const result = await this.emotionSystem.analyzeEmotion(text);
      if (result) {
        console.log(`文本: "${text}"`);
        console.log(`主要情感: ${result.primaryEmotion} (强度: ${result.primaryIntensity.toFixed(2)})`);
        console.log(`置信度: ${result.confidence?.toFixed(2)}`);
        console.log('---');
      }
    }
  }

  /**
   * 运行详细分析示例
   */
  public async runDetailedExample(): Promise<void> {
    console.log('\n=== 详细情感分析示例 ===');

    const text = '我今天非常非常开心，因为收到了期待已久的好消息！😄🎉';
    
    const options: EmotionAnalysisOptions = {
      detailed: true,
      includeSecondary: true,
      depth: 'deep'
    };

    const result = await this.emotionSystem.analyzeEmotion(text, options);
    
    if (result) {
      console.log(`文本: "${text}"`);
      console.log(`主要情感: ${result.primaryEmotion} (强度: ${result.primaryIntensity.toFixed(2)})`);
      
      if (result.secondaryEmotion) {
        console.log(`次要情感: ${result.secondaryEmotion} (强度: ${result.secondaryIntensity?.toFixed(2)})`);
      }
      
      console.log('所有情感分数:');
      Object.entries(result.scores || {}).forEach(([emotion, score]) => {
        if (score > 0) {
          console.log(`  ${emotion}: ${score.toFixed(3)}`);
        }
      });
      
      console.log('详细信息:', result.detailedEmotions);
    }
  }

  /**
   * 运行批量分析示例
   */
  public async runBatchExample(): Promise<void> {
    console.log('\n=== 批量情感分析示例 ===');

    const texts = [
      '今天天气真好，心情也很棒！',
      '工作压力太大了，感觉很累',
      '看到这个新闻让我很震惊',
      '终于完成了这个项目，太开心了！',
      '对不起，我搞砸了这件事'
    ];

    const results = await this.emotionSystem.batchAnalyzeEmotions(texts, {
      detailed: false,
      includeSecondary: true
    });

    results.forEach((result, index) => {
      console.log(`${index + 1}. "${texts[index]}"`);
      console.log(`   情感: ${result.primaryEmotion} (${result.primaryIntensity.toFixed(2)})`);
    });
  }

  /**
   * 运行历史记录示例
   */
  public async runHistoryExample(): Promise<void> {
    console.log('\n=== 情感历史记录示例 ===');

    // 分析一些文本以建立历史记录
    const texts = [
      '早上起床心情不错',
      '中午吃饭很开心',
      '下午工作有点累',
      '晚上看电影很放松',
      '睡前感到满足'
    ];

    for (const text of texts) {
      await this.emotionSystem.analyzeEmotion(text, { context: '日常生活' });
    }

    // 获取历史记录
    const history = this.emotionSystem.getEmotionHistory(3);
    console.log('最近3条情感分析记录:');
    history.forEach((item, index) => {
      console.log(`${index + 1}. "${item.text}" -> ${item.result.primaryEmotion}`);
    });

    // 获取统计信息
    const stats = this.emotionSystem.getEmotionStatistics();
    console.log('\n情感统计:');
    console.log('总分析次数:', stats.totalAnalyses);
    console.log('情感分布:', stats.emotionCounts);
    console.log('平均强度:', stats.averageIntensities);
  }

  /**
   * 运行实时分析示例
   */
  public async runRealtimeExample(): Promise<void> {
    console.log('\n=== 实时情感分析示例 ===');

    // 监听情感分析事件
    this.emotionSystem.addEventListener('emotionAnalyzed', (data) => {
      console.log(`[实时] 检测到情感: ${data.result.primaryEmotion} (${data.result.primaryIntensity.toFixed(2)})`);
    });

    // 模拟实时输入
    const realtimeTexts = [
      '哇，这个功能太棒了！',
      '有点不太理解这个设计',
      '这个bug让我很烦躁',
      '终于修复了，松了一口气'
    ];

    for (const text of realtimeTexts) {
      await this.emotionSystem.analyzeEmotion(text);
      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  /**
   * 运行配置示例
   */
  public async runConfigExample(): Promise<void> {
    console.log('\n=== 配置示例 ===');

    // 显示当前配置
    console.log('当前配置:', this.emotionSystem.getConfig());

    // 更新配置
    this.emotionSystem.updateConfig({
      analysisMethod: AnalysisMethod.KEYWORD_MATCHING,
      enableEmoticonAnalysis: false
    });

    console.log('更新后的配置:', this.emotionSystem.getConfig());

    // 测试不同分析方法的效果
    const testText = '我很开心 😊';
    const result = await this.emotionSystem.analyzeEmotion(testText);
    
    console.log(`使用关键词匹配分析 "${testText}":`, result?.primaryEmotion);
  }

  /**
   * 运行所有示例
   */
  public async runAllExamples(): Promise<void> {
    try {
      await this.runBasicExample();
      await this.runDetailedExample();
      await this.runBatchExample();
      await this.runHistoryExample();
      await this.runRealtimeExample();
      await this.runConfigExample();
      
      console.log('\n=== 所有示例运行完成 ===');
    } catch (error) {
      console.error('运行示例时出错:', error);
    }
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const example = new EmotionAnalysisExample();
  example.runAllExamples();
}
