/**
 * AI模型接口
 * 定义AI模型的通用接口
 */
import { AIModelType } from '../AIModelType';
import { AIModelConfig } from '../AIModelConfig';

/**
 * 文本生成选项
 */
export interface TextGenerationOptions {
  /** 最大令牌数 */
  maxTokens?: number;
  
  /** 温度 (0-1) */
  temperature?: number;
  
  /** 是否使用流式响应 */
  stream?: boolean;
  
  /** 流式响应回调 */
  onStream?: (text: string) => void;
  
  /** 停止序列 */
  stopSequences?: string[];
  
  /** 采样方法 */
  samplingMethod?: 'greedy' | 'topk' | 'topp' | 'beam';
  
  /** Top-K采样参数 */
  topK?: number;
  
  /** Top-P采样参数 */
  topP?: number;
  
  /** Beam搜索宽度 */
  beamWidth?: number;
  
  /** 惩罚重复 */
  repetitionPenalty?: number;
  
  /** 长度惩罚 */
  lengthPenalty?: number;
  
  /** 自定义选项 */
  [key: string]: any;
}

/**
 * 图像生成选项
 */
export interface ImageGenerationOptions {
  /** 图像宽度 */
  width?: number;
  
  /** 图像高度 */
  height?: number;
  
  /** 生成步数 */
  steps?: number;
  
  /** 引导比例 */
  guidanceScale?: number;
  
  /** 种子 */
  seed?: number;
  
  /** 负面提示 */
  negativePrompt?: string;
  
  /** 采样器 */
  sampler?: string;
  
  /** 是否使用NSFW过滤 */
  safetyChecker?: boolean;
  
  /** 进度回调 */
  onProgress?: (progress: number) => void;
  
  /** 自定义选项 */
  [key: string]: any;
}

/**
 * 情感分析结果
 */
export interface EmotionAnalysisResult {
  /** 主要情感 */
  primaryEmotion: string;
  
  /** 情感强度 (0-1) */
  intensity: number;
  
  /** 情感分数映射 */
  scores: Record<string, number>;
  
  /** 置信度 (0-1) */
  confidence: number;
}

/**
 * 文本分类结果
 */
export interface TextClassificationResult {
  /** 分类标签 */
  label: string;
  
  /** 置信度 (0-1) */
  confidence: number;
  
  /** 所有标签及其置信度 */
  allLabels: Record<string, number>;
}

/**
 * 命名实体识别结果
 */
export interface NamedEntityRecognitionResult {
  /** 实体列表 */
  entities: Array<{
    /** 实体文本 */
    text: string;
    
    /** 实体类型 */
    type: string;
    
    /** 开始位置 */
    start: number;
    
    /** 结束位置 */
    end: number;
    
    /** 置信度 (0-1) */
    confidence: number;
  }>;
}

/**
 * 文本摘要结果
 */
export interface TextSummaryResult {
  /** 摘要文本 */
  summary: string;
  
  /** 摘要长度 */
  length: number;
  
  /** 压缩率 */
  compressionRate: number;
}

/**
 * 翻译结果
 */
export interface TranslationResult {
  /** 翻译文本 */
  translatedText: string;

  /** 源语言 */
  sourceLanguage: string;

  /** 目标语言 */
  targetLanguage: string;

  /** 置信度 (0-1) */
  confidence: number;
}

/**
 * 语音识别结果
 */
export interface SpeechRecognitionResult {
  /** 识别的文本 */
  text: string;

  /** 置信度 (0-1) */
  confidence: number;

  /** 语言代码 */
  language: string;

  /** 处理时间（毫秒） */
  processingTime?: number;
}

/**
 * 语音合成结果
 */
export interface SpeechSynthesisResult {
  /** 音频数据 */
  audioData: ArrayBuffer;

  /** 音频时长（秒） */
  duration: number;

  /** 语音类型 */
  voice: string;

  /** 语言代码 */
  language: string;
}

/**
 * 意图识别结果
 */
export interface IntentRecognitionResult {
  /** 识别的意图 */
  intent: string;

  /** 置信度 (0-1) */
  confidence: number;

  /** 提取的实体 */
  entities: Record<string, any>;

  /** 意图参数 */
  parameters?: Record<string, any>;
}

/**
 * 对话处理结果
 */
export interface DialogueResult {
  /** 系统回复 */
  response: string;

  /** 对话上下文 */
  context: Record<string, any>;

  /** 对话状态 */
  state?: string;

  /** 识别的意图 */
  intent?: string;

  /** 下一步建议 */
  nextActions?: string[];
}

/**
 * 知识图谱查询结果
 */
export interface KnowledgeGraphResult {
  /** 查询结果 */
  results: Array<{
    entity: string;
    relation: string;
    value: any;
    confidence: number;
  }>;

  /** 查询时间（毫秒） */
  queryTime?: number;
}

/**
 * 问答系统结果
 */
export interface QuestionAnsweringResult {
  /** 答案 */
  answer: string;

  /** 置信度 (0-1) */
  confidence: number;

  /** 信息来源 */
  sources: Array<{
    title: string;
    content: string;
    url?: string;
    score: number;
  }>;
}

/**
 * 关键词提取结果
 */
export interface KeywordExtractionResult {
  /** 关键词列表 */
  keywords: string[];

  /** 关键词重要性分数 */
  scores: number[];

  /** 关键词详情 */
  details?: Array<{
    keyword: string;
    score: number;
    frequency: number;
    position: number[];
  }>;
}

/**
 * 文本相似度计算结果
 */
export interface TextSimilarityResult {
  /** 相似度分数 (0-1) */
  similarity: number;

  /** 计算方法 */
  method: string;

  /** 详细计算结果 */
  details: {
    cosine?: number;
    jaccard?: number;
    euclidean?: number;
    semantic?: number;
  };
}

/**
 * 语言检测结果
 */
export interface LanguageDetectionResult {
  /** 检测到的语言代码 */
  language: string;

  /** 检测置信度 (0-1) */
  confidence: number;

  /** 所有可能的语言及置信度 */
  allLanguages: Array<{
    language: string;
    confidence: number;
  }>;
}

/**
 * 文本纠错结果
 */
export interface TextCorrectionResult {
  /** 纠错后的文本 */
  correctedText: string;

  /** 纠错详情 */
  corrections: Array<{
    original: string;
    corrected: string;
    position: number;
    type: string;
    confidence: number;
  }>;

  /** 纠错统计 */
  statistics?: {
    totalErrors: number;
    grammarErrors: number;
    spellingErrors: number;
    punctuationErrors: number;
  };
}

/**
 * AI模型接口
 */
export interface IAIModel {
  /** 获取模型ID */
  getId(): string;

  /** 获取模型类型 */
  getType(): AIModelType;

  /** 获取模型配置 */
  getConfig(): AIModelConfig;
  
  /** 初始化模型 */
  initialize(): Promise<boolean>;
  
  /** 生成文本 */
  generateText(prompt: string, options?: TextGenerationOptions): Promise<string>;
  
  /** 生成图像 */
  generateImage?(prompt: string, options?: ImageGenerationOptions): Promise<Blob>;
  
  /** 分析情感 */
  analyzeEmotion?(text: string): Promise<EmotionAnalysisResult>;
  
  /** 分类文本 */
  classifyText?(text: string, categories?: string[]): Promise<TextClassificationResult>;
  
  /** 命名实体识别 */
  recognizeEntities?(text: string): Promise<NamedEntityRecognitionResult>;
  
  /** 文本摘要 */
  summarizeText?(text: string, maxLength?: number): Promise<TextSummaryResult>;
  
  /** 翻译文本 */
  translateText?(text: string, targetLanguage: string, sourceLanguage?: string): Promise<TranslationResult>;

  /** 语音识别 */
  recognizeSpeech?(audioData: ArrayBuffer, options?: any): Promise<SpeechRecognitionResult>;

  /** 语音合成 */
  synthesizeSpeech?(text: string, options?: any): Promise<SpeechSynthesisResult>;

  /** 意图识别 */
  recognizeIntent?(text: string, context?: any): Promise<IntentRecognitionResult>;

  /** 对话处理 */
  processDialogue?(userInput: string, sessionId: string, userId: string): Promise<DialogueResult>;

  /** 知识图谱查询 */
  queryKnowledgeGraph?(query: string, options?: any): Promise<KnowledgeGraphResult>;

  /** 问答系统 */
  answerQuestion?(question: string, options?: any): Promise<QuestionAnsweringResult>;

  /** 关键词提取 */
  extractKeywords?(text: string, options?: any): Promise<KeywordExtractionResult>;

  /** 文本相似度计算 */
  calculateSimilarity?(text1: string, text2: string, options?: any): Promise<TextSimilarityResult>;

  /** 语言检测 */
  detectLanguage?(text: string): Promise<LanguageDetectionResult>;

  /** 文本纠错 */
  correctText?(text: string, options?: any): Promise<TextCorrectionResult>;

  /** 通用推理接口 */
  infer?(input: any, options?: any): Promise<any>;

  /** 批量推理接口 */
  batchInfer?(inputs: any[], options?: any): Promise<any[]>;

  /** 释放资源 */
  dispose(): void;
}
