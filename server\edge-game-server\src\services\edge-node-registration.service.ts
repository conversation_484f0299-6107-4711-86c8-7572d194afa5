import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import axios from 'axios';

/**
 * 边缘节点信息接口
 */
export interface EdgeNodeInfo {
  nodeId: string;
  region: string;
  endpoint: string;
  capabilities: {
    maxUsers: number;
    supportedFeatures: string[];
    resources: {
      cpu: string;
      memory: string;
      storage: string;
    };
  };
  status?: 'online' | 'offline' | 'maintenance';
  lastHeartbeat?: Date;
  metrics?: {
    currentUsers: number;
    cpuUsage: number;
    memoryUsage: number;
    networkLatency: number;
  };
}

/**
 * 边缘节点注册服务
 * 负责向中心节点注册、心跳维持和状态同步
 */
@Injectable()
export class EdgeNodeRegistrationService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(EdgeNodeRegistrationService.name);
  private nodeInfo: EdgeNodeInfo | null = null;
  private centralHubUrl: string | null = null;
  private registrationInterval: NodeJS.Timeout | null = null;
  private isRegistered = false;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async onModuleInit() {
    this.logger.log('边缘节点注册服务初始化');
  }

  async onModuleDestroy() {
    await this.unregisterFromHub();
    if (this.registrationInterval) {
      clearInterval(this.registrationInterval);
    }
  }

  /**
   * 注册到中心节点
   */
  async registerToHub(hubUrl: string, nodeInfo: EdgeNodeInfo): Promise<void> {
    this.centralHubUrl = hubUrl;
    this.nodeInfo = {
      ...nodeInfo,
      status: 'online',
      lastHeartbeat: new Date(),
    };

    try {
      const response = await axios.post(`${hubUrl}/api/edge/register`, this.nodeInfo, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'X-Edge-Node-Id': nodeInfo.nodeId,
        },
      });

      if (response.status === 200 || response.status === 201) {
        this.isRegistered = true;
        this.logger.log(`成功注册到中心节点: ${hubUrl}`);
        
        // 触发注册成功事件
        this.eventEmitter.emit('edge.registered', this.nodeInfo);
        
        // 开始定期心跳
        this.startHeartbeat();
      } else {
        throw new Error(`注册失败，状态码: ${response.status}`);
      }
    } catch (error) {
      this.logger.error(`注册到中心节点失败: ${error.message}`);
      
      // 触发注册失败事件
      this.eventEmitter.emit('edge.registration.failed', error);
      
      // 重试注册
      setTimeout(() => this.registerToHub(hubUrl, nodeInfo), 30000);
      throw error;
    }
  }

  /**
   * 从中心节点注销
   */
  async unregisterFromHub(): Promise<void> {
    if (!this.isRegistered || !this.centralHubUrl || !this.nodeInfo) {
      return;
    }

    try {
      await axios.delete(`${this.centralHubUrl}/api/edge/unregister/${this.nodeInfo.nodeId}`, {
        timeout: 5000,
        headers: {
          'X-Edge-Node-Id': this.nodeInfo.nodeId,
        },
      });

      this.isRegistered = false;
      this.logger.log('成功从中心节点注销');
      
      // 触发注销事件
      this.eventEmitter.emit('edge.unregistered', this.nodeInfo);
    } catch (error) {
      this.logger.error(`从中心节点注销失败: ${error.message}`);
    }
  }

  /**
   * 开始心跳
   */
  private startHeartbeat(): void {
    const heartbeatInterval = this.configService.get<number>('EDGE_HEARTBEAT_INTERVAL', 30000);
    
    this.registrationInterval = setInterval(async () => {
      await this.sendHeartbeat();
    }, heartbeatInterval);
  }

  /**
   * 发送心跳
   */
  @Cron(CronExpression.EVERY_30_SECONDS)
  async sendHeartbeat(): Promise<void> {
    if (!this.isRegistered || !this.centralHubUrl || !this.nodeInfo) {
      return;
    }

    try {
      // 更新节点指标
      await this.updateNodeMetrics();

      const heartbeatData = {
        nodeId: this.nodeInfo.nodeId,
        status: this.nodeInfo.status,
        lastHeartbeat: new Date(),
        metrics: this.nodeInfo.metrics,
      };

      const response = await axios.post(
        `${this.centralHubUrl}/api/edge/heartbeat`,
        heartbeatData,
        {
          timeout: 5000,
          headers: {
            'Content-Type': 'application/json',
            'X-Edge-Node-Id': this.nodeInfo.nodeId,
          },
        }
      );

      if (response.status === 200) {
        this.nodeInfo.lastHeartbeat = new Date();
        
        // 触发心跳成功事件
        this.eventEmitter.emit('edge.heartbeat.success', heartbeatData);
      } else {
        throw new Error(`心跳失败，状态码: ${response.status}`);
      }
    } catch (error) {
      this.logger.warn(`发送心跳失败: ${error.message}`);
      
      // 触发心跳失败事件
      this.eventEmitter.emit('edge.heartbeat.failed', error);
      
      // 如果连续失败，尝试重新注册
      if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
        this.isRegistered = false;
        setTimeout(() => {
          if (this.centralHubUrl && this.nodeInfo) {
            this.registerToHub(this.centralHubUrl, this.nodeInfo);
          }
        }, 60000);
      }
    }
  }

  /**
   * 更新节点指标
   */
  private async updateNodeMetrics(): Promise<void> {
    if (!this.nodeInfo) return;

    try {
      // 获取系统指标
      const metrics = await this.collectSystemMetrics();
      
      this.nodeInfo.metrics = {
        currentUsers: metrics.currentUsers,
        cpuUsage: metrics.cpuUsage,
        memoryUsage: metrics.memoryUsage,
        networkLatency: metrics.networkLatency,
      };
    } catch (error) {
      this.logger.warn(`更新节点指标失败: ${error.message}`);
    }
  }

  /**
   * 收集系统指标
   */
  private async collectSystemMetrics(): Promise<{
    currentUsers: number;
    cpuUsage: number;
    memoryUsage: number;
    networkLatency: number;
  }> {
    // 这里应该集成实际的系统监控
    // 暂时返回模拟数据
    return {
      currentUsers: 0, // 从实例服务获取
      cpuUsage: Math.random() * 100,
      memoryUsage: Math.random() * 100,
      networkLatency: Math.random() * 50 + 10,
    };
  }

  /**
   * 获取节点信息
   */
  getNodeInfo(): EdgeNodeInfo | null {
    return this.nodeInfo;
  }

  /**
   * 更新节点状态
   */
  async updateNodeStatus(status: 'online' | 'offline' | 'maintenance'): Promise<void> {
    if (!this.nodeInfo) return;

    this.nodeInfo.status = status;
    
    // 立即发送心跳更新状态
    await this.sendHeartbeat();
    
    // 触发状态更新事件
    this.eventEmitter.emit('edge.status.updated', { nodeId: this.nodeInfo.nodeId, status });
  }

  /**
   * 检查是否已注册
   */
  isNodeRegistered(): boolean {
    return this.isRegistered;
  }
}
