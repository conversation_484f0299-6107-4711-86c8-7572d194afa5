/**
 * 智能推荐服务核心实现
 * 提供多种推荐算法和个性化推荐功能
 */
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { 
  RecommendationRequest, 
  RecommendationResponse, 
  RecommendationType,
  UserProfile,
  ContentFeature,
  RecommendationFeedback,
  RecommendationAlgorithm
} from './interfaces/recommendation.interface';
import { RecommendationHistory } from './entities/recommendation-history.entity';
import { UserInteraction } from './entities/user-interaction.entity';
import { CacheManager } from './cache/cache.manager';
import { ModelManager } from './models/model.manager';

@Injectable()
export class RecommendationService {
  private readonly logger = new Logger(RecommendationService.name);
  private readonly algorithms: Map<RecommendationType, RecommendationAlgorithm> = new Map();

  constructor(
    @InjectRepository(RecommendationHistory)
    private historyRepository: Repository<RecommendationHistory>,
    @InjectRepository(UserInteraction)
    private interactionRepository: Repository<UserInteraction>,
    private cacheManager: CacheManager,
    private modelManager: ModelManager,
    private configService: ConfigService
  ) {
    this.initializeAlgorithms();
  }

  /**
   * 初始化推荐算法
   */
  private initializeAlgorithms(): void {
    // 注册不同类型的推荐算法
    this.algorithms.set(RecommendationType.ASSET, new AssetRecommendationAlgorithm());
    this.algorithms.set(RecommendationType.SCENE_TEMPLATE, new SceneTemplateRecommendationAlgorithm());
    this.algorithms.set(RecommendationType.COLLABORATOR, new CollaboratorRecommendationAlgorithm());
    this.algorithms.set(RecommendationType.LEARNING_PATH, new LearningPathRecommendationAlgorithm());
    this.algorithms.set(RecommendationType.MATERIAL, new MaterialRecommendationAlgorithm());
    this.algorithms.set(RecommendationType.COMPONENT, new ComponentRecommendationAlgorithm());
  }

  /**
   * 获取个性化推荐
   */
  async getRecommendations(request: RecommendationRequest): Promise<RecommendationResponse[]> {
    const cacheKey = this.generateCacheKey(request);
    
    try {
      // 检查缓存
      const cached = await this.cacheManager.get<RecommendationResponse[]>(cacheKey);
      if (cached) {
        this.logger.debug(`缓存命中: ${cacheKey}`);
        return cached;
      }

      // 获取用户画像
      const userProfile = await this.getUserProfile(request.userId);
      
      // 获取推荐算法
      const algorithm = this.algorithms.get(request.type);
      if (!algorithm) {
        throw new Error(`不支持的推荐类型: ${request.type}`);
      }

      // 生成推荐
      const recommendations = await algorithm.recommend({
        ...request,
        userProfile,
        contextFeatures: await this.extractContextFeatures(request.context)
      });

      // 后处理推荐结果
      const processedRecommendations = await this.postProcessRecommendations(
        recommendations,
        request
      );

      // 缓存结果
      await this.cacheManager.set(cacheKey, processedRecommendations, 300); // 5分钟缓存

      // 记录推荐历史
      await this.recordRecommendationHistory(request.userId, processedRecommendations);

      return processedRecommendations;

    } catch (error) {
      this.logger.error(`推荐生成失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 处理用户反馈
   */
  async processFeedback(feedback: RecommendationFeedback): Promise<void> {
    try {
      // 记录反馈
      await this.recordFeedback(feedback);
      
      // 更新用户画像
      await this.updateUserProfileFromFeedback(feedback);
      
      // 更新推荐模型
      await this.updateRecommendationModel(feedback);
      
      // 清除相关缓存
      await this.invalidateUserCache(feedback.userId);

      this.logger.debug(`处理用户反馈: ${feedback.userId} - ${feedback.rating}`);

    } catch (error) {
      this.logger.error(`处理反馈失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取推荐解释
   */
  async getRecommendationExplanation(
    recommendationId: string,
    userId: string
  ): Promise<string> {
    const recommendation = await this.getRecommendationById(recommendationId);
    const userProfile = await this.getUserProfile(userId);
    
    return this.generateExplanation(recommendation, userProfile);
  }

  /**
   * 后处理推荐结果
   */
  private async postProcessRecommendations(
    recommendations: RecommendationResponse[],
    request: RecommendationRequest
  ): Promise<RecommendationResponse[]> {
    // 去重
    const deduped = this.removeDuplicates(recommendations);
    
    // 多样性优化
    const diversified = await this.optimizeForDiversity(deduped, request.diversityWeight || 0.3);
    
    // 新颖性过滤
    const novel = await this.filterForNovelty(diversified, request.userId);
    
    // 最终排序
    const sorted = this.finalRanking(novel, request);
    
    // 限制数量
    return sorted.slice(0, request.count || 10);
  }

  /**
   * 多样性优化
   */
  private async optimizeForDiversity(
    recommendations: RecommendationResponse[],
    diversityWeight: number
  ): Promise<RecommendationResponse[]> {
    if (recommendations.length <= 1) return recommendations;

    const optimized: RecommendationResponse[] = [];
    const remaining = [...recommendations];

    // 选择第一个最高分的推荐
    if (remaining.length > 0) {
      optimized.push(remaining.shift()!);
    }

    // 迭代选择兼顾相关性和多样性的推荐
    while (remaining.length > 0 && optimized.length < recommendations.length) {
      let bestCandidate: RecommendationResponse | null = null;
      let bestScore = -Infinity;

      for (const candidate of remaining) {
        // 计算与已选推荐的平均相似度
        const avgSimilarity = await this.calculateAverageSimilarity(candidate, optimized);
        
        // 多样性分数 = 相关性分数 - 多样性权重 * 相似度
        const diversityScore = candidate.score - diversityWeight * avgSimilarity;
        
        if (diversityScore > bestScore) {
          bestScore = diversityScore;
          bestCandidate = candidate;
        }
      }

      if (bestCandidate) {
        optimized.push(bestCandidate);
        remaining.splice(remaining.indexOf(bestCandidate), 1);
      } else {
        break;
      }
    }

    return optimized;
  }

  /**
   * 计算平均相似度
   */
  private async calculateAverageSimilarity(
    candidate: RecommendationResponse,
    selected: RecommendationResponse[]
  ): Promise<number> {
    if (selected.length === 0) return 0;

    const similarities = await Promise.all(
      selected.map(item => this.calculateContentSimilarity(candidate, item))
    );

    return similarities.reduce((sum, sim) => sum + sim, 0) / similarities.length;
  }

  /**
   * 计算内容相似度
   */
  private async calculateContentSimilarity(
    item1: RecommendationResponse,
    item2: RecommendationResponse
  ): Promise<number> {
    // 基于内容特征计算相似度
    const features1 = await this.getContentFeatures(item1.itemId);
    const features2 = await this.getContentFeatures(item2.itemId);
    
    return this.cosineSimilarity(features1.vector, features2.vector);
  }

  /**
   * 余弦相似度计算
   */
  private cosineSimilarity(vector1: number[], vector2: number[]): number {
    if (vector1.length !== vector2.length) return 0;

    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i];
      norm1 += vector1[i] * vector1[i];
      norm2 += vector2[i] * vector2[i];
    }

    if (norm1 === 0 || norm2 === 0) return 0;
    
    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(request: RecommendationRequest): string {
    const keyParts = [
      'rec',
      request.type,
      request.userId,
      request.count || 10,
      JSON.stringify(request.filters || {}),
      JSON.stringify(request.context || {})
    ];
    
    return keyParts.join(':');
  }

  /**
   * 记录推荐历史
   */
  private async recordRecommendationHistory(
    userId: string,
    recommendations: RecommendationResponse[]
  ): Promise<void> {
    const historyEntries = recommendations.map(rec => ({
      userId,
      itemId: rec.itemId,
      itemType: rec.itemType,
      score: rec.score,
      algorithm: rec.algorithm,
      context: rec.context,
      timestamp: new Date()
    }));

    await this.historyRepository.save(historyEntries);
  }

  /**
   * 获取用户画像
   */
  private async getUserProfile(userId: string): Promise<UserProfile> {
    // 从用户服务获取用户画像
    // 这里应该调用用户画像服务的API
    return {
      userId,
      preferences: {},
      behaviorPatterns: {},
      skillLevel: 'intermediate',
      interests: []
    };
  }

  /**
   * 提取上下文特征
   */
  private async extractContextFeatures(context: any): Promise<ContentFeature[]> {
    // 从上下文中提取特征
    // 这里应该调用特征提取服务
    return [];
  }

  /**
   * 去重
   */
  private removeDuplicates(recommendations: RecommendationResponse[]): RecommendationResponse[] {
    const seen = new Set<string>();
    return recommendations.filter(rec => {
      if (seen.has(rec.itemId)) {
        return false;
      }
      seen.add(rec.itemId);
      return true;
    });
  }

  /**
   * 新颖性过滤
   */
  private async filterForNovelty(
    recommendations: RecommendationResponse[],
    userId: string
  ): Promise<RecommendationResponse[]> {
    // 获取用户历史交互
    const userHistory = await this.getUserInteractionHistory(userId);
    const interactedItems = new Set(userHistory.map(h => h.itemId));

    // 过滤掉用户已经交互过的内容
    return recommendations.filter(rec => !interactedItems.has(rec.itemId));
  }

  /**
   * 最终排序
   */
  private finalRanking(
    recommendations: RecommendationResponse[],
    request: RecommendationRequest
  ): RecommendationResponse[] {
    // 根据分数和其他因素进行最终排序
    return recommendations.sort((a, b) => {
      // 主要按分数排序
      if (b.score !== a.score) {
        return b.score - a.score;
      }
      
      // 分数相同时按新颖性排序
      return (b.novelty || 0) - (a.novelty || 0);
    });
  }

  /**
   * 获取用户交互历史
   */
  private async getUserInteractionHistory(userId: string): Promise<UserInteraction[]> {
    return this.interactionRepository.find({
      where: { userId },
      order: { timestamp: 'DESC' },
      take: 1000 // 最近1000条交互记录
    });
  }
}
