/**
 * QRCodePlugin.tsx
 * 
 * 二维码组件插件示例
 */

import React, { useState, useEffect } from 'react';
import { Form, Input, Select, ColorPicker, InputNumber, Switch } from 'antd';
import { ComponentPlugin, PluginDecorator } from '../../sdk/PluginSDK';
import type { ComponentType } from '../../core/PluginSystem';
import QRCode from 'qrcode';

/**
 * 二维码组件属性
 */
interface QRCodeProps {
  /** 二维码内容 */
  value: string;
  /** 尺寸 */
  size: number;
  /** 前景色 */
  color: string;
  /** 背景色 */
  backgroundColor: string;
  /** 错误纠正级别 */
  errorCorrectionLevel: 'L' | 'M' | 'Q' | 'H';
  /** 边距 */
  margin: number;
  /** 是否包含logo */
  includeLogo: boolean;
  /** logo URL */
  logoUrl?: string;
  /** logo尺寸 */
  logoSize: number;
}

/**
 * 二维码组件
 */
const QRCodeComponent: React.FC<QRCodeProps> = ({
  value = 'https://example.com',
  size = 200,
  color = '#000000',
  backgroundColor = '#ffffff',
  errorCorrectionLevel = 'M',
  margin = 4,
  includeLogo = false,
  logoUrl,
  logoSize = 40
}) => {
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('');

  useEffect(() => {
    const generateQRCode = async () => {
      try {
        const options = {
          width: size,
          margin,
          color: {
            dark: color,
            light: backgroundColor
          },
          errorCorrectionLevel
        };

        const dataUrl = await QRCode.toDataURL(value, options);
        setQrCodeDataUrl(dataUrl);
      } catch (error) {
        console.error('生成二维码失败:', error);
      }
    };

    generateQRCode();
  }, [value, size, color, backgroundColor, errorCorrectionLevel, margin]);

  return (
    <div className="qrcode-component" style={{ display: 'inline-block', position: 'relative' }}>
      {qrCodeDataUrl && (
        <img
          src={qrCodeDataUrl}
          alt="QR Code"
          style={{
            width: size,
            height: size,
            display: 'block'
          }}
        />
      )}
      
      {includeLogo && logoUrl && (
        <img
          src={logoUrl}
          alt="Logo"
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: logoSize,
            height: logoSize,
            borderRadius: '50%',
            backgroundColor: 'white',
            padding: 2,
            boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
          }}
        />
      )}
    </div>
  );
};

/**
 * 二维码属性编辑器
 */
const QRCodePropertyEditor: React.FC<{
  value: QRCodeProps;
  onChange: (value: QRCodeProps) => void;
}> = ({ value, onChange }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue(value);
  }, [value, form]);

  const handleValueChange = (changedValues: Partial<QRCodeProps>) => {
    const newValue = { ...value, ...changedValues };
    onChange(newValue);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      size="small"
      onValuesChange={handleValueChange}
      initialValues={value}
    >
      <Form.Item
        name="value"
        label="二维码内容"
        rules={[{ required: true, message: '请输入二维码内容' }]}
      >
        <Input.TextArea
          placeholder="输入要编码的文本或URL"
          rows={3}
        />
      </Form.Item>

      <Form.Item
        name="size"
        label="尺寸"
      >
        <InputNumber
          min={50}
          max={500}
          step={10}
          addonAfter="px"
          style={{ width: '100%' }}
        />
      </Form.Item>

      <Form.Item
        name="color"
        label="前景色"
      >
        <ColorPicker showText />
      </Form.Item>

      <Form.Item
        name="backgroundColor"
        label="背景色"
      >
        <ColorPicker showText />
      </Form.Item>

      <Form.Item
        name="errorCorrectionLevel"
        label="错误纠正级别"
      >
        <Select>
          <Select.Option value="L">低 (7%)</Select.Option>
          <Select.Option value="M">中 (15%)</Select.Option>
          <Select.Option value="Q">较高 (25%)</Select.Option>
          <Select.Option value="H">高 (30%)</Select.Option>
        </Select>
      </Form.Item>

      <Form.Item
        name="margin"
        label="边距"
      >
        <InputNumber
          min={0}
          max={20}
          style={{ width: '100%' }}
        />
      </Form.Item>

      <Form.Item
        name="includeLogo"
        label="包含Logo"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      {value.includeLogo && (
        <>
          <Form.Item
            name="logoUrl"
            label="Logo URL"
            rules={[{ type: 'url', message: '请输入有效的URL' }]}
          >
            <Input placeholder="https://example.com/logo.png" />
          </Form.Item>

          <Form.Item
            name="logoSize"
            label="Logo尺寸"
          >
            <InputNumber
              min={20}
              max={100}
              step={5}
              addonAfter="px"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </>
      )}
    </Form>
  );
};

/**
 * 二维码插件
 */
@PluginDecorator({
  id: 'qrcode-plugin',
  name: '二维码组件',
  version: '1.0.0',
  description: '生成可自定义的二维码组件',
  author: 'Editor Team',
  main: 'QRCodePlugin.js'
})
export default class QRCodePlugin extends ComponentPlugin {
  getComponentType(): ComponentType {
    return {
      id: 'qrcode',
      name: '二维码',
      category: 'media',
      icon: 'qrcode',
      defaultProps: {
        value: 'https://example.com',
        size: 200,
        color: '#000000',
        backgroundColor: '#ffffff',
        errorCorrectionLevel: 'M',
        margin: 4,
        includeLogo: false,
        logoSize: 40
      },
      render: (props: QRCodeProps) => <QRCodeComponent {...props} />,
      propertyEditor: QRCodePropertyEditor,
      preview: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB4PSIyMCIgeT0iMjAiIHdpZHRoPSIxNjAiIGhlaWdodD0iMTYwIiBzdHJva2U9ImJsYWNrIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPHJlY3QgeD0iNDAiIHk9IjQwIiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIGZpbGw9ImJsYWNrIi8+CjxyZWN0IHg9IjgwIiB5PSI0MCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBmaWxsPSJibGFjayIvPgo8cmVjdCB4PSIxMjAiIHk9IjQwIiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIGZpbGw9ImJsYWNrIi8+CjxyZWN0IHg9IjE0MCIgeT0iNDAiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0iYmxhY2siLz4KPC9zdmc+'
    };
  }

  async initialize(): Promise<void> {
    await super.initialize();
    
    // 注册工具栏按钮
    this.registerToolbarButton({
      id: 'add-qrcode',
      label: '二维码',
      icon: 'qrcode',
      tooltip: '添加二维码组件',
      onClick: () => {
        const qrCodeComponent = {
          id: this.context.utils.generateId(),
          type: 'qrcode',
          props: {
            value: 'https://example.com',
            size: 200,
            color: '#000000',
            backgroundColor: '#ffffff',
            errorCorrectionLevel: 'M',
            margin: 4,
            includeLogo: false,
            logoSize: 40
          },
          style: {
            position: 'absolute',
            left: 100,
            top: 100
          }
        };
        
        this.addComponent(qrCodeComponent);
        this.notify('二维码组件已添加', 'success');
      },
      position: 'left',
      group: 'media'
    });

    // 注册快捷键
    this.registerShortcut({
      id: 'add-qrcode-shortcut',
      keys: ['Ctrl', 'Shift', 'Q'],
      description: '快速添加二维码组件',
      handler: () => {
        // 触发工具栏按钮的点击事件
        const button = document.querySelector('[data-id="add-qrcode"]') as HTMLElement;
        if (button) {
          button.click();
        }
      }
    });

    this.log.info('二维码插件初始化完成');
  }

  async cleanup(): Promise<void> {
    this.log.info('二维码插件已清理');
  }
}

// 插件清单
export const manifest = {
  id: 'qrcode-plugin',
  name: '二维码组件',
  version: '1.0.0',
  description: '生成可自定义的二维码组件，支持多种样式和Logo嵌入',
  author: 'Editor Team',
  main: 'QRCodePlugin.js',
  dependencies: [],
  config: {
    defaultSize: 200,
    defaultErrorCorrectionLevel: 'M'
  },
  keywords: ['qrcode', '二维码', 'media', '媒体'],
  license: 'MIT',
  repository: 'https://github.com/editor/plugins/qrcode',
  homepage: 'https://editor.example.com/plugins/qrcode'
};
