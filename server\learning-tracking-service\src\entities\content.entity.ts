import { 
  Entity, 
  PrimaryGeneratedColumn, 
  Column, 
  CreateDateColumn, 
  UpdateDateColumn,
  Index 
} from 'typeorm';

/**
 * 内容实体
 * 存储学习内容信息
 */
@Entity('contents')
@Index(['type', 'knowledgeArea'])
@Index(['difficulty'])
@Index(['enabled'])
export class ContentEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ 
    type: 'enum', 
    enum: ['concept', 'exercise', 'video', 'article', 'interactive', 'path', 'resource']
  })
  @Index()
  type: string;

  @Column({ 
    type: 'enum', 
    enum: ['easy', 'medium', 'hard']
  })
  @Index()
  difficulty: string;

  @Column()
  duration: number;

  @Column({ type: 'text' })
  tags: string;

  @Column({ name: 'knowledge_area' })
  @Index()
  knowledgeArea: string;

  @Column({ type: 'text', nullable: true })
  prerequisites: string;

  @Column({ name: 'learning_objectives', type: 'text', nullable: true })
  learningObjectives: string;

  @Column({ name: 'content_url', nullable: true })
  contentUrl: string;

  @Column({ name: 'thumbnail_url', nullable: true })
  thumbnailUrl: string;

  @Column({ name: 'author_id', nullable: true })
  authorId: string;

  @Column({ default: 0 })
  views: number;

  @Column({ default: 0 })
  completions: number;

  @Column({ name: 'average_rating', type: 'decimal', precision: 3, scale: 2, default: 0 })
  averageRating: number;

  @Column({ name: 'rating_count', default: 0 })
  ratingCount: number;

  @Column({ name: 'success_rate', type: 'decimal', precision: 3, scale: 2, default: 0 })
  successRate: number;

  @Column({ default: true })
  @Index()
  enabled: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
