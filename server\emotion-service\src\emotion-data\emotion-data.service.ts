/**
 * 情感数据持久化服务
 * 负责情感分析结果的存储、查询和统计分析
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThan } from 'typeorm';
import { EmotionRecord } from '../entities/emotion-record.entity';
import { EmotionPattern } from '../entities/emotion-pattern.entity';
import { UserEmotionProfile } from '../entities/user-emotion-profile.entity';

/**
 * 情感数据查询选项
 */
export interface EmotionDataQueryOptions {
  /** 用户ID */
  userId?: string;
  /** 情感类型 */
  emotionType?: string;
  /** 开始时间 */
  startTime?: Date;
  /** 结束时间 */
  endTime?: Date;
  /** 最小强度 */
  minIntensity?: number;
  /** 最大强度 */
  maxIntensity?: number;
  /** 分页大小 */
  limit?: number;
  /** 偏移量 */
  offset?: number;
}

/**
 * 情感统计结果
 */
export interface EmotionStatistics {
  /** 总记录数 */
  totalRecords: number;
  /** 情感类型分布 */
  emotionTypeDistribution: { [emotionType: string]: number };
  /** 平均强度 */
  averageIntensity: number;
  /** 最常见情感 */
  mostCommonEmotion: string;
  /** 情感变化趋势 */
  emotionTrend: 'increasing' | 'decreasing' | 'stable';
  /** 统计时间范围 */
  timeRange: {
    start: Date;
    end: Date;
  };
}

/**
 * 情感模式分析结果
 */
export interface EmotionPatternAnalysis {
  /** 检测到的模式数量 */
  patternCount: number;
  /** 模式列表 */
  patterns: EmotionPattern[];
  /** 模式置信度分布 */
  confidenceDistribution: { [range: string]: number };
  /** 最强模式 */
  strongestPattern: EmotionPattern | null;
}

@Injectable()
export class EmotionDataService {
  private readonly logger = new Logger(EmotionDataService.name);

  constructor(
    @InjectRepository(EmotionRecord)
    private emotionRecordRepository: Repository<EmotionRecord>,
    @InjectRepository(EmotionPattern)
    private emotionPatternRepository: Repository<EmotionPattern>,
    @InjectRepository(UserEmotionProfile)
    private userEmotionProfileRepository: Repository<UserEmotionProfile>,
  ) {}

  /**
   * 保存情感记录
   * @param emotionData 情感数据
   * @returns 保存的记录
   */
  async saveEmotionRecord(emotionData: {
    userId: string;
    emotionType: string;
    intensity: number;
    confidence: number;
    source: string;
    context?: any;
    timestamp?: Date;
  }): Promise<EmotionRecord> {
    try {
      const record = this.emotionRecordRepository.create({
        ...emotionData,
        timestamp: emotionData.timestamp || new Date(),
      });

      const savedRecord = await this.emotionRecordRepository.save(record);
      
      // 异步更新用户情感档案
      this.updateUserEmotionProfile(emotionData.userId, emotionData.emotionType, emotionData.intensity);

      this.logger.log(`保存情感记录: ${savedRecord.id}`);
      return savedRecord;
    } catch (error) {
      this.logger.error('保存情感记录失败:', error);
      throw error;
    }
  }

  /**
   * 批量保存情感记录
   * @param emotionDataList 情感数据列表
   * @returns 保存的记录列表
   */
  async batchSaveEmotionRecords(emotionDataList: Array<{
    userId: string;
    emotionType: string;
    intensity: number;
    confidence: number;
    source: string;
    context?: any;
    timestamp?: Date;
  }>): Promise<EmotionRecord[]> {
    try {
      const records = emotionDataList.map(data => 
        this.emotionRecordRepository.create({
          ...data,
          timestamp: data.timestamp || new Date(),
        })
      );

      const savedRecords = await this.emotionRecordRepository.save(records);
      
      // 异步更新用户情感档案
      const userEmotionUpdates = new Map<string, { emotions: string[], intensities: number[] }>();
      
      for (const data of emotionDataList) {
        if (!userEmotionUpdates.has(data.userId)) {
          userEmotionUpdates.set(data.userId, { emotions: [], intensities: [] });
        }
        const update = userEmotionUpdates.get(data.userId)!;
        update.emotions.push(data.emotionType);
        update.intensities.push(data.intensity);
      }

      for (const [userId, update] of userEmotionUpdates) {
        this.batchUpdateUserEmotionProfile(userId, update.emotions, update.intensities);
      }

      this.logger.log(`批量保存情感记录: ${savedRecords.length} 条`);
      return savedRecords;
    } catch (error) {
      this.logger.error('批量保存情感记录失败:', error);
      throw error;
    }
  }

  /**
   * 查询情感记录
   * @param options 查询选项
   * @returns 情感记录列表
   */
  async queryEmotionRecords(options: EmotionDataQueryOptions): Promise<EmotionRecord[]> {
    try {
      const queryBuilder = this.emotionRecordRepository.createQueryBuilder('emotion');

      // 添加查询条件
      if (options.userId) {
        queryBuilder.andWhere('emotion.userId = :userId', { userId: options.userId });
      }

      if (options.emotionType) {
        queryBuilder.andWhere('emotion.emotionType = :emotionType', { emotionType: options.emotionType });
      }

      if (options.startTime && options.endTime) {
        queryBuilder.andWhere('emotion.timestamp BETWEEN :startTime AND :endTime', {
          startTime: options.startTime,
          endTime: options.endTime,
        });
      } else if (options.startTime) {
        queryBuilder.andWhere('emotion.timestamp >= :startTime', { startTime: options.startTime });
      } else if (options.endTime) {
        queryBuilder.andWhere('emotion.timestamp <= :endTime', { endTime: options.endTime });
      }

      if (options.minIntensity !== undefined) {
        queryBuilder.andWhere('emotion.intensity >= :minIntensity', { minIntensity: options.minIntensity });
      }

      if (options.maxIntensity !== undefined) {
        queryBuilder.andWhere('emotion.intensity <= :maxIntensity', { maxIntensity: options.maxIntensity });
      }

      // 排序和分页
      queryBuilder.orderBy('emotion.timestamp', 'DESC');

      if (options.limit) {
        queryBuilder.limit(options.limit);
      }

      if (options.offset) {
        queryBuilder.offset(options.offset);
      }

      const records = await queryBuilder.getMany();
      this.logger.log(`查询情感记录: ${records.length} 条`);
      return records;
    } catch (error) {
      this.logger.error('查询情感记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取情感统计
   * @param userId 用户ID
   * @param timeRange 时间范围
   * @returns 统计结果
   */
  async getEmotionStatistics(
    userId: string,
    timeRange: { start: Date; end: Date }
  ): Promise<EmotionStatistics> {
    try {
      const records = await this.queryEmotionRecords({
        userId,
        startTime: timeRange.start,
        endTime: timeRange.end,
      });

      if (records.length === 0) {
        return {
          totalRecords: 0,
          emotionTypeDistribution: {},
          averageIntensity: 0,
          mostCommonEmotion: 'neutral',
          emotionTrend: 'stable',
          timeRange,
        };
      }

      // 计算情感类型分布
      const emotionTypeDistribution: { [emotionType: string]: number } = {};
      let totalIntensity = 0;

      for (const record of records) {
        emotionTypeDistribution[record.emotionType] = 
          (emotionTypeDistribution[record.emotionType] || 0) + 1;
        totalIntensity += record.intensity;
      }

      // 找出最常见情感
      let mostCommonEmotion = 'neutral';
      let maxCount = 0;
      for (const [emotion, count] of Object.entries(emotionTypeDistribution)) {
        if (count > maxCount) {
          maxCount = count;
          mostCommonEmotion = emotion;
        }
      }

      // 计算平均强度
      const averageIntensity = totalIntensity / records.length;

      // 分析情感趋势
      const emotionTrend = this.analyzeEmotionTrend(records);

      const statistics: EmotionStatistics = {
        totalRecords: records.length,
        emotionTypeDistribution,
        averageIntensity,
        mostCommonEmotion,
        emotionTrend,
        timeRange,
      };

      this.logger.log(`生成情感统计: 用户 ${userId}, ${records.length} 条记录`);
      return statistics;
    } catch (error) {
      this.logger.error('获取情感统计失败:', error);
      throw error;
    }
  }

  /**
   * 保存情感模式
   * @param patternData 模式数据
   * @returns 保存的模式
   */
  async saveEmotionPattern(patternData: {
    userId: string;
    patternName: string;
    emotionSequence: string[];
    timeIntervals: number[];
    intensityPattern: number[];
    frequency: number;
    confidence: number;
  }): Promise<EmotionPattern> {
    try {
      const pattern = this.emotionPatternRepository.create(patternData);
      const savedPattern = await this.emotionPatternRepository.save(pattern);
      
      this.logger.log(`保存情感模式: ${savedPattern.id}`);
      return savedPattern;
    } catch (error) {
      this.logger.error('保存情感模式失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户情感模式
   * @param userId 用户ID
   * @returns 情感模式列表
   */
  async getUserEmotionPatterns(userId: string): Promise<EmotionPattern[]> {
    try {
      const patterns = await this.emotionPatternRepository.find({
        where: { userId },
        order: { confidence: 'DESC' },
      });

      this.logger.log(`获取用户情感模式: 用户 ${userId}, ${patterns.length} 个模式`);
      return patterns;
    } catch (error) {
      this.logger.error('获取用户情感模式失败:', error);
      throw error;
    }
  }

  /**
   * 分析情感模式
   * @param userId 用户ID
   * @param timeRange 时间范围
   * @returns 模式分析结果
   */
  async analyzeEmotionPatterns(
    userId: string,
    timeRange: { start: Date; end: Date }
  ): Promise<EmotionPatternAnalysis> {
    try {
      const patterns = await this.getUserEmotionPatterns(userId);
      
      // 计算置信度分布
      const confidenceDistribution: { [range: string]: number } = {
        'low (0-0.3)': 0,
        'medium (0.3-0.7)': 0,
        'high (0.7-1.0)': 0,
      };

      let strongestPattern: EmotionPattern | null = null;
      let maxConfidence = 0;

      for (const pattern of patterns) {
        // 分类置信度
        if (pattern.confidence < 0.3) {
          confidenceDistribution['low (0-0.3)']++;
        } else if (pattern.confidence < 0.7) {
          confidenceDistribution['medium (0.3-0.7)']++;
        } else {
          confidenceDistribution['high (0.7-1.0)']++;
        }

        // 找出最强模式
        if (pattern.confidence > maxConfidence) {
          maxConfidence = pattern.confidence;
          strongestPattern = pattern;
        }
      }

      const analysis: EmotionPatternAnalysis = {
        patternCount: patterns.length,
        patterns,
        confidenceDistribution,
        strongestPattern,
      };

      this.logger.log(`分析情感模式: 用户 ${userId}, ${patterns.length} 个模式`);
      return analysis;
    } catch (error) {
      this.logger.error('分析情感模式失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户情感档案
   * @param userId 用户ID
   * @returns 用户情感档案
   */
  async getUserEmotionProfile(userId: string): Promise<UserEmotionProfile | null> {
    try {
      const profile = await this.userEmotionProfileRepository.findOne({
        where: { userId },
      });

      this.logger.log(`获取用户情感档案: 用户 ${userId}`);
      return profile;
    } catch (error) {
      this.logger.error('获取用户情感档案失败:', error);
      throw error;
    }
  }

  /**
   * 删除过期数据
   * @param retentionDays 保留天数
   * @returns 删除的记录数
   */
  async cleanupExpiredData(retentionDays: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const result = await this.emotionRecordRepository.delete({
        timestamp: MoreThan(cutoffDate),
      });

      const deletedCount = result.affected || 0;
      this.logger.log(`清理过期数据: 删除 ${deletedCount} 条记录`);
      return deletedCount;
    } catch (error) {
      this.logger.error('清理过期数据失败:', error);
      throw error;
    }
  }

  /**
   * 更新用户情感档案
   * @param userId 用户ID
   * @param emotionType 情感类型
   * @param intensity 强度
   */
  private async updateUserEmotionProfile(
    userId: string,
    emotionType: string,
    intensity: number
  ): Promise<void> {
    try {
      let profile = await this.userEmotionProfileRepository.findOne({
        where: { userId },
      });

      if (!profile) {
        // 创建新档案
        profile = this.userEmotionProfileRepository.create({
          userId,
          dominantEmotion: emotionType,
          averageIntensity: intensity,
          emotionCounts: { [emotionType]: 1 },
          totalInteractions: 1,
          lastUpdated: new Date(),
        });
      } else {
        // 更新现有档案
        profile.totalInteractions++;
        
        // 更新情感计数
        if (!profile.emotionCounts) {
          profile.emotionCounts = {};
        }
        profile.emotionCounts[emotionType] = (profile.emotionCounts[emotionType] || 0) + 1;

        // 更新平均强度
        profile.averageIntensity = 
          (profile.averageIntensity * (profile.totalInteractions - 1) + intensity) / profile.totalInteractions;

        // 更新主导情感
        let maxCount = 0;
        let dominantEmotion = emotionType;
        for (const [emotion, count] of Object.entries(profile.emotionCounts)) {
          if (count > maxCount) {
            maxCount = count;
            dominantEmotion = emotion;
          }
        }
        profile.dominantEmotion = dominantEmotion;
        profile.lastUpdated = new Date();
      }

      await this.userEmotionProfileRepository.save(profile);
    } catch (error) {
      this.logger.error('更新用户情感档案失败:', error);
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 批量更新用户情感档案
   * @param userId 用户ID
   * @param emotions 情感类型列表
   * @param intensities 强度列表
   */
  private async batchUpdateUserEmotionProfile(
    userId: string,
    emotions: string[],
    intensities: number[]
  ): Promise<void> {
    try {
      let profile = await this.userEmotionProfileRepository.findOne({
        where: { userId },
      });

      const emotionCounts: { [emotion: string]: number } = {};
      let totalIntensity = 0;

      // 统计情感类型和强度
      for (let i = 0; i < emotions.length; i++) {
        const emotion = emotions[i];
        const intensity = intensities[i];
        
        emotionCounts[emotion] = (emotionCounts[emotion] || 0) + 1;
        totalIntensity += intensity;
      }

      if (!profile) {
        // 创建新档案
        let dominantEmotion = '';
        let maxCount = 0;
        for (const [emotion, count] of Object.entries(emotionCounts)) {
          if (count > maxCount) {
            maxCount = count;
            dominantEmotion = emotion;
          }
        }

        profile = this.userEmotionProfileRepository.create({
          userId,
          dominantEmotion,
          averageIntensity: totalIntensity / emotions.length,
          emotionCounts,
          totalInteractions: emotions.length,
          lastUpdated: new Date(),
        });
      } else {
        // 更新现有档案
        const oldTotal = profile.totalInteractions;
        profile.totalInteractions += emotions.length;

        // 更新情感计数
        if (!profile.emotionCounts) {
          profile.emotionCounts = {};
        }
        for (const [emotion, count] of Object.entries(emotionCounts)) {
          profile.emotionCounts[emotion] = (profile.emotionCounts[emotion] || 0) + count;
        }

        // 更新平均强度
        profile.averageIntensity = 
          (profile.averageIntensity * oldTotal + totalIntensity) / profile.totalInteractions;

        // 更新主导情感
        let maxCount = 0;
        let dominantEmotion = '';
        for (const [emotion, count] of Object.entries(profile.emotionCounts)) {
          if (count > maxCount) {
            maxCount = count;
            dominantEmotion = emotion;
          }
        }
        profile.dominantEmotion = dominantEmotion;
        profile.lastUpdated = new Date();
      }

      await this.userEmotionProfileRepository.save(profile);
    } catch (error) {
      this.logger.error('批量更新用户情感档案失败:', error);
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 分析情感趋势
   * @param records 情感记录
   * @returns 趋势方向
   */
  private analyzeEmotionTrend(records: EmotionRecord[]): 'increasing' | 'decreasing' | 'stable' {
    if (records.length < 3) return 'stable';

    // 按时间排序
    const sortedRecords = records.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    
    // 计算最近几条记录的强度趋势
    const recentRecords = sortedRecords.slice(-Math.min(10, sortedRecords.length));
    const intensities = recentRecords.map(r => r.intensity);
    
    let increasingCount = 0;
    let decreasingCount = 0;
    
    for (let i = 1; i < intensities.length; i++) {
      if (intensities[i] > intensities[i - 1]) {
        increasingCount++;
      } else if (intensities[i] < intensities[i - 1]) {
        decreasingCount++;
      }
    }

    if (increasingCount > decreasingCount) {
      return 'increasing';
    } else if (decreasingCount > increasingCount) {
      return 'decreasing';
    } else {
      return 'stable';
    }
  }
}
