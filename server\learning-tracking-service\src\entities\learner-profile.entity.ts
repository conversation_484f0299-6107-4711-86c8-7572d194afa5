import { 
  Entity, 
  PrimaryGeneratedColumn, 
  Column, 
  CreateDateColumn, 
  UpdateDateColumn,
  Index 
} from 'typeorm';

/**
 * 学习者画像实体
 * 存储用户的学习画像数据
 */
@Entity('learner_profiles')
export class LearnerProfileEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id', unique: true })
  @Index()
  userId: string;

  @Column({ type: 'text', nullable: true })
  demographics: string;

  @Column({ name: 'learning_preferences', type: 'text', nullable: true })
  learningPreferences: string;

  @Column({ name: 'knowledge_areas', type: 'text', nullable: true })
  knowledgeAreas: string;

  @Column({ name: 'behavior_patterns', type: 'text', nullable: true })
  behaviorPatterns: string;

  @Column({ name: 'learning_goals', type: 'text', nullable: true })
  learningGoals: string;

  @Column({ name: 'social_learning', type: 'text', nullable: true })
  socialLearning: string;

  @Column({ name: 'cognitive_traits', type: 'text', nullable: true })
  cognitiveTraits: string;

  @Column({ name: 'emotional_traits', type: 'text', nullable: true })
  emotionalTraits: string;

  @Column({ name: 'learning_outcomes', type: 'text', nullable: true })
  learningOutcomes: string;

  @Column({ type: 'text', nullable: true })
  metadata: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
