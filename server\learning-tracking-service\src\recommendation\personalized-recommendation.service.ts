import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { 
  Recommendation, 
  RecommendationRequest, 
  RecommendationResult,
  RecommendationFeedback,
  RecommendationContent,
  UserPreference,
  ContentSimilarity
} from './recommendation.interface';
import { LearnerProfile, WeakArea } from '../profile/learner-profile.interface';
import { LearnerProfileAnalyzerService } from '../profile/learner-profile-analyzer.service';
import { RecommendationEntity } from '../entities/recommendation.entity';
import { ContentEntity } from '../entities/content.entity';

/**
 * 个性化推荐引擎服务
 * 基于用户画像和内容特征生成个性化推荐
 */
@Injectable()
export class PersonalizedRecommendationService {
  private readonly logger = new Logger(PersonalizedRecommendationService.name);

  constructor(
    @InjectRepository(RecommendationEntity)
    private readonly recommendationRepository: Repository<RecommendationEntity>,
    @InjectRepository(ContentEntity)
    private readonly contentRepository: Repository<ContentEntity>,
    private readonly profileAnalyzer: LearnerProfileAnalyzerService,
  ) {}

  /**
   * 生成个性化推荐
   * @param request 推荐请求
   * @returns 推荐结果
   */
  async generateRecommendations(request: RecommendationRequest): Promise<RecommendationResult> {
    const startTime = Date.now();
    
    try {
      this.logger.log(`开始生成推荐: 用户=${request.userId}, 数量=${request.limit || 5}`);

      // 获取用户画像
      const profileResult = await this.profileAnalyzer.buildLearnerProfile(request.userId);
      const profile = profileResult.profile;
      const weakAreas = profileResult.weakAreas;

      // 获取用户偏好
      const userPreference = await this.getUserPreference(request.userId);

      // 获取候选内容
      const candidateContents = await this.getCandidateContents(request, profile);

      // 计算推荐分数
      const scoredRecommendations = await this.scoreRecommendations(
        candidateContents,
        profile,
        weakAreas,
        userPreference,
        request
      );

      // 应用多样性和新颖性
      const diversifiedRecommendations = this.applyDiversification(
        scoredRecommendations,
        request.limit || 5
      );

      // 生成推荐记录
      const recommendations = await this.createRecommendationRecords(
        diversifiedRecommendations,
        request.userId,
        profile
      );

      // 保存推荐记录
      await this.saveRecommendations(recommendations);

      const executionTime = Date.now() - startTime;

      const result: RecommendationResult = {
        recommendations,
        totalCount: recommendations.length,
        strategy: 'personalized_hybrid',
        executionTime,
        metadata: {
          algorithms: ['collaborative_filtering', 'content_based', 'knowledge_based'],
          filters: this.getAppliedFilters(request),
          diversityScore: this.calculateDiversityScore(recommendations),
          noveltyScore: this.calculateNoveltyScore(recommendations, userPreference)
        }
      };

      this.logger.log(`推荐生成完成: 用户=${request.userId}, 推荐数=${recommendations.length}, 耗时=${executionTime}ms`);
      return result;
    } catch (error) {
      this.logger.error(`生成推荐失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取候选内容
   * @param request 推荐请求
   * @param profile 用户画像
   * @returns 候选内容数组
   */
  private async getCandidateContents(
    request: RecommendationRequest,
    profile: LearnerProfile
  ): Promise<RecommendationContent[]> {
    const queryBuilder = this.contentRepository.createQueryBuilder('content');

    // 基础过滤
    queryBuilder.where('content.enabled = :enabled', { enabled: true });

    // 类型过滤
    if (request.types && request.types.length > 0) {
      queryBuilder.andWhere('content.type IN (:...types)', { types: request.types });
    }

    // 知识领域过滤
    if (request.knowledgeAreas && request.knowledgeAreas.length > 0) {
      queryBuilder.andWhere('content.knowledgeArea IN (:...areas)', { areas: request.knowledgeAreas });
    } else {
      // 基于用户画像的知识领域
      const userKnowledgeAreas = Object.keys(profile.knowledgeAreas);
      if (userKnowledgeAreas.length > 0) {
        queryBuilder.andWhere('content.knowledgeArea IN (:...areas)', { areas: userKnowledgeAreas });
      }
    }

    // 难度过滤
    if (request.difficulty) {
      queryBuilder.andWhere('content.difficulty = :difficulty', { difficulty: request.difficulty });
    } else {
      // 基于用户偏好的难度
      const preferredDifficulty = profile.learningPreferences.preferredDifficulty;
      queryBuilder.andWhere('content.difficulty = :difficulty', { difficulty: preferredDifficulty });
    }

    // 时长过滤
    if (request.maxDuration) {
      queryBuilder.andWhere('content.duration <= :maxDuration', { maxDuration: request.maxDuration });
    } else {
      // 基于用户注意力持续时间
      const maxDuration = profile.learningPreferences.attentionSpan * 1.5; // 允许超出50%
      queryBuilder.andWhere('content.duration <= :maxDuration', { maxDuration });
    }

    // 排除已完成内容
    if (request.excludeCompleted) {
      const completedContentIds = await this.getCompletedContentIds(request.userId);
      if (completedContentIds.length > 0) {
        queryBuilder.andWhere('content.id NOT IN (:...completedIds)', { completedIds: completedContentIds });
      }
    }

    // 限制数量（获取更多候选以便后续筛选）
    const candidateLimit = (request.limit || 5) * 10;
    queryBuilder.limit(candidateLimit);

    // 按质量排序
    queryBuilder.orderBy('content.averageRating', 'DESC')
                .addOrderBy('content.completions', 'DESC');

    const entities = await queryBuilder.getMany();
    return entities.map(entity => this.entityToContent(entity));
  }

  /**
   * 计算推荐分数
   * @param contents 候选内容
   * @param profile 用户画像
   * @param weakAreas 薄弱环节
   * @param userPreference 用户偏好
   * @param request 推荐请求
   * @returns 评分后的推荐
   */
  private async scoreRecommendations(
    contents: RecommendationContent[],
    profile: LearnerProfile,
    weakAreas: WeakArea[],
    userPreference: UserPreference,
    request: RecommendationRequest
  ): Promise<Array<RecommendationContent & { score: number; reasons: string[] }>> {
    const scoredContents = [];

    for (const content of contents) {
      let score = 0;
      const reasons: string[] = [];

      // 1. 基于薄弱环节的推荐 (权重: 0.4)
      const weakAreaScore = this.calculateWeakAreaScore(content, weakAreas);
      score += weakAreaScore * 0.4;
      if (weakAreaScore > 0.5) {
        reasons.push(`针对您在${content.knowledgeArea}领域的薄弱环节`);
      }

      // 2. 基于用户偏好的推荐 (权重: 0.3)
      const preferenceScore = this.calculatePreferenceScore(content, userPreference, profile);
      score += preferenceScore * 0.3;
      if (preferenceScore > 0.7) {
        reasons.push('符合您的学习偏好');
      }

      // 3. 基于内容质量的推荐 (权重: 0.2)
      const qualityScore = this.calculateQualityScore(content);
      score += qualityScore * 0.2;
      if (qualityScore > 0.8) {
        reasons.push('高质量内容，用户评价优秀');
      }

      // 4. 基于协同过滤的推荐 (权重: 0.1)
      const collaborativeScore = await this.calculateCollaborativeScore(content, profile);
      score += collaborativeScore * 0.1;
      if (collaborativeScore > 0.6) {
        reasons.push('相似学习者也喜欢这个内容');
      }

      // 5. 上下文相关性调整
      score = this.adjustScoreByContext(score, content, request.context);

      scoredContents.push({
        ...content,
        score,
        reasons
      });
    }

    // 按分数排序
    return scoredContents.sort((a, b) => b.score - a.score);
  }

  /**
   * 计算薄弱环节分数
   * @param content 内容
   * @param weakAreas 薄弱环节
   * @returns 分数
   */
  private calculateWeakAreaScore(content: RecommendationContent, weakAreas: WeakArea[]): number {
    const relevantWeakArea = weakAreas.find(area => area.subject === content.knowledgeArea);
    
    if (!relevantWeakArea) {
      return 0.1; // 不相关领域给予低分
    }

    // 基于薄弱程度和优先级计算分数
    const weaknessScore = 1 - relevantWeakArea.confidence;
    const priorityScore = relevantWeakArea.priority / 100; // 假设优先级最高为100
    const urgencyScore = relevantWeakArea.urgency === 'high' ? 1 : 
                        relevantWeakArea.urgency === 'medium' ? 0.7 : 0.4;

    return (weaknessScore * 0.5 + priorityScore * 0.3 + urgencyScore * 0.2);
  }

  /**
   * 计算用户偏好分数
   * @param content 内容
   * @param userPreference 用户偏好
   * @param profile 用户画像
   * @returns 分数
   */
  private calculatePreferenceScore(
    content: RecommendationContent,
    userPreference: UserPreference,
    profile: LearnerProfile
  ): number {
    let score = 0;

    // 内容类型偏好
    const typePreference = userPreference.contentTypes[content.type] || 0;
    score += typePreference * 0.3;

    // 知识领域偏好
    const areaPreference = userPreference.knowledgeAreas[content.knowledgeArea] || 0;
    score += areaPreference * 0.3;

    // 难度偏好
    const difficultyPreference = userPreference.difficulties[content.difficulty] || 0;
    score += difficultyPreference * 0.2;

    // 时长偏好
    const durationScore = this.calculateDurationPreferenceScore(content.duration, userPreference);
    score += durationScore * 0.2;

    return Math.min(1, score);
  }

  /**
   * 计算时长偏好分数
   * @param duration 内容时长
   * @param userPreference 用户偏好
   * @returns 分数
   */
  private calculateDurationPreferenceScore(duration: number, userPreference: UserPreference): number {
    const preferred = userPreference.timePreferences.duration.preferred;
    const min = userPreference.timePreferences.duration.min;
    const max = userPreference.timePreferences.duration.max;

    if (duration < min || duration > max) {
      return 0.1; // 超出偏好范围
    }

    // 计算与偏好时长的接近程度
    const distance = Math.abs(duration - preferred);
    const maxDistance = Math.max(preferred - min, max - preferred);
    
    return maxDistance > 0 ? 1 - (distance / maxDistance) : 1;
  }

  /**
   * 计算内容质量分数
   * @param content 内容
   * @returns 分数
   */
  private calculateQualityScore(content: RecommendationContent): number {
    let score = 0;

    // 平均评分 (权重: 0.4)
    const ratingScore = content.metadata.averageRating / 5; // 假设最高5分
    score += ratingScore * 0.4;

    // 成功率 (权重: 0.3)
    score += content.metadata.successRate * 0.3;

    // 完成次数 (权重: 0.2)
    const completionScore = Math.min(1, content.metadata.completions / 100); // 100次完成为满分
    score += completionScore * 0.2;

    // 评分人数 (权重: 0.1)
    const ratingCountScore = Math.min(1, content.metadata.ratingCount / 50); // 50人评分为满分
    score += ratingCountScore * 0.1;

    return score;
  }

  /**
   * 计算协同过滤分数
   * @param content 内容
   * @param profile 用户画像
   * @returns 分数
   */
  private async calculateCollaborativeScore(
    content: RecommendationContent,
    profile: LearnerProfile
  ): Promise<number> {
    // 简化的协同过滤实现
    // 在实际应用中，这里应该使用更复杂的协同过滤算法
    
    // 基于相似用户的推荐
    const similarUsers = await this.findSimilarUsers(profile.userId);
    if (similarUsers.length === 0) {
      return 0.5; // 默认分数
    }

    // 计算相似用户对该内容的平均评分
    let totalScore = 0;
    let count = 0;

    for (const similarUserId of similarUsers) {
      const userRating = await this.getUserContentRating(similarUserId, content.id);
      if (userRating > 0) {
        totalScore += userRating;
        count++;
      }
    }

    return count > 0 ? totalScore / count / 5 : 0.5; // 归一化到0-1
  }

  /**
   * 根据上下文调整分数
   * @param score 原始分数
   * @param content 内容
   * @param context 上下文
   * @returns 调整后的分数
   */
  private adjustScoreByContext(
    score: number,
    content: RecommendationContent,
    context?: any
  ): number {
    if (!context) {
      return score;
    }

    let adjustedScore = score;

    // 时间段调整
    if (context.timeOfDay) {
      if (context.timeOfDay === 'morning' && content.type === 'video') {
        adjustedScore *= 1.1; // 早上更适合看视频
      } else if (context.timeOfDay === 'evening' && content.type === 'article') {
        adjustedScore *= 1.1; // 晚上更适合阅读
      }
    }

    // 设备类型调整
    if (context.deviceType) {
      if (context.deviceType === 'mobile' && content.duration > 30) {
        adjustedScore *= 0.9; // 移动设备不太适合长时间学习
      } else if (context.deviceType === 'desktop' && content.type === 'interactive') {
        adjustedScore *= 1.1; // 桌面设备更适合交互内容
      }
    }

    return Math.min(1, adjustedScore);
  }

  /**
   * 应用多样性和新颖性
   * @param scoredContents 评分内容
   * @param limit 限制数量
   * @returns 多样化后的内容
   */
  private applyDiversification(
    scoredContents: Array<RecommendationContent & { score: number; reasons: string[] }>,
    limit: number
  ): Array<RecommendationContent & { score: number; reasons: string[] }> {
    const selected: Array<RecommendationContent & { score: number; reasons: string[] }> = [];
    const typeCount = new Map<string, number>();
    const areaCount = new Map<string, number>();

    for (const content of scoredContents) {
      if (selected.length >= limit) {
        break;
      }

      // 检查类型多样性
      const currentTypeCount = typeCount.get(content.type) || 0;
      const currentAreaCount = areaCount.get(content.knowledgeArea) || 0;

      // 多样性惩罚
      let diversityPenalty = 0;
      if (currentTypeCount >= 2) diversityPenalty += 0.1;
      if (currentAreaCount >= 2) diversityPenalty += 0.1;

      const adjustedScore = content.score - diversityPenalty;

      // 如果调整后分数仍然足够高，或者是前几个推荐，则选择
      if (adjustedScore > 0.3 || selected.length < Math.min(3, limit)) {
        selected.push(content);
        typeCount.set(content.type, currentTypeCount + 1);
        areaCount.set(content.knowledgeArea, currentAreaCount + 1);
      }
    }

    return selected;
  }

  /**
   * 创建推荐记录
   * @param scoredContents 评分内容
   * @param userId 用户ID
   * @param profile 用户画像
   * @returns 推荐记录数组
   */
  private async createRecommendationRecords(
    scoredContents: Array<RecommendationContent & { score: number; reasons: string[] }>,
    userId: string,
    profile: LearnerProfile
  ): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];

    for (const content of scoredContents) {
      const recommendation: Recommendation = {
        id: this.generateId(),
        userId,
        contentId: content.id,
        content,
        type: content.type,
        title: content.title,
        description: content.description,
        relevanceScore: content.score,
        difficulty: content.difficulty,
        estimatedDuration: content.duration,
        reason: content.reasons.join('；'),
        tags: content.tags,
        knowledgeArea: content.knowledgeArea,
        prerequisites: content.prerequisites,
        learningObjectives: content.learningObjectives,
        status: 'pending',
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后过期
        metadata: {
          algorithm: 'personalized_hybrid',
          version: '1.0',
          factors: ['weak_areas', 'user_preference', 'content_quality', 'collaborative'],
          confidence: content.score,
          context: {
            profileVersion: profile.metadata.profileVersion,
            analysisDate: profile.metadata.lastAnalysisDate
          }
        }
      };

      recommendations.push(recommendation);
    }

    return recommendations;
  }

  /**
   * 保存推荐记录
   * @param recommendations 推荐记录
   */
  private async saveRecommendations(recommendations: Recommendation[]): Promise<void> {
    const entities = recommendations.map(rec => this.recommendationToEntity(rec));
    await this.recommendationRepository.save(entities);
  }

  /**
   * 获取用户偏好
   * @param userId 用户ID
   * @returns 用户偏好
   */
  private async getUserPreference(userId: string): Promise<UserPreference> {
    // 这里应该从用户偏好表获取数据
    // 暂时返回默认偏好
    return {
      userId,
      contentTypes: {
        'video': 0.8,
        'article': 0.6,
        'interactive': 0.9,
        'exercise': 0.7
      },
      knowledgeAreas: {},
      difficulties: {
        'easy': 0.3,
        'medium': 0.8,
        'hard': 0.5
      },
      authors: {},
      tags: {},
      timePreferences: {
        duration: {
          min: 10,
          max: 60,
          preferred: 30
        },
        timeOfDay: ['morning', 'afternoon']
      },
      updatedAt: new Date()
    };
  }

  /**
   * 获取已完成内容ID
   * @param userId 用户ID
   * @returns 已完成内容ID数组
   */
  private async getCompletedContentIds(userId: string): Promise<string[]> {
    const completedRecommendations = await this.recommendationRepository.find({
      where: { userId, status: 'completed' },
      select: ['contentId']
    });

    return completedRecommendations.map(rec => rec.contentId);
  }

  /**
   * 查找相似用户
   * @param userId 用户ID
   * @returns 相似用户ID数组
   */
  private async findSimilarUsers(userId: string): Promise<string[]> {
    // 简化实现，实际应该基于用户画像相似度计算
    return [];
  }

  /**
   * 获取用户对内容的评分
   * @param userId 用户ID
   * @param contentId 内容ID
   * @returns 评分
   */
  private async getUserContentRating(userId: string, contentId: string): Promise<number> {
    const recommendation = await this.recommendationRepository.findOne({
      where: { userId, contentId, feedbackRating: { $ne: null } }
    });

    return recommendation?.feedbackRating || 0;
  }

  /**
   * 获取应用的过滤器
   * @param request 推荐请求
   * @returns 过滤器数组
   */
  private getAppliedFilters(request: RecommendationRequest): string[] {
    const filters: string[] = [];

    if (request.types) filters.push('content_type');
    if (request.knowledgeAreas) filters.push('knowledge_area');
    if (request.difficulty) filters.push('difficulty');
    if (request.maxDuration) filters.push('duration');
    if (request.excludeCompleted) filters.push('exclude_completed');

    return filters;
  }

  /**
   * 计算多样性分数
   * @param recommendations 推荐列表
   * @returns 多样性分数
   */
  private calculateDiversityScore(recommendations: Recommendation[]): number {
    if (recommendations.length === 0) return 0;

    const types = new Set(recommendations.map(r => r.type));
    const areas = new Set(recommendations.map(r => r.knowledgeArea));

    const typesDiversity = types.size / recommendations.length;
    const areasDiversity = areas.size / recommendations.length;

    return (typesDiversity + areasDiversity) / 2;
  }

  /**
   * 计算新颖性分数
   * @param recommendations 推荐列表
   * @param userPreference 用户偏好
   * @returns 新颖性分数
   */
  private calculateNoveltyScore(
    recommendations: Recommendation[],
    userPreference: UserPreference
  ): number {
    // 简化的新颖性计算
    // 基于推荐内容与用户历史偏好的差异
    return 0.6; // 默认值
  }

  /**
   * 实体转内容
   * @param entity 内容实体
   * @returns 推荐内容
   */
  private entityToContent(entity: any): RecommendationContent {
    return {
      id: entity.id,
      title: entity.title,
      description: entity.description,
      type: entity.type,
      difficulty: entity.difficulty,
      duration: entity.duration,
      tags: JSON.parse(entity.tags || '[]'),
      knowledgeArea: entity.knowledgeArea,
      prerequisites: JSON.parse(entity.prerequisites || '[]'),
      learningObjectives: JSON.parse(entity.learningObjectives || '[]'),
      contentUrl: entity.contentUrl,
      thumbnailUrl: entity.thumbnailUrl,
      authorId: entity.authorId,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      metadata: {
        views: entity.views || 0,
        completions: entity.completions || 0,
        averageRating: entity.averageRating || 0,
        ratingCount: entity.ratingCount || 0,
        successRate: entity.successRate || 0
      }
    };
  }

  /**
   * 推荐转实体
   * @param recommendation 推荐记录
   * @returns 推荐实体
   */
  private recommendationToEntity(recommendation: Recommendation): Partial<RecommendationEntity> {
    return {
      id: recommendation.id,
      userId: recommendation.userId,
      contentId: recommendation.contentId,
      type: recommendation.type,
      title: recommendation.title,
      description: recommendation.description,
      relevanceScore: recommendation.relevanceScore,
      difficulty: recommendation.difficulty,
      estimatedDuration: recommendation.estimatedDuration,
      reason: recommendation.reason,
      tags: JSON.stringify(recommendation.tags),
      knowledgeArea: recommendation.knowledgeArea,
      prerequisites: JSON.stringify(recommendation.prerequisites),
      learningObjectives: JSON.stringify(recommendation.learningObjectives),
      status: recommendation.status,
      createdAt: recommendation.createdAt,
      expiresAt: recommendation.expiresAt,
      metadata: JSON.stringify(recommendation.metadata)
    };
  }

  /**
   * 记录推荐反馈
   * @param feedback 反馈信息
   */
  async recordFeedback(feedback: RecommendationFeedback): Promise<void> {
    try {
      const recommendation = await this.recommendationRepository.findOne({
        where: { id: feedback.recommendationId }
      });

      if (!recommendation) {
        throw new Error(`推荐记录不存在: ${feedback.recommendationId}`);
      }

      // 更新推荐状态
      recommendation.status = feedback.action as any;
      recommendation.feedbackRating = feedback.rating;
      recommendation.feedbackComment = feedback.comment;

      // 设置相应的时间戳
      const now = new Date();
      switch (feedback.action) {
        case 'accepted':
          recommendation.acceptedAt = now;
          break;
        case 'completed':
          recommendation.completedAt = now;
          break;
      }

      await this.recommendationRepository.save(recommendation);

      // 更新用户偏好
      await this.updateUserPreferenceFromFeedback(feedback);

      this.logger.log(`记录推荐反馈: ${feedback.recommendationId}, 动作: ${feedback.action}`);
    } catch (error) {
      this.logger.error(`记录推荐反馈失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 根据反馈更新用户偏好
   * @param feedback 反馈信息
   */
  private async updateUserPreferenceFromFeedback(feedback: RecommendationFeedback): Promise<void> {
    const recommendation = await this.recommendationRepository.findOne({
      where: { id: feedback.recommendationId }
    });

    if (!recommendation) return;

    // 获取当前用户偏好
    const userPreference = await this.getUserPreference(feedback.userId);

    // 根据反馈调整偏好权重
    const adjustmentFactor = this.getAdjustmentFactor(feedback.action, feedback.rating);

    // 更新内容类型偏好
    const currentTypeWeight = userPreference.contentTypes[recommendation.type] || 0.5;
    userPreference.contentTypes[recommendation.type] = Math.max(0, Math.min(1,
      currentTypeWeight + adjustmentFactor * 0.1
    ));

    // 更新知识领域偏好
    const currentAreaWeight = userPreference.knowledgeAreas[recommendation.knowledgeArea] || 0.5;
    userPreference.knowledgeAreas[recommendation.knowledgeArea] = Math.max(0, Math.min(1,
      currentAreaWeight + adjustmentFactor * 0.1
    ));

    // 更新难度偏好
    const currentDifficultyWeight = userPreference.difficulties[recommendation.difficulty] || 0.5;
    userPreference.difficulties[recommendation.difficulty] = Math.max(0, Math.min(1,
      currentDifficultyWeight + adjustmentFactor * 0.1
    ));

    // 保存更新后的偏好
    await this.saveUserPreference(userPreference);
  }

  /**
   * 获取调整因子
   * @param action 反馈动作
   * @param rating 评分
   * @returns 调整因子
   */
  private getAdjustmentFactor(action: string, rating?: number): number {
    switch (action) {
      case 'accepted':
        return 0.5;
      case 'completed':
        return rating ? (rating - 3) / 2 : 0.8; // 基于评分调整
      case 'rejected':
        return -0.8;
      case 'ignored':
        return -0.3;
      default:
        return 0;
    }
  }

  /**
   * 保存用户偏好
   * @param userPreference 用户偏好
   */
  private async saveUserPreference(userPreference: UserPreference): Promise<void> {
    // 这里应该保存到用户偏好表
    // 暂时只记录日志
    this.logger.debug(`更新用户偏好: ${userPreference.userId}`);
  }

  /**
   * 获取推荐统计
   * @param userId 用户ID
   * @param timeRange 时间范围
   * @returns 推荐统计
   */
  async getRecommendationStats(
    userId: string,
    timeRange: { start: Date; end: Date }
  ): Promise<any> {
    const queryBuilder = this.recommendationRepository.createQueryBuilder('rec')
      .where('rec.userId = :userId', { userId })
      .andWhere('rec.createdAt >= :start', { start: timeRange.start })
      .andWhere('rec.createdAt <= :end', { end: timeRange.end });

    const recommendations = await queryBuilder.getMany();

    if (recommendations.length === 0) {
      return {
        totalRecommendations: 0,
        acceptanceRate: 0,
        completionRate: 0,
        averageRating: 0,
        clickThroughRate: 0,
        conversionRate: 0,
        byType: {},
        byKnowledgeArea: {},
        timeRange
      };
    }

    const total = recommendations.length;
    const accepted = recommendations.filter(r => r.status === 'accepted' || r.status === 'completed').length;
    const completed = recommendations.filter(r => r.status === 'completed').length;
    const viewed = recommendations.filter(r => r.viewedAt).length;

    const ratings = recommendations
      .filter(r => r.feedbackRating)
      .map(r => r.feedbackRating);

    const averageRating = ratings.length > 0
      ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length
      : 0;

    // 按类型统计
    const byType = {};
    const typeGroups = this.groupBy(recommendations, 'type');
    Object.entries(typeGroups).forEach(([type, recs]: [string, any[]]) => {
      const typeAccepted = recs.filter(r => r.status === 'accepted' || r.status === 'completed').length;
      const typeCompleted = recs.filter(r => r.status === 'completed').length;

      byType[type] = {
        count: recs.length,
        acceptanceRate: recs.length > 0 ? typeAccepted / recs.length : 0,
        completionRate: recs.length > 0 ? typeCompleted / recs.length : 0
      };
    });

    // 按知识领域统计
    const byKnowledgeArea = {};
    const areaGroups = this.groupBy(recommendations, 'knowledgeArea');
    Object.entries(areaGroups).forEach(([area, recs]: [string, any[]]) => {
      const areaAccepted = recs.filter(r => r.status === 'accepted' || r.status === 'completed').length;
      const areaCompleted = recs.filter(r => r.status === 'completed').length;

      byKnowledgeArea[area] = {
        count: recs.length,
        acceptanceRate: recs.length > 0 ? areaAccepted / recs.length : 0,
        completionRate: recs.length > 0 ? areaCompleted / recs.length : 0
      };
    });

    return {
      totalRecommendations: total,
      acceptanceRate: accepted / total,
      completionRate: completed / total,
      averageRating,
      clickThroughRate: viewed / total,
      conversionRate: completed / total,
      byType,
      byKnowledgeArea,
      timeRange
    };
  }

  /**
   * 按字段分组
   * @param array 数组
   * @param key 分组键
   * @returns 分组结果
   */
  private groupBy(array: any[], key: string): { [key: string]: any[] } {
    return array.reduce((groups, item) => {
      const group = item[key] || 'unknown';
      groups[group] = groups[group] || [];
      groups[group].push(item);
      return groups;
    }, {});
  }

  /**
   * 获取用户推荐历史
   * @param userId 用户ID
   * @param limit 限制数量
   * @returns 推荐历史
   */
  async getUserRecommendationHistory(userId: string, limit: number = 20): Promise<Recommendation[]> {
    const entities = await this.recommendationRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      take: limit
    });

    return entities.map(entity => this.entityToRecommendation(entity));
  }

  /**
   * 实体转推荐
   * @param entity 推荐实体
   * @returns 推荐记录
   */
  private entityToRecommendation(entity: RecommendationEntity): Recommendation {
    return {
      id: entity.id,
      userId: entity.userId,
      contentId: entity.contentId,
      type: entity.type as any,
      title: entity.title,
      description: entity.description,
      relevanceScore: entity.relevanceScore,
      difficulty: entity.difficulty as any,
      estimatedDuration: entity.estimatedDuration,
      reason: entity.reason,
      tags: JSON.parse(entity.tags || '[]'),
      knowledgeArea: entity.knowledgeArea,
      prerequisites: JSON.parse(entity.prerequisites || '[]'),
      learningObjectives: JSON.parse(entity.learningObjectives || '[]'),
      status: entity.status as any,
      feedbackRating: entity.feedbackRating,
      feedbackComment: entity.feedbackComment,
      createdAt: entity.createdAt,
      expiresAt: entity.expiresAt,
      viewedAt: entity.viewedAt,
      acceptedAt: entity.acceptedAt,
      completedAt: entity.completedAt,
      metadata: JSON.parse(entity.metadata || '{}')
    };
  }

  /**
   * 生成ID
   * @returns 唯一ID
   */
  private generateId(): string {
    return 'rec_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}
