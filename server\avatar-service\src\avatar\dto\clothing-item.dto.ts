/**
 * 服装项目DTO
 */
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional } from 'class-validator';

export class ClothingItemDto {
  @ApiProperty({ description: '服装ID' })
  @IsString()
  id: string;

  @ApiProperty({ 
    description: '服装类型', 
    enum: ['shirt', 'pants', 'dress', 'shoes', 'hat', 'accessory'] 
  })
  @IsEnum(['shirt', 'pants', 'dress', 'shoes', 'hat', 'accessory'])
  type: 'shirt' | 'pants' | 'dress' | 'shoes' | 'hat' | 'accessory';

  @ApiProperty({ description: '服装颜色', required: false })
  @IsOptional()
  @IsString()
  color?: string;

  @ApiProperty({ description: '服装尺寸', required: false })
  @IsOptional()
  @IsString()
  size?: string;
}
