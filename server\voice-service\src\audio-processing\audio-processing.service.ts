import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as ffmpeg from 'fluent-ffmpeg';
import * as fs from 'fs-extra';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

/**
 * 音频格式
 */
export enum AudioFormat {
  WAV = 'wav',
  MP3 = 'mp3',
  FLAC = 'flac',
  OGG = 'ogg',
  AAC = 'aac',
  M4A = 'm4a',
}

/**
 * 音频处理配置
 */
export interface AudioProcessingConfig {
  inputFormat?: AudioFormat;
  outputFormat: AudioFormat;
  sampleRate?: number;
  channels?: number;
  bitRate?: number;
  volume?: number;
  normalize?: boolean;
  removeNoise?: boolean;
  trimSilence?: boolean;
  fadeIn?: number;
  fadeOut?: number;
}

/**
 * 音频信息
 */
export interface AudioInfo {
  duration: number;
  sampleRate: number;
  channels: number;
  bitRate: number;
  format: string;
  size: number;
  codec: string;
}

/**
 * 音频分析结果
 */
export interface AudioAnalysis {
  info: AudioInfo;
  volume: {
    peak: number;
    rms: number;
    lufs: number;
  };
  spectrum: {
    frequencies: number[];
    magnitudes: number[];
  };
  quality: {
    snr?: number;
    thd?: number;
    score: number;
    issues: string[];
  };
  speech: {
    hasSpeech: boolean;
    speechRatio: number;
    silenceSegments: Array<{ start: number; end: number }>;
    speechSegments: Array<{ start: number; end: number }>;
  };
}

/**
 * 音频片段
 */
export interface AudioSegment {
  id: string;
  startTime: number;
  endTime: number;
  audioData: Buffer;
  type: 'speech' | 'silence' | 'noise';
}

@Injectable()
export class AudioProcessingService {
  private tempDir: string;

  constructor(private configService: ConfigService) {
    this.tempDir = this.configService.get<string>('TEMP_DIR', './temp');
    this.ensureTempDir();
    this.configureFfmpeg();
  }

  /**
   * 确保临时目录存在
   */
  private async ensureTempDir(): Promise<void> {
    await fs.ensureDir(this.tempDir);
  }

  /**
   * 配置FFmpeg
   */
  private configureFfmpeg(): void {
    const ffmpegPath = this.configService.get<string>('FFMPEG_PATH');
    const ffprobePath = this.configService.get<string>('FFPROBE_PATH');

    if (ffmpegPath) {
      ffmpeg.setFfmpegPath(ffmpegPath);
    }
    if (ffprobePath) {
      ffmpeg.setFfprobePath(ffprobePath);
    }
  }

  /**
   * 转换音频格式
   */
  async convertAudio(
    inputBuffer: Buffer,
    config: AudioProcessingConfig,
  ): Promise<Buffer> {
    const inputFile = path.join(this.tempDir, `input_${uuidv4()}.${config.inputFormat || 'wav'}`);
    const outputFile = path.join(this.tempDir, `output_${uuidv4()}.${config.outputFormat}`);

    try {
      // 写入输入文件
      await fs.writeFile(inputFile, inputBuffer);

      // 执行转换
      await this.executeConversion(inputFile, outputFile, config);

      // 读取输出文件
      const outputBuffer = await fs.readFile(outputFile);

      return outputBuffer;
    } finally {
      // 清理临时文件
      await this.cleanupFiles([inputFile, outputFile]);
    }
  }

  /**
   * 执行音频转换
   */
  private executeConversion(
    inputFile: string,
    outputFile: string,
    config: AudioProcessingConfig,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      let command = ffmpeg(inputFile);

      // 设置输出格式
      command = command.format(config.outputFormat);

      // 设置采样率
      if (config.sampleRate) {
        command = command.audioFrequency(config.sampleRate);
      }

      // 设置声道数
      if (config.channels) {
        command = command.audioChannels(config.channels);
      }

      // 设置比特率
      if (config.bitRate) {
        command = command.audioBitrate(config.bitRate);
      }

      // 音量调整
      if (config.volume && config.volume !== 1.0) {
        command = command.audioFilters(`volume=${config.volume}`);
      }

      // 音频标准化
      if (config.normalize) {
        command = command.audioFilters('loudnorm');
      }

      // 降噪
      if (config.removeNoise) {
        command = command.audioFilters('afftdn');
      }

      // 去除静音
      if (config.trimSilence) {
        command = command.audioFilters('silenceremove=1:0:-50dB');
      }

      // 淡入淡出
      const filters: string[] = [];
      if (config.fadeIn) {
        filters.push(`afade=t=in:d=${config.fadeIn}`);
      }
      if (config.fadeOut) {
        filters.push(`afade=t=out:d=${config.fadeOut}`);
      }
      if (filters.length > 0) {
        command = command.audioFilters(filters);
      }

      // 执行转换
      command
        .output(outputFile)
        .on('end', () => resolve())
        .on('error', (error) => reject(error))
        .run();
    });
  }

  /**
   * 获取音频信息
   */
  async getAudioInfo(audioBuffer: Buffer): Promise<AudioInfo> {
    const inputFile = path.join(this.tempDir, `info_${uuidv4()}.wav`);

    try {
      await fs.writeFile(inputFile, audioBuffer);

      return new Promise((resolve, reject) => {
        ffmpeg.ffprobe(inputFile, (error, metadata) => {
          if (error) {
            reject(error);
            return;
          }

          const audioStream = metadata.streams.find(s => s.codec_type === 'audio');
          if (!audioStream) {
            reject(new Error('未找到音频流'));
            return;
          }

          resolve({
            duration: parseFloat(audioStream.duration || '0'),
            sampleRate: audioStream.sample_rate || 0,
            channels: audioStream.channels || 0,
            bitRate: parseInt(audioStream.bit_rate || '0'),
            format: metadata.format.format_name || 'unknown',
            size: parseInt(metadata.format.size || '0'),
            codec: audioStream.codec_name || 'unknown',
          });
        });
      });
    } finally {
      await this.cleanupFiles([inputFile]);
    }
  }

  /**
   * 分析音频
   */
  async analyzeAudio(audioBuffer: Buffer): Promise<AudioAnalysis> {
    const info = await this.getAudioInfo(audioBuffer);
    
    // 音量分析
    const volume = await this.analyzeVolume(audioBuffer);
    
    // 频谱分析
    const spectrum = await this.analyzeSpectrum(audioBuffer);
    
    // 质量分析
    const quality = await this.analyzeQuality(audioBuffer, info);
    
    // 语音检测
    const speech = await this.detectSpeech(audioBuffer);

    return {
      info,
      volume,
      spectrum,
      quality,
      speech,
    };
  }

  /**
   * 分析音量
   */
  private async analyzeVolume(audioBuffer: Buffer): Promise<{
    peak: number;
    rms: number;
    lufs: number;
  }> {
    const inputFile = path.join(this.tempDir, `volume_${uuidv4()}.wav`);

    try {
      await fs.writeFile(inputFile, audioBuffer);

      return new Promise((resolve, reject) => {
        ffmpeg(inputFile)
          .audioFilters('volumedetect')
          .format('null')
          .output('-')
          .on('stderr', (stderrLine) => {
            // 解析音量信息
            const peakMatch = stderrLine.match(/max_volume: ([-\d.]+) dB/);
            const rmsMatch = stderrLine.match(/mean_volume: ([-\d.]+) dB/);
            
            if (peakMatch && rmsMatch) {
              resolve({
                peak: parseFloat(peakMatch[1]),
                rms: parseFloat(rmsMatch[1]),
                lufs: parseFloat(rmsMatch[1]), // 简化处理
              });
            }
          })
          .on('error', reject)
          .run();
      });
    } catch (error) {
      // 返回默认值
      return { peak: -20, rms: -30, lufs: -23 };
    } finally {
      await this.cleanupFiles([inputFile]);
    }
  }

  /**
   * 分析频谱
   */
  private async analyzeSpectrum(audioBuffer: Buffer): Promise<{
    frequencies: number[];
    magnitudes: number[];
  }> {
    // 这里需要实现FFT分析
    // 暂时返回模拟数据
    const frequencies = Array.from({ length: 512 }, (_, i) => i * 44100 / 1024);
    const magnitudes = Array.from({ length: 512 }, () => Math.random());

    return { frequencies, magnitudes };
  }

  /**
   * 分析音频质量
   */
  private async analyzeQuality(audioBuffer: Buffer, info: AudioInfo): Promise<{
    snr?: number;
    thd?: number;
    score: number;
    issues: string[];
  }> {
    const issues: string[] = [];
    let score = 100;

    // 检查采样率
    if (info.sampleRate < 16000) {
      issues.push('采样率过低');
      score -= 20;
    }

    // 检查比特率
    if (info.bitRate < 64000) {
      issues.push('比特率过低');
      score -= 15;
    }

    // 检查时长
    if (info.duration < 0.5) {
      issues.push('音频过短');
      score -= 10;
    }

    return {
      score: Math.max(0, score),
      issues,
    };
  }

  /**
   * 检测语音
   */
  private async detectSpeech(audioBuffer: Buffer): Promise<{
    hasSpeech: boolean;
    speechRatio: number;
    silenceSegments: Array<{ start: number; end: number }>;
    speechSegments: Array<{ start: number; end: number }>;
  }> {
    const inputFile = path.join(this.tempDir, `speech_${uuidv4()}.wav`);

    try {
      await fs.writeFile(inputFile, audioBuffer);

      // 使用FFmpeg检测静音段
      const silenceSegments = await this.detectSilenceSegments(inputFile);
      
      // 计算语音段
      const info = await this.getAudioInfo(audioBuffer);
      const speechSegments = this.calculateSpeechSegments(silenceSegments, info.duration);
      
      // 计算语音比例
      const speechDuration = speechSegments.reduce((sum, seg) => sum + (seg.end - seg.start), 0);
      const speechRatio = speechDuration / info.duration;

      return {
        hasSpeech: speechRatio > 0.1,
        speechRatio,
        silenceSegments,
        speechSegments,
      };
    } finally {
      await this.cleanupFiles([inputFile]);
    }
  }

  /**
   * 检测静音段
   */
  private async detectSilenceSegments(inputFile: string): Promise<Array<{ start: number; end: number }>> {
    return new Promise((resolve, reject) => {
      const segments: Array<{ start: number; end: number }> = [];

      ffmpeg(inputFile)
        .audioFilters('silencedetect=noise=-30dB:duration=0.5')
        .format('null')
        .output('-')
        .on('stderr', (stderrLine) => {
          const startMatch = stderrLine.match(/silence_start: ([\d.]+)/);
          const endMatch = stderrLine.match(/silence_end: ([\d.]+)/);
          
          if (startMatch && endMatch) {
            segments.push({
              start: parseFloat(startMatch[1]),
              end: parseFloat(endMatch[1]),
            });
          }
        })
        .on('end', () => resolve(segments))
        .on('error', reject)
        .run();
    });
  }

  /**
   * 计算语音段
   */
  private calculateSpeechSegments(
    silenceSegments: Array<{ start: number; end: number }>,
    totalDuration: number,
  ): Array<{ start: number; end: number }> {
    const speechSegments: Array<{ start: number; end: number }> = [];
    let currentStart = 0;

    for (const silence of silenceSegments) {
      if (currentStart < silence.start) {
        speechSegments.push({
          start: currentStart,
          end: silence.start,
        });
      }
      currentStart = silence.end;
    }

    // 添加最后一段
    if (currentStart < totalDuration) {
      speechSegments.push({
        start: currentStart,
        end: totalDuration,
      });
    }

    return speechSegments;
  }

  /**
   * 分割音频
   */
  async segmentAudio(
    audioBuffer: Buffer,
    segments: Array<{ start: number; end: number }>,
  ): Promise<AudioSegment[]> {
    const inputFile = path.join(this.tempDir, `segment_input_${uuidv4()}.wav`);
    const results: AudioSegment[] = [];

    try {
      await fs.writeFile(inputFile, audioBuffer);

      for (let i = 0; i < segments.length; i++) {
        const segment = segments[i];
        const outputFile = path.join(this.tempDir, `segment_${i}_${uuidv4()}.wav`);

        await new Promise<void>((resolve, reject) => {
          ffmpeg(inputFile)
            .seekInput(segment.start)
            .duration(segment.end - segment.start)
            .output(outputFile)
            .on('end', resolve)
            .on('error', reject)
            .run();
        });

        const segmentBuffer = await fs.readFile(outputFile);
        
        results.push({
          id: uuidv4(),
          startTime: segment.start,
          endTime: segment.end,
          audioData: segmentBuffer,
          type: 'speech', // 简化处理
        });

        await this.cleanupFiles([outputFile]);
      }

      return results;
    } finally {
      await this.cleanupFiles([inputFile]);
    }
  }

  /**
   * 合并音频
   */
  async mergeAudio(audioBuffers: Buffer[]): Promise<Buffer> {
    if (audioBuffers.length === 0) {
      throw new BadRequestException('没有音频数据可合并');
    }

    if (audioBuffers.length === 1) {
      return audioBuffers[0];
    }

    const inputFiles: string[] = [];
    const outputFile = path.join(this.tempDir, `merged_${uuidv4()}.wav`);

    try {
      // 写入所有输入文件
      for (let i = 0; i < audioBuffers.length; i++) {
        const inputFile = path.join(this.tempDir, `merge_input_${i}_${uuidv4()}.wav`);
        await fs.writeFile(inputFile, audioBuffers[i]);
        inputFiles.push(inputFile);
      }

      // 合并音频
      await new Promise<void>((resolve, reject) => {
        let command = ffmpeg();
        
        inputFiles.forEach(file => {
          command = command.input(file);
        });

        command
          .complexFilter(`concat=n=${inputFiles.length}:v=0:a=1`)
          .output(outputFile)
          .on('end', resolve)
          .on('error', reject)
          .run();
      });

      return await fs.readFile(outputFile);
    } finally {
      await this.cleanupFiles([...inputFiles, outputFile]);
    }
  }

  /**
   * 清理临时文件
   */
  private async cleanupFiles(files: string[]): Promise<void> {
    for (const file of files) {
      try {
        if (await fs.pathExists(file)) {
          await fs.remove(file);
        }
      } catch (error) {
        console.warn(`清理文件失败: ${file}`, error);
      }
    }
  }

  /**
   * 获取支持的格式
   */
  getSupportedFormats(): {
    input: AudioFormat[];
    output: AudioFormat[];
  } {
    return {
      input: [AudioFormat.WAV, AudioFormat.MP3, AudioFormat.FLAC, AudioFormat.OGG, AudioFormat.AAC, AudioFormat.M4A],
      output: [AudioFormat.WAV, AudioFormat.MP3, AudioFormat.FLAC, AudioFormat.OGG],
    };
  }

  /**
   * 清理临时目录
   */
  async cleanupTempDir(): Promise<void> {
    try {
      const files = await fs.readdir(this.tempDir);
      const oldFiles = files.filter(file => {
        const filePath = path.join(this.tempDir, file);
        const stats = fs.statSync(filePath);
        const ageInHours = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60);
        return ageInHours > 1; // 删除1小时前的文件
      });

      for (const file of oldFiles) {
        await fs.remove(path.join(this.tempDir, file));
      }
    } catch (error) {
      console.warn('清理临时目录失败:', error);
    }
  }
}
