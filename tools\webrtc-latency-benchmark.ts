/**
 * WebRTC延迟基准测试工具
 * 验证30ms以下延迟目标的达成情况
 */
import { UltraLowLatencyWebRTCManager } from '../engine/src/network/UltraLowLatencyWebRTCManager';

export interface BenchmarkConfig {
  testDuration: number;        // 测试持续时间(秒)
  measurementInterval: number; // 测量间隔(毫秒)
  targetLatency: number;       // 目标延迟(毫秒)
  networkConditions: NetworkCondition[];
  testScenarios: TestScenario[];
}

export interface NetworkCondition {
  name: string;
  latency: number;    // 网络延迟(ms)
  bandwidth: number;  // 带宽(bps)
  packetLoss: number; // 丢包率(0-1)
  jitter: number;     // 抖动(ms)
}

export interface TestScenario {
  name: string;
  description: string;
  audioEnabled: boolean;
  videoEnabled: boolean;
  dataChannelEnabled: boolean;
  concurrentConnections: number;
  mediaQuality: 'low' | 'medium' | 'high' | 'ultra';
}

export interface BenchmarkResult {
  testId: string;
  timestamp: Date;
  config: BenchmarkConfig;
  scenarios: ScenarioResult[];
  summary: BenchmarkSummary;
}

export interface ScenarioResult {
  scenario: string;
  networkCondition: string;
  measurements: LatencyMeasurement[];
  statistics: LatencyStatistics;
  targetAchieved: boolean;
  issues: string[];
  recommendations: string[];
}

export interface LatencyStatistics {
  count: number;
  average: number;
  median: number;
  min: number;
  max: number;
  p95: number;
  p99: number;
  standardDeviation: number;
  successRate: number; // 达到目标延迟的比例
}

export interface BenchmarkSummary {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  overallSuccessRate: number;
  averageLatency: number;
  bestScenario: string;
  worstScenario: string;
  recommendations: string[];
}

export class WebRTCLatencyBenchmark {
  private config: BenchmarkConfig;
  private webrtcManager: UltraLowLatencyWebRTCManager;
  private results: BenchmarkResult;

  constructor(config: Partial<BenchmarkConfig> = {}) {
    this.config = {
      testDuration: 60,
      measurementInterval: 10,
      targetLatency: 30,
      networkConditions: this.getDefaultNetworkConditions(),
      testScenarios: this.getDefaultTestScenarios(),
      ...config
    };

    this.webrtcManager = new UltraLowLatencyWebRTCManager();
  }

  async runBenchmark(): Promise<BenchmarkResult> {
    console.log('🚀 开始WebRTC延迟基准测试');
    console.log(`目标延迟: ${this.config.targetLatency}ms`);
    console.log(`测试时长: ${this.config.testDuration}秒`);

    this.results = {
      testId: this.generateTestId(),
      timestamp: new Date(),
      config: this.config,
      scenarios: [],
      summary: {} as BenchmarkSummary
    };

    try {
      // 初始化WebRTC管理器
      await this.webrtcManager.initialize();

      // 运行所有测试场景
      for (const scenario of this.config.testScenarios) {
        for (const networkCondition of this.config.networkConditions) {
          console.log(`\n📋 测试场景: ${scenario.name} - ${networkCondition.name}`);
          
          const scenarioResult = await this.runScenario(scenario, networkCondition);
          this.results.scenarios.push(scenarioResult);
          
          // 输出即时结果
          this.printScenarioResult(scenarioResult);
          
          // 场景间休息
          await this.sleep(2000);
        }
      }

      // 生成总结
      this.results.summary = this.generateSummary();
      
      // 输出最终报告
      this.printFinalReport();

      return this.results;

    } finally {
      this.webrtcManager.close();
    }
  }

  private async runScenario(scenario: TestScenario, networkCondition: NetworkCondition): Promise<ScenarioResult> {
    // 配置网络条件
    await this.simulateNetworkCondition(networkCondition);
    
    // 配置媒体质量
    await this.configureMediaQuality(scenario);

    const measurements: LatencyMeasurement[] = [];
    const issues: string[] = [];
    
    const startTime = Date.now();
    const endTime = startTime + (this.config.testDuration * 1000);

    // 开始测量
    while (Date.now() < endTime) {
      try {
        const measurement = await this.measureLatency();
        measurements.push(measurement);

        // 检查异常情况
        if (measurement.total > this.config.targetLatency * 2) {
          issues.push(`严重延迟: ${measurement.total.toFixed(1)}ms at ${new Date().toISOString()}`);
        }

      } catch (error) {
        issues.push(`测量失败: ${error.message}`);
      }

      await this.sleep(this.config.measurementInterval);
    }

    // 计算统计信息
    const statistics = this.calculateStatistics(measurements);
    const targetAchieved = statistics.successRate >= 0.95; // 95%的测量需要达到目标

    // 生成建议
    const recommendations = this.generateRecommendations(measurements, statistics, issues);

    return {
      scenario: scenario.name,
      networkCondition: networkCondition.name,
      measurements,
      statistics,
      targetAchieved,
      issues,
      recommendations
    };
  }

  private async measureLatency(): Promise<LatencyMeasurement> {
    const startTime = performance.now();
    
    // 发送ping消息
    const pingData = new ArrayBuffer(8);
    const view = new Float64Array(pingData);
    view[0] = startTime;

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('延迟测量超时'));
      }, 1000);

      const handleResponse = (data: ArrayBuffer) => {
        const responseTime = performance.now();
        const sentTime = new Float64Array(data)[0];
        const latency = responseTime - sentTime;

        clearTimeout(timeout);
        resolve({
          timestamp: responseTime,
          total: latency,
          breakdown: {
            capture: 5,    // 估算值
            encoding: 8,   // 估算值
            network: latency / 2,
            decoding: 5,   // 估算值
            rendering: 2,  // 估算值
            buffering: 0   // 零缓冲目标
          },
          targetAchieved: latency <= this.config.targetLatency
        });
      };

      // 发送ping并等待响应
      this.webrtcManager.sendData(pingData);
      this.webrtcManager.once('dataReceived', handleResponse);
    });
  }

  private calculateStatistics(measurements: LatencyMeasurement[]): LatencyStatistics {
    if (measurements.length === 0) {
      return {
        count: 0,
        average: 0,
        median: 0,
        min: 0,
        max: 0,
        p95: 0,
        p99: 0,
        standardDeviation: 0,
        successRate: 0
      };
    }

    const latencies = measurements.map(m => m.total).sort((a, b) => a - b);
    const count = latencies.length;
    const sum = latencies.reduce((acc, val) => acc + val, 0);
    const average = sum / count;
    const median = latencies[Math.floor(count / 2)];
    const min = latencies[0];
    const max = latencies[count - 1];
    const p95 = latencies[Math.floor(count * 0.95)];
    const p99 = latencies[Math.floor(count * 0.99)];
    
    // 计算标准差
    const variance = latencies.reduce((acc, val) => acc + Math.pow(val - average, 2), 0) / count;
    const standardDeviation = Math.sqrt(variance);
    
    // 计算成功率
    const successCount = measurements.filter(m => m.targetAchieved).length;
    const successRate = successCount / count;

    return {
      count,
      average,
      median,
      min,
      max,
      p95,
      p99,
      standardDeviation,
      successRate
    };
  }

  private generateRecommendations(
    measurements: LatencyMeasurement[],
    statistics: LatencyStatistics,
    issues: string[]
  ): string[] {
    const recommendations: string[] = [];

    // 基于统计数据的建议
    if (statistics.average > this.config.targetLatency) {
      recommendations.push(`平均延迟${statistics.average.toFixed(1)}ms超过目标，建议启用硬件加速`);
    }

    if (statistics.p95 > this.config.targetLatency * 1.5) {
      recommendations.push(`95%分位延迟过高，建议优化网络路径或降低媒体质量`);
    }

    if (statistics.standardDeviation > 10) {
      recommendations.push(`延迟波动较大(σ=${statistics.standardDeviation.toFixed(1)}ms)，建议启用自适应码率控制`);
    }

    if (statistics.successRate < 0.8) {
      recommendations.push(`成功率${(statistics.successRate * 100).toFixed(1)}%过低，建议检查网络配置`);
    }

    // 基于问题的建议
    if (issues.length > measurements.length * 0.1) {
      recommendations.push('测量失败率过高，建议检查WebRTC连接稳定性');
    }

    return recommendations;
  }

  private generateSummary(): BenchmarkSummary {
    const totalTests = this.results.scenarios.length;
    const passedTests = this.results.scenarios.filter(s => s.targetAchieved).length;
    const failedTests = totalTests - passedTests;
    const overallSuccessRate = passedTests / totalTests;

    // 计算平均延迟
    const allMeasurements = this.results.scenarios.flatMap(s => s.measurements);
    const averageLatency = allMeasurements.reduce((sum, m) => sum + m.total, 0) / allMeasurements.length;

    // 找出最佳和最差场景
    const scenarioAverages = this.results.scenarios.map(s => ({
      name: `${s.scenario} - ${s.networkCondition}`,
      average: s.statistics.average
    }));
    
    scenarioAverages.sort((a, b) => a.average - b.average);
    const bestScenario = scenarioAverages[0]?.name || 'N/A';
    const worstScenario = scenarioAverages[scenarioAverages.length - 1]?.name || 'N/A';

    // 生成总体建议
    const recommendations: string[] = [];
    if (overallSuccessRate < 0.9) {
      recommendations.push('整体成功率偏低，建议全面优化WebRTC配置');
    }
    if (averageLatency > this.config.targetLatency) {
      recommendations.push('平均延迟超标，建议启用所有可用的延迟优化措施');
    }

    return {
      totalTests,
      passedTests,
      failedTests,
      overallSuccessRate,
      averageLatency,
      bestScenario,
      worstScenario,
      recommendations
    };
  }

  private printScenarioResult(result: ScenarioResult): void {
    const status = result.targetAchieved ? '✅ 通过' : '❌ 失败';
    console.log(`${status} - 平均延迟: ${result.statistics.average.toFixed(1)}ms, 成功率: ${(result.statistics.successRate * 100).toFixed(1)}%`);
    
    if (result.issues.length > 0) {
      console.log(`⚠️  问题: ${result.issues.length}个`);
    }
  }

  private printFinalReport(): void {
    const summary = this.results.summary;
    
    console.log('\n📊 基准测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${summary.totalTests}`);
    console.log(`通过测试: ${summary.passedTests}`);
    console.log(`失败测试: ${summary.failedTests}`);
    console.log(`整体成功率: ${(summary.overallSuccessRate * 100).toFixed(1)}%`);
    console.log(`平均延迟: ${summary.averageLatency.toFixed(1)}ms`);
    console.log(`最佳场景: ${summary.bestScenario}`);
    console.log(`最差场景: ${summary.worstScenario}`);
    
    if (summary.recommendations.length > 0) {
      console.log('\n💡 优化建议:');
      summary.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }

    // 详细场景结果
    console.log('\n📋 详细结果:');
    this.results.scenarios.forEach(scenario => {
      const status = scenario.targetAchieved ? '✅' : '❌';
      console.log(`${status} ${scenario.scenario} - ${scenario.networkCondition}: ${scenario.statistics.average.toFixed(1)}ms (P95: ${scenario.statistics.p95.toFixed(1)}ms)`);
    });
  }

  private getDefaultNetworkConditions(): NetworkCondition[] {
    return [
      {
        name: '理想网络',
        latency: 5,
        bandwidth: 10000000,
        packetLoss: 0,
        jitter: 1
      },
      {
        name: '良好网络',
        latency: 20,
        bandwidth: 5000000,
        packetLoss: 0.001,
        jitter: 3
      },
      {
        name: '一般网络',
        latency: 50,
        bandwidth: 2000000,
        packetLoss: 0.01,
        jitter: 10
      },
      {
        name: '较差网络',
        latency: 100,
        bandwidth: 1000000,
        packetLoss: 0.05,
        jitter: 20
      }
    ];
  }

  private getDefaultTestScenarios(): TestScenario[] {
    return [
      {
        name: '仅数据通道',
        description: '只使用数据通道传输',
        audioEnabled: false,
        videoEnabled: false,
        dataChannelEnabled: true,
        concurrentConnections: 1,
        mediaQuality: 'low'
      },
      {
        name: '音频+数据',
        description: '音频和数据通道',
        audioEnabled: true,
        videoEnabled: false,
        dataChannelEnabled: true,
        concurrentConnections: 1,
        mediaQuality: 'medium'
      },
      {
        name: '完整媒体',
        description: '音频、视频和数据通道',
        audioEnabled: true,
        videoEnabled: true,
        dataChannelEnabled: true,
        concurrentConnections: 1,
        mediaQuality: 'high'
      },
      {
        name: '多连接',
        description: '多个并发连接',
        audioEnabled: true,
        videoEnabled: true,
        dataChannelEnabled: true,
        concurrentConnections: 4,
        mediaQuality: 'medium'
      }
    ];
  }

  private async simulateNetworkCondition(condition: NetworkCondition): Promise<void> {
    // 模拟网络条件的实现
    console.log(`🌐 配置网络条件: ${condition.name} (延迟: ${condition.latency}ms, 带宽: ${condition.bandwidth/1000000}Mbps)`);
  }

  private async configureMediaQuality(scenario: TestScenario): Promise<void> {
    // 配置媒体质量的实现
    console.log(`🎥 配置媒体: 音频=${scenario.audioEnabled}, 视频=${scenario.videoEnabled}, 质量=${scenario.mediaQuality}`);
  }

  private generateTestId(): string {
    return `webrtc-benchmark-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 运行基准测试的主函数
export async function runWebRTCLatencyBenchmark(): Promise<void> {
  const benchmark = new WebRTCLatencyBenchmark({
    testDuration: 30,
    targetLatency: 30,
    measurementInterval: 100
  });

  try {
    const results = await benchmark.runBenchmark();
    
    // 保存结果到文件
    const fs = require('fs');
    const resultsPath = `./benchmark-results-${results.testId}.json`;
    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
    console.log(`\n💾 结果已保存到: ${resultsPath}`);
    
  } catch (error) {
    console.error('❌ 基准测试失败:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runWebRTCLatencyBenchmark();
}
