/**
 * 数字人路径测试
 */
import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import * as THREE from 'three';
import { AvatarPath } from '../../src/navigation/path/AvatarPath';
import { PathPoint } from '../../src/navigation/path/PathPoint';
import { LoopMode, InterpolationType } from '../../src/navigation/types';

describe('AvatarPath', () => {
  let path: AvatarPath;
  let testPoints: PathPoint[];

  beforeEach(() => {
    // 创建测试路径点
    testPoints = [
      new PathPoint(new THREE.Vector3(0, 0, 0), {
        waitTime: 1,
        speed: 1.5,
        animation: 'idle'
      }),
      new PathPoint(new THREE.Vector3(10, 0, 0), {
        waitTime: 0,
        speed: 2.0,
        animation: 'walk'
      }),
      new PathPoint(new THREE.Vector3(10, 0, 10), {
        waitTime: 2,
        speed: 1.0,
        animation: 'idle'
      })
    ];

    // 创建测试路径
    path = new AvatarPath({
      name: '测试路径',
      avatarId: 'test_avatar',
      loopMode: LoopMode.NONE,
      interpolation: InterpolationType.LINEAR
    });
  });

  afterEach(() => {
    // 清理
    path = null as any;
    testPoints = [];
  });

  describe('基本功能', () => {
    test('应该能够创建路径', () => {
      expect(path).toBeDefined();
      expect(path.name).toBe('测试路径');
      expect(path.avatarId).toBe('test_avatar');
      expect(path.loopMode).toBe(LoopMode.NONE);
      expect(path.interpolation).toBe(InterpolationType.LINEAR);
    });

    test('应该能够添加路径点', () => {
      testPoints.forEach(point => {
        path.addPoint(point);
      });

      expect(path.points.length).toBe(3);
      expect(path.getPoint(0)).toBe(testPoints[0]);
      expect(path.getPoint(1)).toBe(testPoints[1]);
      expect(path.getPoint(2)).toBe(testPoints[2]);
    });

    test('应该能够移除路径点', () => {
      testPoints.forEach(point => path.addPoint(point));

      const removed = path.removePoint(1);
      expect(removed).toBe(testPoints[1]);
      expect(path.points.length).toBe(2);
      expect(path.getPoint(1)).toBe(testPoints[2]);
    });

    test('应该能够插入路径点', () => {
      path.addPoint(testPoints[0]);
      path.addPoint(testPoints[2]);

      path.insertPoint(1, testPoints[1]);

      expect(path.points.length).toBe(3);
      expect(path.getPoint(1)).toBe(testPoints[1]);
    });

    test('应该能够清空路径点', () => {
      testPoints.forEach(point => path.addPoint(point));
      expect(path.points.length).toBe(3);

      path.clear();
      expect(path.points.length).toBe(0);
    });
  });

  describe('路径计算', () => {
    beforeEach(() => {
      testPoints.forEach(point => path.addPoint(point));
    });

    test('应该能够计算路径长度', () => {
      const length = path.calculateLength();
      // 第一段: (0,0,0) -> (10,0,0) = 10
      // 第二段: (10,0,0) -> (10,0,10) = 10
      // 总长度: 20
      expect(length).toBeCloseTo(20, 2);
    });

    test('应该能够计算路径持续时间', () => {
      const duration = path.calculateDuration();
      // 第一个点等待时间: 1秒
      // 第一段移动时间: 10米 / 1.5米/秒 = 6.67秒
      // 第二段移动时间: 10米 / 2.0米/秒 = 5秒
      // 第三个点等待时间: 2秒
      // 总时间: 1 + 6.67 + 5 + 2 = 14.67秒
      expect(duration).toBeCloseTo(14.67, 1);
    });

    test('应该能够获取指定时间的位置', () => {
      const result = path.getPositionAtTime(0);
      expect(result).toBeDefined();
      expect(result!.position.x).toBeCloseTo(0);
      expect(result!.position.y).toBeCloseTo(0);
      expect(result!.position.z).toBeCloseTo(0);

      // 测试中间位置
      const midResult = path.getPositionAtTime(path.totalDuration / 2);
      expect(midResult).toBeDefined();
      expect(midResult!.position.x).toBeGreaterThan(0);
    });

    test('应该能够获取指定进度的位置', () => {
      const result = path.getPositionAtProgress(0.5);
      expect(result).toBeDefined();
      expect(result!.position.x).toBeGreaterThan(0);
    });
  });

  describe('路径验证', () => {
    test('空路径应该无效', () => {
      const validation = path.validate();
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('路径至少需要2个路径点');
    });

    test('单点路径应该无效', () => {
      path.addPoint(testPoints[0]);
      const validation = path.validate();
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('路径至少需要2个路径点');
    });

    test('有效路径应该通过验证', () => {
      path.addPoint(testPoints[0]);
      path.addPoint(testPoints[1]);
      const validation = path.validate();
      expect(validation.valid).toBe(true);
      expect(validation.errors.length).toBe(0);
    });

    test('应该验证路径点的有效性', () => {
      const invalidPoint = new PathPoint(new THREE.Vector3(0, 0, 0), {
        speed: -1, // 无效速度
        waitTime: -1, // 无效等待时间
        animation: '' // 空动画名称
      });

      path.addPoint(testPoints[0]);
      path.addPoint(invalidPoint);

      const validation = path.validate();
      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('循环模式', () => {
    beforeEach(() => {
      testPoints.forEach(point => path.addPoint(point));
    });

    test('LOOP模式应该返回起点', () => {
      path.loopMode = LoopMode.LOOP;
      
      // 在路径结束后应该回到起点
      const endTime = path.totalDuration + 1;
      const result = path.getPositionAtTime(endTime);
      
      expect(result).toBeDefined();
      // 应该接近起点位置
      expect(result!.position.distanceTo(testPoints[0].position)).toBeLessThan(1);
    });

    test('PINGPONG模式应该反向移动', () => {
      path.loopMode = LoopMode.PINGPONG;
      
      // 测试往返逻辑
      const halfDuration = path.totalDuration / 2;
      const result1 = path.getPositionAtTime(halfDuration);
      const result2 = path.getPositionAtTime(path.totalDuration + halfDuration);
      
      expect(result1).toBeDefined();
      expect(result2).toBeDefined();
      
      // 往返后应该在相似位置
      expect(result1!.position.distanceTo(result2!.position)).toBeLessThan(2);
    });
  });

  describe('插值模式', () => {
    beforeEach(() => {
      testPoints.forEach(point => path.addPoint(point));
    });

    test('LINEAR插值应该产生直线路径', () => {
      path.interpolation = InterpolationType.LINEAR;
      
      const result1 = path.getPositionAtProgress(0.25);
      const result2 = path.getPositionAtProgress(0.5);
      const result3 = path.getPositionAtProgress(0.75);
      
      expect(result1).toBeDefined();
      expect(result2).toBeDefined();
      expect(result3).toBeDefined();
      
      // 线性插值应该保持直线特性
      expect(result1!.position.y).toBeCloseTo(0);
      expect(result2!.position.y).toBeCloseTo(0);
      expect(result3!.position.y).toBeCloseTo(0);
    });

    test('SMOOTH插值应该产生平滑路径', () => {
      path.interpolation = InterpolationType.SMOOTH;
      
      const result = path.getPositionAtProgress(0.5);
      expect(result).toBeDefined();
      
      // 平滑插值的结果应该与线性插值不同
      path.interpolation = InterpolationType.LINEAR;
      const linearResult = path.getPositionAtProgress(0.5);
      
      expect(result!.position.distanceTo(linearResult!.position)).toBeGreaterThan(0);
    });
  });

  describe('事件系统', () => {
    test('应该能够监听路径事件', (done) => {
      let eventReceived = false;
      
      path.on('pointAdded', (data) => {
        expect(data.point).toBeDefined();
        expect(data.index).toBe(0);
        eventReceived = true;
        done();
      });

      path.addPoint(testPoints[0]);
      
      // 确保事件被触发
      setTimeout(() => {
        if (!eventReceived) {
          done(new Error('事件未被触发'));
        }
      }, 100);
    });

    test('应该能够移除事件监听', () => {
      let eventCount = 0;
      
      const handler = () => {
        eventCount++;
      };

      path.on('pointAdded', handler);
      path.addPoint(testPoints[0]);
      expect(eventCount).toBe(1);

      path.off('pointAdded', handler);
      path.addPoint(testPoints[1]);
      expect(eventCount).toBe(1); // 应该没有增加
    });
  });

  describe('序列化', () => {
    beforeEach(() => {
      testPoints.forEach(point => path.addPoint(point));
    });

    test('应该能够序列化为JSON', () => {
      const json = path.toJSON();
      
      expect(json).toBeDefined();
      expect(json.name).toBe(path.name);
      expect(json.avatarId).toBe(path.avatarId);
      expect(json.points).toHaveLength(3);
      expect(json.loopMode).toBe(path.loopMode);
      expect(json.interpolation).toBe(path.interpolation);
    });

    test('应该能够从JSON反序列化', () => {
      const json = path.toJSON();
      const newPath = AvatarPath.fromJSON(json);
      
      expect(newPath.name).toBe(path.name);
      expect(newPath.avatarId).toBe(path.avatarId);
      expect(newPath.points.length).toBe(path.points.length);
      expect(newPath.loopMode).toBe(path.loopMode);
      expect(newPath.interpolation).toBe(path.interpolation);
      
      // 验证路径点
      for (let i = 0; i < path.points.length; i++) {
        const originalPoint = path.getPoint(i);
        const newPoint = newPath.getPoint(i);
        
        expect(newPoint.position.distanceTo(originalPoint.position)).toBeCloseTo(0);
        expect(newPoint.speed).toBe(originalPoint.speed);
        expect(newPoint.waitTime).toBe(originalPoint.waitTime);
        expect(newPoint.animation).toBe(originalPoint.animation);
      }
    });
  });

  describe('性能测试', () => {
    test('应该能够处理大量路径点', () => {
      const startTime = performance.now();
      
      // 添加1000个路径点
      for (let i = 0; i < 1000; i++) {
        const point = new PathPoint(new THREE.Vector3(i, 0, 0), {
          speed: 1.0,
          waitTime: 0,
          animation: 'walk'
        });
        path.addPoint(point);
      }
      
      const addTime = performance.now() - startTime;
      expect(addTime).toBeLessThan(1000); // 应该在1秒内完成
      
      // 测试计算性能
      const calcStartTime = performance.now();
      const length = path.calculateLength();
      const duration = path.calculateDuration();
      const calcTime = performance.now() - calcStartTime;
      
      expect(calcTime).toBeLessThan(100); // 计算应该在100ms内完成
      expect(length).toBeGreaterThan(0);
      expect(duration).toBeGreaterThan(0);
    });

    test('位置查询应该高效', () => {
      // 添加100个路径点
      for (let i = 0; i < 100; i++) {
        const point = new PathPoint(new THREE.Vector3(i, 0, 0), {
          speed: 1.0,
          waitTime: 0.1,
          animation: 'walk'
        });
        path.addPoint(point);
      }

      const startTime = performance.now();
      
      // 执行1000次位置查询
      for (let i = 0; i < 1000; i++) {
        const progress = Math.random();
        path.getPositionAtProgress(progress);
      }
      
      const queryTime = performance.now() - startTime;
      expect(queryTime).toBeLessThan(500); // 应该在500ms内完成
    });
  });
});
