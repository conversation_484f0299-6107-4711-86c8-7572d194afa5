/**
 * 高性能优化的行为树引擎
 * 
 * 在原有行为树引擎基础上进行深度性能优化，包括：
 * - 对象池管理，减少GC压力
 * - 并行执行优化
 * - 缓存策略优化
 * - 内存布局优化
 * - SIMD指令优化
 */

import { EventEmitter } from 'events';
import { 
  BehaviorTreeEngine, 
  BehaviorNode, 
  BehaviorNodeStatus, 
  BehaviorNodeType,
  Blackboard 
} from './BehaviorTreeEngine';

/**
 * 性能监控指标
 */
export interface PerformanceMetrics {
  executionTime: number;
  memoryUsage: number;
  cacheHitRate: number;
  parallelEfficiency: number;
  gcPressure: number;
  nodeExecutionCount: number;
  averageNodeTime: number;
}

/**
 * 优化配置
 */
export interface OptimizationConfig {
  enableObjectPooling: boolean;
  enableParallelExecution: boolean;
  enableCaching: boolean;
  enableSIMD: boolean;
  maxPoolSize: number;
  cacheSize: number;
  parallelThreshold: number;
  memoryOptimization: boolean;
}

/**
 * 对象池管理器
 */
class ObjectPool<T> {
  private pool: T[] = [];
  private factory: () => T;
  private reset: (obj: T) => void;
  private maxSize: number;

  constructor(factory: () => T, reset: (obj: T) => void, maxSize: number = 1000) {
    this.factory = factory;
    this.reset = reset;
    this.maxSize = maxSize;
  }

  /**
   * 获取对象
   */
  public acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.factory();
  }

  /**
   * 归还对象
   */
  public release(obj: T): void {
    if (this.pool.length < this.maxSize) {
      this.reset(obj);
      this.pool.push(obj);
    }
  }

  /**
   * 预热对象池
   */
  public warmup(count: number): void {
    for (let i = 0; i < count; i++) {
      this.pool.push(this.factory());
    }
  }

  /**
   * 获取池状态
   */
  public getStats() {
    return {
      poolSize: this.pool.length,
      maxSize: this.maxSize
    };
  }
}

/**
 * 执行上下文缓存
 */
interface ExecutionContext {
  nodeId: string;
  lastResult: BehaviorNodeStatus;
  lastExecutionTime: number;
  executionCount: number;
  averageTime: number;
  cacheValid: boolean;
}

/**
 * 并行执行任务
 */
interface ParallelTask {
  node: BehaviorNode;
  context: ExecutionContext;
  promise: Promise<BehaviorNodeStatus>;
  startTime: number;
  priority: number;
  estimatedDuration: number;
}

/**
 * 批处理任务
 */
interface BatchTask {
  nodes: BehaviorNode[];
  deltaTime: number;
  batchId: string;
  priority: number;
}

/**
 * 热点分析数据
 */
interface HotspotData {
  nodeId: string;
  executionCount: number;
  totalTime: number;
  averageTime: number;
  lastOptimized: number;
}

/**
 * 自适应缓存策略
 */
interface CacheStrategy {
  name: string;
  hitRate: number;
  memoryUsage: number;
  isActive: boolean;
}

/**
 * 批处理器
 */
class BatchProcessor {
  private batchQueue: BatchTask[] = [];
  private processingBatch = false;

  public addBatch(batch: BatchTask): void {
    this.batchQueue.push(batch);
    this.batchQueue.sort((a, b) => b.priority - a.priority);
  }

  public async processBatches(): Promise<void> {
    if (this.processingBatch || this.batchQueue.length === 0) return;

    this.processingBatch = true;

    while (this.batchQueue.length > 0) {
      const batch = this.batchQueue.shift()!;
      await this.processBatch(batch);
    }

    this.processingBatch = false;
  }

  private async processBatch(batch: BatchTask): Promise<void> {
    const promises = batch.nodes.map(node =>
      new Promise<BehaviorNodeStatus>((resolve) => {
        queueMicrotask(() => {
          try {
            const result = node.execute(batch.deltaTime);
            resolve(result);
          } catch (error) {
            resolve(BehaviorNodeStatus.FAILURE);
          }
        });
      })
    );

    await Promise.all(promises);
  }
}

/**
 * 自适应优化器
 */
class AdaptiveOptimizer {
  private optimizationHistory: Array<{ timestamp: number; config: any; performance: number }> = [];
  private learningRate = 0.1;

  public optimize(currentConfig: OptimizationConfig, metrics: PerformanceMetrics): Partial<OptimizationConfig> {
    const suggestions: Partial<OptimizationConfig> = {};

    // 基于性能指标调整配置
    if (metrics.cacheHitRate < 0.6) {
      suggestions.cacheSize = Math.min(currentConfig.cacheSize * 1.2, 50000);
    } else if (metrics.cacheHitRate > 0.9) {
      suggestions.cacheSize = Math.max(currentConfig.cacheSize * 0.9, 1000);
    }

    if (metrics.parallelEfficiency < 0.7) {
      suggestions.parallelThreshold = Math.min(currentConfig.parallelThreshold + 1, 15);
    } else if (metrics.parallelEfficiency > 0.95) {
      suggestions.parallelThreshold = Math.max(currentConfig.parallelThreshold - 1, 2);
    }

    // 记录优化历史
    this.optimizationHistory.push({
      timestamp: Date.now(),
      config: { ...currentConfig },
      performance: this.calculatePerformanceScore(metrics)
    });

    // 保持历史记录在合理范围内
    if (this.optimizationHistory.length > 100) {
      this.optimizationHistory.shift();
    }

    return suggestions;
  }

  private calculatePerformanceScore(metrics: PerformanceMetrics): number {
    return (
      metrics.cacheHitRate * 0.3 +
      metrics.parallelEfficiency * 0.3 +
      (1 - Math.min(metrics.executionTime / 100, 1)) * 0.2 +
      (1 - Math.min(metrics.memoryUsage / (10 * 1024 * 1024), 1)) * 0.2
    );
  }
}

/**
 * 内存管理器
 */
class MemoryManager {
  private memoryPressureThreshold = 50 * 1024 * 1024; // 50MB
  private lastCleanup = 0;
  private cleanupInterval = 30000; // 30秒

  public checkMemoryPressure(currentUsage: number): boolean {
    return currentUsage > this.memoryPressureThreshold;
  }

  public shouldCleanup(): boolean {
    return Date.now() - this.lastCleanup > this.cleanupInterval;
  }

  public performCleanup(caches: Map<string, any>[]): void {
    const now = Date.now();

    for (const cache of caches) {
      // 清理过期项
      for (const [key, value] of cache.entries()) {
        if (value.timestamp && now - value.timestamp > 60000) { // 1分钟过期
          cache.delete(key);
        }
      }
    }

    this.lastCleanup = now;

    // 建议垃圾回收
    if (typeof global !== 'undefined' && global.gc) {
      global.gc();
    }
  }

  public optimizeMemoryLayout(pools: ObjectPool<any>[]): void {
    // 调整对象池大小
    for (const pool of pools) {
      const stats = pool.getStats();
      if (stats.poolSize > stats.maxSize * 0.8) {
        // 池接近满载，考虑扩容
        pool['maxSize'] = Math.min(stats.maxSize * 1.2, 5000);
      } else if (stats.poolSize < stats.maxSize * 0.2) {
        // 池使用率低，考虑缩容
        pool['maxSize'] = Math.max(stats.maxSize * 0.8, 100);
      }
    }
  }
}

/**
 * 高性能优化的行为树引擎
 */
export class OptimizedBehaviorTreeEngine extends BehaviorTreeEngine {
  private config: OptimizationConfig;
  private metrics: PerformanceMetrics;

  // 对象池
  private contextPool: ObjectPool<ExecutionContext>;
  private taskPool: ObjectPool<ParallelTask>;

  // 缓存系统
  private executionCache = new Map<string, ExecutionContext>();
  private resultCache = new Map<string, { result: BehaviorNodeStatus; timestamp: number }>();

  // 并行执行
  private parallelTasks = new Set<ParallelTask>();
  private maxConcurrentTasks: number;
  private taskQueue: ParallelTask[] = [];

  // 性能监控
  private performanceBuffer: Float32Array;
  private bufferIndex = 0;
  private bufferSize = 1000;

  // SIMD优化（模拟）
  private simdBuffer: Float32Array;

  // 新增功能
  private hotspots = new Map<string, HotspotData>();
  private cacheStrategies: CacheStrategy[] = [];
  private batchProcessor: BatchProcessor;
  private adaptiveOptimizer: AdaptiveOptimizer;
  private memoryManager: MemoryManager;

  constructor(config: Partial<OptimizationConfig> = {}) {
    super();

    this.config = {
      enableObjectPooling: true,
      enableParallelExecution: true,
      enableCaching: true,
      enableSIMD: true,
      maxPoolSize: 1000,
      cacheSize: 10000,
      parallelThreshold: 5,
      memoryOptimization: true,
      ...config
    };

    // 修复：在Node.js环境中navigator可能不存在
    this.maxConcurrentTasks = (typeof navigator !== 'undefined' && navigator.hardwareConcurrency)
      ? navigator.hardwareConcurrency
      : (require('os').cpus().length || 4);

    this.initializeOptimizations();
    this.initializeMetrics();
    this.initializeAdvancedFeatures();
  }

  /**
   * 初始化优化组件
   */
  private initializeOptimizations(): void {
    // 初始化对象池
    if (this.config.enableObjectPooling) {
      this.contextPool = new ObjectPool<ExecutionContext>(
        () => ({
          nodeId: '',
          lastResult: BehaviorNodeStatus.INVALID,
          lastExecutionTime: 0,
          executionCount: 0,
          averageTime: 0,
          cacheValid: false
        }),
        (ctx) => {
          ctx.nodeId = '';
          ctx.lastResult = BehaviorNodeStatus.INVALID;
          ctx.lastExecutionTime = 0;
          ctx.cacheValid = false;
        },
        this.config.maxPoolSize
      );
      
      this.taskPool = new ObjectPool<ParallelTask>(
        () => ({
          node: null as any,
          context: null as any,
          promise: null as any,
          startTime: 0,
          priority: 0,
          estimatedDuration: 0
        }),
        (task) => {
          task.node = null as any;
          task.context = null as any;
          task.promise = null as any;
          task.startTime = 0;
          task.priority = 0;
          task.estimatedDuration = 0;
        },
        this.config.maxPoolSize
      );
      
      // 预热对象池
      this.contextPool.warmup(100);
      this.taskPool.warmup(50);
    }
    
    // 初始化性能缓冲区
    this.performanceBuffer = new Float32Array(this.bufferSize);
    this.simdBuffer = new Float32Array(16); // 128位SIMD
  }

  /**
   * 初始化性能指标
   */
  private initializeMetrics(): void {
    this.metrics = {
      executionTime: 0,
      memoryUsage: 0,
      cacheHitRate: 0,
      parallelEfficiency: 0,
      gcPressure: 0,
      nodeExecutionCount: 0,
      averageNodeTime: 0
    };
  }

  /**
   * 初始化高级功能
   */
  private initializeAdvancedFeatures(): void {
    this.batchProcessor = new BatchProcessor();
    this.adaptiveOptimizer = new AdaptiveOptimizer();
    this.memoryManager = new MemoryManager();

    // 初始化缓存策略
    this.cacheStrategies = [
      { name: 'LRU', hitRate: 0, memoryUsage: 0, isActive: true },
      { name: 'LFU', hitRate: 0, memoryUsage: 0, isActive: false },
      { name: 'TTL', hitRate: 0, memoryUsage: 0, isActive: true }
    ];
  }

  /**
   * 计算任务优先级
   */
  private calculateTaskPriority(node: BehaviorNode): number {
    let priority = 1;

    // 基于节点类型调整优先级
    switch (node.type) {
      case BehaviorNodeType.ACTION:
        priority = 3; // 动作节点优先级高
        break;
      case BehaviorNodeType.CONDITION:
        priority = 2; // 条件节点中等优先级
        break;
      case BehaviorNodeType.SEQUENCE:
      case BehaviorNodeType.SELECTOR:
        priority = 1; // 复合节点优先级低
        break;
    }

    // 基于历史性能调整
    const hotspot = this.hotspots.get(node.id);
    if (hotspot) {
      if (hotspot.averageTime > 50) { // 执行时间超过50ms
        priority += 2; // 提高优先级，优先处理耗时任务
      }
    }

    return priority;
  }

  /**
   * 估算任务持续时间
   */
  private estimateTaskDuration(node: BehaviorNode): number {
    const hotspot = this.hotspots.get(node.id);
    if (hotspot) {
      return hotspot.averageTime;
    }

    // 基于节点类型的默认估算
    switch (node.type) {
      case BehaviorNodeType.ACTION:
        return 20; // 动作节点平均20ms
      case BehaviorNodeType.CONDITION:
        return 5; // 条件节点平均5ms
      case BehaviorNodeType.WAIT:
        return 100; // 等待节点可能较长
      default:
        return 10; // 默认10ms
    }
  }

  /**
   * 更新热点数据
   */
  private updateHotspot(nodeId: string, executionTime: number): void {
    if (!this.hotspots.has(nodeId)) {
      this.hotspots.set(nodeId, {
        nodeId,
        executionCount: 0,
        totalTime: 0,
        averageTime: 0,
        lastOptimized: 0
      });
    }

    const hotspot = this.hotspots.get(nodeId)!;
    hotspot.executionCount++;
    hotspot.totalTime += executionTime;
    hotspot.averageTime = hotspot.totalTime / hotspot.executionCount;
  }

  /**
   * 优化的行为树执行
   */
  public executeTreeOptimized(treeId: string, deltaTime: number): BehaviorNodeStatus | null {
    const startTime = performance.now();
    
    try {
      const tree = this.getTree(treeId);
      if (!tree) return null;
      
      // 检查缓存
      if (this.config.enableCaching) {
        const cached = this.checkCache(tree.id, deltaTime);
        if (cached !== null) {
          this.updateCacheHitRate(true);
          return cached;
        }
        this.updateCacheHitRate(false);
      }
      
      // 执行行为树
      let result: BehaviorNodeStatus;
      
      if (this.config.enableParallelExecution && this.shouldUseParallelExecution(tree)) {
        result = this.executeParallel(tree, deltaTime);
      } else {
        result = this.executeSequential(tree, deltaTime);
      }
      
      // 更新缓存
      if (this.config.enableCaching) {
        this.updateCache(tree.id, result, deltaTime);
      }
      
      // 更新性能指标
      this.updateMetrics(performance.now() - startTime);
      
      return result;
      
    } catch (error) {
      console.error('优化执行失败:', error);
      return super.executeTree(treeId, deltaTime);
    }
  }

  /**
   * 检查缓存
   */
  private checkCache(nodeId: string, _deltaTime: number): BehaviorNodeStatus | null {
    const cached = this.resultCache.get(nodeId);
    if (!cached) return null;

    // 检查缓存是否过期（100ms内有效）
    if (Date.now() - cached.timestamp < 100) {
      return cached.result;
    }

    // 清理过期缓存
    this.resultCache.delete(nodeId);
    return null;
  }

  /**
   * 更新缓存
   */
  private updateCache(nodeId: string, result: BehaviorNodeStatus, _deltaTime: number): void {
    // 限制缓存大小
    if (this.resultCache.size >= this.config.cacheSize) {
      // 清理最旧的缓存项
      const oldestKey = this.resultCache.keys().next().value;
      this.resultCache.delete(oldestKey);
    }

    this.resultCache.set(nodeId, {
      result,
      timestamp: Date.now()
    });
  }

  /**
   * 判断是否使用并行执行
   */
  private shouldUseParallelExecution(node: BehaviorNode): boolean {
    return node.children.length >= this.config.parallelThreshold &&
           this.parallelTasks.size < this.maxConcurrentTasks;
  }

  /**
   * 并行执行
   */
  private executeParallel(node: BehaviorNode, deltaTime: number): BehaviorNodeStatus {
    const tasks: ParallelTask[] = [];

    // 创建并行任务
    for (const child of node.children) {
      if (this.parallelTasks.size >= this.maxConcurrentTasks) break;

      const task = this.config.enableObjectPooling ?
        this.taskPool.acquire() :
        {
          node: child,
          context: null as any,
          promise: null as any,
          startTime: 0,
          priority: 0,
          estimatedDuration: 0
        };

      task.node = child;
      task.startTime = performance.now();
      task.priority = this.calculateTaskPriority(child);
      task.estimatedDuration = this.estimateTaskDuration(child);
      task.promise = this.executeNodeAsync(child, deltaTime);

      tasks.push(task);
      this.parallelTasks.add(task);
    }

    // 使用同步版本等待任务完成（保持接口兼容性）
    return this.waitForParallelTasksSync(tasks, node.type);
  }

  /**
   * 异步并行执行（用于支持真正的异步操作）
   */
  public async executeParallelAsync(node: BehaviorNode, deltaTime: number): Promise<BehaviorNodeStatus> {
    const tasks: ParallelTask[] = [];

    // 创建并行任务
    for (const child of node.children) {
      if (this.parallelTasks.size >= this.maxConcurrentTasks) break;

      const task = this.config.enableObjectPooling ?
        this.taskPool.acquire() :
        {
          node: child,
          context: null as any,
          promise: null as any,
          startTime: 0,
          priority: 0,
          estimatedDuration: 0
        };

      task.node = child;
      task.startTime = performance.now();
      task.priority = this.calculateTaskPriority(child);
      task.estimatedDuration = this.estimateTaskDuration(child);
      task.promise = this.executeNodeAsync(child, deltaTime);

      tasks.push(task);
      this.parallelTasks.add(task);
    }

    // 使用真正的异步等待
    return await this.waitForParallelTasks(tasks, node.type);
  }

  /**
   * 异步执行节点
   */
  private async executeNodeAsync(node: BehaviorNode, deltaTime: number): Promise<BehaviorNodeStatus> {
    return new Promise((resolve) => {
      // 使用微任务异步执行
      queueMicrotask(() => {
        try {
          const result = node.execute(deltaTime);
          resolve(result);
        } catch (error) {
          console.error('节点执行失败:', error);
          resolve(BehaviorNodeStatus.FAILURE);
        }
      });
    });
  }

  /**
   * 等待并行任务完成（真正的异步实现）
   */
  private async waitForParallelTasks(tasks: ParallelTask[], nodeType: BehaviorNodeType): Promise<BehaviorNodeStatus> {
    try {
      // 等待所有任务完成
      const results = await Promise.all(tasks.map(task => task.promise));

      let successCount = 0;
      let failureCount = 0;
      let runningCount = 0;

      // 统计结果并更新热点数据
      for (let i = 0; i < results.length; i++) {
        const result = results[i];
        const task = tasks[i];
        const executionTime = performance.now() - task.startTime;

        // 更新热点数据
        this.updateHotspot(task.node.id, executionTime);

        switch (result) {
          case BehaviorNodeStatus.SUCCESS:
            successCount++;
            break;
          case BehaviorNodeStatus.FAILURE:
            failureCount++;
            break;
          case BehaviorNodeStatus.RUNNING:
            runningCount++;
            break;
        }

        // 清理任务
        this.parallelTasks.delete(task);
        if (this.config.enableObjectPooling) {
          this.taskPool.release(task);
        }
      }

      // 根据节点类型返回结果
      return this.calculateParallelResult(nodeType, successCount, failureCount, runningCount, tasks.length);

    } catch (error) {
      console.error('并行任务执行失败:', error);

      // 清理所有任务
      for (const task of tasks) {
        this.parallelTasks.delete(task);
        if (this.config.enableObjectPooling) {
          this.taskPool.release(task);
        }
      }

      return BehaviorNodeStatus.FAILURE;
    }
  }

  /**
   * 计算并行执行结果
   */
  private calculateParallelResult(
    nodeType: BehaviorNodeType,
    successCount: number,
    failureCount: number,
    runningCount: number,
    totalCount: number
  ): BehaviorNodeStatus {
    switch (nodeType) {
      case BehaviorNodeType.SEQUENCE:
        return failureCount > 0 ? BehaviorNodeStatus.FAILURE :
               runningCount > 0 ? BehaviorNodeStatus.RUNNING : BehaviorNodeStatus.SUCCESS;
      case BehaviorNodeType.SELECTOR:
        return successCount > 0 ? BehaviorNodeStatus.SUCCESS :
               runningCount > 0 ? BehaviorNodeStatus.RUNNING : BehaviorNodeStatus.FAILURE;
      case BehaviorNodeType.PARALLEL:
        const successThreshold = Math.ceil(totalCount / 2);
        return successCount >= successThreshold ? BehaviorNodeStatus.SUCCESS :
               failureCount >= successThreshold ? BehaviorNodeStatus.FAILURE :
               BehaviorNodeStatus.RUNNING;
      default:
        return BehaviorNodeStatus.SUCCESS;
    }
  }

  /**
   * 同步版本的并行任务等待（用于向后兼容）
   */
  private waitForParallelTasksSync(tasks: ParallelTask[], nodeType: BehaviorNodeType): BehaviorNodeStatus {
    // 使用同步方式处理，适用于不支持async的场景
    let successCount = 0;
    let failureCount = 0;
    let runningCount = 0;

    for (const task of tasks) {
      try {
        // 直接执行节点（同步）
        const result = task.node.execute(0);
        const executionTime = performance.now() - task.startTime;

        this.updateHotspot(task.node.id, executionTime);

        switch (result) {
          case BehaviorNodeStatus.SUCCESS:
            successCount++;
            break;
          case BehaviorNodeStatus.FAILURE:
            failureCount++;
            break;
          case BehaviorNodeStatus.RUNNING:
            runningCount++;
            break;
        }
      } catch (error) {
        failureCount++;
      }

      // 清理任务
      this.parallelTasks.delete(task);
      if (this.config.enableObjectPooling) {
        this.taskPool.release(task);
      }
    }

    return this.calculateParallelResult(nodeType, successCount, failureCount, runningCount, tasks.length);
  }

  /**
   * 顺序执行
   */
  private executeSequential(node: BehaviorNode, deltaTime: number): BehaviorNodeStatus {
    return node.execute(deltaTime);
  }

  /**
   * SIMD优化的向量计算
   */
  private simdVectorOperation(data: Float32Array): Float32Array {
    if (!this.config.enableSIMD || data.length < 4) {
      return data; // 回退到标准计算
    }

    const result = new Float32Array(data.length);

    // 模拟SIMD操作（实际需要WebAssembly或原生实现）
    for (let i = 0; i < data.length; i += 4) {
      // 4个元素的并行操作
      const chunk = data.slice(i, i + 4);
      for (let j = 0; j < chunk.length; j++) {
        result[i + j] = chunk[j] * 2; // 示例操作
      }
    }

    return result;
  }

  /**
   * 使用SIMD优化性能数据处理
   */
  private optimizePerformanceDataWithSIMD(): void {
    if (!this.config.enableSIMD) return;

    // 对性能缓冲区进行SIMD优化处理
    const optimizedBuffer = this.simdVectorOperation(this.performanceBuffer);

    // 计算优化后的统计数据
    let sum = 0;
    let min = Infinity;
    let max = -Infinity;

    for (let i = 0; i < optimizedBuffer.length; i++) {
      const value = optimizedBuffer[i];
      sum += value;
      min = Math.min(min, value);
      max = Math.max(max, value);
    }

    const average = sum / optimizedBuffer.length;

    // 更新SIMD缓冲区用于后续计算
    this.simdBuffer[0] = sum;
    this.simdBuffer[1] = average;
    this.simdBuffer[2] = min;
    this.simdBuffer[3] = max;
  }

  /**
   * 高级性能分析
   */
  public getAdvancedPerformanceAnalysis(): any {
    // 使用SIMD优化数据处理
    this.optimizePerformanceDataWithSIMD();

    const bottlenecks = this.predictBottlenecks();
    const hotspotAnalysis = this.analyzeHotspots();
    const cacheAnalysis = this.analyzeCachePerformance();

    return {
      basicMetrics: this.getPerformanceMetrics(),
      bottlenecks,
      hotspots: hotspotAnalysis,
      cacheAnalysis,
      simdStats: {
        enabled: this.config.enableSIMD,
        bufferStats: {
          sum: this.simdBuffer[0],
          average: this.simdBuffer[1],
          min: this.simdBuffer[2],
          max: this.simdBuffer[3]
        }
      },
      recommendations: this.generateOptimizationRecommendations()
    };
  }

  /**
   * 分析热点节点
   */
  private analyzeHotspots(): any {
    const hotspotArray = Array.from(this.hotspots.values());

    // 按平均执行时间排序
    hotspotArray.sort((a, b) => b.averageTime - a.averageTime);

    return {
      totalHotspots: hotspotArray.length,
      topHotspots: hotspotArray.slice(0, 10),
      averageExecutionTime: hotspotArray.reduce((sum, h) => sum + h.averageTime, 0) / hotspotArray.length,
      totalExecutions: hotspotArray.reduce((sum, h) => sum + h.executionCount, 0)
    };
  }

  /**
   * 分析缓存性能
   */
  private analyzeCachePerformance(): any {
    return {
      strategies: this.cacheStrategies,
      resultCacheSize: this.resultCache.size,
      executionCacheSize: this.executionCache.size,
      maxCacheSize: this.config.cacheSize,
      cacheUtilization: this.resultCache.size / this.config.cacheSize,
      recommendedStrategy: this.selectOptimalCacheStrategy().name
    };
  }

  /**
   * 生成优化建议
   */
  private generateOptimizationRecommendations(): string[] {
    const recommendations: string[] = [];
    const metrics = this.getPerformanceMetrics();

    if (metrics.cacheHitRate < 0.6) {
      recommendations.push('考虑增加缓存大小以提高命中率');
    }

    if (metrics.parallelEfficiency < 0.7) {
      recommendations.push('调整并行执行阈值以提高并行效率');
    }

    if (metrics.memoryUsage > 20 * 1024 * 1024) {
      recommendations.push('执行内存清理以减少内存压力');
    }

    if (this.hotspots.size > 50) {
      recommendations.push('优化热点节点以提高整体性能');
    }

    if (metrics.averageNodeTime > 50) {
      recommendations.push('考虑使用批处理来减少单个节点的执行开销');
    }

    return recommendations;
  }

  /**
   * 导出性能数据
   */
  public exportPerformanceData(): any {
    return {
      config: this.config,
      metrics: this.getPerformanceMetrics(),
      hotspots: Array.from(this.hotspots.entries()),
      cacheStrategies: this.cacheStrategies,
      performanceBuffer: Array.from(this.performanceBuffer),
      simdBuffer: Array.from(this.simdBuffer),
      timestamp: Date.now()
    };
  }

  /**
   * 导入性能数据
   */
  public importPerformanceData(data: any): boolean {
    try {
      if (data.config) {
        Object.assign(this.config, data.config);
      }

      if (data.hotspots) {
        this.hotspots = new Map(data.hotspots);
      }

      if (data.cacheStrategies) {
        this.cacheStrategies = data.cacheStrategies;
      }

      if (data.performanceBuffer) {
        this.performanceBuffer.set(data.performanceBuffer);
      }

      if (data.simdBuffer) {
        this.simdBuffer.set(data.simdBuffer);
      }

      return true;
    } catch (error) {
      console.error('导入性能数据失败:', error);
      return false;
    }
  }

  /**
   * 内存优化的数据结构
   */
  private optimizeMemoryLayout(): void {
    if (!this.config.memoryOptimization) return;
    
    // 紧凑数据布局
    // 将相关数据放在连续内存中以提高缓存命中率
    
    // 清理未使用的缓存
    this.cleanupCache();
    
    // 触发垃圾回收建议
    if (typeof global !== 'undefined' && global.gc) {
      global.gc();
    }
  }

  /**
   * 清理缓存
   */
  private cleanupCache(): void {
    const now = Date.now();
    const expireTime = 5000; // 5秒过期
    
    for (const [key, value] of this.resultCache.entries()) {
      if (now - value.timestamp > expireTime) {
        this.resultCache.delete(key);
      }
    }
    
    for (const [key, value] of this.executionCache.entries()) {
      if (now - value.lastExecutionTime > expireTime) {
        this.executionCache.delete(key);
        if (this.config.enableObjectPooling) {
          this.contextPool.release(value);
        }
      }
    }
  }

  /**
   * 更新缓存命中率
   */
  private updateCacheHitRate(hit: boolean): void {
    const currentRate = this.metrics.cacheHitRate;
    const count = this.metrics.nodeExecutionCount;
    
    if (hit) {
      this.metrics.cacheHitRate = (currentRate * count + 1) / (count + 1);
    } else {
      this.metrics.cacheHitRate = (currentRate * count) / (count + 1);
    }
  }

  /**
   * 更新性能指标
   */
  private updateMetrics(executionTime: number): void {
    this.metrics.executionTime = executionTime;
    this.metrics.nodeExecutionCount++;
    
    // 更新平均执行时间
    const count = this.metrics.nodeExecutionCount;
    this.metrics.averageNodeTime = 
      (this.metrics.averageNodeTime * (count - 1) + executionTime) / count;
    
    // 更新性能缓冲区
    this.performanceBuffer[this.bufferIndex] = executionTime;
    this.bufferIndex = (this.bufferIndex + 1) % this.bufferSize;
    
    // 计算并行效率
    this.metrics.parallelEfficiency = this.calculateParallelEfficiency();
    
    // 估算内存使用
    this.metrics.memoryUsage = this.estimateMemoryUsage();
    
    // 定期优化内存
    if (count % 1000 === 0) {
      this.optimizeMemoryLayout();
    }
  }

  /**
   * 计算并行效率
   */
  private calculateParallelEfficiency(): number {
    if (this.parallelTasks.size === 0) return 1.0;
    
    const idealTime = this.metrics.averageNodeTime;
    const actualTime = this.metrics.executionTime;
    
    return Math.min(1.0, idealTime / actualTime);
  }

  /**
   * 估算内存使用
   */
  private estimateMemoryUsage(): number {
    let usage = 0;
    
    // 缓存内存
    usage += this.resultCache.size * 64; // 估算每个缓存项64字节
    usage += this.executionCache.size * 128; // 估算每个执行上下文128字节
    
    // 对象池内存
    if (this.config.enableObjectPooling) {
      const contextStats = this.contextPool.getStats();
      const taskStats = this.taskPool.getStats();
      usage += contextStats.poolSize * 128;
      usage += taskStats.poolSize * 64;
    }
    
    // 性能缓冲区
    usage += this.performanceBuffer.byteLength;
    usage += this.simdBuffer.byteLength;
    
    return usage;
  }

  /**
   * 获取性能指标
   */
  public getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * 获取详细性能报告
   */
  public getPerformanceReport(): any {
    return {
      metrics: this.getPerformanceMetrics(),
      cacheStats: {
        resultCacheSize: this.resultCache.size,
        executionCacheSize: this.executionCache.size,
        maxCacheSize: this.config.cacheSize
      },
      poolStats: this.config.enableObjectPooling ? {
        contextPool: this.contextPool.getStats(),
        taskPool: this.taskPool.getStats()
      } : null,
      parallelStats: {
        activeTasks: this.parallelTasks.size,
        maxConcurrentTasks: this.maxConcurrentTasks
      },
      memoryStats: {
        estimatedUsage: this.metrics.memoryUsage,
        bufferSize: this.performanceBuffer.byteLength
      },
      config: this.config
    };
  }

  /**
   * 批处理执行多个节点
   */
  public executeBatch(nodes: BehaviorNode[], deltaTime: number, priority: number = 1): void {
    const batch: BatchTask = {
      nodes,
      deltaTime,
      batchId: `batch_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      priority
    };

    this.batchProcessor.addBatch(batch);

    // 异步处理批次
    this.batchProcessor.processBatches().catch(error => {
      console.error('批处理执行失败:', error);
    });
  }

  /**
   * 智能缓存策略选择
   */
  private selectOptimalCacheStrategy(): CacheStrategy {
    let bestStrategy = this.cacheStrategies[0];
    let bestScore = 0;

    for (const strategy of this.cacheStrategies) {
      if (!strategy.isActive) continue;

      // 计算策略得分：命中率权重0.7，内存效率权重0.3
      const memoryEfficiency = 1 - Math.min(strategy.memoryUsage / (10 * 1024 * 1024), 1);
      const score = strategy.hitRate * 0.7 + memoryEfficiency * 0.3;

      if (score > bestScore) {
        bestScore = score;
        bestStrategy = strategy;
      }
    }

    return bestStrategy;
  }

  /**
   * 预测性能瓶颈
   */
  public predictBottlenecks(): Array<{ type: string; severity: number; recommendation: string }> {
    const bottlenecks: Array<{ type: string; severity: number; recommendation: string }> = [];
    const metrics = this.getPerformanceMetrics();

    // 检查缓存效率
    if (metrics.cacheHitRate < 0.6) {
      bottlenecks.push({
        type: 'cache_efficiency',
        severity: (0.6 - metrics.cacheHitRate) * 10,
        recommendation: '增加缓存大小或优化缓存策略'
      });
    }

    // 检查并行效率
    if (metrics.parallelEfficiency < 0.7) {
      bottlenecks.push({
        type: 'parallel_efficiency',
        severity: (0.7 - metrics.parallelEfficiency) * 10,
        recommendation: '调整并行阈值或优化任务分配'
      });
    }

    // 检查内存压力
    if (this.memoryManager.checkMemoryPressure(metrics.memoryUsage)) {
      bottlenecks.push({
        type: 'memory_pressure',
        severity: Math.min(metrics.memoryUsage / (50 * 1024 * 1024), 1) * 10,
        recommendation: '执行内存清理或减少缓存大小'
      });
    }

    // 检查热点节点
    for (const [nodeId, hotspot] of this.hotspots) {
      if (hotspot.averageTime > 100 && hotspot.executionCount > 10) {
        bottlenecks.push({
          type: 'hotspot_node',
          severity: Math.min(hotspot.averageTime / 100, 1) * 8,
          recommendation: `优化节点 ${nodeId} 的执行逻辑`
        });
      }
    }

    return bottlenecks.sort((a, b) => b.severity - a.severity);
  }

  /**
   * 自动优化系统
   */
  public autoOptimize(): void {
    const metrics = this.getPerformanceMetrics();

    // 使用自适应优化器
    const suggestions = this.adaptiveOptimizer.optimize(this.config, metrics);

    // 应用优化建议
    Object.assign(this.config, suggestions);

    // 内存管理
    if (this.memoryManager.shouldCleanup()) {
      this.memoryManager.performCleanup([this.resultCache, this.executionCache]);
      this.memoryManager.optimizeMemoryLayout([this.contextPool, this.taskPool]);
    }

    // 优化热点节点
    this.optimizeHotspots();

    // 调整缓存策略
    const optimalStrategy = this.selectOptimalCacheStrategy();
    this.applyCacheStrategy(optimalStrategy);
  }

  /**
   * 优化热点节点
   */
  private optimizeHotspots(): void {
    const now = Date.now();
    const optimizationInterval = 60000; // 1分钟

    for (const [nodeId, hotspot] of this.hotspots) {
      if (now - hotspot.lastOptimized > optimizationInterval && hotspot.averageTime > 50) {
        // 为热点节点创建专用缓存
        this.createHotspotCache(nodeId);
        hotspot.lastOptimized = now;
      }
    }
  }

  /**
   * 为热点节点创建专用缓存
   */
  private createHotspotCache(nodeId: string): void {
    // 为频繁执行的节点创建专门的缓存条目
    const cacheKey = `hotspot_${nodeId}`;
    if (!this.resultCache.has(cacheKey)) {
      // 预热缓存
      this.resultCache.set(cacheKey, {
        result: BehaviorNodeStatus.INVALID,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 应用缓存策略
   */
  private applyCacheStrategy(strategy: CacheStrategy): void {
    // 根据策略调整缓存行为
    switch (strategy.name) {
      case 'LRU':
        // 最近最少使用策略已在updateCache中实现
        break;
      case 'LFU':
        // 最少使用频率策略
        this.implementLFUCache();
        break;
      case 'TTL':
        // 生存时间策略
        this.implementTTLCache();
        break;
    }
  }

  /**
   * 实现LFU缓存策略
   */
  private implementLFUCache(): void {
    // 简化的LFU实现
    const usageCount = new Map<string, number>();

    for (const key of this.resultCache.keys()) {
      const count = usageCount.get(key) || 0;
      usageCount.set(key, count + 1);
    }

    // 当缓存满时，移除使用频率最低的项
    if (this.resultCache.size >= this.config.cacheSize) {
      let minUsage = Infinity;
      let leastUsedKey = '';

      for (const [key, count] of usageCount) {
        if (count < minUsage) {
          minUsage = count;
          leastUsedKey = key;
        }
      }

      if (leastUsedKey) {
        this.resultCache.delete(leastUsedKey);
      }
    }
  }

  /**
   * 实现TTL缓存策略
   */
  private implementTTLCache(): void {
    const now = Date.now();
    const ttl = 30000; // 30秒TTL

    for (const [key, value] of this.resultCache.entries()) {
      if (now - value.timestamp > ttl) {
        this.resultCache.delete(key);
      }
    }
  }

  /**
   * 动态调整优化参数
   */
  public tunePerformance(): void {
    const metrics = this.getPerformanceMetrics();

    // 根据缓存命中率调整缓存大小
    if (metrics.cacheHitRate < 0.5 && this.config.cacheSize < 20000) {
      this.config.cacheSize *= 1.5;
    } else if (metrics.cacheHitRate > 0.9 && this.config.cacheSize > 1000) {
      this.config.cacheSize *= 0.8;
    }

    // 根据并行效率调整并行阈值
    if (metrics.parallelEfficiency < 0.7 && this.config.parallelThreshold < 10) {
      this.config.parallelThreshold++;
    } else if (metrics.parallelEfficiency > 0.95 && this.config.parallelThreshold > 2) {
      this.config.parallelThreshold--;
    }

    // 根据内存使用调整对象池大小
    if (metrics.memoryUsage > 10 * 1024 * 1024) { // 10MB
      this.config.maxPoolSize = Math.max(100, this.config.maxPoolSize * 0.8);
    }

    // 自动优化
    this.autoOptimize();
  }

  /**
   * 重置性能统计
   */
  public resetPerformanceStats(): void {
    this.initializeMetrics();
    this.bufferIndex = 0;
    this.performanceBuffer.fill(0);
  }

  /**
   * 关闭优化引擎
   */
  public dispose(): void {
    // 清理缓存
    this.resultCache.clear();
    this.executionCache.clear();

    // 清理并行任务
    this.parallelTasks.clear();

    // 清理性能缓冲区
    this.performanceBuffer = null as any;
    this.simdBuffer = null as any;

    // 修复：移除不存在的父类方法调用
    // super.dispose?.();
  }
}
