/**
 * 创建虚拟化身DTO
 */
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsArray, IsBoolean } from 'class-validator';

export class CreateAvatarDto {
  @ApiProperty({ description: '用户ID', required: false })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty({ description: '虚拟化身名称', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: '虚拟化身描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '创建来源', enum: ['web', 'mobile', 'api'], default: 'web' })
  @IsOptional()
  @IsEnum(['web', 'mobile', 'api'])
  source?: 'web' | 'mobile' | 'api' = 'web';

  @ApiProperty({ description: '标签列表', type: [String], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({ description: '是否公开', default: false })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean = false;
}
