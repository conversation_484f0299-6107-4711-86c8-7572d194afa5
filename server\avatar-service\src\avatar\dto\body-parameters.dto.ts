/**
 * 身体参数DTO
 */
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, Min, Max } from 'class-validator';

export class BodyParametersDto {
  @ApiProperty({ description: '性别', enum: ['male', 'female'] })
  @IsEnum(['male', 'female'])
  gender: 'male' | 'female';

  @ApiProperty({ description: '身高(cm)', minimum: 100, maximum: 250 })
  @IsNumber()
  @Min(100)
  @Max(250)
  height: number;

  @ApiProperty({ description: '体重(kg)', minimum: 30, maximum: 200 })
  @IsNumber()
  @Min(30)
  @Max(200)
  weight: number;

  @ApiProperty({ description: '体型(-2到2)', minimum: -2, maximum: 2 })
  @IsNumber()
  @Min(-2)
  @Max(2)
  build: number;

  @ApiProperty({ description: '肌肉量(0到1)', minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  muscle: number;

  @ApiProperty({ description: '肤色(0到1)', minimum: 0, maximum: 1 })
  @IsNumber()
  @Min(0)
  @Max(1)
  skinTone: number;
}
