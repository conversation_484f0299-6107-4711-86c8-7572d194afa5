/**
 * AILayoutGenerator.tsx
 * 
 * AI布局生成器组件
 */

import React, { useState, useCallback, useMemo } from 'react';
import {
  Modal,
  Form,
  Select,
  Card,
  Button,
  Space,
  Row,
  Col,
  Slider,
  Switch,
  Radio,
  Checkbox,
  Input,
  Divider,
  Typography,
  Tag,
  Spin,
  Empty,
  message,
  Tooltip,
  Badge
} from 'antd';
import {
  AppstoreOutlined,
  LayoutOutlined,
  MobileOutlined,
  TabletOutlined,
  DesktopOutlined,
  ThunderboltOutlined,
  EyeOutlined,
  DownloadOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { aiDesignAssistant } from '../../services/AIDesignAssistant';
import './AILayoutGenerator.module.css';

const { Title, Text } = Typography;
const { TextArea } = Input;

/**
 * 布局类型配置
 */
const LAYOUT_TYPES = [
  {
    value: 'grid',
    label: '网格布局',
    icon: <AppstoreOutlined />,
    description: '适用于展示多个同类型内容项',
    preview: '/images/layouts/grid-preview.png'
  },
  {
    value: 'flex',
    label: '弹性布局',
    icon: <LayoutOutlined />,
    description: '灵活的响应式布局方案',
    preview: '/images/layouts/flex-preview.png'
  },
  {
    value: 'masonry',
    label: '瀑布流',
    icon: <AppstoreOutlined />,
    description: '适用于不同高度的内容展示',
    preview: '/images/layouts/masonry-preview.png'
  },
  {
    value: 'dashboard',
    label: '仪表板',
    icon: <DesktopOutlined />,
    description: '数据可视化和监控界面',
    preview: '/images/layouts/dashboard-preview.png'
  },
  {
    value: 'form',
    label: '表单布局',
    icon: <LayoutOutlined />,
    description: '用户输入和数据收集界面',
    preview: '/images/layouts/form-preview.png'
  },
  {
    value: 'landing',
    label: '着陆页',
    icon: <DesktopOutlined />,
    description: '营销和产品展示页面',
    preview: '/images/layouts/landing-preview.png'
  }
];

/**
 * 主题样式配置
 */
const THEME_STYLES = [
  { value: 'modern', label: '现代风格', color: '#1890ff' },
  { value: 'classic', label: '经典风格', color: '#722ed1' },
  { value: 'minimal', label: '极简风格', color: '#52c41a' },
  { value: 'colorful', label: '多彩风格', color: '#fa541c' }
];

/**
 * 间距选项
 */
const SPACING_OPTIONS = [
  { value: 'tight', label: '紧凑', description: '适合信息密集的界面' },
  { value: 'normal', label: '标准', description: '平衡的视觉效果' },
  { value: 'loose', label: '宽松', description: '更好的可读性和呼吸感' }
];

/**
 * 对齐方式
 */
const ALIGNMENT_OPTIONS = [
  { value: 'left', label: '左对齐' },
  { value: 'center', label: '居中对齐' },
  { value: 'right', label: '右对齐' },
  { value: 'justify', label: '两端对齐' }
];

/**
 * 生成的布局接口
 */
interface GeneratedLayout {
  id: string;
  name: string;
  description: string;
  components: any[];
  styles: Record<string, any>;
  preview: string;
  confidence: number;
}

/**
 * AI布局生成器属性
 */
export interface AILayoutGeneratorProps {
  /** 是否显示 */
  visible: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 布局应用回调 */
  onApplyLayout?: (layout: GeneratedLayout) => void;
  /** 可用组件列表 */
  availableComponents?: string[];
}

/**
 * AI布局生成器组件
 */
export const AILayoutGenerator: React.FC<AILayoutGeneratorProps> = ({
  visible,
  onClose,
  onApplyLayout,
  availableComponents = ['Button', 'Input', 'Card', 'Image', 'Text']
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [generatedLayouts, setGeneratedLayouts] = useState<GeneratedLayout[]>([]);
  const [selectedLayout, setSelectedLayout] = useState<GeneratedLayout | null>(null);
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

  // 生成布局
  const generateLayouts = useCallback(async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const options = {
        type: values.layoutType,
        components: values.components || availableComponents,
        constraints: {
          width: values.width,
          height: values.height,
          columns: values.columns,
          rows: values.rows,
          responsive: values.responsive
        },
        style: {
          theme: values.theme,
          spacing: values.spacing,
          alignment: values.alignment
        }
      };

      const layouts = await aiDesignAssistant.generateLayout(options);
      setGeneratedLayouts(layouts);
      
      if (layouts.length > 0) {
        setSelectedLayout(layouts[0]);
      }
    } catch (error) {
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('布局生成失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, [form, availableComponents]);

  // 应用布局
  const handleApplyLayout = useCallback(() => {
    if (selectedLayout && onApplyLayout) {
      onApplyLayout(selectedLayout);
      message.success(`已应用布局: ${selectedLayout.name}`);
      onClose();
    }
  }, [selectedLayout, onApplyLayout, onClose]);

  // 预览布局
  const handlePreviewLayout = useCallback((layout: GeneratedLayout) => {
    setSelectedLayout(layout);
  }, []);

  // 重新生成
  const handleRegenerate = useCallback(() => {
    generateLayouts();
  }, [generateLayouts]);

  // 渲染布局类型选择
  const renderLayoutTypeSelector = useCallback(() => (
    <Form.Item
      name="layoutType"
      label="布局类型"
      rules={[{ required: true, message: '请选择布局类型' }]}
    >
      <Radio.Group className="layout-type-selector">
        <Row gutter={[16, 16]}>
          {LAYOUT_TYPES.map(type => (
            <Col span={8} key={type.value}>
              <Radio.Button
                value={type.value}
                className="layout-type-option"
              >
                <div className="layout-type-content">
                  <div className="layout-type-icon">{type.icon}</div>
                  <div className="layout-type-label">{type.label}</div>
                  <div className="layout-type-description">{type.description}</div>
                </div>
              </Radio.Button>
            </Col>
          ))}
        </Row>
      </Radio.Group>
    </Form.Item>
  ), []);

  // 渲染约束条件
  const renderConstraints = useCallback(() => (
    <Card title="布局约束" size="small">
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item name="width" label="宽度">
            <Slider
              min={300}
              max={1920}
              marks={{ 300: '300px', 1200: '1200px', 1920: '1920px' }}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item name="height" label="高度">
            <Slider
              min={200}
              max={1080}
              marks={{ 200: '200px', 600: '600px', 1080: '1080px' }}
            />
          </Form.Item>
        </Col>
      </Row>
      
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item name="columns" label="列数">
            <Slider min={1} max={12} marks={{ 1: '1', 6: '6', 12: '12' }} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item name="rows" label="行数">
            <Slider min={1} max={10} marks={{ 1: '1', 5: '5', 10: '10' }} />
          </Form.Item>
        </Col>
      </Row>
      
      <Form.Item name="responsive" valuePropName="checked">
        <Switch checkedChildren="响应式" unCheckedChildren="固定" />
      </Form.Item>
    </Card>
  ), []);

  // 渲染样式配置
  const renderStyleConfig = useCallback(() => (
    <Card title="样式配置" size="small">
      <Form.Item name="theme" label="主题风格">
        <Select>
          {THEME_STYLES.map(theme => (
            <Select.Option key={theme.value} value={theme.value}>
              <Space>
                <Badge color={theme.color} />
                {theme.label}
              </Space>
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      
      <Form.Item name="spacing" label="间距">
        <Radio.Group>
          {SPACING_OPTIONS.map(option => (
            <Radio key={option.value} value={option.value}>
              <Tooltip title={option.description}>
                {option.label}
              </Tooltip>
            </Radio>
          ))}
        </Radio.Group>
      </Form.Item>
      
      <Form.Item name="alignment" label="对齐方式">
        <Radio.Group>
          {ALIGNMENT_OPTIONS.map(option => (
            <Radio key={option.value} value={option.value}>
              {option.label}
            </Radio>
          ))}
        </Radio.Group>
      </Form.Item>
    </Card>
  ), []);

  // 渲染组件选择
  const renderComponentSelector = useCallback(() => (
    <Form.Item name="components" label="包含组件">
      <Checkbox.Group>
        <Row>
          {availableComponents.map(component => (
            <Col span={8} key={component}>
              <Checkbox value={component}>{component}</Checkbox>
            </Col>
          ))}
        </Row>
      </Checkbox.Group>
    </Form.Item>
  ), [availableComponents]);

  // 渲染生成的布局
  const renderGeneratedLayouts = useCallback(() => {
    if (loading) {
      return (
        <div className="loading-container">
          <Spin size="large" />
          <p>AI正在生成布局方案...</p>
        </div>
      );
    }

    if (generatedLayouts.length === 0) {
      return (
        <Empty
          description="暂无生成的布局"
          image={<LayoutOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
        />
      );
    }

    return (
      <div className="generated-layouts">
        <Row gutter={[16, 16]}>
          {generatedLayouts.map(layout => (
            <Col span={8} key={layout.id}>
              <Card
                hoverable
                className={`layout-card ${selectedLayout?.id === layout.id ? 'selected' : ''}`}
                cover={
                  <div className="layout-preview">
                    <img src={layout.preview} alt={layout.name} />
                    <div className="layout-overlay">
                      <Space>
                        <Button
                          type="primary"
                          size="small"
                          icon={<EyeOutlined />}
                          onClick={() => handlePreviewLayout(layout)}
                        >
                          预览
                        </Button>
                      </Space>
                    </div>
                  </div>
                }
                actions={[
                  <Tooltip title="置信度">
                    <Space>
                      <ThunderboltOutlined />
                      {(layout.confidence * 100).toFixed(0)}%
                    </Space>
                  </Tooltip>
                ]}
                onClick={() => handlePreviewLayout(layout)}
              >
                <Card.Meta
                  title={layout.name}
                  description={layout.description}
                />
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    );
  }, [loading, generatedLayouts, selectedLayout, handlePreviewLayout]);

  // 渲染预览区域
  const renderPreviewArea = useCallback(() => {
    if (!selectedLayout) {
      return (
        <Empty
          description="请选择一个布局进行预览"
          image={<LayoutOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
        />
      );
    }

    return (
      <div className="layout-preview-area">
        <div className="preview-header">
          <Space>
            <Title level={5}>{selectedLayout.name}</Title>
            <Tag color="blue">置信度: {(selectedLayout.confidence * 100).toFixed(0)}%</Tag>
          </Space>
          
          <Space>
            <Radio.Group
              value={previewMode}
              onChange={(e) => setPreviewMode(e.target.value)}
              size="small"
            >
              <Radio.Button value="desktop">
                <DesktopOutlined /> 桌面
              </Radio.Button>
              <Radio.Button value="tablet">
                <TabletOutlined /> 平板
              </Radio.Button>
              <Radio.Button value="mobile">
                <MobileOutlined /> 手机
              </Radio.Button>
            </Radio.Group>
          </Space>
        </div>
        
        <div className={`preview-container ${previewMode}`}>
          <div className="preview-frame">
            <img src={selectedLayout.preview} alt={selectedLayout.name} />
          </div>
        </div>
        
        <div className="preview-description">
          <Text>{selectedLayout.description}</Text>
        </div>
      </div>
    );
  }, [selectedLayout, previewMode]);

  return (
    <Modal
      title={
        <Space>
          <ThunderboltOutlined />
          <span>AI布局生成器</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={
        <Space>
          <Button onClick={onClose}>取消</Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRegenerate}
            loading={loading}
          >
            重新生成
          </Button>
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            onClick={handleApplyLayout}
            disabled={!selectedLayout}
          >
            应用布局
          </Button>
        </Space>
      }
      className="ai-layout-generator"
    >
      <Row gutter={24}>
        {/* 配置区域 */}
        <Col span={10}>
          <div className="config-area">
            <Form
              form={form}
              layout="vertical"
              initialValues={{
                layoutType: 'grid',
                width: 1200,
                height: 800,
                columns: 3,
                rows: 3,
                responsive: true,
                theme: 'modern',
                spacing: 'normal',
                alignment: 'left',
                components: availableComponents.slice(0, 3)
              }}
            >
              {renderLayoutTypeSelector()}
              
              <Divider />
              
              {renderComponentSelector()}
              
              <Divider />
              
              {renderConstraints()}
              
              <Divider />
              
              {renderStyleConfig()}
              
              <Button
                type="primary"
                block
                size="large"
                icon={<ThunderboltOutlined />}
                onClick={generateLayouts}
                loading={loading}
              >
                生成布局
              </Button>
            </Form>
          </div>
        </Col>
        
        {/* 结果区域 */}
        <Col span={14}>
          <div className="results-area">
            <div className="results-header">
              <Title level={4}>生成结果</Title>
              {generatedLayouts.length > 0 && (
                <Text type="secondary">
                  共生成 {generatedLayouts.length} 个布局方案
                </Text>
              )}
            </div>
            
            {renderGeneratedLayouts()}
            
            {selectedLayout && (
              <>
                <Divider />
                {renderPreviewArea()}
              </>
            )}
          </div>
        </Col>
      </Row>
    </Modal>
  );
};

export default AILayoutGenerator;
