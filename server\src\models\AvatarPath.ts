/**
 * 数字人路径数据模型
 */
import { Schema, model, Document, Types } from 'mongoose';

/**
 * 路径点接口
 */
export interface IPathPoint {
  id: string;
  position: {
    x: number;
    y: number;
    z: number;
  };
  waitTime: number;
  speed: number;
  animation: string;
  lookAt?: {
    x: number;
    y: number;
    z: number;
  };
  triggers: IPathTrigger[];
  userData?: any;
}

/**
 * 路径触发器接口
 */
export interface IPathTrigger {
  type: 'dialogue' | 'animation' | 'sound' | 'event' | 'custom';
  data: any;
  condition?: string;
  delay?: number;
  once?: boolean;
}

/**
 * 路径元数据接口
 */
export interface IPathMetadata {
  createdAt: Date;
  updatedAt: Date;
  creator: string;
  version: number;
  description?: string;
  tags: string[];
  category?: string;
}

/**
 * 数字人路径接口
 */
export interface IAvatarPath extends Document {
  id: string;
  name: string;
  avatarId: string;
  projectId: string;
  sceneId?: string;
  points: IPathPoint[];
  loopMode: 'none' | 'loop' | 'pingpong';
  interpolation: 'linear' | 'smooth' | 'bezier' | 'spline';
  totalDuration: number;
  totalLength: number;
  enabled: boolean;
  metadata: IPathMetadata;
  
  // 方法
  calculateDuration(): number;
  calculateLength(): number;
  validate(): { valid: boolean; errors: string[] };
  clone(newName?: string): IAvatarPath;
}

/**
 * 路径点Schema
 */
const PathPointSchema = new Schema({
  id: {
    type: String,
    required: true
  },
  position: {
    x: { type: Number, required: true },
    y: { type: Number, required: true },
    z: { type: Number, required: true }
  },
  waitTime: {
    type: Number,
    default: 0,
    min: 0
  },
  speed: {
    type: Number,
    default: 1.0,
    min: 0.1,
    max: 20.0
  },
  animation: {
    type: String,
    default: 'walk'
  },
  lookAt: {
    x: { type: Number },
    y: { type: Number },
    z: { type: Number }
  },
  triggers: [{
    type: {
      type: String,
      enum: ['dialogue', 'animation', 'sound', 'event', 'custom'],
      required: true
    },
    data: {
      type: Schema.Types.Mixed,
      required: true
    },
    condition: String,
    delay: {
      type: Number,
      default: 0,
      min: 0
    },
    once: {
      type: Boolean,
      default: false
    }
  }],
  userData: Schema.Types.Mixed
}, { _id: false });

/**
 * 路径元数据Schema
 */
const PathMetadataSchema = new Schema({
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  creator: {
    type: String,
    required: true
  },
  version: {
    type: Number,
    default: 1,
    min: 1
  },
  description: String,
  tags: [{
    type: String,
    trim: true
  }],
  category: String
}, { _id: false });

/**
 * 数字人路径Schema
 */
const AvatarPathSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  avatarId: {
    type: String,
    required: true,
    index: true
  },
  projectId: {
    type: String,
    required: true,
    index: true
  },
  sceneId: {
    type: String,
    index: true
  },
  points: {
    type: [PathPointSchema],
    validate: {
      validator: function(points: IPathPoint[]) {
        return points.length >= 2;
      },
      message: '路径至少需要2个点'
    }
  },
  loopMode: {
    type: String,
    enum: ['none', 'loop', 'pingpong'],
    default: 'none'
  },
  interpolation: {
    type: String,
    enum: ['linear', 'smooth', 'bezier', 'spline'],
    default: 'linear'
  },
  totalDuration: {
    type: Number,
    default: 0,
    min: 0
  },
  totalLength: {
    type: Number,
    default: 0,
    min: 0
  },
  enabled: {
    type: Boolean,
    default: true
  },
  metadata: {
    type: PathMetadataSchema,
    required: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

/**
 * 索引
 */
AvatarPathSchema.index({ projectId: 1, avatarId: 1 });
AvatarPathSchema.index({ projectId: 1, sceneId: 1 });
AvatarPathSchema.index({ 'metadata.tags': 1 });
AvatarPathSchema.index({ 'metadata.category': 1 });
AvatarPathSchema.index({ enabled: 1 });

/**
 * 虚拟字段
 */
AvatarPathSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

/**
 * 中间件 - 保存前更新计算字段
 */
AvatarPathSchema.pre('save', function(next) {
  if (this.isModified('points')) {
    this.totalDuration = this.calculateDuration();
    this.totalLength = this.calculateLength();
  }
  
  if (this.isModified()) {
    this.metadata.updatedAt = new Date();
    this.metadata.version += 1;
  }
  
  next();
});

/**
 * 实例方法 - 计算路径持续时间
 */
AvatarPathSchema.methods.calculateDuration = function(): number {
  if (!this.points || this.points.length === 0) return 0;

  let totalDuration = 0;

  for (let i = 0; i < this.points.length; i++) {
    const point = this.points[i];
    totalDuration += point.waitTime || 0;

    if (i < this.points.length - 1) {
      const nextPoint = this.points[i + 1];
      const dx = nextPoint.position.x - point.position.x;
      const dy = nextPoint.position.y - point.position.y;
      const dz = nextPoint.position.z - point.position.z;
      const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
      const speed = point.speed || 1;
      totalDuration += distance / speed;
    }
  }

  return totalDuration;
};

/**
 * 实例方法 - 计算路径长度
 */
AvatarPathSchema.methods.calculateLength = function(): number {
  if (!this.points || this.points.length < 2) return 0;

  let totalLength = 0;

  for (let i = 0; i < this.points.length - 1; i++) {
    const point = this.points[i];
    const nextPoint = this.points[i + 1];
    const dx = nextPoint.position.x - point.position.x;
    const dy = nextPoint.position.y - point.position.y;
    const dz = nextPoint.position.z - point.position.z;
    totalLength += Math.sqrt(dx * dx + dy * dy + dz * dz);
  }

  return totalLength;
};

/**
 * 实例方法 - 验证路径
 */
AvatarPathSchema.methods.validate = function(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // 验证基本信息
  if (!this.name || this.name.trim() === '') {
    errors.push('路径名称不能为空');
  }

  if (!this.avatarId || this.avatarId.trim() === '') {
    errors.push('必须指定关联的数字人ID');
  }

  if (!this.projectId || this.projectId.trim() === '') {
    errors.push('必须指定项目ID');
  }

  // 验证路径点
  if (!this.points || this.points.length < 2) {
    errors.push('路径至少需要2个路径点');
  } else {
    this.points.forEach((point: IPathPoint, index: number) => {
      if (!point.id) {
        errors.push(`路径点${index + 1}缺少ID`);
      }

      if (!point.position || 
          typeof point.position.x !== 'number' ||
          typeof point.position.y !== 'number' ||
          typeof point.position.z !== 'number') {
        errors.push(`路径点${index + 1}位置数据无效`);
      }

      if (typeof point.speed !== 'number' || point.speed <= 0) {
        errors.push(`路径点${index + 1}速度必须大于0`);
      }

      if (typeof point.waitTime !== 'number' || point.waitTime < 0) {
        errors.push(`路径点${index + 1}等待时间不能为负数`);
      }

      if (!point.animation || point.animation.trim() === '') {
        errors.push(`路径点${index + 1}动画名称不能为空`);
      }
    });
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * 实例方法 - 克隆路径
 */
AvatarPathSchema.methods.clone = function(newName?: string): IAvatarPath {
  const clonedData = this.toObject();
  delete clonedData._id;
  delete clonedData.__v;
  
  clonedData.name = newName || `${this.name} (副本)`;
  clonedData.metadata = {
    ...clonedData.metadata,
    createdAt: new Date(),
    updatedAt: new Date(),
    version: 1
  };

  return new AvatarPath(clonedData);
};

/**
 * 静态方法 - 根据项目ID查找路径
 */
AvatarPathSchema.statics.findByProject = function(projectId: string) {
  return this.find({ projectId }).sort({ 'metadata.updatedAt': -1 });
};

/**
 * 静态方法 - 根据数字人ID查找路径
 */
AvatarPathSchema.statics.findByAvatar = function(avatarId: string) {
  return this.find({ avatarId }).sort({ 'metadata.updatedAt': -1 });
};

/**
 * 静态方法 - 根据场景ID查找路径
 */
AvatarPathSchema.statics.findByScene = function(sceneId: string) {
  return this.find({ sceneId }).sort({ 'metadata.updatedAt': -1 });
};

/**
 * 静态方法 - 搜索路径
 */
AvatarPathSchema.statics.search = function(query: {
  projectId: string;
  keyword?: string;
  avatarId?: string;
  sceneId?: string;
  tags?: string[];
  category?: string;
  enabled?: boolean;
}) {
  const filter: any = { projectId: query.projectId };

  if (query.keyword) {
    filter.$or = [
      { name: { $regex: query.keyword, $options: 'i' } },
      { 'metadata.description': { $regex: query.keyword, $options: 'i' } }
    ];
  }

  if (query.avatarId) {
    filter.avatarId = query.avatarId;
  }

  if (query.sceneId) {
    filter.sceneId = query.sceneId;
  }

  if (query.tags && query.tags.length > 0) {
    filter['metadata.tags'] = { $in: query.tags };
  }

  if (query.category) {
    filter['metadata.category'] = query.category;
  }

  if (typeof query.enabled === 'boolean') {
    filter.enabled = query.enabled;
  }

  return this.find(filter).sort({ 'metadata.updatedAt': -1 });
};

/**
 * 导出模型
 */
export const AvatarPath = model<IAvatarPath>('AvatarPath', AvatarPathSchema);
