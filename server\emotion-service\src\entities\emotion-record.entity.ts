/**
 * 情感记录实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('emotion_records')
@Index(['userId', 'timestamp'])
@Index(['emotionType', 'timestamp'])
export class EmotionRecord {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  userId: string;

  @Column({ type: 'varchar', length: 100 })
  emotionType: string;

  @Column({ type: 'decimal', precision: 5, scale: 3 })
  intensity: number;

  @Column({ type: 'decimal', precision: 5, scale: 3 })
  confidence: number;

  @Column({ type: 'varchar', length: 255 })
  source: string;

  @Column({ type: 'json', nullable: true })
  context: any;

  @Column({ type: 'timestamp' })
  @Index()
  timestamp: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
