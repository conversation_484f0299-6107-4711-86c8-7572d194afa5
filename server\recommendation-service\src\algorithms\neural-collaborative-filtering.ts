/**
 * 神经协同过滤推荐算法
 * 基于深度学习的协同过滤实现
 */
import * as tf from '@tensorflow/tfjs-node';
import { Injectable, Logger } from '@nestjs/common';
import { 
  RecommendationAlgorithm, 
  RecommendationRequest, 
  RecommendationResponse,
  UserProfile,
  TrainingData,
  ModelConfig
} from '../interfaces/recommendation.interface';

@Injectable()
export class NeuralCollaborativeFiltering implements RecommendationAlgorithm {
  private readonly logger = new Logger(NeuralCollaborativeFiltering.name);
  private model: tf.LayersModel | null = null;
  private userEmbeddings: Map<string, number[]> = new Map();
  private itemEmbeddings: Map<string, number[]> = new Map();
  private userToIndex: Map<string, number> = new Map();
  private itemToIndex: Map<string, number> = new Map();
  private indexToUser: Map<number, string> = new Map();
  private indexToItem: Map<number, string> = new Map();

  constructor(private config: ModelConfig = {
    embeddingDim: 64,
    hiddenLayers: [128, 64, 32],
    dropoutRate: 0.2,
    learningRate: 0.001
  }) {}

  /**
   * 生成推荐
   */
  async recommend(request: RecommendationRequest): Promise<RecommendationResponse[]> {
    if (!this.model) {
      await this.loadModel();
    }

    const userId = request.userId;
    const userIndex = this.userToIndex.get(userId);
    
    if (userIndex === undefined) {
      // 新用户，使用冷启动策略
      return this.handleColdStart(request);
    }

    // 获取候选物品
    const candidateItems = await this.getCandidateItems(request);
    
    // 批量预测评分
    const predictions = await this.batchPredict(userIndex, candidateItems);
    
    // 构建推荐结果
    const recommendations = predictions
      .map((score, index) => ({
        itemId: candidateItems[index].id,
        itemType: candidateItems[index].type,
        score,
        algorithm: 'neural_collaborative_filtering',
        confidence: this.calculateConfidence(score),
        explanation: this.generateExplanation(userId, candidateItems[index], score),
        context: request.context
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, request.count || 10);

    return recommendations;
  }

  /**
   * 批量预测评分
   */
  private async batchPredict(userIndex: number, items: any[]): Promise<number[]> {
    if (!this.model) {
      throw new Error('模型未加载');
    }

    const itemIndices = items.map(item => this.itemToIndex.get(item.id) || 0);
    
    // 创建输入张量
    const userInputs = tf.tensor2d([Array(items.length).fill(userIndex)]);
    const itemInputs = tf.tensor2d([itemIndices]);

    try {
      // 模型预测
      const predictions = this.model.predict([userInputs, itemInputs]) as tf.Tensor;
      const scores = await predictions.data();
      
      // 清理张量
      userInputs.dispose();
      itemInputs.dispose();
      predictions.dispose();

      return Array.from(scores);
    } catch (error) {
      this.logger.error('批量预测失败:', error);
      return items.map(() => 0.5); // 返回默认分数
    }
  }

  /**
   * 训练模型
   */
  async train(trainingData: TrainingData): Promise<void> {
    this.logger.log('开始训练神经协同过滤模型...');

    // 准备训练数据
    const { userInputs, itemInputs, ratings } = this.prepareTrainingData(trainingData);
    
    // 构建模型
    this.model = this.buildModel();
    
    // 编译模型
    this.model.compile({
      optimizer: tf.train.adam(this.config.learningRate),
      loss: 'meanSquaredError',
      metrics: ['mae']
    });

    // 训练模型
    const history = await this.model.fit(
      [userInputs, itemInputs],
      ratings,
      {
        epochs: 100,
        batchSize: 256,
        validationSplit: 0.2,
        shuffle: true,
        callbacks: {
          onEpochEnd: (epoch, logs) => {
            if (epoch % 10 === 0) {
              this.logger.log(`Epoch ${epoch}: loss = ${logs?.loss?.toFixed(4)}, val_loss = ${logs?.val_loss?.toFixed(4)}`);
            }
          }
        }
      }
    );

    // 更新嵌入向量
    await this.updateEmbeddings();
    
    this.logger.log('模型训练完成');
  }

  /**
   * 构建神经网络模型
   */
  private buildModel(): tf.LayersModel {
    const numUsers = this.userToIndex.size;
    const numItems = this.itemToIndex.size;
    const embeddingDim = this.config.embeddingDim;

    // 用户输入
    const userInput = tf.input({ shape: [1], name: 'user_input' });
    const userEmbedding = tf.layers.embedding({
      inputDim: numUsers,
      outputDim: embeddingDim,
      name: 'user_embedding'
    }).apply(userInput) as tf.SymbolicTensor;

    // 物品输入
    const itemInput = tf.input({ shape: [1], name: 'item_input' });
    const itemEmbedding = tf.layers.embedding({
      inputDim: numItems,
      outputDim: embeddingDim,
      name: 'item_embedding'
    }).apply(itemInput) as tf.SymbolicTensor;

    // 展平嵌入向量
    const userFlat = tf.layers.flatten().apply(userEmbedding) as tf.SymbolicTensor;
    const itemFlat = tf.layers.flatten().apply(itemEmbedding) as tf.SymbolicTensor;

    // 连接用户和物品嵌入
    const concat = tf.layers.concatenate().apply([userFlat, itemFlat]) as tf.SymbolicTensor;

    // 隐藏层
    let hidden = concat;
    for (const units of this.config.hiddenLayers) {
      hidden = tf.layers.dense({
        units,
        activation: 'relu'
      }).apply(hidden) as tf.SymbolicTensor;
      
      hidden = tf.layers.dropout({
        rate: this.config.dropoutRate
      }).apply(hidden) as tf.SymbolicTensor;
    }

    // 输出层
    const output = tf.layers.dense({
      units: 1,
      activation: 'sigmoid',
      name: 'rating_output'
    }).apply(hidden) as tf.SymbolicTensor;

    // 创建模型
    return tf.model({
      inputs: [userInput, itemInput],
      outputs: output
    });
  }

  /**
   * 准备训练数据
   */
  private prepareTrainingData(trainingData: TrainingData): {
    userInputs: tf.Tensor2D;
    itemInputs: tf.Tensor2D;
    ratings: tf.Tensor2D;
  } {
    const interactions = trainingData.interactions;
    
    // 构建用户和物品索引映射
    this.buildIndexMappings(interactions);

    const userIndices: number[] = [];
    const itemIndices: number[] = [];
    const ratingValues: number[] = [];

    for (const interaction of interactions) {
      const userIndex = this.userToIndex.get(interaction.userId);
      const itemIndex = this.itemToIndex.get(interaction.itemId);
      
      if (userIndex !== undefined && itemIndex !== undefined) {
        userIndices.push(userIndex);
        itemIndices.push(itemIndex);
        ratingValues.push(interaction.rating);
      }
    }

    return {
      userInputs: tf.tensor2d(userIndices.map(i => [i])),
      itemInputs: tf.tensor2d(itemIndices.map(i => [i])),
      ratings: tf.tensor2d(ratingValues.map(r => [r]))
    };
  }

  /**
   * 构建索引映射
   */
  private buildIndexMappings(interactions: any[]): void {
    const users = new Set<string>();
    const items = new Set<string>();

    for (const interaction of interactions) {
      users.add(interaction.userId);
      items.add(interaction.itemId);
    }

    // 构建用户索引映射
    let userIndex = 0;
    for (const userId of users) {
      this.userToIndex.set(userId, userIndex);
      this.indexToUser.set(userIndex, userId);
      userIndex++;
    }

    // 构建物品索引映射
    let itemIndex = 0;
    for (const itemId of items) {
      this.itemToIndex.set(itemId, itemIndex);
      this.indexToItem.set(itemIndex, itemId);
      itemIndex++;
    }
  }

  /**
   * 更新嵌入向量
   */
  private async updateEmbeddings(): Promise<void> {
    if (!this.model) return;

    // 获取用户嵌入层
    const userEmbeddingLayer = this.model.getLayer('user_embedding') as tf.layers.Embedding;
    const userWeights = userEmbeddingLayer.getWeights()[0];
    const userEmbeddingData = await userWeights.data();

    // 更新用户嵌入映射
    for (const [userId, index] of this.userToIndex) {
      const start = index * this.config.embeddingDim;
      const end = start + this.config.embeddingDim;
      const embedding = Array.from(userEmbeddingData.slice(start, end));
      this.userEmbeddings.set(userId, embedding);
    }

    // 获取物品嵌入层
    const itemEmbeddingLayer = this.model.getLayer('item_embedding') as tf.layers.Embedding;
    const itemWeights = itemEmbeddingLayer.getWeights()[0];
    const itemEmbeddingData = await itemWeights.data();

    // 更新物品嵌入映射
    for (const [itemId, index] of this.itemToIndex) {
      const start = index * this.config.embeddingDim;
      const end = start + this.config.embeddingDim;
      const embedding = Array.from(itemEmbeddingData.slice(start, end));
      this.itemEmbeddings.set(itemId, embedding);
    }
  }

  /**
   * 处理冷启动问题
   */
  private async handleColdStart(request: RecommendationRequest): Promise<RecommendationResponse[]> {
    // 对于新用户，基于流行度和内容特征推荐
    const popularItems = await this.getPopularItems(request.type);
    
    return popularItems.slice(0, request.count || 10).map((item, index) => ({
      itemId: item.id,
      itemType: item.type,
      score: 0.8 - (index * 0.05), // 递减分数
      algorithm: 'cold_start_popularity',
      confidence: 0.6,
      explanation: '基于热门内容的推荐',
      context: request.context
    }));
  }

  /**
   * 获取候选物品
   */
  private async getCandidateItems(request: RecommendationRequest): Promise<any[]> {
    // 这里应该从物品服务获取候选物品
    // 可以基于类型、过滤条件等筛选
    return [];
  }

  /**
   * 获取热门物品
   */
  private async getPopularItems(type: string): Promise<any[]> {
    // 这里应该从统计服务获取热门物品
    return [];
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(score: number): number {
    // 基于分数计算置信度
    return Math.min(0.95, Math.max(0.1, score));
  }

  /**
   * 生成解释
   */
  private generateExplanation(userId: string, item: any, score: number): string {
    return `基于您的历史行为和偏好，我们认为您会喜欢这个${item.type}（置信度：${(score * 100).toFixed(0)}%）`;
  }

  /**
   * 加载模型
   */
  private async loadModel(): Promise<void> {
    try {
      // 从文件系统或模型服务加载预训练模型
      // this.model = await tf.loadLayersModel('file://path/to/model.json');
      this.logger.log('模型加载完成');
    } catch (error) {
      this.logger.warn('模型加载失败，将使用默认模型');
      // 如果加载失败，创建一个新模型
      this.model = this.buildModel();
    }
  }

  /**
   * 保存模型
   */
  async saveModel(path: string): Promise<void> {
    if (!this.model) {
      throw new Error('没有可保存的模型');
    }

    await this.model.save(`file://${path}`);
    this.logger.log(`模型已保存到: ${path}`);
  }
}
