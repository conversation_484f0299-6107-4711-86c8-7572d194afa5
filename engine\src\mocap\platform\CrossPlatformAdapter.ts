/**
 * 跨平台适配器
 * 提供不同平台的统一接口和适配功能
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';

/**
 * 平台类型
 */
export enum PlatformType {
  DESKTOP = 'desktop',
  MOBILE = 'mobile',
  TABLET = 'tablet',
  VR = 'vr',
  AR = 'ar',
  WEB = 'web'
}

/**
 * 设备能力
 */
export interface DeviceCapabilities {
  /** 是否支持摄像头 */
  hasCamera: boolean;
  /** 是否支持多摄像头 */
  hasMultipleCameras: boolean;
  /** 是否支持深度摄像头 */
  hasDepthCamera: boolean;
  /** 是否支持陀螺仪 */
  hasGyroscope: boolean;
  /** 是否支持加速度计 */
  hasAccelerometer: boolean;
  /** 是否支持触摸 */
  hasTouch: boolean;
  /** 是否支持WebGL */
  hasWebGL: boolean;
  /** 是否支持WebXR */
  hasWebXR: boolean;
  /** CPU核心数 */
  cpuCores: number;
  /** 内存大小(MB) */
  memorySize: number;
  /** 屏幕分辨率 */
  screenResolution: { width: number; height: number };
  /** 像素密度 */
  pixelRatio: number;
}

/**
 * 平台配置
 */
export interface PlatformConfig {
  /** 平台类型 */
  type: PlatformType;
  /** 设备能力 */
  capabilities: DeviceCapabilities;
  /** 性能等级 (1-5) */
  performanceLevel: number;
  /** 推荐配置 */
  recommendedSettings: {
    resolution: { width: number; height: number };
    frameRate: number;
    qualityLevel: number;
    enabledFeatures: string[];
  };
}

/**
 * 跨平台适配器
 */
export class CrossPlatformAdapter extends EventEmitter {
  private currentPlatform: PlatformType;
  private deviceCapabilities: DeviceCapabilities;
  private platformConfig: PlatformConfig;
  private adaptationStrategies: Map<PlatformType, AdaptationStrategy> = new Map();

  constructor() {
    super();
    this.currentPlatform = this.detectPlatform();
    this.deviceCapabilities = this.detectDeviceCapabilities();
    this.platformConfig = this.generatePlatformConfig();
    
    this.initializeAdaptationStrategies();
    
    Debug.log('CrossPlatformAdapter', `检测到平台: ${this.currentPlatform}`, this.deviceCapabilities);
  }

  /**
   * 检测当前平台
   */
  private detectPlatform(): PlatformType {
    // 检测VR/AR设备
    if (typeof navigator !== 'undefined') {
      if (navigator.userAgent.includes('Quest') || navigator.userAgent.includes('Oculus')) {
        return PlatformType.VR;
      }
      
      if (navigator.userAgent.includes('ARCore') || navigator.userAgent.includes('ARKit')) {
        return PlatformType.AR;
      }
    }

    // 检测移动设备
    if (typeof window !== 'undefined') {
      const userAgent = window.navigator.userAgent;
      
      if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
        if (/iPad/i.test(userAgent) || (window.screen.width >= 768 && window.screen.height >= 1024)) {
          return PlatformType.TABLET;
        }
        return PlatformType.MOBILE;
      }
    }

    // 默认为桌面平台
    return PlatformType.DESKTOP;
  }

  /**
   * 检测设备能力
   */
  private detectDeviceCapabilities(): DeviceCapabilities {
    const capabilities: DeviceCapabilities = {
      hasCamera: false,
      hasMultipleCameras: false,
      hasDepthCamera: false,
      hasGyroscope: false,
      hasAccelerometer: false,
      hasTouch: false,
      hasWebGL: false,
      hasWebXR: false,
      cpuCores: 1,
      memorySize: 1024,
      screenResolution: { width: 1920, height: 1080 },
      pixelRatio: 1
    };

    if (typeof navigator !== 'undefined') {
      // 检测摄像头
      capabilities.hasCamera = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
      
      // 检测多摄像头
      if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
        navigator.mediaDevices.enumerateDevices().then(devices => {
          const videoDevices = devices.filter(device => device.kind === 'videoinput');
          capabilities.hasMultipleCameras = videoDevices.length > 1;
        });
      }

      // 检测传感器
      capabilities.hasGyroscope = 'DeviceOrientationEvent' in window;
      capabilities.hasAccelerometer = 'DeviceMotionEvent' in window;
      
      // 检测触摸
      capabilities.hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      
      // 检测WebXR
      capabilities.hasWebXR = 'xr' in navigator;
      
      // 检测CPU核心数
      capabilities.cpuCores = navigator.hardwareConcurrency || 1;
    }

    if (typeof window !== 'undefined') {
      // 检测WebGL
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      capabilities.hasWebGL = !!gl;

      // 检测内存
      if ((performance as any).memory) {
        capabilities.memorySize = (performance as any).memory.jsHeapSizeLimit / 1024 / 1024;
      }

      // 检测屏幕信息
      capabilities.screenResolution = {
        width: window.screen.width,
        height: window.screen.height
      };
      capabilities.pixelRatio = window.devicePixelRatio || 1;
    }

    return capabilities;
  }

  /**
   * 生成平台配置
   */
  private generatePlatformConfig(): PlatformConfig {
    const performanceLevel = this.calculatePerformanceLevel();
    
    return {
      type: this.currentPlatform,
      capabilities: this.deviceCapabilities,
      performanceLevel,
      recommendedSettings: this.generateRecommendedSettings(performanceLevel)
    };
  }

  /**
   * 计算性能等级
   */
  private calculatePerformanceLevel(): number {
    let score = 0;

    // CPU评分
    if (this.deviceCapabilities.cpuCores >= 8) score += 2;
    else if (this.deviceCapabilities.cpuCores >= 4) score += 1;

    // 内存评分
    if (this.deviceCapabilities.memorySize >= 8192) score += 2;
    else if (this.deviceCapabilities.memorySize >= 4096) score += 1;

    // 平台评分
    switch (this.currentPlatform) {
      case PlatformType.DESKTOP:
        score += 2;
        break;
      case PlatformType.TABLET:
        score += 1;
        break;
      case PlatformType.VR:
      case PlatformType.AR:
        score += 1;
        break;
      case PlatformType.MOBILE:
        score += 0;
        break;
    }

    // WebGL评分
    if (this.deviceCapabilities.hasWebGL) score += 1;

    return Math.max(1, Math.min(5, score));
  }

  /**
   * 生成推荐设置
   */
  private generateRecommendedSettings(performanceLevel: number): any {
    const baseSettings = {
      resolution: { width: 640, height: 480 },
      frameRate: 15,
      qualityLevel: 0.3,
      enabledFeatures: ['basic_pose', 'basic_gestures']
    };

    switch (performanceLevel) {
      case 5: // 高性能
        return {
          resolution: { width: 1280, height: 720 },
          frameRate: 30,
          qualityLevel: 1.0,
          enabledFeatures: [
            'enhanced_pose', 'advanced_gestures', 'hand_tracking',
            'interaction_mapping', 'performance_optimization'
          ]
        };
      
      case 4: // 中高性能
        return {
          resolution: { width: 960, height: 540 },
          frameRate: 30,
          qualityLevel: 0.8,
          enabledFeatures: [
            'enhanced_pose', 'advanced_gestures', 'hand_tracking',
            'interaction_mapping'
          ]
        };
      
      case 3: // 中等性能
        return {
          resolution: { width: 640, height: 480 },
          frameRate: 25,
          qualityLevel: 0.6,
          enabledFeatures: [
            'basic_pose', 'advanced_gestures', 'hand_tracking'
          ]
        };
      
      case 2: // 中低性能
        return {
          resolution: { width: 480, height: 360 },
          frameRate: 20,
          qualityLevel: 0.4,
          enabledFeatures: [
            'basic_pose', 'basic_gestures'
          ]
        };
      
      default: // 低性能
        return baseSettings;
    }
  }

  /**
   * 初始化适配策略
   */
  private initializeAdaptationStrategies(): void {
    // 桌面平台策略
    this.adaptationStrategies.set(PlatformType.DESKTOP, {
      optimizeForPerformance: () => {
        return {
          useWebWorkers: true,
          enableGPUAcceleration: true,
          batchProcessing: true,
          cacheOptimization: true
        };
      },
      adaptInterface: () => {
        return {
          showAdvancedControls: true,
          enableKeyboardShortcuts: true,
          supportMultipleWindows: true
        };
      },
      handleInput: (inputType: string) => {
        return inputType === 'mouse' || inputType === 'keyboard';
      }
    });

    // 移动平台策略
    this.adaptationStrategies.set(PlatformType.MOBILE, {
      optimizeForPerformance: () => {
        return {
          useWebWorkers: false,
          enableGPUAcceleration: false,
          batchProcessing: false,
          cacheOptimization: true,
          reducedQuality: true
        };
      },
      adaptInterface: () => {
        return {
          showAdvancedControls: false,
          enableTouchGestures: true,
          responsiveLayout: true,
          largerButtons: true
        };
      },
      handleInput: (inputType: string) => {
        return inputType === 'touch';
      }
    });

    // 平板策略
    this.adaptationStrategies.set(PlatformType.TABLET, {
      optimizeForPerformance: () => {
        return {
          useWebWorkers: true,
          enableGPUAcceleration: true,
          batchProcessing: true,
          cacheOptimization: true
        };
      },
      adaptInterface: () => {
        return {
          showAdvancedControls: true,
          enableTouchGestures: true,
          responsiveLayout: true,
          adaptiveLayout: true
        };
      },
      handleInput: (inputType: string) => {
        return inputType === 'touch' || inputType === 'stylus';
      }
    });

    // VR平台策略
    this.adaptationStrategies.set(PlatformType.VR, {
      optimizeForPerformance: () => {
        return {
          useWebWorkers: true,
          enableGPUAcceleration: true,
          lowLatencyMode: true,
          spatialTracking: true
        };
      },
      adaptInterface: () => {
        return {
          immersiveInterface: true,
          spatialUI: true,
          handTracking: true,
          eyeTracking: false
        };
      },
      handleInput: (inputType: string) => {
        return inputType === 'controller' || inputType === 'hand' || inputType === 'gaze';
      }
    });

    // AR平台策略
    this.adaptationStrategies.set(PlatformType.AR, {
      optimizeForPerformance: () => {
        return {
          useWebWorkers: true,
          enableGPUAcceleration: true,
          realTimeTracking: true,
          environmentMapping: true
        };
      },
      adaptInterface: () => {
        return {
          overlayInterface: true,
          worldSpaceUI: true,
          gestureRecognition: true,
          environmentInteraction: true
        };
      },
      handleInput: (inputType: string) => {
        return inputType === 'touch' || inputType === 'gesture' || inputType === 'voice';
      }
    });
  }

  /**
   * 获取当前平台配置
   */
  public getPlatformConfig(): PlatformConfig {
    return { ...this.platformConfig };
  }

  /**
   * 获取适配策略
   */
  public getAdaptationStrategy(): AdaptationStrategy | null {
    return this.adaptationStrategies.get(this.currentPlatform) || null;
  }

  /**
   * 应用平台优化
   */
  public applyPlatformOptimizations(): any {
    const strategy = this.getAdaptationStrategy();
    if (!strategy) return {};

    const optimizations = strategy.optimizeForPerformance();
    
    Debug.log('CrossPlatformAdapter', '应用平台优化', optimizations);
    this.emit('optimizationsApplied', optimizations);
    
    return optimizations;
  }

  /**
   * 适配用户界面
   */
  public adaptUserInterface(): any {
    const strategy = this.getAdaptationStrategy();
    if (!strategy) return {};

    const interfaceConfig = strategy.adaptInterface();
    
    Debug.log('CrossPlatformAdapter', '适配用户界面', interfaceConfig);
    this.emit('interfaceAdapted', interfaceConfig);
    
    return interfaceConfig;
  }

  /**
   * 检查输入类型支持
   */
  public supportsInputType(inputType: string): boolean {
    const strategy = this.getAdaptationStrategy();
    return strategy ? strategy.handleInput(inputType) : false;
  }

  /**
   * 获取推荐的摄像头配置
   */
  public getRecommendedCameraConfig(): any {
    const settings = this.platformConfig.recommendedSettings;
    
    return {
      resolution: settings.resolution,
      frameRate: settings.frameRate,
      facingMode: this.currentPlatform === PlatformType.MOBILE ? 'user' : 'environment',
      audio: false,
      video: {
        width: { ideal: settings.resolution.width },
        height: { ideal: settings.resolution.height },
        frameRate: { ideal: settings.frameRate }
      }
    };
  }

  /**
   * 获取性能建议
   */
  public getPerformanceRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.platformConfig.performanceLevel <= 2) {
      recommendations.push('建议降低视频分辨率以提高性能');
      recommendations.push('建议关闭高级功能以节省资源');
    }
    
    if (!this.deviceCapabilities.hasWebGL) {
      recommendations.push('设备不支持WebGL，某些功能可能受限');
    }
    
    if (this.deviceCapabilities.memorySize < 2048) {
      recommendations.push('设备内存较低，建议关闭内存密集型功能');
    }
    
    if (this.currentPlatform === PlatformType.MOBILE) {
      recommendations.push('移动设备建议使用省电模式');
      recommendations.push('建议在充电时使用以获得最佳性能');
    }
    
    return recommendations;
  }

  /**
   * 检查功能兼容性
   */
  public checkFeatureCompatibility(featureName: string): boolean {
    const enabledFeatures = this.platformConfig.recommendedSettings.enabledFeatures;
    return enabledFeatures.includes(featureName);
  }

  /**
   * 获取平台限制
   */
  public getPlatformLimitations(): string[] {
    const limitations: string[] = [];
    
    if (!this.deviceCapabilities.hasCamera) {
      limitations.push('设备不支持摄像头');
    }
    
    if (!this.deviceCapabilities.hasMultipleCameras) {
      limitations.push('设备只有单个摄像头');
    }
    
    if (this.currentPlatform === PlatformType.MOBILE) {
      limitations.push('移动设备性能限制');
      limitations.push('电池续航限制');
    }
    
    if (!this.deviceCapabilities.hasWebXR && 
        (this.currentPlatform === PlatformType.VR || this.currentPlatform === PlatformType.AR)) {
      limitations.push('设备不完全支持WebXR标准');
    }
    
    return limitations;
  }
}

/**
 * 适配策略接口
 */
interface AdaptationStrategy {
  optimizeForPerformance(): any;
  adaptInterface(): any;
  handleInput(inputType: string): boolean;
}
