import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { 
  LearnerProfile, 
  ProfileAnalysisOptions, 
  ProfileAnalysisResult,
  WeakArea,
  LearningPreferences,
  BehaviorPatterns,
  KnowledgeArea,
  CognitiveTraits,
  EmotionalTraits
} from './learner-profile.interface';
import { LearningRecord } from '../entities/learning-record.entity';
import { LearnerProfileEntity } from '../entities/learner-profile.entity';

/**
 * 学习者画像分析引擎
 * 基于学习记录数据构建和分析用户画像
 */
@Injectable()
export class LearnerProfileAnalyzerService {
  private readonly logger = new Logger(LearnerProfileAnalyzerService.name);

  constructor(
    @InjectRepository(LearningRecord)
    private readonly learningRecordRepository: Repository<LearningRecord>,
    @InjectRepository(LearnerProfileEntity)
    private readonly learnerProfileRepository: Repository<LearnerProfileEntity>,
  ) {}

  /**
   * 构建学习者画像
   * @param userId 用户ID
   * @param options 分析选项
   * @returns 画像分析结果
   */
  async buildLearnerProfile(
    userId: string, 
    options: ProfileAnalysisOptions = {
      includeHistoricalData: true,
      minDataPoints: 10,
      analysisDepth: 'detailed',
      updateExisting: true
    }
  ): Promise<ProfileAnalysisResult> {
    try {
      this.logger.log(`开始构建用户画像: ${userId}`);

      // 获取学习记录
      const learningRecords = await this.getLearningRecords(userId, options);

      if (learningRecords.length < options.minDataPoints) {
        throw new Error(`数据点不足，需要至少 ${options.minDataPoints} 条记录，当前只有 ${learningRecords.length} 条`);
      }

      // 获取现有画像
      const existingProfile = await this.getExistingProfile(userId);

      // 分析学习数据
      const profile = existingProfile && options.updateExisting
        ? await this.updateProfile(existingProfile, learningRecords, options)
        : await this.createProfile(userId, learningRecords, options);

      // 识别薄弱环节
      const weakAreas = this.identifyWeakAreas(profile);

      // 生成学习路径建议
      const pathSuggestions = await this.generatePathSuggestions(profile, weakAreas);

      // 生成个性化建议
      const personalizations = this.generatePersonalizationSuggestions(profile);

      // 生成洞察
      const insights = this.generateInsights(profile, learningRecords);

      // 计算置信度
      const confidence = this.calculateConfidence(profile, learningRecords.length);

      // 保存画像
      await this.saveProfile(profile);

      const result: ProfileAnalysisResult = {
        profile,
        weakAreas,
        pathSuggestions,
        personalizations,
        insights,
        confidence,
        analysisDate: new Date()
      };

      this.logger.log(`用户画像构建完成: ${userId}, 置信度: ${confidence.toFixed(2)}`);
      return result;
    } catch (error) {
      this.logger.error(`构建用户画像失败: ${userId}, ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取学习记录
   * @param userId 用户ID
   * @param options 分析选项
   * @returns 学习记录数组
   */
  private async getLearningRecords(
    userId: string, 
    options: ProfileAnalysisOptions
  ): Promise<LearningRecord[]> {
    const queryBuilder = this.learningRecordRepository
      .createQueryBuilder('record')
      .where('record.userId = :userId', { userId })
      .orderBy('record.timestamp', 'ASC');

    if (options.timeRange) {
      queryBuilder
        .andWhere('record.timestamp >= :start', { start: options.timeRange.start })
        .andWhere('record.timestamp <= :end', { end: options.timeRange.end });
    }

    return await queryBuilder.getMany();
  }

  /**
   * 获取现有画像
   * @param userId 用户ID
   * @returns 现有画像或null
   */
  private async getExistingProfile(userId: string): Promise<LearnerProfile | null> {
    const entity = await this.learnerProfileRepository.findOne({
      where: { userId }
    });

    return entity ? this.entityToProfile(entity) : null;
  }

  /**
   * 创建新画像
   * @param userId 用户ID
   * @param records 学习记录
   * @param options 分析选项
   * @returns 学习者画像
   */
  private async createProfile(
    userId: string,
    records: LearningRecord[],
    options: ProfileAnalysisOptions
  ): Promise<LearnerProfile> {
    const profile: LearnerProfile = {
      userId,
      createdAt: new Date(),
      updatedAt: new Date(),
      demographics: await this.inferDemographics(userId, records),
      learningPreferences: this.analyzeLearningPreferences(records),
      knowledgeAreas: this.assessKnowledgeAreas(records),
      behaviorPatterns: this.analyzeBehaviorPatterns(records),
      learningGoals: this.inferLearningGoals(records),
      socialLearning: this.analyzeSocialLearning(records),
      cognitiveTraits: this.assessCognitiveTraits(records),
      emotionalTraits: this.assessEmotionalTraits(records),
      learningOutcomes: this.calculateLearningOutcomes(records),
      metadata: {
        profileVersion: '1.0',
        dataQuality: this.assessDataQuality(records),
        lastAnalysisDate: new Date(),
        analysisCount: 1,
        confidenceScore: 0
      }
    };

    profile.metadata.confidenceScore = this.calculateConfidence(profile, records.length);
    return profile;
  }

  /**
   * 更新现有画像
   * @param existingProfile 现有画像
   * @param records 学习记录
   * @param options 分析选项
   * @returns 更新后的画像
   */
  private async updateProfile(
    existingProfile: LearnerProfile,
    records: LearningRecord[],
    options: ProfileAnalysisOptions
  ): Promise<LearnerProfile> {
    // 获取新的学习记录（自上次分析以来）
    const newRecords = records.filter(record => 
      record.timestamp > existingProfile.metadata.lastAnalysisDate
    );

    if (newRecords.length === 0) {
      this.logger.debug(`用户 ${existingProfile.userId} 没有新的学习记录，跳过更新`);
      return existingProfile;
    }

    // 增量更新画像
    const updatedProfile = { ...existingProfile };
    updatedProfile.updatedAt = new Date();
    updatedProfile.metadata.lastAnalysisDate = new Date();
    updatedProfile.metadata.analysisCount++;

    // 更新各个维度
    updatedProfile.learningPreferences = this.updateLearningPreferences(
      existingProfile.learningPreferences,
      newRecords
    );

    updatedProfile.knowledgeAreas = this.updateKnowledgeAreas(
      existingProfile.knowledgeAreas,
      newRecords
    );

    updatedProfile.behaviorPatterns = this.updateBehaviorPatterns(
      existingProfile.behaviorPatterns,
      newRecords
    );

    updatedProfile.emotionalTraits = this.updateEmotionalTraits(
      existingProfile.emotionalTraits,
      newRecords
    );

    updatedProfile.learningOutcomes = this.updateLearningOutcomes(
      existingProfile.learningOutcomes,
      newRecords
    );

    // 重新计算置信度
    updatedProfile.metadata.confidenceScore = this.calculateConfidence(
      updatedProfile, 
      records.length
    );

    return updatedProfile;
  }

  /**
   * 分析学习偏好
   * @param records 学习记录
   * @returns 学习偏好
   */
  private analyzeLearningPreferences(records: LearningRecord[]): LearningPreferences {
    const contentTypes = new Map<string, number>();
    const sessionDurations: number[] = [];
    const interactionCounts: number[] = [];
    const timeOfDayMap = new Map<string, number>();

    records.forEach(record => {
      try {
        const context = record.context ? JSON.parse(record.context) : {};
        const result = record.result ? JSON.parse(record.result) : {};

        // 分析内容类型偏好
        const contentType = context.extensions?.['http://dl-engine.com/xapi/extensions/content-type'];
        if (contentType) {
          contentTypes.set(contentType, (contentTypes.get(contentType) || 0) + 1);
        }

        // 分析会话时长
        if (result.duration) {
          const duration = this.parseDuration(result.duration);
          sessionDurations.push(duration / 60000); // 转换为分钟
        }

        // 分析学习时间偏好
        const hour = record.timestamp.getHours();
        const timeOfDay = this.getTimeOfDay(hour);
        timeOfDayMap.set(timeOfDay, (timeOfDayMap.get(timeOfDay) || 0) + 1);

        // 统计交互次数
        if (record.verb.includes('interacted') || record.verb.includes('asked')) {
          interactionCounts.push(1);
        }
      } catch (error) {
        this.logger.warn(`解析学习记录失败: ${record.id}`, error);
      }
    });

    return {
      preferredContentTypes: Array.from(contentTypes.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3)
        .map(([type]) => type),
      learningPace: this.inferLearningPace(sessionDurations),
      interactionStyle: this.inferInteractionStyle(records),
      attentionSpan: this.calculateAverageAttentionSpan(sessionDurations),
      preferredDifficulty: this.inferPreferredDifficulty(records),
      sessionDuration: sessionDurations.length > 0 
        ? sessionDurations.reduce((a, b) => a + b, 0) / sessionDurations.length 
        : 30,
      preferredTimeOfDay: this.getMostFrequentTimeOfDay(timeOfDayMap),
      breakFrequency: this.calculateBreakFrequency(records)
    };
  }

  /**
   * 评估知识领域
   * @param records 学习记录
   * @returns 知识领域映射
   */
  private assessKnowledgeAreas(records: LearningRecord[]): { [subject: string]: KnowledgeArea } {
    const knowledgeAreas: { [subject: string]: KnowledgeArea } = {};

    records.forEach(record => {
      try {
        const context = record.context ? JSON.parse(record.context) : {};
        const result = record.result ? JSON.parse(record.result) : {};
        const knowledgeArea = context.extensions?.['http://dl-engine.com/xapi/extensions/knowledge-area'];

        if (knowledgeArea) {
          if (!knowledgeAreas[knowledgeArea]) {
            knowledgeAreas[knowledgeArea] = {
              level: 'beginner',
              confidence: 0,
              lastAssessed: new Date(),
              weakPoints: [],
              strengths: [],
              masteredConcepts: [],
              strugglingConcepts: [],
              progressTrend: 'stable',
              timeSpent: 0
            };
          }

          // 更新知识领域评估
          this.updateKnowledgeAreaAssessment(knowledgeAreas[knowledgeArea], record);
        }
      } catch (error) {
        this.logger.warn(`评估知识领域失败: ${record.id}`, error);
      }
    });

    return knowledgeAreas;
  }

  /**
   * 更新知识领域评估
   * @param area 知识领域
   * @param record 学习记录
   */
  private updateKnowledgeAreaAssessment(area: KnowledgeArea, record: LearningRecord): void {
    try {
      const result = record.result ? JSON.parse(record.result) : {};

      // 根据学习结果更新置信度
      if (result.success !== undefined) {
        const adjustment = result.success ? 0.1 : -0.05;
        area.confidence = Math.max(0, Math.min(1, area.confidence + adjustment));
      }

      // 根据分数更新等级
      if (result.score?.scaled !== undefined) {
        const score = result.score.scaled;
        if (score >= 0.8 && area.confidence > 0.7) {
          area.level = 'advanced';
        } else if (score >= 0.6 && area.confidence > 0.5) {
          area.level = 'intermediate';
        } else {
          area.level = 'beginner';
        }
      }

      // 更新时间花费
      if (result.duration) {
        area.timeSpent += this.parseDuration(result.duration) / 60000; // 转换为分钟
      }

      area.lastAssessed = record.timestamp;
    } catch (error) {
      this.logger.warn(`更新知识领域评估失败: ${record.id}`, error);
    }
  }

  /**
   * 分析行为模式
   * @param records 学习记录
   * @returns 行为模式
   */
  private analyzeBehaviorPatterns(records: LearningRecord[]): BehaviorPatterns {
    const sessions = new Set<string>();
    const emotions = new Map<string, number>();
    let questionCount = 0;
    let completedPaths = 0;
    let totalPaths = 0;
    let acceptedRecommendations = 0;
    let totalRecommendations = 0;

    records.forEach(record => {
      try {
        const context = record.context ? JSON.parse(record.context) : {};
        const result = record.result ? JSON.parse(record.result) : {};

        // 统计会话
        const sessionId = context.extensions?.['http://dl-engine.com/xapi/extensions/session-id'];
        if (sessionId) {
          sessions.add(sessionId);
        }

        // 统计情感
        const emotion = result.extensions?.['http://dl-engine.com/xapi/extensions/emotion'];
        if (emotion) {
          emotions.set(emotion, (emotions.get(emotion) || 0) + 1);
        }

        // 统计提问
        if (record.verb.includes('asked')) {
          questionCount++;
        }

        // 统计路径完成
        if (record.verb.includes('followed-path')) {
          totalPaths++;
          if (result.completion) {
            completedPaths++;
          }
        }

        // 统计推荐接受
        if (record.verb.includes('received-recommendation')) {
          totalRecommendations++;
          if (result.response === 'accepted') {
            acceptedRecommendations++;
          }
        }
      } catch (error) {
        this.logger.warn(`分析行为模式失败: ${record.id}`, error);
      }
    });

    // 计算会话频率（假设数据跨度为一周）
    const sessionFrequency = sessions.size;
    const questionsPerSession = sessions.size > 0 ? questionCount / sessions.size : 0;
    const pathCompletionRate = totalPaths > 0 ? completedPaths / totalPaths : 0;
    const recommendationAcceptanceRate = totalRecommendations > 0 
      ? acceptedRecommendations / totalRecommendations 
      : 0;

    // 转换情感频率为相对频率
    const totalEmotions = Array.from(emotions.values()).reduce((a, b) => a + b, 0);
    const emotionalStates: { [emotion: string]: number } = {};
    emotions.forEach((count, emotion) => {
      emotionalStates[emotion] = totalEmotions > 0 ? count / totalEmotions : 0;
    });

    return {
      sessionFrequency,
      questionsPerSession,
      emotionalStates,
      pathCompletionRate,
      recommendationAcceptanceRate,
      helpSeekingBehavior: this.inferHelpSeekingBehavior(questionsPerSession),
      explorationTendency: this.calculateExplorationTendency(records),
      persistenceLevel: this.calculatePersistenceLevel(records),
      collaborationPreference: this.calculateCollaborationPreference(records),
      feedbackSensitivity: this.calculateFeedbackSensitivity(records)
    };
  }

  /**
   * 推断学习节奏
   * @param durations 持续时间数组（分钟）
   * @returns 学习节奏
   */
  private inferLearningPace(durations: number[]): 'slow' | 'medium' | 'fast' {
    if (durations.length === 0) return 'medium';

    const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;

    if (avgDuration > 45) return 'slow';
    if (avgDuration > 20) return 'medium';
    return 'fast';
  }

  /**
   * 推断交互风格
   * @param records 学习记录
   * @returns 交互风格
   */
  private inferInteractionStyle(records: LearningRecord[]): 'passive' | 'active' | 'exploratory' {
    const questionCount = records.filter(r => r.verb.includes('asked')).length;
    const explorationCount = records.filter(r => r.verb.includes('explored')).length;
    const totalRecords = records.length;

    if (totalRecords === 0) return 'passive';

    const questionRatio = questionCount / totalRecords;
    const explorationRatio = explorationCount / totalRecords;

    if (explorationRatio > 0.3) return 'exploratory';
    if (questionRatio > 0.2) return 'active';
    return 'passive';
  }

  /**
   * 解析ISO 8601持续时间
   * @param duration ISO 8601持续时间字符串
   * @returns 毫秒数
   */
  private parseDuration(duration: string): number {
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return 0;

    const hours = parseInt(match[1] || '0', 10);
    const minutes = parseInt(match[2] || '0', 10);
    const seconds = parseInt(match[3] || '0', 10);

    return (hours * 3600 + minutes * 60 + seconds) * 1000;
  }

  /**
   * 获取时间段
   * @param hour 小时
   * @returns 时间段
   */
  private getTimeOfDay(hour: number): 'morning' | 'afternoon' | 'evening' | 'night' {
    if (hour >= 6 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 18) return 'afternoon';
    if (hour >= 18 && hour < 22) return 'evening';
    return 'night';
  }

  /**
   * 计算平均注意力持续时间
   * @param durations 持续时间数组（分钟）
   * @returns 平均注意力持续时间
   */
  private calculateAverageAttentionSpan(durations: number[]): number {
    if (durations.length === 0) return 30;
    
    // 过滤异常值（超过2小时的会话）
    const filteredDurations = durations.filter(d => d <= 120);
    
    return filteredDurations.length > 0
      ? filteredDurations.reduce((a, b) => a + b, 0) / filteredDurations.length
      : 30;
  }

  /**
   * 推断偏好难度
   * @param records 学习记录
   * @returns 偏好难度
   */
  private inferPreferredDifficulty(records: LearningRecord[]): 'easy' | 'medium' | 'hard' {
    const difficultyScores: number[] = [];

    records.forEach(record => {
      try {
        const result = record.result ? JSON.parse(record.result) : {};
        const context = record.context ? JSON.parse(record.context) : {};

        const difficulty = context.extensions?.['http://dl-engine.com/xapi/extensions/difficulty'];
        const success = result.success;

        if (difficulty && success !== undefined) {
          let score = 0;
          if (difficulty === 'easy') score = 1;
          else if (difficulty === 'medium') score = 2;
          else if (difficulty === 'hard') score = 3;

          // 如果成功完成，增加该难度的权重
          if (success) {
            difficultyScores.push(score);
          }
        }
      } catch (error) {
        // 忽略解析错误
      }
    });

    if (difficultyScores.length === 0) return 'medium';

    const avgScore = difficultyScores.reduce((a, b) => a + b, 0) / difficultyScores.length;

    if (avgScore >= 2.5) return 'hard';
    if (avgScore >= 1.5) return 'medium';
    return 'easy';
  }

  /**
   * 获取最频繁的学习时间
   * @param timeOfDayMap 时间段映射
   * @returns 最频繁的时间段
   */
  private getMostFrequentTimeOfDay(timeOfDayMap: Map<string, number>): 'morning' | 'afternoon' | 'evening' | 'night' {
    if (timeOfDayMap.size === 0) return 'afternoon';

    let maxCount = 0;
    let mostFrequent: 'morning' | 'afternoon' | 'evening' | 'night' = 'afternoon';

    timeOfDayMap.forEach((count, timeOfDay) => {
      if (count > maxCount) {
        maxCount = count;
        mostFrequent = timeOfDay as any;
      }
    });

    return mostFrequent;
  }

  /**
   * 计算休息频率
   * @param records 学习记录
   * @returns 休息频率（分钟）
   */
  private calculateBreakFrequency(records: LearningRecord[]): number {
    // 基于会话间隔计算休息频率
    const sessions = new Map<string, Date[]>();

    records.forEach(record => {
      try {
        const context = record.context ? JSON.parse(record.context) : {};
        const sessionId = context.extensions?.['http://dl-engine.com/xapi/extensions/session-id'];

        if (sessionId) {
          if (!sessions.has(sessionId)) {
            sessions.set(sessionId, []);
          }
          sessions.get(sessionId)!.push(record.timestamp);
        }
      } catch (error) {
        // 忽略解析错误
      }
    });

    const intervals: number[] = [];
    sessions.forEach(timestamps => {
      timestamps.sort((a, b) => a.getTime() - b.getTime());
      for (let i = 1; i < timestamps.length; i++) {
        const interval = (timestamps[i].getTime() - timestamps[i-1].getTime()) / 60000; // 分钟
        if (interval > 5 && interval < 120) { // 5分钟到2小时之间的间隔
          intervals.push(interval);
        }
      }
    });

    return intervals.length > 0
      ? intervals.reduce((a, b) => a + b, 0) / intervals.length
      : 30;
  }

  /**
   * 推断求助行为
   * @param questionsPerSession 每会话问题数
   * @returns 求助行为类型
   */
  private inferHelpSeekingBehavior(questionsPerSession: number): 'frequent' | 'moderate' | 'rare' {
    if (questionsPerSession > 5) return 'frequent';
    if (questionsPerSession > 2) return 'moderate';
    return 'rare';
  }

  /**
   * 计算探索倾向
   * @param records 学习记录
   * @returns 探索倾向分数
   */
  private calculateExplorationTendency(records: LearningRecord[]): number {
    const explorationActions = records.filter(r =>
      r.verb.includes('explored') || r.verb.includes('interacted')
    ).length;

    return records.length > 0 ? Math.min(1, explorationActions / records.length) : 0;
  }

  /**
   * 计算坚持程度
   * @param records 学习记录
   * @returns 坚持程度分数
   */
  private calculatePersistenceLevel(records: LearningRecord[]): number {
    let completedActivities = 0;
    let totalActivities = 0;

    records.forEach(record => {
      try {
        const result = record.result ? JSON.parse(record.result) : {};
        if (result.completion !== undefined) {
          totalActivities++;
          if (result.completion) {
            completedActivities++;
          }
        }
      } catch (error) {
        // 忽略解析错误
      }
    });

    return totalActivities > 0 ? completedActivities / totalActivities : 0.5;
  }

  /**
   * 计算协作偏好
   * @param records 学习记录
   * @returns 协作偏好分数
   */
  private calculateCollaborationPreference(records: LearningRecord[]): number {
    // 基于社交相关的学习活动计算
    const socialActions = records.filter(r =>
      r.verb.includes('shared') || r.verb.includes('commented') || r.verb.includes('collaborated')
    ).length;

    return records.length > 0 ? Math.min(1, socialActions / records.length * 2) : 0.3;
  }

  /**
   * 计算反馈敏感度
   * @param records 学习记录
   * @returns 反馈敏感度分数
   */
  private calculateFeedbackSensitivity(records: LearningRecord[]): number {
    // 基于对推荐和反馈的响应计算
    let feedbackResponses = 0;
    let totalFeedback = 0;

    records.forEach(record => {
      try {
        if (record.verb.includes('received-recommendation')) {
          totalFeedback++;
          const result = record.result ? JSON.parse(record.result) : {};
          if (result.response && result.response !== 'ignored') {
            feedbackResponses++;
          }
        }
      } catch (error) {
        // 忽略解析错误
      }
    });

    return totalFeedback > 0 ? feedbackResponses / totalFeedback : 0.5;
  }

  /**
   * 推断学习目标
   * @param records 学习记录
   * @returns 学习目标
   */
  private inferLearningGoals(records: LearningRecord[]): any {
    // 基于学习活动模式推断目标
    const knowledgeAreas = new Set<string>();
    const skillActivities = records.filter(r => r.verb.includes('practiced') || r.verb.includes('applied')).length;
    const certificationActivities = records.filter(r => r.verb.includes('tested') || r.verb.includes('assessed')).length;

    records.forEach(record => {
      try {
        const context = record.context ? JSON.parse(record.context) : {};
        const area = context.extensions?.['http://dl-engine.com/xapi/extensions/knowledge-area'];
        if (area) {
          knowledgeAreas.add(area);
        }
      } catch (error) {
        // 忽略解析错误
      }
    });

    let priority: 'knowledge' | 'skills' | 'certification' = 'knowledge';
    if (certificationActivities > skillActivities && certificationActivities > knowledgeAreas.size) {
      priority = 'certification';
    } else if (skillActivities > knowledgeAreas.size) {
      priority = 'skills';
    }

    return {
      shortTerm: Array.from(knowledgeAreas).slice(0, 3),
      longTerm: Array.from(knowledgeAreas),
      priority,
      motivationType: 'mixed',
      difficultyPreference: 'mixed'
    };
  }

  /**
   * 分析社交学习特征
   * @param records 学习记录
   * @returns 社交学习特征
   */
  private analyzeSocialLearning(records: LearningRecord[]): any {
    const socialActivities = records.filter(r =>
      r.verb.includes('shared') || r.verb.includes('commented') || r.verb.includes('collaborated')
    ).length;

    return {
      collaborationPreference: socialActivities > records.length * 0.3 ? 'small_group' : 'individual',
      communicationStyle: 'mixed',
      feedbackPreference: 'immediate',
      leadershipTendency: Math.min(1, socialActivities / Math.max(records.length, 1)),
      mentorshipInterest: 0.5,
      peerLearningEffectiveness: 0.7
    };
  }

  /**
   * 评估认知特征
   * @param records 学习记录
   * @returns 认知特征
   */
  private assessCognitiveTraits(records: LearningRecord[]): CognitiveTraits {
    // 基于学习行为推断认知特征
    const avgResponseTime = this.calculateAverageResponseTime(records);
    const problemSolvingActivities = records.filter(r =>
      r.verb.includes('solved') || r.verb.includes('analyzed')
    ).length;

    return {
      processingSpeed: avgResponseTime < 30 ? 'fast' : avgResponseTime < 60 ? 'medium' : 'slow',
      memoryRetention: 'average',
      abstractThinking: Math.min(1, problemSolvingActivities / Math.max(records.length, 1)),
      analyticalSkills: Math.min(1, problemSolvingActivities / Math.max(records.length, 1) * 1.5),
      creativityLevel: 0.6,
      problemSolvingStyle: 'mixed',
      learningStyle: 'mixed'
    };
  }

  /**
   * 评估情感特征
   * @param records 学习记录
   * @returns 情感特征
   */
  private assessEmotionalTraits(records: LearningRecord[]): EmotionalTraits {
    const emotions = new Map<string, number>();

    records.forEach(record => {
      try {
        const result = record.result ? JSON.parse(record.result) : {};
        const emotion = result.extensions?.['http://dl-engine.com/xapi/extensions/emotion'];
        if (emotion) {
          emotions.set(emotion, (emotions.get(emotion) || 0) + 1);
        }
      } catch (error) {
        // 忽略解析错误
      }
    });

    const dominantEmotions = Array.from(emotions.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([emotion]) => emotion);

    const positiveEmotions = ['happy', 'excited', 'confident', 'satisfied'].filter(e => emotions.has(e));
    const negativeEmotions = ['frustrated', 'confused', 'anxious', 'bored'].filter(e => emotions.has(e));

    const emotionalStability = positiveEmotions.length > negativeEmotions.length ? 0.7 : 0.4;

    return {
      dominantEmotions,
      emotionalStability,
      stressResilience: 0.6,
      motivationLevel: 0.7,
      confidenceLevel: 0.6,
      frustrationTolerance: 0.5,
      curiosityLevel: 0.8
    };
  }

  /**
   * 计算学习成果
   * @param records 学习记录
   * @returns 学习成果
   */
  private calculateLearningOutcomes(records: LearningRecord[]): any {
    let totalScore = 0;
    let scoreCount = 0;
    let completedActivities = 0;
    const skills = new Set<string>();

    records.forEach(record => {
      try {
        const result = record.result ? JSON.parse(record.result) : {};

        if (result.score?.scaled !== undefined) {
          totalScore += result.score.scaled;
          scoreCount++;
        }

        if (result.completion) {
          completedActivities++;
        }

        // 提取技能信息
        const context = record.context ? JSON.parse(record.context) : {};
        const skill = context.extensions?.['http://dl-engine.com/xapi/extensions/skill'];
        if (skill) {
          skills.add(skill);
        }
      } catch (error) {
        // 忽略解析错误
      }
    });

    return {
      totalActivitiesCompleted: completedActivities,
      averageScore: scoreCount > 0 ? totalScore / scoreCount : 0,
      improvementRate: 0.1, // 需要基于历史数据计算
      skillsAcquired: Array.from(skills),
      certificationsEarned: [],
      milestonesAchieved: [],
      timeToMastery: {}
    };
  }

  /**
   * 计算平均响应时间
   * @param records 学习记录
   * @returns 平均响应时间（秒）
   */
  private calculateAverageResponseTime(records: LearningRecord[]): number {
    const responseTimes: number[] = [];

    records.forEach(record => {
      try {
        const result = record.result ? JSON.parse(record.result) : {};
        const decisionTime = result.extensions?.['http://dl-engine.com/xapi/extensions/decision-time'];
        if (decisionTime && typeof decisionTime === 'number') {
          responseTimes.push(decisionTime / 1000); // 转换为秒
        }
      } catch (error) {
        // 忽略解析错误
      }
    });

    return responseTimes.length > 0
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
      : 45;
  }

  /**
   * 评估数据质量
   * @param records 学习记录
   * @returns 数据质量分数
   */
  private assessDataQuality(records: LearningRecord[]): number {
    if (records.length === 0) return 0;

    let qualityScore = 0;
    let validRecords = 0;

    records.forEach(record => {
      let recordScore = 0;

      // 检查必要字段
      if (record.userId) recordScore += 0.2;
      if (record.verb) recordScore += 0.2;
      if (record.timestamp) recordScore += 0.2;

      // 检查结果数据
      try {
        const result = record.result ? JSON.parse(record.result) : null;
        if (result) recordScore += 0.2;

        const context = record.context ? JSON.parse(record.context) : null;
        if (context) recordScore += 0.2;

        validRecords++;
        qualityScore += recordScore;
      } catch (error) {
        // 解析失败，质量较低
        qualityScore += recordScore * 0.5;
      }
    });

    return validRecords > 0 ? qualityScore / validRecords : 0;
  }

  /**
   * 计算置信度
   * @param profile 学习者画像
   * @param recordCount 记录数量
   * @returns 置信度分数
   */
  private calculateConfidence(profile: LearnerProfile, recordCount: number): number {
    let confidence = 0;

    // 基于数据量的置信度
    const dataConfidence = Math.min(1, recordCount / 100); // 100条记录为满分
    confidence += dataConfidence * 0.4;

    // 基于数据质量的置信度
    confidence += profile.metadata.dataQuality * 0.3;

    // 基于分析深度的置信度
    const knowledgeAreaCount = Object.keys(profile.knowledgeAreas).length;
    const analysisDepth = Math.min(1, knowledgeAreaCount / 5); // 5个知识领域为满分
    confidence += analysisDepth * 0.3;

    return Math.min(1, confidence);
  }

  /**
   * 识别薄弱环节
   * @param profile 学习者画像
   * @returns 薄弱环节数组
   */
  private identifyWeakAreas(profile: LearnerProfile): WeakArea[] {
    const weakAreas: WeakArea[] = [];

    Object.entries(profile.knowledgeAreas).forEach(([subject, knowledge]) => {
      if (knowledge.confidence < 0.6 || knowledge.weakPoints.length > 0) {
        const priority = this.calculateWeakAreaPriority(knowledge, profile.learningGoals);
        const urgency = this.assessWeakAreaUrgency(knowledge);

        weakAreas.push({
          subject,
          confidence: knowledge.confidence,
          weakPoints: knowledge.weakPoints,
          priority,
          urgency,
          recommendedActions: this.generateRecommendedActions(knowledge),
          estimatedImprovementTime: this.estimateImprovementTime(knowledge)
        });
      }
    });

    return weakAreas.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 计算薄弱环节优先级
   * @param knowledge 知识领域
   * @param goals 学习目标
   * @returns 优先级分数
   */
  private calculateWeakAreaPriority(knowledge: KnowledgeArea, goals: any): number {
    let priority = 0;

    // 基于置信度的优先级
    priority += (1 - knowledge.confidence) * 50;

    // 基于薄弱点数量的优先级
    priority += knowledge.weakPoints.length * 10;

    // 基于学习目标的优先级
    if (goals.shortTerm.includes(knowledge)) {
      priority += 30;
    }

    return priority;
  }

  /**
   * 评估薄弱环节紧急程度
   * @param knowledge 知识领域
   * @returns 紧急程度
   */
  private assessWeakAreaUrgency(knowledge: KnowledgeArea): 'low' | 'medium' | 'high' {
    if (knowledge.confidence < 0.3) return 'high';
    if (knowledge.weakPoints.length > 2) return 'medium';
    return 'low';
  }

  /**
   * 生成推荐行动
   * @param knowledge 知识领域
   * @returns 推荐行动数组
   */
  private generateRecommendedActions(knowledge: KnowledgeArea): string[] {
    const actions: string[] = [];

    if (knowledge.confidence < 0.4) {
      actions.push('加强基础知识学习');
      actions.push('增加练习时间');
    }

    if (knowledge.weakPoints.length > 0) {
      actions.push(`重点关注：${knowledge.weakPoints.join('、')}`);
    }

    if (knowledge.level === 'beginner') {
      actions.push('从简单内容开始，循序渐进');
    }

    return actions;
  }

  /**
   * 估算改进时间
   * @param knowledge 知识领域
   * @returns 预计改进时间（天）
   */
  private estimateImprovementTime(knowledge: KnowledgeArea): number {
    let days = 7; // 基础时间

    // 根据当前水平调整
    if (knowledge.level === 'beginner') days += 14;
    else if (knowledge.level === 'intermediate') days += 7;

    // 根据薄弱点数量调整
    days += knowledge.weakPoints.length * 3;

    // 根据置信度调整
    days += (1 - knowledge.confidence) * 10;

    return Math.min(90, days); // 最多90天
  }

  /**
   * 生成学习路径建议
   * @param profile 学习者画像
   * @param weakAreas 薄弱环节
   * @returns 学习路径建议数组
   */
  private async generatePathSuggestions(
    profile: LearnerProfile,
    weakAreas: WeakArea[]
  ): Promise<any[]> {
    // 这里应该调用内容推荐服务
    // 暂时返回模拟数据
    return weakAreas.slice(0, 3).map((area, index) => ({
      pathId: `path_${area.subject}_${index}`,
      name: `${area.subject}强化学习路径`,
      description: `针对${area.subject}领域的个性化学习路径`,
      difficulty: this.getRecommendedDifficulty(profile, area),
      estimatedDuration: area.estimatedImprovementTime * 2, // 小时
      prerequisites: [],
      learningObjectives: area.recommendedActions,
      suitabilityScore: 1 - area.confidence,
      reasons: [`您在${area.subject}领域需要加强`, ...area.recommendedActions]
    }));
  }

  /**
   * 获取推荐难度
   * @param profile 学习者画像
   * @param area 薄弱环节
   * @returns 推荐难度
   */
  private getRecommendedDifficulty(profile: LearnerProfile, area: WeakArea): 'easy' | 'medium' | 'hard' {
    if (area.confidence < 0.3) return 'easy';
    if (profile.learningPreferences.preferredDifficulty === 'hard' && area.confidence > 0.5) return 'hard';
    return 'medium';
  }

  /**
   * 生成个性化建议
   * @param profile 学习者画像
   * @returns 个性化建议数组
   */
  private generatePersonalizationSuggestions(profile: LearnerProfile): any[] {
    const suggestions: any[] = [];

    // 学习节奏建议
    if (profile.learningPreferences.learningPace === 'fast') {
      suggestions.push({
        type: 'pace',
        suggestion: '建议适当放慢学习节奏，增加复习时间',
        rationale: '您的学习节奏较快，适当放慢有助于知识巩固',
        expectedImpact: 0.7,
        implementationDifficulty: 'easy',
        priority: 6
      });
    }

    // 交互风格建议
    if (profile.learningPreferences.interactionStyle === 'passive') {
      suggestions.push({
        type: 'method',
        suggestion: '增加互动练习和主动提问',
        rationale: '您偏向被动学习，增加互动有助于提高参与度',
        expectedImpact: 0.8,
        implementationDifficulty: 'medium',
        priority: 8
      });
    }

    // 学习时间建议
    suggestions.push({
      type: 'schedule',
      suggestion: `建议在${profile.learningPreferences.preferredTimeOfDay}时段学习`,
      rationale: '根据您的学习习惯，这个时段效果最佳',
      expectedImpact: 0.6,
      implementationDifficulty: 'easy',
      priority: 5
    });

    return suggestions.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 生成洞察
   * @param profile 学习者画像
   * @param records 学习记录
   * @returns 洞察数组
   */
  private generateInsights(profile: LearnerProfile, records: LearningRecord[]): string[] {
    const insights: string[] = [];

    // 学习习惯洞察
    insights.push(`您偏好${profile.learningPreferences.learningPace}节奏的学习方式`);
    insights.push(`您的平均学习会话时长为${profile.learningPreferences.sessionDuration.toFixed(0)}分钟`);

    // 知识掌握洞察
    const knowledgeAreaCount = Object.keys(profile.knowledgeAreas).length;
    if (knowledgeAreaCount > 0) {
      insights.push(`您目前涉及${knowledgeAreaCount}个知识领域`);

      const avgConfidence = Object.values(profile.knowledgeAreas)
        .reduce((sum, area) => sum + area.confidence, 0) / knowledgeAreaCount;
      insights.push(`您的平均知识掌握度为${(avgConfidence * 100).toFixed(0)}%`);
    }

    // 行为模式洞察
    if (profile.behaviorPatterns.recommendationAcceptanceRate > 0.7) {
      insights.push('您对系统推荐的接受度较高，善于采纳建议');
    } else if (profile.behaviorPatterns.recommendationAcceptanceRate < 0.3) {
      insights.push('您对推荐内容较为挑剔，有自己的学习偏好');
    }

    // 情感状态洞察
    if (profile.emotionalTraits.dominantEmotions.length > 0) {
      insights.push(`您在学习中主要表现出${profile.emotionalTraits.dominantEmotions.join('、')}的情感状态`);
    }

    return insights;
  }

  /**
   * 保存画像到数据库
   * @param profile 学习者画像
   */
  private async saveProfile(profile: LearnerProfile): Promise<void> {
    const entity = await this.learnerProfileRepository.findOne({
      where: { userId: profile.userId }
    });

    if (entity) {
      // 更新现有画像
      Object.assign(entity, this.profileToEntity(profile));
      await this.learnerProfileRepository.save(entity);
    } else {
      // 创建新画像
      const newEntity = this.learnerProfileRepository.create(this.profileToEntity(profile));
      await this.learnerProfileRepository.save(newEntity);
    }
  }

  /**
   * 画像转实体
   * @param profile 学习者画像
   * @returns 实体对象
   */
  private profileToEntity(profile: LearnerProfile): Partial<LearnerProfileEntity> {
    return {
      userId: profile.userId,
      demographics: JSON.stringify(profile.demographics),
      learningPreferences: JSON.stringify(profile.learningPreferences),
      knowledgeAreas: JSON.stringify(profile.knowledgeAreas),
      behaviorPatterns: JSON.stringify(profile.behaviorPatterns),
      learningGoals: JSON.stringify(profile.learningGoals),
      socialLearning: JSON.stringify(profile.socialLearning),
      cognitiveTraits: JSON.stringify(profile.cognitiveTraits),
      emotionalTraits: JSON.stringify(profile.emotionalTraits),
      learningOutcomes: JSON.stringify(profile.learningOutcomes),
      metadata: JSON.stringify(profile.metadata),
      updatedAt: profile.updatedAt
    };
  }

  /**
   * 实体转画像
   * @param entity 实体对象
   * @returns 学习者画像
   */
  private entityToProfile(entity: LearnerProfileEntity): LearnerProfile {
    return {
      userId: entity.userId,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      demographics: JSON.parse(entity.demographics || '{}'),
      learningPreferences: JSON.parse(entity.learningPreferences || '{}'),
      knowledgeAreas: JSON.parse(entity.knowledgeAreas || '{}'),
      behaviorPatterns: JSON.parse(entity.behaviorPatterns || '{}'),
      learningGoals: JSON.parse(entity.learningGoals || '{}'),
      socialLearning: JSON.parse(entity.socialLearning || '{}'),
      cognitiveTraits: JSON.parse(entity.cognitiveTraits || '{}'),
      emotionalTraits: JSON.parse(entity.emotionalTraits || '{}'),
      learningOutcomes: JSON.parse(entity.learningOutcomes || '{}'),
      metadata: JSON.parse(entity.metadata || '{}')
    };
  }

  /**
   * 推断人口统计信息
   * @param userId 用户ID
   * @param records 学习记录
   * @returns 人口统计信息
   */
  private async inferDemographics(userId: string, records: LearningRecord[]): Promise<any> {
    // 这里可以从用户服务获取基本信息
    // 暂时返回默认值
    return {
      age: undefined,
      education: undefined,
      profession: undefined,
      location: undefined,
      timezone: 'Asia/Shanghai',
      language: 'zh-CN'
    };
  }

  /**
   * 更新学习偏好（增量更新）
   * @param existing 现有偏好
   * @param newRecords 新记录
   * @returns 更新后的偏好
   */
  private updateLearningPreferences(existing: LearningPreferences, newRecords: LearningRecord[]): LearningPreferences {
    // 基于新记录增量更新偏好
    const newPreferences = this.analyzeLearningPreferences(newRecords);

    // 加权平均更新
    const weight = 0.3; // 新数据权重

    return {
      ...existing,
      sessionDuration: existing.sessionDuration * (1 - weight) + newPreferences.sessionDuration * weight,
      attentionSpan: existing.attentionSpan * (1 - weight) + newPreferences.attentionSpan * weight,
      breakFrequency: existing.breakFrequency * (1 - weight) + newPreferences.breakFrequency * weight,
      // 其他字段保持不变或根据新数据更新
      learningPace: newPreferences.learningPace || existing.learningPace,
      interactionStyle: newPreferences.interactionStyle || existing.interactionStyle,
      preferredDifficulty: newPreferences.preferredDifficulty || existing.preferredDifficulty,
      preferredTimeOfDay: newPreferences.preferredTimeOfDay || existing.preferredTimeOfDay,
      preferredContentTypes: this.mergeContentTypes(existing.preferredContentTypes, newPreferences.preferredContentTypes)
    };
  }

  /**
   * 合并内容类型偏好
   * @param existing 现有类型
   * @param newTypes 新类型
   * @returns 合并后的类型
   */
  private mergeContentTypes(existing: string[], newTypes: string[]): string[] {
    const merged = [...existing];
    newTypes.forEach(type => {
      if (!merged.includes(type)) {
        merged.push(type);
      }
    });
    return merged.slice(0, 5); // 保持前5个
  }

  /**
   * 更新知识领域（增量更新）
   * @param existing 现有知识领域
   * @param newRecords 新记录
   * @returns 更新后的知识领域
   */
  private updateKnowledgeAreas(
    existing: { [subject: string]: KnowledgeArea },
    newRecords: LearningRecord[]
  ): { [subject: string]: KnowledgeArea } {
    const updated = { ...existing };

    newRecords.forEach(record => {
      try {
        const context = record.context ? JSON.parse(record.context) : {};
        const knowledgeArea = context.extensions?.['http://dl-engine.com/xapi/extensions/knowledge-area'];

        if (knowledgeArea) {
          if (!updated[knowledgeArea]) {
            updated[knowledgeArea] = {
              level: 'beginner',
              confidence: 0,
              lastAssessed: new Date(),
              weakPoints: [],
              strengths: [],
              masteredConcepts: [],
              strugglingConcepts: [],
              progressTrend: 'stable',
              timeSpent: 0
            };
          }

          this.updateKnowledgeAreaAssessment(updated[knowledgeArea], record);
        }
      } catch (error) {
        this.logger.warn(`更新知识领域失败: ${record.id}`, error);
      }
    });

    return updated;
  }

  /**
   * 更新行为模式（增量更新）
   * @param existing 现有行为模式
   * @param newRecords 新记录
   * @returns 更新后的行为模式
   */
  private updateBehaviorPatterns(existing: BehaviorPatterns, newRecords: LearningRecord[]): BehaviorPatterns {
    const newPatterns = this.analyzeBehaviorPatterns(newRecords);
    const weight = 0.2; // 新数据权重

    return {
      ...existing,
      sessionFrequency: existing.sessionFrequency * (1 - weight) + newPatterns.sessionFrequency * weight,
      questionsPerSession: existing.questionsPerSession * (1 - weight) + newPatterns.questionsPerSession * weight,
      pathCompletionRate: existing.pathCompletionRate * (1 - weight) + newPatterns.pathCompletionRate * weight,
      recommendationAcceptanceRate: existing.recommendationAcceptanceRate * (1 - weight) + newPatterns.recommendationAcceptanceRate * weight,
      explorationTendency: existing.explorationTendency * (1 - weight) + newPatterns.explorationTendency * weight,
      persistenceLevel: existing.persistenceLevel * (1 - weight) + newPatterns.persistenceLevel * weight,
      collaborationPreference: existing.collaborationPreference * (1 - weight) + newPatterns.collaborationPreference * weight,
      feedbackSensitivity: existing.feedbackSensitivity * (1 - weight) + newPatterns.feedbackSensitivity * weight,
      emotionalStates: this.mergeEmotionalStates(existing.emotionalStates, newPatterns.emotionalStates),
      helpSeekingBehavior: newPatterns.helpSeekingBehavior || existing.helpSeekingBehavior
    };
  }

  /**
   * 合并情感状态
   * @param existing 现有情感状态
   * @param newStates 新情感状态
   * @returns 合并后的情感状态
   */
  private mergeEmotionalStates(
    existing: { [emotion: string]: number },
    newStates: { [emotion: string]: number }
  ): { [emotion: string]: number } {
    const merged = { ...existing };
    const weight = 0.3;

    Object.entries(newStates).forEach(([emotion, frequency]) => {
      if (merged[emotion]) {
        merged[emotion] = merged[emotion] * (1 - weight) + frequency * weight;
      } else {
        merged[emotion] = frequency * weight;
      }
    });

    return merged;
  }

  /**
   * 更新情感特征（增量更新）
   * @param existing 现有情感特征
   * @param newRecords 新记录
   * @returns 更新后的情感特征
   */
  private updateEmotionalTraits(existing: EmotionalTraits, newRecords: LearningRecord[]): EmotionalTraits {
    const newTraits = this.assessEmotionalTraits(newRecords);
    const weight = 0.2;

    return {
      ...existing,
      emotionalStability: existing.emotionalStability * (1 - weight) + newTraits.emotionalStability * weight,
      motivationLevel: existing.motivationLevel * (1 - weight) + newTraits.motivationLevel * weight,
      confidenceLevel: existing.confidenceLevel * (1 - weight) + newTraits.confidenceLevel * weight,
      frustrationTolerance: existing.frustrationTolerance * (1 - weight) + newTraits.frustrationTolerance * weight,
      curiosityLevel: existing.curiosityLevel * (1 - weight) + newTraits.curiosityLevel * weight,
      stressResilience: existing.stressResilience * (1 - weight) + newTraits.stressResilience * weight,
      dominantEmotions: this.mergeDominantEmotions(existing.dominantEmotions, newTraits.dominantEmotions)
    };
  }

  /**
   * 合并主导情感
   * @param existing 现有主导情感
   * @param newEmotions 新主导情感
   * @returns 合并后的主导情感
   */
  private mergeDominantEmotions(existing: string[], newEmotions: string[]): string[] {
    const merged = [...existing];
    newEmotions.forEach(emotion => {
      if (!merged.includes(emotion)) {
        merged.push(emotion);
      }
    });
    return merged.slice(0, 5); // 保持前5个
  }

  /**
   * 更新学习成果（增量更新）
   * @param existing 现有学习成果
   * @param newRecords 新记录
   * @returns 更新后的学习成果
   */
  private updateLearningOutcomes(existing: any, newRecords: LearningRecord[]): any {
    const newOutcomes = this.calculateLearningOutcomes(newRecords);

    return {
      ...existing,
      totalActivitiesCompleted: existing.totalActivitiesCompleted + newOutcomes.totalActivitiesCompleted,
      averageScore: this.calculateWeightedAverage(
        existing.averageScore,
        existing.totalActivitiesCompleted,
        newOutcomes.averageScore,
        newOutcomes.totalActivitiesCompleted
      ),
      skillsAcquired: this.mergeSkills(existing.skillsAcquired, newOutcomes.skillsAcquired)
    };
  }

  /**
   * 计算加权平均
   * @param oldValue 旧值
   * @param oldCount 旧计数
   * @param newValue 新值
   * @param newCount 新计数
   * @returns 加权平均值
   */
  private calculateWeightedAverage(oldValue: number, oldCount: number, newValue: number, newCount: number): number {
    const totalCount = oldCount + newCount;
    return totalCount > 0 ? (oldValue * oldCount + newValue * newCount) / totalCount : 0;
  }

  /**
   * 合并技能
   * @param existing 现有技能
   * @param newSkills 新技能
   * @returns 合并后的技能
   */
  private mergeSkills(existing: string[], newSkills: string[]): string[] {
    const merged = [...existing];
    newSkills.forEach(skill => {
      if (!merged.includes(skill)) {
        merged.push(skill);
      }
    });
    return merged;
  }
}
}
