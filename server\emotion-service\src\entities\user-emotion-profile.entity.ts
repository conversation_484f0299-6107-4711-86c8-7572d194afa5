/**
 * 用户情感档案实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('user_emotion_profiles')
export class UserEmotionProfile {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  @Index()
  userId: string;

  @Column({ type: 'varchar', length: 100 })
  dominantEmotion: string;

  @Column({ type: 'decimal', precision: 5, scale: 3 })
  averageIntensity: number;

  @Column({ type: 'json' })
  emotionCounts: { [emotionType: string]: number };

  @Column({ type: 'int', default: 0 })
  totalInteractions: number;

  @Column({ type: 'timestamp' })
  lastUpdated: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
