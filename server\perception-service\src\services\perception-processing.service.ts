/**
 * 感知数据处理服务
 *
 * 提供大规模感知数据的实时处理、分析和存储功能。
 * 支持多模态数据融合、异常检测、预测分析等高级功能。
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import Redis from 'ioredis';
import {
  PerceptionData,
  FusedPerceptionData,
  PerceptionModality,
  PerceptionAnomaly
} from '../../../engine/src/ai/perception/MultiModalPerceptionSystem';

/**
 * 感知数据实体
 */
export interface PerceptionDataEntity {
  id: string;
  entityId: string;
  sessionId: string;
  modality: PerceptionModality;
  timestamp: number;
  confidence: number;
  data: any;
  metadata: any;
  processed: boolean;
  anomalies: PerceptionAnomaly[];
}

/**
 * 融合感知数据实体
 */
export interface FusedPerceptionDataEntity {
  id: string;
  entityId: string;
  sessionId: string;
  timestamp: number;
  confidence: number;
  worldModel: any;
  attentionFocus: any[];
  predictions: any[];
  anomalies: PerceptionAnomaly[];
  sourceDataIds: string[];
}

/**
 * 感知统计数据
 */
export interface PerceptionStatistics {
  totalPerceptions: number;
  modalityBreakdown: { [key: string]: number };
  averageConfidence: number;
  anomalyRate: number;
  processingLatency: number;
  fusionRate: number;
  dataQuality: number;
}

/**
 * 感知处理配置
 */
export interface PerceptionProcessingConfig {
  enableRealTimeProcessing: boolean;
  batchSize: number;
  fusionThreshold: number;
  anomalyThreshold: number;
  retentionPeriod: number;
  qualityThreshold: number;
  enablePrediction: boolean;
  predictionHorizon: number;
}

/**
 * 感知数据处理服务
 */
@Injectable()
export class PerceptionProcessingService {
  private readonly logger = new Logger(PerceptionProcessingService.name);
  private readonly redis: Redis;

  private config: PerceptionProcessingConfig = {
    enableRealTimeProcessing: true,
    batchSize: 100,
    fusionThreshold: 0.7,
    anomalyThreshold: 0.3,
    retentionPeriod: 7 * 24 * 60 * 60 * 1000, // 7天
    qualityThreshold: 0.6,
    enablePrediction: true,
    predictionHorizon: 5000 // 5秒
  };

  private processingQueue: PerceptionDataEntity[] = [];
  private fusionQueue: PerceptionDataEntity[] = [];
  private statistics: PerceptionStatistics = {
    totalPerceptions: 0,
    modalityBreakdown: {},
    averageConfidence: 0,
    anomalyRate: 0,
    processingLatency: 0,
    fusionRate: 0,
    dataQuality: 0
  };

  private isProcessing = false;

  constructor(
    private readonly eventEmitter: EventEmitter2,
    redisConfig: any
  ) {
    this.redis = new Redis(redisConfig);
    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private async initializeService(): Promise<void> {
    try {
      // 设置消息监听
      await this.setupMessageHandlers();

      // 启动处理循环
      this.startProcessingLoop();

      this.logger.log('感知数据处理服务已启动');

    } catch (error) {
      this.logger.error('服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置消息处理器
   */
  private async setupMessageHandlers(): Promise<void> {
    // 监听感知数据
    this.redis.subscribe('perception:data');
    this.redis.subscribe('perception:batch');

    this.redis.on('message', async (channel, message) => {
      try {
        const data = JSON.parse(message);

        switch (channel) {
          case 'perception:data':
            await this.handlePerceptionData(data);
            break;
          case 'perception:batch':
            await this.handleBatchData(data);
            break;
        }
      } catch (error) {
        this.logger.error('消息处理失败:', error);
      }
    });
  }

  /**
   * 启动处理循环
   */
  private startProcessingLoop(): void {
    setInterval(async () => {
      if (!this.isProcessing && this.processingQueue.length > 0) {
        await this.processQueuedData();
      }
    }, 100); // 100ms间隔

    // 融合处理循环
    setInterval(async () => {
      if (this.fusionQueue.length >= 2) {
        await this.processFusion();
      }
    }, 500); // 500ms间隔
  }

  /**
   * 处理感知数据
   */
  public async processPerceptionData(data: PerceptionDataEntity): Promise<void> {
    const startTime = Date.now();

    try {
      // 数据验证
      if (!this.validatePerceptionData(data)) {
        throw new Error('感知数据验证失败');
      }

      // 质量评估
      const quality = this.assessDataQuality(data);
      if (quality < this.config.qualityThreshold) {
        this.logger.warn(`数据质量过低: ${quality}, 数据ID: ${data.id}`);
        return;
      }

      // 异常检测
      const anomalies = await this.detectAnomalies(data);
      data.anomalies = anomalies;

      // 数据增强
      await this.enhanceData(data);

      // 存储原始数据
      await this.storePerceptionData(data);

      // 添加到融合队列
      this.fusionQueue.push(data);

      // 更新统计
      this.updateStatistics(data, Date.now() - startTime);

      // 发布处理完成事件
      this.eventEmitter.emit('perception.processed', {
        dataId: data.id,
        entityId: data.entityId,
        modality: data.modality,
        quality,
        anomalies: anomalies.length
      });

      this.logger.debug(`感知数据处理完成: ${data.id}`);

    } catch (error) {
      this.logger.error(`感知数据处理失败 [${data.id}]:`, error);
      throw error;
    }
  }

  /**
   * 处理队列中的数据
   */
  private async processQueuedData(): Promise<void> {
    this.isProcessing = true;

    try {
      const batchSize = Math.min(this.config.batchSize, this.processingQueue.length);
      const batch = this.processingQueue.splice(0, batchSize);

      // 并行处理批次数据
      await Promise.all(batch.map(data => this.processPerceptionData(data)));

      this.logger.debug(`批次处理完成: ${batch.length} 条数据`);

    } catch (error) {
      this.logger.error('批次处理失败:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 处理数据融合
   */
  private async processFusion(): Promise<void> {
    try {
      // 按实体和时间窗口分组
      const groups = this.groupDataForFusion();

      for (const [key, dataList] of groups) {
        if (dataList.length >= 2) {
          const fusedData = await this.fusePerceptionData(dataList);
          await this.storeFusedData(fusedData);

          // 从融合队列中移除已处理的数据
          dataList.forEach(data => {
            const index = this.fusionQueue.indexOf(data);
            if (index !== -1) {
              this.fusionQueue.splice(index, 1);
            }
          });

          // 发布融合完成事件
          this.eventEmitter.emit('perception.fused', {
            fusedDataId: fusedData.id,
            entityId: fusedData.entityId,
            sourceCount: dataList.length,
            confidence: fusedData.confidence
          });
        }
      }

    } catch (error) {
      this.logger.error('数据融合失败:', error);
    }
  }

  /**
   * 验证感知数据
   */
  private validatePerceptionData(data: PerceptionDataEntity): boolean {
    if (!data.id || !data.entityId || !data.modality) {
      return false;
    }

    if (data.confidence < 0 || data.confidence > 1) {
      return false;
    }

    if (!data.timestamp || data.timestamp > Date.now()) {
      return false;
    }

    return true;
  }

  /**
   * 评估数据质量
   */
  private assessDataQuality(data: PerceptionDataEntity): number {
    let quality = data.confidence;

    // 时效性评估
    const age = Date.now() - data.timestamp;
    const timeDecay = Math.exp(-age / 10000); // 10秒衰减
    quality *= timeDecay;

    // 数据完整性评估
    const completeness = this.assessDataCompleteness(data);
    quality *= completeness;

    // 一致性评估
    const consistency = this.assessDataConsistency(data);
    quality *= consistency;

    return Math.max(0, Math.min(1, quality));
  }

  /**
   * 评估数据完整性
   */
  private assessDataCompleteness(data: PerceptionDataEntity): number {
    let completeness = 1.0;

    // 检查必要字段
    if (!data.data) completeness *= 0.5;
    if (!data.metadata) completeness *= 0.8;

    // 检查模态特定字段
    switch (data.modality) {
      case PerceptionModality.VISUAL:
        if (!data.data.objects) completeness *= 0.7;
        if (!data.data.lighting) completeness *= 0.9;
        break;
      case PerceptionModality.AUDITORY:
        if (!data.data.sounds) completeness *= 0.7;
        break;
      case PerceptionModality.SOCIAL:
        if (!data.data.nearbyEntities) completeness *= 0.8;
        break;
    }

    return completeness;
  }

  /**
   * 评估数据一致性
   */
  private assessDataConsistency(data: PerceptionDataEntity): number {
    // 简化的一致性检查
    // 实际实现应该检查数据内部的逻辑一致性
    return 1.0;
  }

  /**
   * 异常检测
   */
  private async detectAnomalies(data: PerceptionDataEntity): Promise<PerceptionAnomaly[]> {
    const anomalies: PerceptionAnomaly[] = [];

    // 置信度异常
    if (data.confidence < this.config.anomalyThreshold) {
      anomalies.push({
        type: 'low_confidence',
        description: `置信度过低: ${data.confidence}`,
        severity: 1 - data.confidence,
        timestamp: data.timestamp
      });
    }

    // 时间异常
    const age = Date.now() - data.timestamp;
    if (age > 60000) { // 超过1分钟
      anomalies.push({
        type: 'stale_data',
        description: `数据过期: ${age}ms`,
        severity: Math.min(1, age / 300000), // 5分钟为最大严重程度
        timestamp: data.timestamp
      });
    }

    // 模态特定异常检测
    const modalityAnomalies = await this.detectModalityAnomalies(data);
    anomalies.push(...modalityAnomalies);

    return anomalies;
  }

  /**
   * 模态特定异常检测
   */
  private async detectModalityAnomalies(data: PerceptionDataEntity): Promise<PerceptionAnomaly[]> {
    const anomalies: PerceptionAnomaly[] = [];

    switch (data.modality) {
      case PerceptionModality.VISUAL:
        // 视觉异常检测
        if (data.data.objects && data.data.objects.length > 100) {
          anomalies.push({
            type: 'excessive_objects',
            description: `检测到过多对象: ${data.data.objects.length}`,
            severity: 0.6,
            timestamp: data.timestamp
          });
        }
        break;

      case PerceptionModality.AUDITORY:
        // 听觉异常检测
        if (data.data.ambientNoise > 0.9) {
          anomalies.push({
            type: 'high_noise',
            description: `环境噪音过高: ${data.data.ambientNoise}`,
            severity: data.data.ambientNoise,
            timestamp: data.timestamp
          });
        }
        break;

      case PerceptionModality.SOCIAL:
        // 社交异常检测
        if (data.data.nearbyEntities && data.data.nearbyEntities.length > 50) {
          anomalies.push({
            type: 'crowded_environment',
            description: `环境过于拥挤: ${data.data.nearbyEntities.length} 个实体`,
            severity: 0.7,
            timestamp: data.timestamp
          });
        }
        break;
    }

    return anomalies;
  }

  /**
   * 数据增强
   */
  private async enhanceData(data: PerceptionDataEntity): Promise<void> {
    // 添加上下文信息
    data.metadata = {
      ...data.metadata,
      processedAt: Date.now(),
      quality: this.assessDataQuality(data),
      enhanced: true
    };

    // 模态特定增强
    switch (data.modality) {
      case PerceptionModality.VISUAL:
        await this.enhanceVisualData(data);
        break;
      case PerceptionModality.AUDITORY:
        await this.enhanceAuditoryData(data);
        break;
      case PerceptionModality.SOCIAL:
        await this.enhanceSocialData(data);
        break;
    }
  }

  /**
   * 增强视觉数据
   */
  private async enhanceVisualData(data: PerceptionDataEntity): Promise<void> {
    if (data.data.objects) {
      // 对象分类和标注
      data.data.objects = data.data.objects.map((obj: any) => ({
        ...obj,
        category: this.classifyObject(obj),
        importance: this.calculateObjectImportance(obj)
      }));
    }
  }

  /**
   * 增强听觉数据
   */
  private async enhanceAuditoryData(data: PerceptionDataEntity): Promise<void> {
    if (data.data.sounds) {
      // 声音分类和分析
      data.data.sounds = data.data.sounds.map((sound: any) => ({
        ...sound,
        category: this.classifySound(sound),
        urgency: this.calculateSoundUrgency(sound)
      }));
    }
  }

  /**
   * 增强社交数据
   */
  private async enhanceSocialData(data: PerceptionDataEntity): Promise<void> {
    if (data.data.nearbyEntities) {
      // 社交关系分析
      data.data.socialAnalysis = {
        groupCount: this.countGroups(data.data.nearbyEntities),
        averageDistance: this.calculateAverageDistance(data.data.nearbyEntities),
        interactionLevel: this.assessInteractionLevel(data.data.nearbyEntities)
      };
    }
  }

  /**
   * 对象分类
   */
  private classifyObject(obj: any): string {
    // 简化的对象分类
    if (obj.type) return obj.type;
    if (obj.size && obj.size.y > 2) return 'building';
    if (obj.velocity && obj.velocity.length() > 0) return 'moving_object';
    return 'static_object';
  }

  /**
   * 计算对象重要性
   */
  private calculateObjectImportance(obj: any): number {
    let importance = 0.5;

    // 距离因子
    if (obj.distance < 10) importance += 0.3;
    else if (obj.distance < 50) importance += 0.1;

    // 运动因子
    if (obj.velocity && obj.velocity.length() > 0) importance += 0.2;

    // 大小因子
    if (obj.size && obj.size.x * obj.size.y * obj.size.z > 100) importance += 0.1;

    return Math.min(1, importance);
  }

  /**
   * 声音分类
   */
  private classifySound(sound: any): string {
    if (sound.type) return sound.type;
    if (sound.frequency > 2000) return 'high_frequency';
    if (sound.frequency < 200) return 'low_frequency';
    return 'mid_frequency';
  }

  /**
   * 计算声音紧急程度
   */
  private calculateSoundUrgency(sound: any): number {
    let urgency = 0.5;

    // 音量因子
    if (sound.volume > 0.8) urgency += 0.3;
    else if (sound.volume > 0.5) urgency += 0.1;

    // 距离因子
    if (sound.source && sound.source.length() < 20) urgency += 0.2;

    return Math.min(1, urgency);
  }

  /**
   * 计算群体数量
   */
  private countGroups(entities: any[]): number {
    // 简化的群体检测
    return Math.ceil(entities.length / 5);
  }

  /**
   * 计算平均距离
   */
  private calculateAverageDistance(entities: any[]): number {
    if (entities.length === 0) return 0;

    const totalDistance = entities.reduce((sum, entity) => {
      return sum + (entity.distance || 0);
    }, 0);

    return totalDistance / entities.length;
  }

  /**
   * 评估交互水平
   */
  private assessInteractionLevel(entities: any[]): number {
    // 简化的交互水平评估
    const closeEntities = entities.filter(e => e.distance < 5).length;
    return Math.min(1, closeEntities / entities.length);
  }

  /**
   * 按实体和时间窗口分组数据
   */
  private groupDataForFusion(): Map<string, PerceptionDataEntity[]> {
    const groups = new Map<string, PerceptionDataEntity[]>();
    const timeWindow = 1000; // 1秒时间窗口

    for (const data of this.fusionQueue) {
      const timeSlot = Math.floor(data.timestamp / timeWindow);
      const key = `${data.entityId}_${timeSlot}`;

      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(data);
    }

    return groups;
  }

  /**
   * 融合感知数据
   */
  private async fusePerceptionData(dataList: PerceptionDataEntity[]): Promise<FusedPerceptionDataEntity> {
    const entityId = dataList[0].entityId;
    const sessionId = dataList[0].sessionId;

    // 计算融合置信度
    const totalConfidence = dataList.reduce((sum, data) => sum + data.confidence, 0);
    const averageConfidence = totalConfidence / dataList.length;

    // 收集所有异常
    const allAnomalies: PerceptionAnomaly[] = [];
    dataList.forEach(data => allAnomalies.push(...data.anomalies));

    // 构建世界模型
    const worldModel = await this.buildWorldModel(dataList);

    // 生成注意力焦点
    const attentionFocus = this.generateAttentionFocus(dataList);

    // 生成预测
    const predictions = this.config.enablePrediction ?
      await this.generatePredictions(dataList) : [];

    const fusedData: FusedPerceptionDataEntity = {
      id: `fused_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      entityId,
      sessionId,
      timestamp: Math.max(...dataList.map(d => d.timestamp)),
      confidence: averageConfidence,
      worldModel,
      attentionFocus,
      predictions,
      anomalies: allAnomalies,
      sourceDataIds: dataList.map(d => d.id)
    };

    return fusedData;
  }

  /**
   * 构建世界模型
   */
  private async buildWorldModel(dataList: PerceptionDataEntity[]): Promise<any> {
    const worldModel = {
      entities: new Map(),
      environment: {
        lighting: null,
        weather: null,
        obstacles: [],
        resources: [],
        hazards: []
      },
      social: {
        relationships: new Map(),
        groups: [],
        interactions: []
      },
      temporal: {
        currentTime: Date.now(),
        timeOfDay: this.getTimeOfDay(),
        patterns: []
      }
    };

    // 融合不同模态的数据
    for (const data of dataList) {
      switch (data.modality) {
        case PerceptionModality.VISUAL:
          this.mergeVisualData(worldModel, data);
          break;
        case PerceptionModality.AUDITORY:
          this.mergeAuditoryData(worldModel, data);
          break;
        case PerceptionModality.SOCIAL:
          this.mergeSocialData(worldModel, data);
          break;
        case PerceptionModality.ENVIRONMENTAL:
          this.mergeEnvironmentalData(worldModel, data);
          break;
      }
    }

    return worldModel;
  }

  /**
   * 融合视觉数据
   */
  private mergeVisualData(worldModel: any, data: PerceptionDataEntity): void {
    if (data.data.objects) {
      data.data.objects.forEach((obj: any) => {
        worldModel.entities.set(obj.id, {
          id: obj.id,
          type: obj.type,
          position: obj.position,
          confidence: obj.confidence,
          lastSeen: data.timestamp,
          source: 'visual'
        });
      });
    }

    if (data.data.lighting) {
      worldModel.environment.lighting = data.data.lighting;
    }
  }

  /**
   * 融合听觉数据
   */
  private mergeAuditoryData(worldModel: any, data: PerceptionDataEntity): void {
    if (data.data.sounds) {
      data.data.sounds.forEach((sound: any) => {
        // 将声音信息添加到环境模型
        worldModel.environment.sounds = worldModel.environment.sounds || [];
        worldModel.environment.sounds.push({
          ...sound,
          timestamp: data.timestamp,
          source: 'auditory'
        });
      });
    }
  }

  /**
   * 融合社交数据
   */
  private mergeSocialData(worldModel: any, data: PerceptionDataEntity): void {
    if (data.data.nearbyEntities) {
      data.data.nearbyEntities.forEach((entity: any) => {
        worldModel.entities.set(entity.id, {
          ...entity,
          lastSeen: data.timestamp,
          source: 'social'
        });
      });
    }

    if (data.data.interactions) {
      worldModel.social.interactions.push(...data.data.interactions);
    }
  }

  /**
   * 融合环境数据
   */
  private mergeEnvironmentalData(worldModel: any, data: PerceptionDataEntity): void {
    if (data.data.weather) {
      worldModel.environment.weather = data.data.weather;
    }

    if (data.data.obstacles) {
      worldModel.environment.obstacles.push(...data.data.obstacles);
    }
  }

  /**
   * 生成注意力焦点
   */
  private generateAttentionFocus(dataList: PerceptionDataEntity[]): any[] {
    const focuses: any[] = [];

    // 基于异常生成注意力焦点
    dataList.forEach(data => {
      data.anomalies.forEach(anomaly => {
        if (anomaly.severity > 0.5) {
          focuses.push({
            target: data.id,
            type: 'anomaly',
            priority: anomaly.severity,
            reason: anomaly.description,
            duration: 5000
          });
        }
      });
    });

    // 基于重要对象生成注意力焦点
    dataList.forEach(data => {
      if (data.modality === PerceptionModality.VISUAL && data.data.objects) {
        data.data.objects.forEach((obj: any) => {
          if (obj.importance && obj.importance > 0.7) {
            focuses.push({
              target: obj.id,
              type: 'important_object',
              priority: obj.importance,
              reason: `重要对象: ${obj.type}`,
              duration: 3000
            });
          }
        });
      }
    });

    // 按优先级排序并限制数量
    return focuses
      .sort((a, b) => b.priority - a.priority)
      .slice(0, 5);
  }

  /**
   * 生成预测
   */
  private async generatePredictions(dataList: PerceptionDataEntity[]): Promise<any[]> {
    const predictions: any[] = [];

    // 基于运动对象预测位置
    dataList.forEach(data => {
      if (data.modality === PerceptionModality.VISUAL && data.data.objects) {
        data.data.objects.forEach((obj: any) => {
          if (obj.velocity && obj.velocity.length() > 0) {
            const futurePosition = {
              x: obj.position.x + obj.velocity.x * (this.config.predictionHorizon / 1000),
              y: obj.position.y + obj.velocity.y * (this.config.predictionHorizon / 1000),
              z: obj.position.z + obj.velocity.z * (this.config.predictionHorizon / 1000)
            };

            predictions.push({
              type: 'position_prediction',
              target: obj.id,
              prediction: { position: futurePosition },
              confidence: obj.confidence * 0.8, // 预测置信度略低
              timeHorizon: this.config.predictionHorizon
            });
          }
        });
      }
    });

    return predictions;
  }

  /**
   * 获取时间段
   */
  private getTimeOfDay(): string {
    const hour = new Date().getHours();
    if (hour >= 6 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 18) return 'afternoon';
    if (hour >= 18 && hour < 22) return 'evening';
    return 'night';
  }

  /**
   * 存储感知数据
   */
  private async storePerceptionData(data: PerceptionDataEntity): Promise<void> {
    // 存储到Redis缓存
    await this.redis.setex(
      `perception:data:${data.id}`,
      3600, // 1小时过期
      JSON.stringify(data)
    );

    // 添加到实体数据列表
    await this.redis.lpush(
      `perception:entity:${data.entityId}`,
      data.id
    );

    // 限制列表长度
    await this.redis.ltrim(`perception:entity:${data.entityId}`, 0, 999);
  }

  /**
   * 存储融合数据
   */
  private async storeFusedData(data: FusedPerceptionDataEntity): Promise<void> {
    // 存储到Redis缓存
    await this.redis.setex(
      `perception:fused:${data.id}`,
      7200, // 2小时过期
      JSON.stringify(data)
    );

    // 添加到实体融合数据列表
    await this.redis.lpush(
      `perception:fused:entity:${data.entityId}`,
      data.id
    );

    // 限制列表长度
    await this.redis.ltrim(`perception:fused:entity:${data.entityId}`, 0, 99);
  }

  /**
   * 处理感知数据消息
   */
  private async handlePerceptionData(data: any): Promise<void> {
    const perceptionData = data as PerceptionDataEntity;

    if (this.config.enableRealTimeProcessing) {
      // 实时处理
      await this.processPerceptionData(perceptionData);
    } else {
      // 添加到队列
      this.processingQueue.push(perceptionData);
    }
  }

  /**
   * 处理批次数据
   */
  private async handleBatchData(data: any): Promise<void> {
    const batchData = data.data as PerceptionDataEntity[];
    this.processingQueue.push(...batchData);
  }

  /**
   * 更新统计信息
   */
  private updateStatistics(data: PerceptionDataEntity, processingTime: number): void {
    this.statistics.totalPerceptions++;

    // 更新模态统计
    const modality = data.modality;
    this.statistics.modalityBreakdown[modality] =
      (this.statistics.modalityBreakdown[modality] || 0) + 1;

    // 更新平均置信度
    this.statistics.averageConfidence =
      (this.statistics.averageConfidence * (this.statistics.totalPerceptions - 1) + data.confidence) /
      this.statistics.totalPerceptions;

    // 更新异常率
    if (data.anomalies.length > 0) {
      const anomalyCount = this.statistics.anomalyRate * (this.statistics.totalPerceptions - 1) + 1;
      this.statistics.anomalyRate = anomalyCount / this.statistics.totalPerceptions;
    }

    // 更新处理延迟
    this.statistics.processingLatency =
      (this.statistics.processingLatency * (this.statistics.totalPerceptions - 1) + processingTime) /
      this.statistics.totalPerceptions;

    // 更新数据质量
    const quality = this.assessDataQuality(data);
    this.statistics.dataQuality =
      (this.statistics.dataQuality * (this.statistics.totalPerceptions - 1) + quality) /
      this.statistics.totalPerceptions;
  }

  /**
   * 获取统计信息
   */
  public getStatistics(): PerceptionStatistics {
    return { ...this.statistics };
  }

  /**
   * 获取实体感知数据
   */
  public async getEntityPerceptionData(entityId: string, limit: number = 10): Promise<PerceptionDataEntity[]> {
    const dataIds = await this.redis.lrange(`perception:entity:${entityId}`, 0, limit - 1);
    const dataList: PerceptionDataEntity[] = [];

    for (const id of dataIds) {
      const dataStr = await this.redis.get(`perception:data:${id}`);
      if (dataStr) {
        dataList.push(JSON.parse(dataStr));
      }
    }

    return dataList;
  }

  /**
   * 获取实体融合数据
   */
  public async getEntityFusedData(entityId: string, limit: number = 10): Promise<FusedPerceptionDataEntity[]> {
    const dataIds = await this.redis.lrange(`perception:fused:entity:${entityId}`, 0, limit - 1);
    const dataList: FusedPerceptionDataEntity[] = [];

    for (const id of dataIds) {
      const dataStr = await this.redis.get(`perception:fused:${id}`);
      if (dataStr) {
        dataList.push(JSON.parse(dataStr));
      }
    }

    return dataList;
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<PerceptionProcessingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.logger.log('感知处理配置已更新');
  }

  /**
   * 定期清理过期数据
   */
  @Cron(CronExpression.EVERY_HOUR)
  private async cleanupExpiredData(): Promise<void> {
    try {
      const cutoffTime = Date.now() - this.config.retentionPeriod;

      // 清理过期的感知数据
      // 这里应该实现具体的清理逻辑

      this.logger.log('过期数据清理完成');

    } catch (error) {
      this.logger.error('数据清理失败:', error);
    }
  }

  /**
   * 关闭服务
   */
  public async shutdown(): Promise<void> {
    this.logger.log('正在关闭感知数据处理服务...');

    // 处理剩余队列数据
    if (this.processingQueue.length > 0) {
      await this.processQueuedData();
    }

    // 关闭Redis连接
    this.redis.disconnect();

    this.logger.log('感知数据处理服务已关闭');
  }
}