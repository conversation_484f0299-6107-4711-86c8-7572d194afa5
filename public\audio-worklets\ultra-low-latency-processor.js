/**
 * 超低延迟音频处理器
 * 实现零缓冲音频处理，延迟控制在1-2ms
 */
class UltraLowLatencyProcessor extends AudioWorkletProcessor {
  constructor(options) {
    super();
    
    // 处理器配置
    this.bufferSize = options.processorOptions?.bufferSize || 64;
    this.latencyMode = options.processorOptions?.latencyMode || 'ultra-low';
    this.channels = options.processorOptions?.channels || 2;
    this.sampleRate = options.processorOptions?.sampleRate || 48000;
    
    // 性能监控
    this.processCount = 0;
    this.totalProcessTime = 0;
    this.maxProcessTime = 0;
    this.lastReportTime = 0;
    
    // 音频处理状态
    this.inputBuffer = new Float32Array(this.bufferSize * this.channels);
    this.outputBuffer = new Float32Array(this.bufferSize * this.channels);
    this.bufferIndex = 0;
    
    // 延迟测量
    this.latencyMeasurements = [];
    this.measurementInterval = 1000; // 每秒测量一次
    
    // 音频处理参数
    this.gain = 1.0;
    this.enableProcessing = true;
    this.enableLatencyMeasurement = true;
    
    // 监听来自主线程的消息
    this.port.onmessage = (event) => {
      this.handleMessage(event.data);
    };
    
    console.log(`UltraLowLatencyProcessor initialized: ${this.bufferSize} samples, ${this.sampleRate}Hz`);
  }

  static get parameterDescriptors() {
    return [
      {
        name: 'gain',
        defaultValue: 1.0,
        minValue: 0.0,
        maxValue: 2.0,
        automationRate: 'a-rate'
      },
      {
        name: 'latencyCompensation',
        defaultValue: 0.0,
        minValue: -10.0,
        maxValue: 10.0,
        automationRate: 'k-rate'
      }
    ];
  }

  process(inputs, outputs, parameters) {
    const startTime = performance.now();
    
    const input = inputs[0];
    const output = outputs[0];
    
    if (!input || !output || input.length === 0 || output.length === 0) {
      return true;
    }

    const inputChannel0 = input[0];
    const inputChannel1 = input[1] || inputChannel0; // 单声道时复制到双声道
    const outputChannel0 = output[0];
    const outputChannel1 = output[1] || outputChannel0;

    const frameLength = inputChannel0.length;
    const gainValue = parameters.gain[0];

    // 超低延迟音频处理
    if (this.enableProcessing) {
      this.processAudioFrames(
        inputChannel0, inputChannel1,
        outputChannel0, outputChannel1,
        frameLength, gainValue
      );
    } else {
      // 直通模式，零延迟
      this.passthroughAudio(
        inputChannel0, inputChannel1,
        outputChannel0, outputChannel1,
        frameLength
      );
    }

    // 性能监控
    const processTime = performance.now() - startTime;
    this.updatePerformanceMetrics(processTime);

    // 延迟测量
    if (this.enableLatencyMeasurement) {
      this.measureLatency(frameLength);
    }

    this.processCount++;
    return true;
  }

  processAudioFrames(inputL, inputR, outputL, outputR, frameLength, gain) {
    // 超低延迟音频处理算法
    for (let i = 0; i < frameLength; i++) {
      // 简单的增益控制
      let sampleL = inputL[i] * gain;
      let sampleR = inputR[i] * gain;

      // 可选的音频处理效果
      if (this.latencyMode === 'ultra-low') {
        // 最小处理，保持超低延迟
        outputL[i] = sampleL;
        outputR[i] = sampleR;
      } else {
        // 可以添加更多处理，但会增加延迟
        outputL[i] = this.applyMinimalProcessing(sampleL);
        outputR[i] = this.applyMinimalProcessing(sampleR);
      }
    }
  }

  passthroughAudio(inputL, inputR, outputL, outputR, frameLength) {
    // 零延迟直通
    for (let i = 0; i < frameLength; i++) {
      outputL[i] = inputL[i];
      outputR[i] = inputR[i];
    }
  }

  applyMinimalProcessing(sample) {
    // 最小化的音频处理
    // 可以添加轻量级的降噪、压缩等
    
    // 简单的软限制器
    if (sample > 0.95) {
      sample = 0.95 + (sample - 0.95) * 0.1;
    } else if (sample < -0.95) {
      sample = -0.95 + (sample + 0.95) * 0.1;
    }
    
    return sample;
  }

  measureLatency(frameLength) {
    const currentTime = performance.now();
    
    // 计算理论延迟
    const theoreticalLatency = (frameLength / this.sampleRate) * 1000; // ms
    
    // 记录延迟测量
    this.latencyMeasurements.push({
      timestamp: currentTime,
      theoreticalLatency,
      bufferSize: frameLength,
      processTime: this.totalProcessTime / this.processCount
    });

    // 限制测量历史长度
    if (this.latencyMeasurements.length > 100) {
      this.latencyMeasurements.shift();
    }

    // 定期报告延迟统计
    if (currentTime - this.lastReportTime > this.measurementInterval) {
      this.reportLatencyStatistics();
      this.lastReportTime = currentTime;
    }
  }

  updatePerformanceMetrics(processTime) {
    this.totalProcessTime += processTime;
    this.maxProcessTime = Math.max(this.maxProcessTime, processTime);
  }

  reportLatencyStatistics() {
    if (this.latencyMeasurements.length === 0) return;

    const recent = this.latencyMeasurements.slice(-10);
    const avgLatency = recent.reduce((sum, m) => sum + m.theoreticalLatency, 0) / recent.length;
    const avgProcessTime = this.totalProcessTime / this.processCount;
    
    const statistics = {
      type: 'latency-statistics',
      averageLatency: avgLatency,
      averageProcessTime: avgProcessTime,
      maxProcessTime: this.maxProcessTime,
      bufferSize: this.bufferSize,
      sampleRate: this.sampleRate,
      processCount: this.processCount,
      timestamp: performance.now()
    };

    // 发送统计信息到主线程
    this.port.postMessage(statistics);

    // 重置统计
    this.totalProcessTime = 0;
    this.maxProcessTime = 0;
    this.processCount = 0;
  }

  handleMessage(data) {
    switch (data.type) {
      case 'set-gain':
        this.gain = data.value;
        break;
        
      case 'set-processing-enabled':
        this.enableProcessing = data.enabled;
        break;
        
      case 'set-latency-mode':
        this.latencyMode = data.mode;
        break;
        
      case 'set-buffer-size':
        this.bufferSize = data.size;
        this.inputBuffer = new Float32Array(this.bufferSize * this.channels);
        this.outputBuffer = new Float32Array(this.bufferSize * this.channels);
        break;
        
      case 'get-statistics':
        this.reportLatencyStatistics();
        break;
        
      case 'reset-statistics':
        this.latencyMeasurements = [];
        this.totalProcessTime = 0;
        this.maxProcessTime = 0;
        this.processCount = 0;
        break;
        
      case 'enable-latency-measurement':
        this.enableLatencyMeasurement = data.enabled;
        break;
        
      default:
        console.warn('Unknown message type:', data.type);
    }
  }

  // 优化的数学函数
  fastSin(x) {
    // 快速正弦近似，用于音频效果
    const x2 = x * x;
    return x * (1 - x2 / 6 + x2 * x2 / 120);
  }

  fastCos(x) {
    // 快速余弦近似
    const x2 = x * x;
    return 1 - x2 / 2 + x2 * x2 / 24;
  }

  // 简单的IIR滤波器（低延迟）
  applyLowPassFilter(sample, cutoff = 0.1) {
    // 简单的一阶低通滤波器
    this.filterState = this.filterState || 0;
    this.filterState += cutoff * (sample - this.filterState);
    return this.filterState;
  }

  // 动态范围压缩（最小延迟）
  applyCompression(sample, threshold = 0.7, ratio = 4) {
    const absample = Math.abs(sample);
    if (absample > threshold) {
      const excess = absample - threshold;
      const compressedExcess = excess / ratio;
      const compressedSample = threshold + compressedExcess;
      return sample >= 0 ? compressedSample : -compressedSample;
    }
    return sample;
  }

  // 噪声门（去除低电平噪声）
  applyNoiseGate(sample, threshold = 0.01) {
    return Math.abs(sample) > threshold ? sample : 0;
  }

  // 立体声宽度控制
  applyStereoWidth(sampleL, sampleR, width = 1.0) {
    const mid = (sampleL + sampleR) * 0.5;
    const side = (sampleL - sampleR) * 0.5 * width;
    return {
      left: mid + side,
      right: mid - side
    };
  }
}

// 注册音频工作线程处理器
registerProcessor('ultra-low-latency-processor', UltraLowLatencyProcessor);
