import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as fs from 'fs-extra';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

/**
 * 口型类型（基于国际音标）
 */
export enum VisemeType {
  SILENCE = 'silence',    // 静音
  AA = 'aa',             // 开口音 /a/
  EE = 'ee',             // 闭口音 /i/
  IH = 'ih',             // 半开音 /ɪ/
  OH = 'oh',             // 圆唇音 /o/
  OU = 'ou',             // 圆唇音 /u/
  PP = 'pp',             // 双唇音 /p/, /b/, /m/
  FF = 'ff',             // 唇齿音 /f/, /v/
  TH = 'th',             // 舌齿音 /θ/, /ð/
  DD = 'dd',             // 舌尖音 /t/, /d/, /n/, /l/
  KK = 'kk',             // 舌根音 /k/, /g/
  CH = 'ch',             // 舌面音 /tʃ/, /dʒ/
  SS = 'ss',             // 摩擦音 /s/, /z/
  NN = 'nn',             // 鼻音 /n/, /ŋ/
  RR = 'rr',             // 颤音 /r/
  MM = 'mm',             // 双唇鼻音 /m/
}

/**
 * 中文音素到口型映射
 */
const CHINESE_PHONEME_VISEME_MAP: Record<string, VisemeType> = {
  // 单韵母
  'a': VisemeType.AA,
  'o': VisemeType.OH,
  'e': VisemeType.EE,
  'i': VisemeType.IH,
  'u': VisemeType.OU,
  'ü': VisemeType.IH,
  
  // 复韵母
  'ai': VisemeType.AA,
  'ei': VisemeType.EE,
  'ao': VisemeType.AA,
  'ou': VisemeType.OU,
  
  // 鼻韵母
  'an': VisemeType.AA,
  'en': VisemeType.EE,
  'ang': VisemeType.AA,
  'eng': VisemeType.EE,
  'er': VisemeType.RR,
  
  // 声母
  'b': VisemeType.PP,
  'p': VisemeType.PP,
  'm': VisemeType.MM,
  'f': VisemeType.FF,
  'd': VisemeType.DD,
  't': VisemeType.DD,
  'n': VisemeType.NN,
  'l': VisemeType.DD,
  'g': VisemeType.KK,
  'k': VisemeType.KK,
  'h': VisemeType.KK,
  'j': VisemeType.CH,
  'q': VisemeType.CH,
  'x': VisemeType.SS,
  'zh': VisemeType.CH,
  'ch': VisemeType.CH,
  'sh': VisemeType.SS,
  'r': VisemeType.RR,
  'z': VisemeType.SS,
  'c': VisemeType.SS,
  's': VisemeType.SS,
};

/**
 * 口型关键帧
 */
export interface VisemeKeyframe {
  time: number;
  viseme: VisemeType;
  intensity: number;
  duration: number;
}

/**
 * 嘴形同步数据
 */
export interface LipSyncData {
  id: string;
  duration: number;
  keyframes: VisemeKeyframe[];
  sampleRate: number;
  language: string;
  method: 'phoneme' | 'audio' | 'hybrid';
  confidence: number;
}

/**
 * 音频分析结果
 */
export interface AudioAnalysisResult {
  phonemes: Array<{
    phoneme: string;
    startTime: number;
    endTime: number;
    confidence: number;
  }>;
  volume: number[];
  pitch: number[];
  formants: Array<{
    f1: number;
    f2: number;
    f3: number;
    time: number;
  }>;
}

/**
 * 嘴形同步配置
 */
export interface LipSyncConfig {
  method: 'phoneme' | 'audio' | 'hybrid';
  language: string;
  smoothing: number;
  intensity: number;
  frameRate: number;
  enableFormantAnalysis?: boolean;
  enablePitchAnalysis?: boolean;
}

@Injectable()
export class LipSyncService {
  private tempDir: string;

  constructor(
    private configService: ConfigService,
    private eventEmitter: EventEmitter2,
  ) {
    this.tempDir = this.configService.get<string>('TEMP_DIR', './temp');
    this.ensureTempDir();
  }

  /**
   * 确保临时目录存在
   */
  private async ensureTempDir(): Promise<void> {
    await fs.ensureDir(this.tempDir);
  }

  /**
   * 生成嘴形同步数据
   */
  async generateLipSync(
    audioBuffer: Buffer,
    text: string,
    config: LipSyncConfig,
  ): Promise<LipSyncData> {
    const id = uuidv4();

    try {
      switch (config.method) {
        case 'phoneme':
          return await this.generateFromPhonemes(id, text, config);
        case 'audio':
          return await this.generateFromAudio(id, audioBuffer, config);
        case 'hybrid':
          return await this.generateHybrid(id, audioBuffer, text, config);
        default:
          throw new BadRequestException(`不支持的嘴形同步方法: ${config.method}`);
      }
    } catch (error) {
      throw new BadRequestException(`生成嘴形同步数据失败: ${error.message}`);
    }
  }

  /**
   * 基于音素生成嘴形同步
   */
  private async generateFromPhonemes(
    id: string,
    text: string,
    config: LipSyncConfig,
  ): Promise<LipSyncData> {
    // 分析文本音素
    const phonemes = this.analyzeTextPhonemes(text, config.language);
    
    // 生成关键帧
    const keyframes = this.generateKeyframesFromPhonemes(phonemes, config);
    
    // 计算总时长
    const duration = keyframes.length > 0 
      ? keyframes[keyframes.length - 1].time + keyframes[keyframes.length - 1].duration
      : 0;

    return {
      id,
      duration,
      keyframes,
      sampleRate: config.frameRate,
      language: config.language,
      method: 'phoneme',
      confidence: 0.8,
    };
  }

  /**
   * 基于音频生成嘴形同步
   */
  private async generateFromAudio(
    id: string,
    audioBuffer: Buffer,
    config: LipSyncConfig,
  ): Promise<LipSyncData> {
    // 分析音频
    const analysis = await this.analyzeAudio(audioBuffer, config);
    
    // 生成关键帧
    const keyframes = this.generateKeyframesFromAudio(analysis, config);
    
    return {
      id,
      duration: analysis.phonemes.length > 0 
        ? analysis.phonemes[analysis.phonemes.length - 1].endTime
        : 0,
      keyframes,
      sampleRate: config.frameRate,
      language: config.language,
      method: 'audio',
      confidence: 0.9,
    };
  }

  /**
   * 混合方法生成嘴形同步
   */
  private async generateHybrid(
    id: string,
    audioBuffer: Buffer,
    text: string,
    config: LipSyncConfig,
  ): Promise<LipSyncData> {
    // 同时使用音素和音频分析
    const [phonemeData, audioData] = await Promise.all([
      this.generateFromPhonemes(id + '_phoneme', text, config),
      this.generateFromAudio(id + '_audio', audioBuffer, config),
    ]);

    // 融合两种方法的结果
    const keyframes = this.mergeKeyframes(phonemeData.keyframes, audioData.keyframes, config);

    return {
      id,
      duration: Math.max(phonemeData.duration, audioData.duration),
      keyframes,
      sampleRate: config.frameRate,
      language: config.language,
      method: 'hybrid',
      confidence: 0.95,
    };
  }

  /**
   * 分析文本音素
   */
  private analyzeTextPhonemes(text: string, language: string): Array<{
    phoneme: string;
    startTime: number;
    endTime: number;
    confidence: number;
  }> {
    const phonemes: Array<{
      phoneme: string;
      startTime: number;
      endTime: number;
      confidence: number;
    }> = [];

    if (language.startsWith('zh')) {
      // 中文音素分析
      const chars = text.split('');
      let currentTime = 0;
      const avgCharDuration = 0.3; // 平均每个字符0.3秒

      for (const char of chars) {
        if (/[\s\p{P}]/u.test(char)) {
          // 跳过空格和标点
          currentTime += 0.1;
          continue;
        }

        // 简化的中文音素映射
        const phoneme = this.getChinesePhoneme(char);
        
        phonemes.push({
          phoneme,
          startTime: currentTime,
          endTime: currentTime + avgCharDuration,
          confidence: 0.8,
        });

        currentTime += avgCharDuration;
      }
    } else {
      // 英文音素分析（简化实现）
      const words = text.split(/\s+/);
      let currentTime = 0;
      const avgWordDuration = 0.5;

      for (const word of words) {
        phonemes.push({
          phoneme: word.toLowerCase(),
          startTime: currentTime,
          endTime: currentTime + avgWordDuration,
          confidence: 0.7,
        });

        currentTime += avgWordDuration + 0.1; // 词间停顿
      }
    }

    return phonemes;
  }

  /**
   * 获取中文字符的音素
   */
  private getChinesePhoneme(char: string): string {
    // 这里应该使用专业的中文音素分析库
    // 暂时使用简化映射
    const vowelMap: Record<string, string> = {
      '啊': 'a', '哦': 'o', '额': 'e', '衣': 'i', '乌': 'u',
      '安': 'an', '恩': 'en', '昂': 'ang', '英': 'eng',
    };

    // 检查是否为已知元音
    for (const [key, phoneme] of Object.entries(vowelMap)) {
      if (char.includes(key)) {
        return phoneme;
      }
    }

    // 默认返回通用音素
    return 'a';
  }

  /**
   * 分析音频
   */
  private async analyzeAudio(
    audioBuffer: Buffer,
    config: LipSyncConfig,
  ): Promise<AudioAnalysisResult> {
    // 这里需要实现音频分析
    // 包括音素检测、音量分析、基频分析、共振峰分析等
    // 暂时返回模拟数据

    const duration = 3.0; // 假设3秒音频
    const frameCount = Math.floor(duration * config.frameRate);

    return {
      phonemes: [
        { phoneme: 'a', startTime: 0.0, endTime: 0.5, confidence: 0.9 },
        { phoneme: 'o', startTime: 0.5, endTime: 1.0, confidence: 0.8 },
        { phoneme: 'e', startTime: 1.0, endTime: 1.5, confidence: 0.9 },
        { phoneme: 'i', startTime: 1.5, endTime: 2.0, confidence: 0.7 },
        { phoneme: 'u', startTime: 2.0, endTime: 2.5, confidence: 0.8 },
      ],
      volume: Array.from({ length: frameCount }, () => Math.random() * 0.8 + 0.2),
      pitch: Array.from({ length: frameCount }, () => 200 + Math.random() * 100),
      formants: Array.from({ length: frameCount }, (_, i) => ({
        f1: 500 + Math.random() * 300,
        f2: 1500 + Math.random() * 500,
        f3: 2500 + Math.random() * 500,
        time: i / config.frameRate,
      })),
    };
  }

  /**
   * 从音素生成关键帧
   */
  private generateKeyframesFromPhonemes(
    phonemes: Array<{
      phoneme: string;
      startTime: number;
      endTime: number;
      confidence: number;
    }>,
    config: LipSyncConfig,
  ): VisemeKeyframe[] {
    const keyframes: VisemeKeyframe[] = [];

    for (const phoneme of phonemes) {
      const viseme = this.phonemeToViseme(phoneme.phoneme, config.language);
      const duration = phoneme.endTime - phoneme.startTime;
      const intensity = config.intensity * phoneme.confidence;

      keyframes.push({
        time: phoneme.startTime,
        viseme,
        intensity,
        duration,
      });
    }

    return this.smoothKeyframes(keyframes, config.smoothing);
  }

  /**
   * 从音频分析生成关键帧
   */
  private generateKeyframesFromAudio(
    analysis: AudioAnalysisResult,
    config: LipSyncConfig,
  ): VisemeKeyframe[] {
    const keyframes: VisemeKeyframe[] = [];

    // 基于音素生成主要关键帧
    for (const phoneme of analysis.phonemes) {
      const viseme = this.phonemeToViseme(phoneme.phoneme, config.language);
      const duration = phoneme.endTime - phoneme.startTime;
      const intensity = config.intensity * phoneme.confidence;

      keyframes.push({
        time: phoneme.startTime,
        viseme,
        intensity,
        duration,
      });
    }

    // 基于音量调整强度
    for (let i = 0; i < keyframes.length; i++) {
      const keyframe = keyframes[i];
      const volumeIndex = Math.floor(keyframe.time * config.frameRate);
      
      if (volumeIndex < analysis.volume.length) {
        keyframe.intensity *= analysis.volume[volumeIndex];
      }
    }

    return this.smoothKeyframes(keyframes, config.smoothing);
  }

  /**
   * 音素到口型映射
   */
  private phonemeToViseme(phoneme: string, language: string): VisemeType {
    if (language.startsWith('zh')) {
      return CHINESE_PHONEME_VISEME_MAP[phoneme] || VisemeType.AA;
    } else {
      // 英文音素映射（简化）
      const englishMap: Record<string, VisemeType> = {
        'a': VisemeType.AA,
        'e': VisemeType.EE,
        'i': VisemeType.IH,
        'o': VisemeType.OH,
        'u': VisemeType.OU,
        'p': VisemeType.PP,
        'b': VisemeType.PP,
        'm': VisemeType.MM,
        'f': VisemeType.FF,
        'v': VisemeType.FF,
        't': VisemeType.DD,
        'd': VisemeType.DD,
        'n': VisemeType.NN,
        'l': VisemeType.DD,
        'k': VisemeType.KK,
        'g': VisemeType.KK,
        's': VisemeType.SS,
        'z': VisemeType.SS,
        'r': VisemeType.RR,
      };

      return englishMap[phoneme.toLowerCase()] || VisemeType.AA;
    }
  }

  /**
   * 融合关键帧
   */
  private mergeKeyframes(
    phonemeKeyframes: VisemeKeyframe[],
    audioKeyframes: VisemeKeyframe[],
    config: LipSyncConfig,
  ): VisemeKeyframe[] {
    const merged: VisemeKeyframe[] = [];
    const phonemeWeight = 0.6;
    const audioWeight = 0.4;

    // 简化的融合算法：以音素为主，音频为辅
    for (let i = 0; i < phonemeKeyframes.length; i++) {
      const phonemeKf = phonemeKeyframes[i];
      
      // 找到对应时间的音频关键帧
      const audioKf = audioKeyframes.find(kf => 
        Math.abs(kf.time - phonemeKf.time) < 0.1
      );

      if (audioKf) {
        // 融合强度
        const mergedIntensity = phonemeKf.intensity * phonemeWeight + 
                               audioKf.intensity * audioWeight;
        
        merged.push({
          time: phonemeKf.time,
          viseme: phonemeKf.viseme, // 以音素的口型为准
          intensity: mergedIntensity,
          duration: phonemeKf.duration,
        });
      } else {
        merged.push(phonemeKf);
      }
    }

    return this.smoothKeyframes(merged, config.smoothing);
  }

  /**
   * 平滑关键帧
   */
  private smoothKeyframes(keyframes: VisemeKeyframe[], smoothing: number): VisemeKeyframe[] {
    if (smoothing <= 0 || keyframes.length < 2) {
      return keyframes;
    }

    const smoothed = [...keyframes];

    // 应用移动平均平滑
    for (let i = 1; i < smoothed.length - 1; i++) {
      const prev = keyframes[i - 1];
      const curr = keyframes[i];
      const next = keyframes[i + 1];

      // 平滑强度
      smoothed[i].intensity = 
        prev.intensity * (1 - smoothing) * 0.5 +
        curr.intensity * smoothing +
        next.intensity * (1 - smoothing) * 0.5;
    }

    return smoothed;
  }

  /**
   * 导出为标准格式
   */
  async exportLipSyncData(
    lipSyncData: LipSyncData,
    format: 'json' | 'csv' | 'lpc',
  ): Promise<string> {
    switch (format) {
      case 'json':
        return JSON.stringify(lipSyncData, null, 2);
      
      case 'csv':
        let csv = 'Time,Viseme,Intensity,Duration\n';
        for (const kf of lipSyncData.keyframes) {
          csv += `${kf.time},${kf.viseme},${kf.intensity},${kf.duration}\n`;
        }
        return csv;
      
      case 'lpc':
        // LPC (Linear Predictive Coding) 格式
        return this.exportToLPC(lipSyncData);
      
      default:
        throw new BadRequestException(`不支持的导出格式: ${format}`);
    }
  }

  /**
   * 导出为LPC格式
   */
  private exportToLPC(lipSyncData: LipSyncData): string {
    // 简化的LPC格式实现
    let lpc = `# LipSync Data\n`;
    lpc += `# Duration: ${lipSyncData.duration}\n`;
    lpc += `# Sample Rate: ${lipSyncData.sampleRate}\n`;
    lpc += `# Language: ${lipSyncData.language}\n\n`;

    for (const kf of lipSyncData.keyframes) {
      lpc += `${kf.time.toFixed(3)} ${kf.viseme} ${kf.intensity.toFixed(3)}\n`;
    }

    return lpc;
  }

  /**
   * 获取支持的语言
   */
  getSupportedLanguages(): string[] {
    return ['zh-CN', 'zh-TW', 'en-US', 'en-GB', 'ja-JP', 'ko-KR'];
  }

  /**
   * 获取支持的口型
   */
  getSupportedVisemes(): VisemeType[] {
    return Object.values(VisemeType);
  }

  /**
   * 验证嘴形同步数据
   */
  validateLipSyncData(data: LipSyncData): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!data.id) {
      errors.push('缺少ID');
    }

    if (data.duration <= 0) {
      errors.push('时长必须大于0');
    }

    if (data.keyframes.length === 0) {
      errors.push('必须包含至少一个关键帧');
    }

    // 检查关键帧时间顺序
    for (let i = 1; i < data.keyframes.length; i++) {
      if (data.keyframes[i].time < data.keyframes[i - 1].time) {
        errors.push('关键帧时间必须按顺序排列');
        break;
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
