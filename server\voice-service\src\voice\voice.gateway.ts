import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { VoiceService } from './voice.service';
import { WsJwtGuard } from '../common/guards/ws-jwt.guard';
import {
  SpeechRecognitionConfig,
  SpeechProvider,
} from '../speech-recognition/speech-recognition.service';
import {
  SpeechSynthesisConfig,
  TTSProvider,
} from '../speech-synthesis/speech-synthesis.service';

/**
 * 实时语音会话
 */
interface VoiceSession {
  id: string;
  userId: string;
  recognitionSessionId?: string;
  isRecognizing: boolean;
  isSynthesizing: boolean;
  config: {
    recognition: SpeechRecognitionConfig;
    synthesis: SpeechSynthesisConfig;
  };
}

@WebSocketGateway({
  namespace: '/voice',
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
    credentials: true,
  },
})
@UseGuards(WsJwtGuard)
export class VoiceGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(VoiceGateway.name);
  private sessions: Map<string, VoiceSession> = new Map();

  constructor(private voiceService: VoiceService) {}

  /**
   * 客户端连接
   */
  async handleConnection(client: Socket) {
    try {
      const userId = client.handshake.auth?.userId || client.id;
      
      this.logger.log(`客户端连接: ${client.id}, 用户: ${userId}`);
      
      // 创建语音会话
      const session: VoiceSession = {
        id: client.id,
        userId,
        isRecognizing: false,
        isSynthesizing: false,
        config: {
          recognition: {
            provider: SpeechProvider.AZURE,
            language: 'zh-CN',
            enableWordTimestamps: true,
          },
          synthesis: {
            provider: TTSProvider.AZURE,
            voice: 'zh-CN-XiaoxiaoNeural',
            language: 'zh-CN',
            rate: 1.0,
            pitch: 1.0,
            volume: 1.0,
          },
        },
      };

      this.sessions.set(client.id, session);

      // 发送连接成功消息
      client.emit('connected', {
        sessionId: client.id,
        supportedLanguages: this.voiceService.getSupportedLanguages(SpeechProvider.AZURE),
        availableVoices: await this.voiceService.getAvailableVoices(TTSProvider.AZURE),
      });

    } catch (error) {
      this.logger.error(`连接处理失败: ${error.message}`);
      client.disconnect();
    }
  }

  /**
   * 客户端断开连接
   */
  async handleDisconnect(client: Socket) {
    const session = this.sessions.get(client.id);
    
    if (session) {
      // 停止正在进行的语音识别
      if (session.recognitionSessionId) {
        await this.voiceService.stopRealtimeRecognition(session.recognitionSessionId);
      }

      this.sessions.delete(client.id);
      this.logger.log(`客户端断开连接: ${client.id}`);
    }
  }

  /**
   * 配置语音服务
   */
  @SubscribeMessage('configure')
  async handleConfigure(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: {
      recognition?: Partial<SpeechRecognitionConfig>;
      synthesis?: Partial<SpeechSynthesisConfig>;
    },
  ) {
    const session = this.sessions.get(client.id);
    if (!session) {
      client.emit('error', { message: '会话不存在' });
      return;
    }

    try {
      // 更新配置
      if (data.recognition) {
        session.config.recognition = { ...session.config.recognition, ...data.recognition };
      }
      if (data.synthesis) {
        session.config.synthesis = { ...session.config.synthesis, ...data.synthesis };
      }

      client.emit('configured', {
        recognition: session.config.recognition,
        synthesis: session.config.synthesis,
      });

    } catch (error) {
      client.emit('error', { message: `配置失败: ${error.message}` });
    }
  }

  /**
   * 开始语音识别
   */
  @SubscribeMessage('start-recognition')
  async handleStartRecognition(@ConnectedSocket() client: Socket) {
    const session = this.sessions.get(client.id);
    if (!session) {
      client.emit('error', { message: '会话不存在' });
      return;
    }

    if (session.isRecognizing) {
      client.emit('error', { message: '语音识别已在进行中' });
      return;
    }

    try {
      session.isRecognizing = true;

      // 开始实时语音识别
      session.recognitionSessionId = await this.voiceService.startRealtimeRecognition(
        session.config.recognition,
        (result) => {
          // 发送识别结果
          client.emit('recognition-result', {
            text: result.text,
            confidence: result.confidence,
            isFinal: result.confidence && result.confidence > 0.8,
          });
        },
        (error) => {
          // 发送错误信息
          client.emit('recognition-error', { message: error });
          session.isRecognizing = false;
        },
      );

      client.emit('recognition-started', {
        sessionId: session.recognitionSessionId,
      });

    } catch (error) {
      session.isRecognizing = false;
      client.emit('error', { message: `开始识别失败: ${error.message}` });
    }
  }

  /**
   * 停止语音识别
   */
  @SubscribeMessage('stop-recognition')
  async handleStopRecognition(@ConnectedSocket() client: Socket) {
    const session = this.sessions.get(client.id);
    if (!session) {
      client.emit('error', { message: '会话不存在' });
      return;
    }

    try {
      if (session.recognitionSessionId) {
        await this.voiceService.stopRealtimeRecognition(session.recognitionSessionId);
        session.recognitionSessionId = undefined;
      }

      session.isRecognizing = false;
      client.emit('recognition-stopped');

    } catch (error) {
      client.emit('error', { message: `停止识别失败: ${error.message}` });
    }
  }

  /**
   * 语音合成
   */
  @SubscribeMessage('synthesize')
  async handleSynthesize(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { text: string; config?: Partial<SpeechSynthesisConfig> },
  ) {
    const session = this.sessions.get(client.id);
    if (!session) {
      client.emit('error', { message: '会话不存在' });
      return;
    }

    if (session.isSynthesizing) {
      client.emit('error', { message: '语音合成正在进行中' });
      return;
    }

    try {
      session.isSynthesizing = true;
      client.emit('synthesis-started');

      // 合并配置
      const config = { ...session.config.synthesis, ...data.config };

      // 执行语音合成
      const result = await this.voiceService.synthesizeSpeech(data.text, config);

      // 发送合成结果
      client.emit('synthesis-completed', {
        id: result.id,
        audioData: result.audioData.toString('base64'),
        duration: result.duration,
        format: result.format,
        sampleRate: result.sampleRate,
      });

    } catch (error) {
      client.emit('synthesis-error', { message: `合成失败: ${error.message}` });
    } finally {
      session.isSynthesizing = false;
    }
  }

  /**
   * 处理音频数据
   */
  @SubscribeMessage('audio-data')
  async handleAudioData(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { audioData: string; format?: string },
  ) {
    const session = this.sessions.get(client.id);
    if (!session) {
      client.emit('error', { message: '会话不存在' });
      return;
    }

    try {
      // 解码音频数据
      const audioBuffer = Buffer.from(data.audioData, 'base64');

      // 执行语音识别
      const result = await this.voiceService.recognizeSpeech(
        audioBuffer,
        session.config.recognition,
      );

      // 发送识别结果
      client.emit('audio-recognized', {
        id: result.id,
        text: result.text,
        confidence: result.confidence,
        duration: result.duration,
        words: result.words,
      });

    } catch (error) {
      client.emit('error', { message: `音频处理失败: ${error.message}` });
    }
  }

  /**
   * 生成嘴形同步数据
   */
  @SubscribeMessage('generate-lip-sync')
  async handleGenerateLipSync(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: {
      text: string;
      audioData?: string;
      method?: 'phoneme' | 'audio' | 'hybrid';
      language?: string;
    },
  ) {
    const session = this.sessions.get(client.id);
    if (!session) {
      client.emit('error', { message: '会话不存在' });
      return;
    }

    try {
      const audioBuffer = data.audioData 
        ? Buffer.from(data.audioData, 'base64')
        : Buffer.alloc(0);

      const lipSyncData = await this.voiceService.generateLipSync(
        audioBuffer,
        data.text,
        {
          method: data.method || 'phoneme',
          language: data.language || session.config.recognition.language,
          frameRate: 30,
          smoothing: 0.5,
          intensity: 1.0,
        },
      );

      client.emit('lip-sync-generated', lipSyncData);

    } catch (error) {
      client.emit('error', { message: `生成嘴形同步失败: ${error.message}` });
    }
  }

  /**
   * 获取会话状态
   */
  @SubscribeMessage('get-status')
  handleGetStatus(@ConnectedSocket() client: Socket) {
    const session = this.sessions.get(client.id);
    if (!session) {
      client.emit('error', { message: '会话不存在' });
      return;
    }

    client.emit('status', {
      sessionId: session.id,
      isRecognizing: session.isRecognizing,
      isSynthesizing: session.isSynthesizing,
      config: session.config,
    });
  }

  /**
   * 获取服务统计信息
   */
  @SubscribeMessage('get-statistics')
  handleGetStatistics(@ConnectedSocket() client: Socket) {
    const statistics = this.voiceService.getStatistics();
    client.emit('statistics', {
      ...statistics,
      activeSessions: this.sessions.size,
    });
  }

  /**
   * 广播消息到所有客户端
   */
  broadcastToAll(event: string, data: any) {
    this.server.emit(event, data);
  }

  /**
   * 发送消息到特定用户
   */
  sendToUser(userId: string, event: string, data: any) {
    for (const [socketId, session] of this.sessions.entries()) {
      if (session.userId === userId) {
        this.server.to(socketId).emit(event, data);
      }
    }
  }

  /**
   * 获取活跃会话数
   */
  getActiveSessionCount(): number {
    return this.sessions.size;
  }

  /**
   * 清理过期会话
   */
  async cleanupExpiredSessions() {
    // 这里可以添加会话过期清理逻辑
    // 例如：超过一定时间没有活动的会话
  }
}
