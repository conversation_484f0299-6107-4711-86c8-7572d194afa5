/**
 * 用户体验测试器
 * 评估和优化用户交互体验
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';

/**
 * 用户体验指标
 */
export interface UXMetrics {
  /** 易用性评分 (0-10) */
  usabilityScore: number;
  /** 响应性评分 (0-10) */
  responsivenessScore: number;
  /** 准确性评分 (0-10) */
  accuracyScore: number;
  /** 舒适度评分 (0-10) */
  comfortScore: number;
  /** 学习曲线评分 (0-10) */
  learnabilityScore: number;
  /** 整体满意度 (0-10) */
  overallSatisfaction: number;
  /** 任务完成率 (0-1) */
  taskCompletionRate: number;
  /** 平均任务时间 (秒) */
  averageTaskTime: number;
  /** 错误率 (0-1) */
  errorRate: number;
}

/**
 * 测试任务
 */
export interface UXTestTask {
  id: string;
  name: string;
  description: string;
  expectedDuration: number;
  difficulty: TaskDifficulty;
  category: TaskCategory;
  steps: TaskStep[];
  successCriteria: SuccessCriteria;
}

/**
 * 任务难度
 */
export enum TaskDifficulty {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert'
}

/**
 * 任务类别
 */
export enum TaskCategory {
  BASIC_GESTURES = 'basic_gestures',
  OBJECT_MANIPULATION = 'object_manipulation',
  COLLABORATION = 'collaboration',
  COMPLEX_INTERACTIONS = 'complex_interactions',
  ACCESSIBILITY = 'accessibility'
}

/**
 * 任务步骤
 */
export interface TaskStep {
  id: string;
  instruction: string;
  expectedGesture?: string;
  expectedDuration: number;
  hints: string[];
}

/**
 * 成功标准
 */
export interface SuccessCriteria {
  minAccuracy: number;
  maxTime: number;
  maxErrors: number;
  requiredGestures: string[];
}

/**
 * 测试会话
 */
export interface UXTestSession {
  sessionId: string;
  userId: string;
  startTime: number;
  endTime?: number;
  tasks: TaskResult[];
  overallMetrics: UXMetrics;
  feedback: UserFeedback[];
  recommendations: string[];
}

/**
 * 任务结果
 */
export interface TaskResult {
  taskId: string;
  startTime: number;
  endTime: number;
  duration: number;
  completed: boolean;
  accuracy: number;
  errors: TaskError[];
  stepResults: StepResult[];
  userFeedback?: TaskFeedback;
}

/**
 * 步骤结果
 */
export interface StepResult {
  stepId: string;
  startTime: number;
  endTime: number;
  duration: number;
  success: boolean;
  attempts: number;
  gesturesPerformed: string[];
}

/**
 * 任务错误
 */
export interface TaskError {
  type: string;
  description: string;
  timestamp: number;
  stepId?: string;
  severity: 'low' | 'medium' | 'high';
}

/**
 * 用户反馈
 */
export interface UserFeedback {
  type: 'rating' | 'comment' | 'suggestion';
  content: string | number;
  timestamp: number;
  category?: string;
}

/**
 * 任务反馈
 */
export interface TaskFeedback {
  difficulty: number; // 1-5
  clarity: number; // 1-5
  satisfaction: number; // 1-5
  comments: string;
}

/**
 * 用户体验测试器
 */
export class UserExperienceTester extends EventEmitter {
  private testTasks: Map<string, UXTestTask> = new Map();
  private activeSessions: Map<string, UXTestSession> = new Map();
  private completedSessions: UXTestSession[] = [];
  private analytics: UXAnalytics;

  constructor() {
    super();
    this.analytics = new UXAnalytics();
    this.initializeDefaultTasks();
  }

  /**
   * 初始化默认测试任务
   */
  private initializeDefaultTasks(): void {
    const defaultTasks: UXTestTask[] = [
      {
        id: 'basic_pointing',
        name: '基础指向测试',
        description: '测试用户执行基本指向手势的能力',
        expectedDuration: 30000,
        difficulty: TaskDifficulty.BEGINNER,
        category: TaskCategory.BASIC_GESTURES,
        steps: [
          {
            id: 'step1',
            instruction: '请指向屏幕上的红色圆圈',
            expectedGesture: 'pointing',
            expectedDuration: 5000,
            hints: ['伸出食指', '保持手势稳定']
          },
          {
            id: 'step2',
            instruction: '请指向屏幕上的蓝色方块',
            expectedGesture: 'pointing',
            expectedDuration: 5000,
            hints: ['移动手指到目标位置']
          }
        ],
        successCriteria: {
          minAccuracy: 0.8,
          maxTime: 30000,
          maxErrors: 2,
          requiredGestures: ['pointing']
        }
      },
      {
        id: 'object_grab_move',
        name: '物体抓取移动测试',
        description: '测试用户抓取和移动虚拟物体的能力',
        expectedDuration: 60000,
        difficulty: TaskDifficulty.INTERMEDIATE,
        category: TaskCategory.OBJECT_MANIPULATION,
        steps: [
          {
            id: 'step1',
            instruction: '请抓取桌上的立方体',
            expectedGesture: 'grab',
            expectedDuration: 10000,
            hints: ['做出抓取手势', '确保手在物体附近']
          },
          {
            id: 'step2',
            instruction: '将立方体移动到目标位置',
            expectedGesture: 'move',
            expectedDuration: 15000,
            hints: ['保持抓取状态', '缓慢移动到目标']
          },
          {
            id: 'step3',
            instruction: '释放立方体',
            expectedGesture: 'release',
            expectedDuration: 5000,
            hints: ['张开手掌释放物体']
          }
        ],
        successCriteria: {
          minAccuracy: 0.7,
          maxTime: 60000,
          maxErrors: 3,
          requiredGestures: ['grab', 'move', 'release']
        }
      },
      {
        id: 'collaborative_task',
        name: '协作任务测试',
        description: '测试多用户协作完成任务的能力',
        expectedDuration: 120000,
        difficulty: TaskDifficulty.ADVANCED,
        category: TaskCategory.COLLABORATION,
        steps: [
          {
            id: 'step1',
            instruction: '与其他用户一起抓取大型物体',
            expectedGesture: 'collaborative_grab',
            expectedDuration: 20000,
            hints: ['等待其他用户准备', '同时执行抓取']
          },
          {
            id: 'step2',
            instruction: '协作移动物体到目标位置',
            expectedGesture: 'collaborative_move',
            expectedDuration: 30000,
            hints: ['保持同步', '沟通移动方向']
          }
        ],
        successCriteria: {
          minAccuracy: 0.6,
          maxTime: 120000,
          maxErrors: 5,
          requiredGestures: ['collaborative_grab', 'collaborative_move']
        }
      }
    ];

    for (const task of defaultTasks) {
      this.testTasks.set(task.id, task);
    }
  }

  /**
   * 开始用户体验测试
   */
  public startUXTest(userId: string, taskIds?: string[]): string {
    const sessionId = this.generateSessionId();
    const tasksToTest = taskIds ? 
      taskIds.map(id => this.testTasks.get(id)).filter(Boolean) as UXTestTask[] :
      Array.from(this.testTasks.values());

    const session: UXTestSession = {
      sessionId,
      userId,
      startTime: Date.now(),
      tasks: [],
      overallMetrics: this.initializeMetrics(),
      feedback: [],
      recommendations: []
    };

    this.activeSessions.set(sessionId, session);

    Debug.log('UserExperienceTester', `开始UX测试会话: ${sessionId}`, {
      userId,
      taskCount: tasksToTest.length
    });

    this.emit('testSessionStarted', { sessionId, userId, tasks: tasksToTest });
    return sessionId;
  }

  /**
   * 开始任务
   */
  public startTask(sessionId: string, taskId: string): boolean {
    const session = this.activeSessions.get(sessionId);
    const task = this.testTasks.get(taskId);

    if (!session || !task) {
      return false;
    }

    const taskResult: TaskResult = {
      taskId,
      startTime: Date.now(),
      endTime: 0,
      duration: 0,
      completed: false,
      accuracy: 0,
      errors: [],
      stepResults: []
    };

    session.tasks.push(taskResult);

    Debug.log('UserExperienceTester', `开始任务: ${taskId}`, { sessionId });
    this.emit('taskStarted', { sessionId, taskId, task });

    return true;
  }

  /**
   * 记录手势执行
   */
  public recordGestureExecution(
    sessionId: string,
    taskId: string,
    stepId: string,
    gestureType: string,
    accuracy: number,
    duration: number
  ): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    const taskResult = session.tasks.find(t => t.taskId === taskId);
    if (!taskResult) return;

    let stepResult = taskResult.stepResults.find(s => s.stepId === stepId);
    if (!stepResult) {
      stepResult = {
        stepId,
        startTime: Date.now(),
        endTime: 0,
        duration: 0,
        success: false,
        attempts: 0,
        gesturesPerformed: []
      };
      taskResult.stepResults.push(stepResult);
    }

    stepResult.attempts++;
    stepResult.gesturesPerformed.push(gestureType);

    // 检查手势是否正确
    const task = this.testTasks.get(taskId);
    const step = task?.steps.find(s => s.id === stepId);
    
    if (step && step.expectedGesture === gestureType && accuracy >= 0.7) {
      stepResult.success = true;
      stepResult.endTime = Date.now();
      stepResult.duration = stepResult.endTime - stepResult.startTime;

      this.emit('stepCompleted', { sessionId, taskId, stepId, stepResult });
    }

    this.emit('gestureRecorded', { sessionId, taskId, stepId, gestureType, accuracy });
  }

  /**
   * 记录任务错误
   */
  public recordTaskError(
    sessionId: string,
    taskId: string,
    errorType: string,
    description: string,
    severity: 'low' | 'medium' | 'high',
    stepId?: string
  ): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    const taskResult = session.tasks.find(t => t.taskId === taskId);
    if (!taskResult) return;

    const error: TaskError = {
      type: errorType,
      description,
      timestamp: Date.now(),
      stepId,
      severity
    };

    taskResult.errors.push(error);

    this.emit('taskError', { sessionId, taskId, error });
  }

  /**
   * 完成任务
   */
  public completeTask(sessionId: string, taskId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    const taskResult = session.tasks.find(t => t.taskId === taskId);
    if (!taskResult) return;

    taskResult.endTime = Date.now();
    taskResult.duration = taskResult.endTime - taskResult.startTime;
    taskResult.completed = true;

    // 计算任务准确性
    taskResult.accuracy = this.calculateTaskAccuracy(taskResult);

    Debug.log('UserExperienceTester', `任务完成: ${taskId}`, {
      sessionId,
      duration: taskResult.duration,
      accuracy: taskResult.accuracy
    });

    this.emit('taskCompleted', { sessionId, taskId, taskResult });
  }

  /**
   * 添加用户反馈
   */
  public addUserFeedback(
    sessionId: string,
    feedback: UserFeedback
  ): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    session.feedback.push(feedback);

    this.emit('feedbackReceived', { sessionId, feedback });
  }

  /**
   * 添加任务反馈
   */
  public addTaskFeedback(
    sessionId: string,
    taskId: string,
    feedback: TaskFeedback
  ): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    const taskResult = session.tasks.find(t => t.taskId === taskId);
    if (taskResult) {
      taskResult.userFeedback = feedback;
    }

    this.emit('taskFeedbackReceived', { sessionId, taskId, feedback });
  }

  /**
   * 完成测试会话
   */
  public completeTestSession(sessionId: string): UXTestSession | null {
    const session = this.activeSessions.get(sessionId);
    if (!session) return null;

    session.endTime = Date.now();
    session.overallMetrics = this.calculateOverallMetrics(session);
    session.recommendations = this.generateRecommendations(session);

    this.activeSessions.delete(sessionId);
    this.completedSessions.push(session);

    // 更新分析数据
    this.analytics.addSession(session);

    Debug.log('UserExperienceTester', `测试会话完成: ${sessionId}`, {
      duration: session.endTime - session.startTime,
      overallScore: session.overallMetrics.overallSatisfaction
    });

    this.emit('testSessionCompleted', session);
    return session;
  }

  /**
   * 计算任务准确性
   */
  private calculateTaskAccuracy(taskResult: TaskResult): number {
    const task = this.testTasks.get(taskResult.taskId);
    if (!task) return 0;

    let totalSteps = task.steps.length;
    let successfulSteps = taskResult.stepResults.filter(s => s.success).length;

    return totalSteps > 0 ? successfulSteps / totalSteps : 0;
  }

  /**
   * 计算整体指标
   */
  private calculateOverallMetrics(session: UXTestSession): UXMetrics {
    const tasks = session.tasks;
    if (tasks.length === 0) return this.initializeMetrics();

    const completedTasks = tasks.filter(t => t.completed);
    const taskCompletionRate = completedTasks.length / tasks.length;

    const averageAccuracy = tasks.reduce((sum, t) => sum + t.accuracy, 0) / tasks.length;
    const averageTaskTime = tasks.reduce((sum, t) => sum + t.duration, 0) / tasks.length / 1000;

    const totalErrors = tasks.reduce((sum, t) => sum + t.errors.length, 0);
    const errorRate = totalErrors / tasks.length;

    // 基于任务表现计算各项评分
    const accuracyScore = Math.min(10, averageAccuracy * 10);
    const responsivenessScore = Math.max(0, 10 - (averageTaskTime / 10)); // 假设10秒为基准
    const usabilityScore = Math.max(0, 10 - errorRate * 2);

    // 基于用户反馈计算其他评分
    const feedbackScores = this.calculateFeedbackScores(session.feedback);

    return {
      usabilityScore,
      responsivenessScore: Math.max(0, responsivenessScore),
      accuracyScore,
      comfortScore: feedbackScores.comfort,
      learnabilityScore: feedbackScores.learnability,
      overallSatisfaction: feedbackScores.satisfaction,
      taskCompletionRate,
      averageTaskTime,
      errorRate
    };
  }

  /**
   * 计算反馈评分
   */
  private calculateFeedbackScores(feedback: UserFeedback[]): any {
    const ratings = feedback.filter(f => f.type === 'rating');
    
    return {
      comfort: this.getAverageRating(ratings, 'comfort') || 7,
      learnability: this.getAverageRating(ratings, 'learnability') || 7,
      satisfaction: this.getAverageRating(ratings, 'satisfaction') || 7
    };
  }

  /**
   * 获取平均评分
   */
  private getAverageRating(ratings: UserFeedback[], category: string): number {
    const categoryRatings = ratings.filter(r => r.category === category);
    if (categoryRatings.length === 0) return 0;

    const sum = categoryRatings.reduce((total, r) => total + (r.content as number), 0);
    return sum / categoryRatings.length;
  }

  /**
   * 生成建议
   */
  private generateRecommendations(session: UXTestSession): string[] {
    const recommendations: string[] = [];
    const metrics = session.overallMetrics;

    if (metrics.accuracyScore < 6) {
      recommendations.push('建议增加手势识别训练以提高准确性');
    }

    if (metrics.responsivenessScore < 6) {
      recommendations.push('建议优化系统响应速度');
    }

    if (metrics.taskCompletionRate < 0.8) {
      recommendations.push('建议简化任务流程或提供更多指导');
    }

    if (metrics.errorRate > 0.3) {
      recommendations.push('建议改进错误处理和用户提示');
    }

    if (metrics.comfortScore < 6) {
      recommendations.push('建议优化人体工程学设计');
    }

    return recommendations;
  }

  /**
   * 初始化指标
   */
  private initializeMetrics(): UXMetrics {
    return {
      usabilityScore: 0,
      responsivenessScore: 0,
      accuracyScore: 0,
      comfortScore: 0,
      learnabilityScore: 0,
      overallSatisfaction: 0,
      taskCompletionRate: 0,
      averageTaskTime: 0,
      errorRate: 0
    };
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `ux_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取测试任务
   */
  public getTestTasks(): UXTestTask[] {
    return Array.from(this.testTasks.values());
  }

  /**
   * 获取活跃会话
   */
  public getActiveSession(sessionId: string): UXTestSession | null {
    return this.activeSessions.get(sessionId) || null;
  }

  /**
   * 获取完成的会话
   */
  public getCompletedSessions(): UXTestSession[] {
    return [...this.completedSessions];
  }

  /**
   * 获取分析报告
   */
  public getAnalyticsReport(): any {
    return this.analytics.generateReport();
  }

  /**
   * 添加自定义任务
   */
  public addCustomTask(task: UXTestTask): void {
    this.testTasks.set(task.id, task);
    this.emit('customTaskAdded', task);
  }

  /**
   * 销毁测试器
   */
  public destroy(): void {
    this.activeSessions.clear();
    this.completedSessions = [];
    this.testTasks.clear();
    this.analytics.destroy();
    this.removeAllListeners();
  }
}

/**
 * UX分析器
 */
class UXAnalytics {
  private sessions: UXTestSession[] = [];

  public addSession(session: UXTestSession): void {
    this.sessions.push(session);
  }

  public generateReport(): any {
    if (this.sessions.length === 0) {
      return { message: '暂无数据' };
    }

    const totalSessions = this.sessions.length;
    const avgMetrics = this.calculateAverageMetrics();
    const taskAnalysis = this.analyzeTaskPerformance();
    const userPatterns = this.identifyUserPatterns();

    return {
      totalSessions,
      averageMetrics: avgMetrics,
      taskAnalysis,
      userPatterns,
      trends: this.analyzeTrends(),
      recommendations: this.generateGlobalRecommendations()
    };
  }

  private calculateAverageMetrics(): UXMetrics {
    const totalMetrics = this.sessions.reduce((acc, session) => {
      const metrics = session.overallMetrics;
      acc.usabilityScore += metrics.usabilityScore;
      acc.responsivenessScore += metrics.responsivenessScore;
      acc.accuracyScore += metrics.accuracyScore;
      acc.comfortScore += metrics.comfortScore;
      acc.learnabilityScore += metrics.learnabilityScore;
      acc.overallSatisfaction += metrics.overallSatisfaction;
      acc.taskCompletionRate += metrics.taskCompletionRate;
      acc.averageTaskTime += metrics.averageTaskTime;
      acc.errorRate += metrics.errorRate;
      return acc;
    }, {
      usabilityScore: 0,
      responsivenessScore: 0,
      accuracyScore: 0,
      comfortScore: 0,
      learnabilityScore: 0,
      overallSatisfaction: 0,
      taskCompletionRate: 0,
      averageTaskTime: 0,
      errorRate: 0
    });

    const count = this.sessions.length;
    return {
      usabilityScore: totalMetrics.usabilityScore / count,
      responsivenessScore: totalMetrics.responsivenessScore / count,
      accuracyScore: totalMetrics.accuracyScore / count,
      comfortScore: totalMetrics.comfortScore / count,
      learnabilityScore: totalMetrics.learnabilityScore / count,
      overallSatisfaction: totalMetrics.overallSatisfaction / count,
      taskCompletionRate: totalMetrics.taskCompletionRate / count,
      averageTaskTime: totalMetrics.averageTaskTime / count,
      errorRate: totalMetrics.errorRate / count
    };
  }

  private analyzeTaskPerformance(): any {
    const taskStats = new Map<string, any>();

    for (const session of this.sessions) {
      for (const task of session.tasks) {
        if (!taskStats.has(task.taskId)) {
          taskStats.set(task.taskId, {
            attempts: 0,
            completions: 0,
            totalDuration: 0,
            totalErrors: 0,
            totalAccuracy: 0
          });
        }

        const stats = taskStats.get(task.taskId);
        stats.attempts++;
        if (task.completed) stats.completions++;
        stats.totalDuration += task.duration;
        stats.totalErrors += task.errors.length;
        stats.totalAccuracy += task.accuracy;
      }
    }

    const analysis: any = {};
    for (const [taskId, stats] of taskStats.entries()) {
      analysis[taskId] = {
        completionRate: stats.completions / stats.attempts,
        averageDuration: stats.totalDuration / stats.attempts,
        averageErrors: stats.totalErrors / stats.attempts,
        averageAccuracy: stats.totalAccuracy / stats.attempts
      };
    }

    return analysis;
  }

  private identifyUserPatterns(): any {
    // 简化的用户模式识别
    return {
      commonDifficulties: this.findCommonDifficulties(),
      learningCurves: this.analyzeLearningCurves(),
      preferredInteractions: this.findPreferredInteractions()
    };
  }

  private findCommonDifficulties(): string[] {
    const difficulties: string[] = [];
    // 实现困难识别逻辑
    return difficulties;
  }

  private analyzeLearningCurves(): any {
    // 实现学习曲线分析
    return {};
  }

  private findPreferredInteractions(): string[] {
    // 实现偏好交互识别
    return [];
  }

  private analyzeTrends(): any {
    // 实现趋势分析
    return {};
  }

  private generateGlobalRecommendations(): string[] {
    const recommendations: string[] = [];
    const avgMetrics = this.calculateAverageMetrics();

    if (avgMetrics.overallSatisfaction < 6) {
      recommendations.push('整体用户满意度需要改进');
    }

    if (avgMetrics.taskCompletionRate < 0.8) {
      recommendations.push('任务完成率偏低，需要优化任务设计');
    }

    return recommendations;
  }

  public destroy(): void {
    this.sessions = [];
  }
}
