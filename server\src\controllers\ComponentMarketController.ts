/**
 * ComponentMarketController.ts
 * 
 * 组件市场控制器
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
  Res,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { JwtAuthGuard } from '../guards/JwtAuthGuard';
import { CurrentUser } from '../decorators/CurrentUser';
import { ComponentMarketService, ComponentSearchParams, PublishComponentParams } from '../services/ComponentMarketService';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, IsNumber, Min, Max, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';
import * as fs from 'fs-extra';

/**
 * 发布组件DTO
 */
export class PublishComponentDto implements PublishComponentParams {
  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsString()
  category: string;

  @IsArray()
  @IsString({ each: true })
  tags: string[];

  @IsString()
  version: string;

  componentData: any;

  @IsOptional()
  @IsString()
  previewImage?: string;

  @IsOptional()
  @IsString()
  documentation?: string;

  @IsOptional()
  @IsString()
  license?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  dependencies?: string[];
}

/**
 * 搜索组件DTO
 */
export class SearchComponentsDto implements ComponentSearchParams {
  @IsOptional()
  @IsString()
  keyword?: string;

  @IsOptional()
  @IsString()
  category?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => Array.isArray(value) ? value : [value])
  tags?: string[];

  @IsOptional()
  @IsString()
  author?: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  @Transform(({ value }) => parseFloat(value))
  minRating?: number;

  @IsOptional()
  @IsString()
  sortBy?: 'downloads' | 'rating' | 'created' | 'updated';

  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC';

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  page?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => parseInt(value))
  limit?: number;
}

/**
 * 评分组件DTO
 */
export class RateComponentDto {
  @IsNumber()
  @Min(1)
  @Max(5)
  rating: number;

  @IsOptional()
  @IsString()
  comment?: string;
}

@ApiTags('组件市场')
@Controller('api/component-market')
export class ComponentMarketController {
  constructor(private readonly componentMarketService: ComponentMarketService) {}

  /**
   * 发布组件
   */
  @Post('publish')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FileFieldsInterceptor([
    { name: 'componentFile', maxCount: 1 },
    { name: 'previewImage', maxCount: 1 }
  ]))
  @ApiOperation({ summary: '发布组件' })
  @ApiBearerAuth()
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: '组件发布成功' })
  async publishComponent(
    @CurrentUser() user: any,
    @Body(ValidationPipe) publishDto: PublishComponentDto,
    @UploadedFiles() files: {
      componentFile?: Express.Multer.File[];
      previewImage?: Express.Multer.File[];
    }
  ) {
    try {
      const fileParams = {
        componentFile: files.componentFile?.[0],
        previewImage: files.previewImage?.[0]
      };

      const component = await this.componentMarketService.publishComponent(
        user.id,
        publishDto,
        fileParams
      );

      return {
        success: true,
        message: '组件发布成功',
        data: component
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        statusCode: HttpStatus.BAD_REQUEST
      };
    }
  }

  /**
   * 搜索组件
   */
  @Get('search')
  @ApiOperation({ summary: '搜索组件' })
  @ApiResponse({ status: 200, description: '搜索结果' })
  async searchComponents(@Query(ValidationPipe) searchDto: SearchComponentsDto) {
    try {
      const result = await this.componentMarketService.searchComponents(searchDto);
      
      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR
      };
    }
  }

  /**
   * 获取组件详情
   */
  @Get('components/:id')
  @ApiOperation({ summary: '获取组件详情' })
  @ApiResponse({ status: 200, description: '组件详情' })
  async getComponent(@Param('id', ParseUUIDPipe) componentId: string) {
    try {
      const component = await this.componentMarketService.getComponentById(componentId);
      
      return {
        success: true,
        data: component
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        statusCode: HttpStatus.NOT_FOUND
      };
    }
  }

  /**
   * 下载组件
   */
  @Get('components/:id/download')
  @ApiOperation({ summary: '下载组件' })
  @ApiResponse({ status: 200, description: '组件文件' })
  async downloadComponent(
    @Param('id', ParseUUIDPipe) componentId: string,
    @CurrentUser() user: any,
    @Res() res: Response
  ) {
    try {
      const { filePath, fileName } = await this.componentMarketService.downloadComponent(
        componentId,
        user?.id
      );

      if (!fs.existsSync(filePath)) {
        return res.status(HttpStatus.NOT_FOUND).json({
          success: false,
          message: '文件不存在'
        });
      }

      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      res.setHeader('Content-Type', 'application/zip');
      
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * 评分组件
   */
  @Post('components/:id/rate')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '评分组件' })
  @ApiBearerAuth()
  @ApiResponse({ status: 201, description: '评分成功' })
  async rateComponent(
    @Param('id', ParseUUIDPipe) componentId: string,
    @CurrentUser() user: any,
    @Body(ValidationPipe) rateDto: RateComponentDto
  ) {
    try {
      const rating = await this.componentMarketService.rateComponent(
        componentId,
        user.id,
        rateDto.rating,
        rateDto.comment
      );

      return {
        success: true,
        message: '评分成功',
        data: rating
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        statusCode: HttpStatus.BAD_REQUEST
      };
    }
  }

  /**
   * 获取统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '获取组件市场统计信息' })
  @ApiResponse({ status: 200, description: '统计信息' })
  async getStats() {
    try {
      const stats = await this.componentMarketService.getComponentStats();
      
      return {
        success: true,
        data: stats
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR
      };
    }
  }

  /**
   * 获取用户的组件
   */
  @Get('my-components')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '获取用户的组件' })
  @ApiBearerAuth()
  @ApiResponse({ status: 200, description: '用户组件列表' })
  async getMyComponents(@CurrentUser() user: any) {
    try {
      const components = await this.componentMarketService.getUserComponents(user.id);
      
      return {
        success: true,
        data: components
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR
      };
    }
  }

  /**
   * 更新组件
   */
  @Put('components/:id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '更新组件' })
  @ApiBearerAuth()
  @ApiResponse({ status: 200, description: '组件更新成功' })
  async updateComponent(
    @Param('id', ParseUUIDPipe) componentId: string,
    @CurrentUser() user: any,
    @Body(ValidationPipe) updateDto: Partial<PublishComponentDto>
  ) {
    try {
      const component = await this.componentMarketService.updateComponent(
        componentId,
        user.id,
        updateDto
      );

      return {
        success: true,
        message: '组件更新成功',
        data: component
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        statusCode: HttpStatus.BAD_REQUEST
      };
    }
  }

  /**
   * 删除组件
   */
  @Delete('components/:id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '删除组件' })
  @ApiBearerAuth()
  @ApiResponse({ status: 200, description: '组件删除成功' })
  async deleteComponent(
    @Param('id', ParseUUIDPipe) componentId: string,
    @CurrentUser() user: any
  ) {
    try {
      await this.componentMarketService.deleteComponent(componentId, user.id);

      return {
        success: true,
        message: '组件删除成功'
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        statusCode: HttpStatus.BAD_REQUEST
      };
    }
  }

  /**
   * 获取热门组件
   */
  @Get('popular')
  @ApiOperation({ summary: '获取热门组件' })
  @ApiResponse({ status: 200, description: '热门组件列表' })
  async getPopularComponents(@Query('limit') limit: number = 10) {
    try {
      const result = await this.componentMarketService.searchComponents({
        sortBy: 'downloads',
        sortOrder: 'DESC',
        limit: Math.min(limit, 50),
        minRating: 3.0
      });

      return {
        success: true,
        data: result.components
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR
      };
    }
  }

  /**
   * 获取最新组件
   */
  @Get('latest')
  @ApiOperation({ summary: '获取最新组件' })
  @ApiResponse({ status: 200, description: '最新组件列表' })
  async getLatestComponents(@Query('limit') limit: number = 10) {
    try {
      const result = await this.componentMarketService.searchComponents({
        sortBy: 'created',
        sortOrder: 'DESC',
        limit: Math.min(limit, 50)
      });

      return {
        success: true,
        data: result.components
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR
      };
    }
  }
}
