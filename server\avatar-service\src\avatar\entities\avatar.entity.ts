/**
 * 虚拟化身实体
 * 定义虚拟化身的数据模型
 */
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 面部数据接口
 */
export interface FaceData {
  /** 面部几何数据 */
  geometry: {
    vertices: number[];
    faces: number[];
    normals: number[];
    uvs: number[];
  };
  /** 面部纹理 */
  texture: {
    url: string;
    width: number;
    height: number;
  };
  /** 面部参数 */
  parameters: {
    shape: number[];
    expression: number[];
    skinTone: number;
    features: {
      eyeSize: number;
      noseSize: number;
      mouthSize: number;
      jawWidth: number;
    };
  };
  /** 面部关键点 */
  landmarks: number[];
}

/**
 * 身体数据接口
 */
export interface BodyData {
  /** 身体几何数据 */
  geometry: {
    vertices: number[];
    faces: number[];
    normals: number[];
    uvs: number[];
  };
  /** 身体纹理 */
  texture: {
    url: string;
    width: number;
    height: number;
  };
  /** 身体参数 */
  parameters: {
    gender: 'male' | 'female';
    height: number;
    weight: number;
    build: number;
    muscle: number;
    skinTone: number;
  };
}

/**
 * 服装数据接口
 */
export interface ClothingData {
  /** 服装项目列表 */
  items: Array<{
    id: string;
    type: string;
    geometry: {
      vertices: number[];
      faces: number[];
      normals: number[];
      uvs: number[];
    };
    material: {
      type: string;
      color: string;
      texture?: string;
      properties: Record<string, any>;
    };
    size: string;
  }>;
  /** 服装组合ID */
  outfitId?: string;
}

/**
 * 纹理数据接口
 */
export interface TextureData {
  /** 面部纹理 */
  faceTexture?: {
    url: string;
    width: number;
    height: number;
  };
  /** 身体纹理 */
  bodyTexture?: {
    url: string;
    width: number;
    height: number;
  };
  /** 服装纹理 */
  clothingTextures?: Array<{
    itemId: string;
    url: string;
    width: number;
    height: number;
  }>;
  /** 纹理分辨率 */
  resolution: number;
  /** 纹理质量 */
  quality: number;
}

/**
 * 照片数据接口
 */
export interface PhotoData {
  /** 照片ID */
  photoId: string;
  /** 预处理数据 */
  preprocessed: {
    enhancedUrl: string;
    croppedUrl: string;
    normalizedUrl: string;
  };
  /** 质量评估 */
  quality: {
    score: number;
    issues: string[];
    suggestions: string[];
  };
  /** 面部检测 */
  faceDetection: {
    faceDetected: boolean;
    boundingBox?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
    confidence: number;
  };
}

@Entity('avatars')
@Index(['userId'])
@Index(['status'])
@Index(['createdAt'])
export class Avatar {
  @ApiProperty({ description: '虚拟化身ID' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '用户ID' })
  @Column({ type: 'varchar', length: 255, nullable: true })
  @Index()
  userId?: string;

  @ApiProperty({ description: '虚拟化身名称' })
  @Column({ type: 'varchar', length: 255, nullable: true })
  name?: string;

  @ApiProperty({ description: '虚拟化身描述' })
  @Column({ type: 'text', nullable: true })
  description?: string;

  @ApiProperty({ description: '虚拟化身状态' })
  @Column({
    type: 'enum',
    enum: ['created', 'processing', 'completed', 'failed'],
    default: 'created'
  })
  status: 'created' | 'processing' | 'completed' | 'failed';

  @ApiProperty({ description: '照片数据' })
  @Column({ type: 'json', nullable: true })
  photoData?: PhotoData;

  @ApiProperty({ description: '面部数据' })
  @Column({ type: 'json', nullable: true })
  faceData?: FaceData;

  @ApiProperty({ description: '身体数据' })
  @Column({ type: 'json', nullable: true })
  bodyData?: BodyData;

  @ApiProperty({ description: '服装数据' })
  @Column({ type: 'json', nullable: true })
  clothingData?: ClothingData;

  @ApiProperty({ description: '纹理数据' })
  @Column({ type: 'json', nullable: true })
  textureData?: TextureData;

  @ApiProperty({ description: '元数据' })
  @Column({ type: 'json', nullable: true })
  metadata?: {
    /** 创建来源 */
    source: 'web' | 'mobile' | 'api';
    /** 版本号 */
    version: string;
    /** 标签 */
    tags: string[];
    /** 是否公开 */
    isPublic: boolean;
    /** 预览图URL */
    previewUrl?: string;
    /** 缩略图URL */
    thumbnailUrl?: string;
  };

  @ApiProperty({ description: '处理日志' })
  @Column({ type: 'json', nullable: true })
  processingLog?: Array<{
    timestamp: Date;
    step: string;
    status: 'started' | 'completed' | 'failed';
    progress: number;
    message: string;
    error?: string;
    duration?: number;
  }>;

  @ApiProperty({ description: '质量评分' })
  @Column({ type: 'decimal', precision: 3, scale: 2, nullable: true })
  qualityScore?: number;

  @ApiProperty({ description: '文件大小(字节)' })
  @Column({ type: 'bigint', nullable: true })
  fileSize?: number;

  @ApiProperty({ description: '多边形数量' })
  @Column({ type: 'int', nullable: true })
  polygonCount?: number;

  @ApiProperty({ description: '纹理分辨率' })
  @Column({ type: 'int', nullable: true })
  textureResolution?: number;

  @ApiProperty({ description: '是否已删除' })
  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({ description: '最后访问时间' })
  @Column({ type: 'timestamp', nullable: true })
  lastAccessedAt?: Date;

  /**
   * 获取完整度百分比
   */
  getCompletionPercentage(): number {
    let completed = 0;
    let total = 4; // 照片、面部、身体、纹理

    if (this.photoData) completed++;
    if (this.faceData) completed++;
    if (this.bodyData) completed++;
    if (this.textureData) completed++;

    return Math.round((completed / total) * 100);
  }

  /**
   * 检查是否可以导出
   */
  canExport(): boolean {
    return !!(this.faceData && this.bodyData && this.textureData);
  }

  /**
   * 获取预览URL
   */
  getPreviewUrl(): string | null {
    return this.metadata?.previewUrl || null;
  }

  /**
   * 获取缩略图URL
   */
  getThumbnailUrl(): string | null {
    return this.metadata?.thumbnailUrl || null;
  }

  /**
   * 添加处理日志
   */
  addProcessingLog(
    step: string,
    status: 'started' | 'completed' | 'failed',
    progress: number,
    message: string,
    error?: string
  ): void {
    if (!this.processingLog) {
      this.processingLog = [];
    }

    const logEntry = {
      timestamp: new Date(),
      step,
      status,
      progress,
      message,
      error
    };

    // 如果是同一步骤的完成状态，计算持续时间
    if (status === 'completed' || status === 'failed') {
      const startEntry = this.processingLog
        .slice()
        .reverse()
        .find(log => log.step === step && log.status === 'started');
      
      if (startEntry) {
        logEntry.duration = Date.now() - startEntry.timestamp.getTime();
      }
    }

    this.processingLog.push(logEntry);

    // 保持最近100条日志
    if (this.processingLog.length > 100) {
      this.processingLog = this.processingLog.slice(-100);
    }
  }

  /**
   * 获取最新处理状态
   */
  getLatestProcessingStatus(): {
    step: string;
    status: string;
    progress: number;
    message: string;
  } | null {
    if (!this.processingLog || this.processingLog.length === 0) {
      return null;
    }

    const latest = this.processingLog[this.processingLog.length - 1];
    return {
      step: latest.step,
      status: latest.status,
      progress: latest.progress,
      message: latest.message
    };
  }

  /**
   * 计算总文件大小
   */
  calculateTotalFileSize(): number {
    let totalSize = 0;

    // 这里可以根据实际的文件大小计算
    // 暂时返回估算值
    if (this.photoData) totalSize += 5 * 1024 * 1024; // 5MB
    if (this.faceData) totalSize += 10 * 1024 * 1024; // 10MB
    if (this.bodyData) totalSize += 20 * 1024 * 1024; // 20MB
    if (this.clothingData) totalSize += 15 * 1024 * 1024; // 15MB
    if (this.textureData) totalSize += 30 * 1024 * 1024; // 30MB

    return totalSize;
  }

  /**
   * 更新最后访问时间
   */
  updateLastAccessed(): void {
    this.lastAccessedAt = new Date();
  }

  /**
   * 软删除
   */
  softDelete(): void {
    this.isDeleted = true;
    this.updatedAt = new Date();
  }

  /**
   * 恢复删除
   */
  restore(): void {
    this.isDeleted = false;
    this.updatedAt = new Date();
  }
}
