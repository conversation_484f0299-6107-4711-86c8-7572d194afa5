/**
 * 超低延迟信令服务
 * 优化WebRTC信令过程以减少连接建立时间
 */
import { Injectable, Logger } from '@nestjs/common';
import { WebSocketGateway, WebSocketServer, SubscribeMessage, ConnectedSocket, MessageBody } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Redis } from 'ioredis';

export interface SignalingMessage {
  type: 'offer' | 'answer' | 'ice-candidate' | 'ping' | 'pong';
  data: any;
  timestamp: number;
  sessionId: string;
  peerId: string;
}

export interface PeerConnection {
  id: string;
  socket: Socket;
  lastSeen: number;
  latency: number;
  region: string;
  capabilities: PeerCapabilities;
}

export interface PeerCapabilities {
  hardwareAcceleration: boolean;
  simdSupport: boolean;
  webCodecsSupport: boolean;
  maxBitrate: number;
  preferredCodecs: string[];
}

@Injectable()
@WebSocketGateway({
  cors: {
    origin: '*',
    credentials: true
  },
  transports: ['websocket'],
  pingTimeout: 5000,
  pingInterval: 2000
})
export class UltraLowLatencySignalingService {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(UltraLowLatencySignalingService.name);
  private peers: Map<string, PeerConnection> = new Map();
  private sessions: Map<string, Set<string>> = new Map();
  private redis: Redis;
  private latencyMonitor: LatencyMonitor;

  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true
    });

    this.latencyMonitor = new LatencyMonitor();
    this.startPerformanceMonitoring();
  }

  afterInit(server: Server) {
    this.logger.log('超低延迟信令服务已启动');
    
    // 配置Socket.IO优化
    server.engine.generateId = () => {
      // 使用更短的ID以减少传输开销
      return Math.random().toString(36).substring(2, 15);
    };

    // 启用二进制传输
    server.engine.compression = false;
    server.engine.perMessageDeflate = false;
  }

  async handleConnection(@ConnectedSocket() client: Socket): Promise<void> {
    const peerId = client.id;
    const region = this.detectRegion(client);
    
    this.logger.log(`新连接: ${peerId} (区域: ${region})`);

    // 创建对等连接记录
    const peerConnection: PeerConnection = {
      id: peerId,
      socket: client,
      lastSeen: Date.now(),
      latency: 0,
      region,
      capabilities: await this.detectCapabilities(client)
    };

    this.peers.set(peerId, peerConnection);

    // 发送优化配置
    client.emit('config', {
      enableBinaryTransport: true,
      enableCompression: false,
      heartbeatInterval: 1000,
      maxRetries: 3,
      timeoutMs: 5000
    });

    // 开始延迟监控
    this.startLatencyMonitoring(client);

    client.on('disconnect', () => {
      this.handleDisconnection(peerId);
    });
  }

  @SubscribeMessage('join-session')
  async handleJoinSession(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { sessionId: string; capabilities: PeerCapabilities }
  ): Promise<void> {
    const peerId = client.id;
    const { sessionId, capabilities } = data;

    this.logger.log(`${peerId} 加入会话: ${sessionId}`);

    // 更新对等连接能力
    const peer = this.peers.get(peerId);
    if (peer) {
      peer.capabilities = capabilities;
    }

    // 添加到会话
    if (!this.sessions.has(sessionId)) {
      this.sessions.set(sessionId, new Set());
    }
    this.sessions.get(sessionId)!.add(peerId);

    // 缓存会话信息到Redis
    await this.redis.sadd(`session:${sessionId}`, peerId);
    await this.redis.expire(`session:${sessionId}`, 3600); // 1小时过期

    // 通知会话中的其他对等方
    const sessionPeers = this.sessions.get(sessionId)!;
    for (const otherPeerId of sessionPeers) {
      if (otherPeerId !== peerId) {
        const otherPeer = this.peers.get(otherPeerId);
        if (otherPeer) {
          otherPeer.socket.emit('peer-joined', {
            peerId,
            capabilities,
            region: peer?.region
          });
        }
      }
    }

    // 发送现有对等方列表
    const existingPeers = Array.from(sessionPeers)
      .filter(id => id !== peerId)
      .map(id => {
        const p = this.peers.get(id);
        return p ? {
          peerId: id,
          capabilities: p.capabilities,
          region: p.region,
          latency: p.latency
        } : null;
      })
      .filter(Boolean);

    client.emit('session-peers', existingPeers);
  }

  @SubscribeMessage('signal')
  async handleSignal(
    @ConnectedSocket() client: Socket,
    @MessageBody() message: SignalingMessage
  ): Promise<void> {
    const startTime = performance.now();
    const { peerId: targetPeerId, type, data, sessionId } = message;

    // 验证会话
    const sessionPeers = this.sessions.get(sessionId);
    if (!sessionPeers || !sessionPeers.has(client.id)) {
      client.emit('error', { message: '未加入会话' });
      return;
    }

    // 查找目标对等方
    const targetPeer = this.peers.get(targetPeerId);
    if (!targetPeer) {
      client.emit('error', { message: '目标对等方不存在' });
      return;
    }

    // 优化信令消息
    const optimizedMessage = await this.optimizeSignalingMessage({
      ...message,
      fromPeerId: client.id,
      timestamp: Date.now()
    });

    // 直接转发到目标对等方
    targetPeer.socket.emit('signal', optimizedMessage);

    // 记录信令延迟
    const processingTime = performance.now() - startTime;
    this.latencyMonitor.recordSignalingLatency(processingTime);

    // 如果是ICE候选，尝试优化路由
    if (type === 'ice-candidate') {
      await this.optimizeICECandidate(data, client.id, targetPeerId);
    }
  }

  @SubscribeMessage('ping')
  handlePing(@ConnectedSocket() client: Socket, @MessageBody() timestamp: number): void {
    // 立即响应ping以测量延迟
    client.emit('pong', timestamp);
    
    // 更新对等方延迟
    const peer = this.peers.get(client.id);
    if (peer) {
      peer.lastSeen = Date.now();
      peer.latency = Date.now() - timestamp;
    }
  }

  private async optimizeSignalingMessage(message: SignalingMessage): Promise<SignalingMessage> {
    // 压缩SDP以减少传输大小
    if (message.type === 'offer' || message.type === 'answer') {
      message.data.sdp = this.compressSDP(message.data.sdp);
    }

    // 优化ICE候选
    if (message.type === 'ice-candidate' && message.data.candidate) {
      message.data = this.optimizeICECandidate(message.data);
    }

    return message;
  }

  private compressSDP(sdp: string): string {
    // 移除不必要的属性以减少SDP大小
    return sdp
      .replace(/a=rtcp-fb:.*\r\n/g, '') // 移除RTCP反馈
      .replace(/a=fmtp:.*useinbandfec=1.*\r\n/g, '') // 移除FEC
      .replace(/a=fmtp:.*usedtx=1.*\r\n/g, '') // 移除DTX
      .replace(/a=ssrc-group:.*\r\n/g, '') // 移除SSRC组
      .replace(/a=msid-semantic:.*\r\n/g, ''); // 移除语义
  }

  private optimizeICECandidate(candidate: any): any {
    // 优先选择UDP候选
    if (candidate.candidate && candidate.candidate.includes('udp')) {
      candidate.priority = candidate.priority || 0;
      candidate.priority += 1000; // 提高UDP优先级
    }

    // 优先选择本地候选
    if (candidate.candidate && candidate.candidate.includes('typ host')) {
      candidate.priority = candidate.priority || 0;
      candidate.priority += 2000; // 提高本地候选优先级
    }

    return candidate;
  }

  private startLatencyMonitoring(client: Socket): void {
    const interval = setInterval(() => {
      const peer = this.peers.get(client.id);
      if (!peer) {
        clearInterval(interval);
        return;
      }

      // 发送ping测试延迟
      const timestamp = Date.now();
      client.emit('ping', timestamp);

      // 检查连接健康状态
      if (Date.now() - peer.lastSeen > 10000) { // 10秒无响应
        this.logger.warn(`对等方 ${client.id} 可能已断开连接`);
        client.disconnect();
        clearInterval(interval);
      }
    }, 1000); // 每秒ping一次
  }

  private detectRegion(client: Socket): string {
    // 根据IP地址检测区域
    const clientIP = client.handshake.address;
    // 这里应该实现实际的IP地理位置检测
    return 'default';
  }

  private async detectCapabilities(client: Socket): Promise<PeerCapabilities> {
    // 从客户端获取能力信息
    return new Promise((resolve) => {
      client.emit('capability-request');
      
      const timeout = setTimeout(() => {
        resolve({
          hardwareAcceleration: false,
          simdSupport: false,
          webCodecsSupport: false,
          maxBitrate: 1000000,
          preferredCodecs: ['opus', 'vp8']
        });
      }, 1000);

      client.once('capabilities', (capabilities: PeerCapabilities) => {
        clearTimeout(timeout);
        resolve(capabilities);
      });
    });
  }

  private handleDisconnection(peerId: string): void {
    this.logger.log(`连接断开: ${peerId}`);

    // 从所有会话中移除
    for (const [sessionId, sessionPeers] of this.sessions) {
      if (sessionPeers.has(peerId)) {
        sessionPeers.delete(peerId);
        
        // 通知会话中的其他对等方
        for (const otherPeerId of sessionPeers) {
          const otherPeer = this.peers.get(otherPeerId);
          if (otherPeer) {
            otherPeer.socket.emit('peer-left', { peerId });
          }
        }

        // 从Redis中移除
        this.redis.srem(`session:${sessionId}`, peerId);
      }
    }

    // 移除对等连接记录
    this.peers.delete(peerId);
  }

  private startPerformanceMonitoring(): void {
    setInterval(() => {
      const stats = {
        connectedPeers: this.peers.size,
        activeSessions: this.sessions.size,
        averageLatency: this.latencyMonitor.getAverageLatency(),
        signalingLatency: this.latencyMonitor.getSignalingLatency()
      };

      this.logger.log(`性能统计: ${JSON.stringify(stats)}`);

      // 发送统计信息到监控系统
      this.server.emit('performance-stats', stats);
    }, 10000); // 每10秒报告一次
  }

  getPerformanceMetrics(): any {
    return {
      connectedPeers: this.peers.size,
      activeSessions: this.sessions.size,
      latencyStats: this.latencyMonitor.getStatistics()
    };
  }
}

class LatencyMonitor {
  private latencies: number[] = [];
  private signalingLatencies: number[] = [];

  recordSignalingLatency(latency: number): void {
    this.signalingLatencies.push(latency);
    if (this.signalingLatencies.length > 1000) {
      this.signalingLatencies.shift();
    }
  }

  getAverageLatency(): number {
    if (this.latencies.length === 0) return 0;
    return this.latencies.reduce((sum, lat) => sum + lat, 0) / this.latencies.length;
  }

  getSignalingLatency(): number {
    if (this.signalingLatencies.length === 0) return 0;
    return this.signalingLatencies.reduce((sum, lat) => sum + lat, 0) / this.signalingLatencies.length;
  }

  getStatistics(): any {
    return {
      averageLatency: this.getAverageLatency(),
      signalingLatency: this.getSignalingLatency(),
      sampleCount: this.latencies.length
    };
  }
}
