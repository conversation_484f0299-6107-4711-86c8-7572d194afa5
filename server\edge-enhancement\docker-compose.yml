version: '3.8'

services:
  # 边缘计算增强服务
  edge-enhancement:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: edge-enhancement
    hostname: edge-enhancement
    restart: unless-stopped
    ports:
      - "${EDGE_ENHANCEMENT_PORT:-3040}:3040"
    environment:
      # 服务配置
      - NODE_ENV=production
      - PORT=3040
      - LOG_LEVEL=${LOG_LEVEL:-info}
      
      # 智能调度配置
      - SCHEDULER_LEARNING_RATE=${SCHEDULER_LEARNING_RATE:-0.1}
      - SCHEDULER_DISCOUNT_FACTOR=${SCHEDULER_DISCOUNT_FACTOR:-0.9}
      - SCHEDULER_EXPLORATION_RATE=${SCHEDULER_EXPLORATION_RATE:-0.1}
      
      # 缓存配置
      - CACHE_L1_MAX_SIZE=${CACHE_L1_MAX_SIZE:-1000}
      - CACHE_L2_MAX_SIZE=${CACHE_L2_MAX_SIZE:-10000}
      - CACHE_L3_MAX_SIZE=${CACHE_L3_MAX_SIZE:-100000}
      - CACHE_L1_MAX_MEMORY=${CACHE_L1_MAX_MEMORY:-536870912}
      - CACHE_L2_MAX_MEMORY=${CACHE_L2_MAX_MEMORY:-2147483648}
      - CACHE_L3_MAX_MEMORY=${CACHE_L3_MAX_MEMORY:-10737418240}
      - CACHE_DEFAULT_TTL=${CACHE_DEFAULT_TTL:-3600}
      - CACHE_CLEANUP_INTERVAL=${CACHE_CLEANUP_INTERVAL:-300000}
      - CACHE_PRELOAD_THRESHOLD=${CACHE_PRELOAD_THRESHOLD:-0.7}
      
      # 网络配置
      - NETWORK_ENABLE_COMPRESSION=${NETWORK_ENABLE_COMPRESSION:-true}
      - NETWORK_ENABLE_FEC=${NETWORK_ENABLE_FEC:-true}
      - NETWORK_DEFAULT_RELIABILITY=${NETWORK_DEFAULT_RELIABILITY:-reliable}
      
      # 数据库配置（如果需要）
      - DATABASE_URL=${DATABASE_URL:-****************************************/edge_enhancement}
      - REDIS_URL=${REDIS_URL:-redis://redis:6379}
      
      # 监控配置
      - METRICS_ENABLED=${METRICS_ENABLED:-true}
      - METRICS_PORT=${METRICS_PORT:-9090}
      - HEALTH_CHECK_INTERVAL=${HEALTH_CHECK_INTERVAL:-30000}
      
    volumes:
      - edge-enhancement-data:/app/data
      - edge-enhancement-logs:/app/logs
      - edge-enhancement-cache:/app/cache
    
    networks:
      - edge-network
      - monitoring
    
    depends_on:
      - redis
      - postgres
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3040/edge-enhancement/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: edge-enhancement-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
    networks:
      - edge-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: edge-enhancement-postgres
    restart: unless-stopped
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-edge_enhancement}
      - POSTGRES_USER=${POSTGRES_USER:-edge_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-edge_password}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - edge-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-edge_user} -d ${POSTGRES_DB:-edge_enhancement}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: edge-enhancement-prometheus
    restart: unless-stopped
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - monitoring
    depends_on:
      - edge-enhancement

  # Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: edge-enhancement-grafana
    restart: unless-stopped
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - monitoring
    depends_on:
      - prometheus

  # Nginx负载均衡器
  nginx:
    image: nginx:alpine
    container_name: edge-enhancement-nginx
    restart: unless-stopped
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_SSL_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - nginx-logs:/var/log/nginx
    networks:
      - edge-network
    depends_on:
      - edge-enhancement

# 网络配置
networks:
  edge-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  monitoring:
    driver: bridge

# 数据卷配置
volumes:
  edge-enhancement-data:
    driver: local
  edge-enhancement-logs:
    driver: local
  edge-enhancement-cache:
    driver: local
  redis-data:
    driver: local
  postgres-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  nginx-logs:
    driver: local
