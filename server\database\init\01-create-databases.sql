-- DL引擎RAG应用系统数据库初始化脚本

-- 创建知识库数据库
CREATE DATABASE IF NOT EXISTS ir_engine_knowledge
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- 创建RAG服务数据库
CREATE DATABASE IF NOT EXISTS ir_engine_rag
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- 创建数字人服务数据库
CREATE DATABASE IF NOT EXISTS ir_engine_avatars
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- 创建用户和权限
CREATE USER IF NOT EXISTS 'knowledge_user'@'%' IDENTIFIED BY 'knowledge_password';
CREATE USER IF NOT EXISTS 'rag_user'@'%' IDENTIFIED BY 'rag_password';
CREATE USER IF NOT EXISTS 'avatar_user'@'%' IDENTIFIED BY 'avatar_password';

-- 授权
GRANT ALL PRIVILEGES ON ir_engine_knowledge.* TO 'knowledge_user'@'%';
GRANT ALL PRIVILEGES ON ir_engine_rag.* TO 'rag_user'@'%';
GRANT ALL PRIVILEGES ON ir_engine_avatars.* TO 'avatar_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 使用知识库数据库
USE ir_engine_knowledge;

-- 知识库表
CREATE TABLE IF NOT EXISTS knowledge_bases (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    language VARCHAR(10) DEFAULT 'zh-CN',
    status ENUM('active', 'inactive', 'processing') DEFAULT 'active',
    document_count INT DEFAULT 0,
    vector_count BIGINT DEFAULT 0,
    size_bytes BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 文档表
CREATE TABLE IF NOT EXISTS documents (
    id VARCHAR(36) PRIMARY KEY,
    knowledge_base_id VARCHAR(36) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size BIGINT NOT NULL,
    status ENUM('uploading', 'processing', 'completed', 'failed') DEFAULT 'uploading',
    chunk_count INT DEFAULT 0,
    vector_count INT DEFAULT 0,
    error_message TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (knowledge_base_id) REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    INDEX idx_knowledge_base_id (knowledge_base_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 文档块表
CREATE TABLE IF NOT EXISTS document_chunks (
    id VARCHAR(36) PRIMARY KEY,
    document_id VARCHAR(36) NOT NULL,
    chunk_index INT NOT NULL,
    content TEXT NOT NULL,
    content_hash VARCHAR(64) NOT NULL,
    token_count INT DEFAULT 0,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    INDEX idx_document_id (document_id),
    INDEX idx_chunk_index (chunk_index),
    INDEX idx_content_hash (content_hash)
);

-- 使用RAG服务数据库
USE ir_engine_rag;

-- RAG应用表
CREATE TABLE IF NOT EXISTS rag_applications (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    knowledge_base_id VARCHAR(36) NOT NULL,
    status ENUM('draft', 'active', 'inactive', 'error') DEFAULT 'draft',
    config JSON NOT NULL,
    total_sessions INT DEFAULT 0,
    total_messages INT DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_knowledge_base_id (knowledge_base_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 对话会话表
CREATE TABLE IF NOT EXISTS rag_sessions (
    id VARCHAR(36) PRIMARY KEY,
    application_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(255),
    status ENUM('active', 'idle', 'ended') DEFAULT 'active',
    message_count INT DEFAULT 0,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    end_time TIMESTAMP NULL,
    metadata JSON,
    FOREIGN KEY (application_id) REFERENCES rag_applications(id) ON DELETE CASCADE,
    INDEX idx_application_id (application_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time)
);

-- 对话消息表
CREATE TABLE IF NOT EXISTS rag_messages (
    id VARCHAR(36) PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    role ENUM('user', 'assistant') NOT NULL,
    content TEXT NOT NULL,
    sources JSON,
    confidence DECIMAL(4,3),
    processing_time_ms INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES rag_sessions(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_role (role),
    INDEX idx_created_at (created_at)
);

-- 使用数字人服务数据库
USE ir_engine_avatars;

-- 数字人配置表
CREATE TABLE IF NOT EXISTS avatars (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    appearance_config JSON NOT NULL,
    voice_config JSON NOT NULL,
    personality_config JSON,
    animation_config JSON,
    status ENUM('active', 'inactive') DEFAULT 'active',
    usage_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 数字人实例表
CREATE TABLE IF NOT EXISTS avatar_instances (
    id VARCHAR(36) PRIMARY KEY,
    avatar_id VARCHAR(36) NOT NULL,
    scene_id VARCHAR(255),
    application_id VARCHAR(36),
    position JSON NOT NULL,
    rotation JSON NOT NULL,
    scale JSON NOT NULL,
    status ENUM('inactive', 'active', 'talking', 'listening') DEFAULT 'inactive',
    current_session_id VARCHAR(36),
    interaction_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (avatar_id) REFERENCES avatars(id) ON DELETE CASCADE,
    INDEX idx_avatar_id (avatar_id),
    INDEX idx_scene_id (scene_id),
    INDEX idx_application_id (application_id),
    INDEX idx_status (status)
);

-- 交互记录表
CREATE TABLE IF NOT EXISTS interactions (
    id VARCHAR(36) PRIMARY KEY,
    avatar_instance_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(255),
    interaction_type ENUM('click', 'voice', 'proximity', 'gesture') NOT NULL,
    session_id VARCHAR(36),
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP NULL,
    duration_seconds INT,
    message_count INT DEFAULT 0,
    satisfaction_rating INT,
    metadata JSON,
    FOREIGN KEY (avatar_instance_id) REFERENCES avatar_instances(id) ON DELETE CASCADE,
    INDEX idx_avatar_instance_id (avatar_instance_id),
    INDEX idx_user_id (user_id),
    INDEX idx_interaction_type (interaction_type),
    INDEX idx_start_time (start_time)
);

-- 创建视图和存储过程

-- 知识库统计视图
USE ir_engine_knowledge;
CREATE OR REPLACE VIEW knowledge_base_stats AS
SELECT 
    kb.id,
    kb.name,
    kb.status,
    COUNT(d.id) as document_count,
    SUM(d.file_size) as total_size_bytes,
    SUM(d.chunk_count) as total_chunks,
    SUM(d.vector_count) as total_vectors,
    kb.created_at,
    kb.updated_at
FROM knowledge_bases kb
LEFT JOIN documents d ON kb.id = d.knowledge_base_id AND d.status = 'completed'
GROUP BY kb.id;

-- RAG应用统计视图
USE ir_engine_rag;
CREATE OR REPLACE VIEW rag_application_stats AS
SELECT 
    app.id,
    app.name,
    app.status,
    COUNT(DISTINCT s.id) as total_sessions,
    COUNT(DISTINCT CASE WHEN s.status = 'active' THEN s.id END) as active_sessions,
    COUNT(m.id) as total_messages,
    AVG(CASE WHEN m.confidence IS NOT NULL THEN m.confidence END) as avg_confidence,
    AVG(CASE WHEN m.processing_time_ms IS NOT NULL THEN m.processing_time_ms END) as avg_processing_time,
    app.created_at,
    app.updated_at
FROM rag_applications app
LEFT JOIN rag_sessions s ON app.id = s.application_id
LEFT JOIN rag_messages m ON s.id = m.session_id
GROUP BY app.id;

-- 数字人使用统计视图
USE ir_engine_avatars;
CREATE OR REPLACE VIEW avatar_usage_stats AS
SELECT 
    a.id,
    a.name,
    a.status,
    COUNT(DISTINCT ai.id) as instance_count,
    COUNT(DISTINCT CASE WHEN ai.status = 'active' THEN ai.id END) as active_instances,
    COUNT(i.id) as total_interactions,
    AVG(i.duration_seconds) as avg_interaction_duration,
    AVG(i.satisfaction_rating) as avg_satisfaction_rating,
    a.created_at,
    a.updated_at
FROM avatars a
LEFT JOIN avatar_instances ai ON a.id = ai.avatar_id
LEFT JOIN interactions i ON ai.id = i.avatar_instance_id
GROUP BY a.id;
