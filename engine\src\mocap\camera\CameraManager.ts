/**
 * 摄像头管理器
 * 负责摄像头设备的访问、配置和数据获取
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';

/**
 * 摄像头配置
 */
export interface CameraConfig {
  /** 设备ID */
  deviceId?: string;
  /** 分辨率 */
  resolution: {
    width: number;
    height: number;
  };
  /** 帧率 */
  frameRate: number;
  /** 是否启用音频 */
  audio: boolean;
  /** 视频约束 */
  videoConstraints?: MediaTrackConstraints;
}

/**
 * 摄像头设备信息
 */
export interface CameraDevice {
  /** 设备ID */
  deviceId: string;
  /** 设备标签 */
  label: string;
  /** 设备类型 */
  kind: MediaDeviceKind;
  /** 是否为默认设备 */
  isDefault: boolean;
}

/**
 * 摄像头状态
 */
export enum CameraState {
  IDLE = 'idle',
  INITIALIZING = 'initializing',
  ACTIVE = 'active',
  ERROR = 'error',
  STOPPED = 'stopped'
}

/**
 * 摄像头管理器
 */
export class CameraManager extends EventEmitter {
  private videoElement: HTMLVideoElement | null = null;
  private stream: MediaStream | null = null;
  private config: CameraConfig;
  private state: CameraState = CameraState.IDLE;
  private availableDevices: CameraDevice[] = [];
  private canvas: HTMLCanvasElement;
  private context: CanvasRenderingContext2D;
  private animationFrameId: number | null = null;
  private lastFrameTime = 0;
  private frameCount = 0;
  private actualFPS = 0;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: CameraConfig = {
    resolution: { width: 640, height: 480 },
    frameRate: 30,
    audio: false
  };

  constructor(config: Partial<CameraConfig> = {}) {
    super();
    this.config = { ...CameraManager.DEFAULT_CONFIG, ...config };
    
    // 创建画布用于帧提取
    this.canvas = document.createElement('canvas');
    this.context = this.canvas.getContext('2d')!;
    
    // 监听设备变化
    this.setupDeviceChangeListener();
  }

  /**
   * 初始化摄像头
   */
  public async initialize(deviceId?: string): Promise<void> {
    try {
      this.setState(CameraState.INITIALIZING);
      
      // 更新设备ID
      if (deviceId) {
        this.config.deviceId = deviceId;
      }
      
      // 获取可用设备
      await this.updateAvailableDevices();
      
      // 创建视频元素
      this.createVideoElement();
      
      // 获取媒体流
      await this.createMediaStream();
      
      // 启动视频
      await this.startVideo();
      
      this.setState(CameraState.ACTIVE);
      this.emit('initialized', this.config);
      
      Debug.log('CameraManager', '摄像头初始化成功', {
        deviceId: this.config.deviceId,
        resolution: this.config.resolution,
        frameRate: this.config.frameRate
      });
      
    } catch (error) {
      this.setState(CameraState.ERROR);
      this.emit('error', error);
      Debug.error('CameraManager', '摄像头初始化失败', error);
      throw error;
    }
  }

  /**
   * 获取可用摄像头设备
   */
  public async getAvailableDevices(): Promise<CameraDevice[]> {
    await this.updateAvailableDevices();
    return [...this.availableDevices];
  }

  /**
   * 更新可用设备列表
   */
  private async updateAvailableDevices(): Promise<void> {
    try {
      // 请求权限
      await navigator.mediaDevices.getUserMedia({ video: true });
      
      // 获取设备列表
      const devices = await navigator.mediaDevices.enumerateDevices();
      
      this.availableDevices = devices
        .filter(device => device.kind === 'videoinput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `摄像头 ${device.deviceId.slice(0, 8)}`,
          kind: device.kind,
          isDefault: device.deviceId === 'default'
        }));
        
      this.emit('devicesUpdated', this.availableDevices);
      
    } catch (error) {
      Debug.error('CameraManager', '获取摄像头设备失败', error);
      throw error;
    }
  }

  /**
   * 创建视频元素
   */
  private createVideoElement(): void {
    if (this.videoElement) {
      this.destroyVideoElement();
    }
    
    this.videoElement = document.createElement('video');
    this.videoElement.style.display = 'none';
    this.videoElement.autoplay = true;
    this.videoElement.muted = true;
    this.videoElement.playsInline = true;
    
    // 添加到DOM
    document.body.appendChild(this.videoElement);
    
    // 监听视频事件
    this.videoElement.addEventListener('loadedmetadata', () => {
      this.emit('videoReady', {
        width: this.videoElement!.videoWidth,
        height: this.videoElement!.videoHeight
      });
    });
    
    this.videoElement.addEventListener('error', (event) => {
      this.emit('videoError', event);
    });
  }

  /**
   * 创建媒体流
   */
  private async createMediaStream(): Promise<void> {
    const constraints: MediaStreamConstraints = {
      video: {
        deviceId: this.config.deviceId ? { exact: this.config.deviceId } : undefined,
        width: { ideal: this.config.resolution.width },
        height: { ideal: this.config.resolution.height },
        frameRate: { ideal: this.config.frameRate },
        ...this.config.videoConstraints
      },
      audio: this.config.audio
    };
    
    this.stream = await navigator.mediaDevices.getUserMedia(constraints);
    
    // 获取实际的视频轨道设置
    const videoTrack = this.stream.getVideoTracks()[0];
    if (videoTrack) {
      const settings = videoTrack.getSettings();
      Debug.log('CameraManager', '摄像头实际设置', settings);
      
      // 更新实际分辨率
      if (settings.width && settings.height) {
        this.config.resolution.width = settings.width;
        this.config.resolution.height = settings.height;
      }
      
      // 更新实际帧率
      if (settings.frameRate) {
        this.config.frameRate = settings.frameRate;
      }
    }
  }

  /**
   * 启动视频
   */
  private async startVideo(): Promise<void> {
    if (!this.videoElement || !this.stream) {
      throw new Error('视频元素或媒体流未准备好');
    }
    
    this.videoElement.srcObject = this.stream;
    
    return new Promise((resolve, reject) => {
      this.videoElement!.onloadedmetadata = () => {
        this.videoElement!.play()
          .then(() => {
            // 设置画布尺寸
            this.canvas.width = this.videoElement!.videoWidth;
            this.canvas.height = this.videoElement!.videoHeight;
            
            // 开始帧率监控
            this.startFrameRateMonitoring();
            
            resolve();
          })
          .catch(reject);
      };
      
      this.videoElement!.onerror = reject;
    });
  }

  /**
   * 获取当前帧
   */
  public getCurrentFrame(): ImageData | null {
    if (!this.videoElement || this.state !== CameraState.ACTIVE) {
      return null;
    }
    
    try {
      // 绘制视频帧到画布
      this.context.drawImage(
        this.videoElement,
        0, 0,
        this.canvas.width,
        this.canvas.height
      );
      
      // 获取图像数据
      const imageData = this.context.getImageData(
        0, 0,
        this.canvas.width,
        this.canvas.height
      );
      
      this.frameCount++;
      return imageData;
      
    } catch (error) {
      Debug.error('CameraManager', '获取当前帧失败', error);
      return null;
    }
  }

  /**
   * 开始帧率监控
   */
  private startFrameRateMonitoring(): void {
    const updateFPS = () => {
      const now = performance.now();
      if (this.lastFrameTime > 0) {
        const deltaTime = now - this.lastFrameTime;
        this.actualFPS = 1000 / deltaTime;
      }
      this.lastFrameTime = now;
      
      if (this.state === CameraState.ACTIVE) {
        this.animationFrameId = requestAnimationFrame(updateFPS);
      }
    };
    
    updateFPS();
  }

  /**
   * 停止帧率监控
   */
  private stopFrameRateMonitoring(): void {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  /**
   * 更新配置
   */
  public async updateConfig(newConfig: Partial<CameraConfig>): Promise<void> {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };
    
    // 如果关键配置发生变化，需要重新初始化
    const needsReinitialization = 
      oldConfig.deviceId !== this.config.deviceId ||
      oldConfig.resolution.width !== this.config.resolution.width ||
      oldConfig.resolution.height !== this.config.resolution.height ||
      oldConfig.frameRate !== this.config.frameRate;
    
    if (needsReinitialization && this.state === CameraState.ACTIVE) {
      await this.stop();
      await this.initialize(this.config.deviceId);
    }
    
    this.emit('configUpdated', this.config);
  }

  /**
   * 停止摄像头
   */
  public async stop(): Promise<void> {
    this.setState(CameraState.STOPPED);
    
    // 停止帧率监控
    this.stopFrameRateMonitoring();
    
    // 停止媒体流
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    
    // 清理视频元素
    this.destroyVideoElement();
    
    this.emit('stopped');
    Debug.log('CameraManager', '摄像头已停止');
  }

  /**
   * 销毁视频元素
   */
  private destroyVideoElement(): void {
    if (this.videoElement) {
      if (this.videoElement.parentNode) {
        this.videoElement.parentNode.removeChild(this.videoElement);
      }
      this.videoElement = null;
    }
  }

  /**
   * 设置设备变化监听器
   */
  private setupDeviceChangeListener(): void {
    if (navigator.mediaDevices && navigator.mediaDevices.addEventListener) {
      navigator.mediaDevices.addEventListener('devicechange', () => {
        this.updateAvailableDevices().catch(error => {
          Debug.error('CameraManager', '设备变化处理失败', error);
        });
      });
    }
  }

  /**
   * 设置状态
   */
  private setState(newState: CameraState): void {
    if (this.state !== newState) {
      const oldState = this.state;
      this.state = newState;
      this.emit('stateChanged', newState, oldState);
    }
  }

  /**
   * 获取当前状态
   */
  public getState(): CameraState {
    return this.state;
  }

  /**
   * 获取当前配置
   */
  public getConfig(): CameraConfig {
    return { ...this.config };
  }

  /**
   * 获取实际FPS
   */
  public getActualFPS(): number {
    return this.actualFPS;
  }

  /**
   * 获取帧计数
   */
  public getFrameCount(): number {
    return this.frameCount;
  }

  /**
   * 是否处于活跃状态
   */
  public get isActive(): boolean {
    return this.state === CameraState.ACTIVE;
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.stop().catch(error => {
      Debug.error('CameraManager', '销毁时停止摄像头失败', error);
    });
    
    this.removeAllListeners();
    Debug.log('CameraManager', '摄像头管理器已销毁');
  }
}
