/**
 * 并发压力测试
 * 验证系统在100+并发用户下的性能表现
 */
import { io, Socket } from 'socket.io-client';
import { performance } from 'perf_hooks';

// 测试配置
interface TestConfig {
  serverUrl: string;
  maxUsers: number;
  rampUpTime: number; // 用户增长时间（秒）
  testDuration: number; // 测试持续时间（秒）
  messageInterval: number; // 消息发送间隔（毫秒）
  roomId: string;
}

// 用户模拟器
class UserSimulator {
  private socket: Socket;
  private userId: string;
  private isConnected = false;
  private messagesSent = 0;
  private messagesReceived = 0;
  private latencies: number[] = [];
  private errors: string[] = [];
  private startTime: number;
  
  constructor(
    private config: TestConfig,
    private userIndex: number
  ) {
    this.userId = `user_${userIndex}_${Date.now()}`;
    this.startTime = performance.now();
  }
  
  /**
   * 连接到服务器
   */
  public async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.socket = io(this.config.serverUrl, {
        auth: {
          userId: this.userId,
          roomId: this.config.roomId,
        },
        transports: ['websocket'],
      });
      
      this.socket.on('connect', () => {
        this.isConnected = true;
        console.log(`用户 ${this.userId} 已连接`);
        this.setupEventListeners();
        resolve();
      });
      
      this.socket.on('connect_error', (error) => {
        this.errors.push(`连接错误: ${error.message}`);
        reject(error);
      });
      
      // 连接超时
      setTimeout(() => {
        if (!this.isConnected) {
          reject(new Error('连接超时'));
        }
      }, 10000);
    });
  }
  
  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    this.socket.on('object_update', (data) => {
      this.messagesReceived++;
      
      // 计算延迟
      if (data.timestamp) {
        const latency = Date.now() - data.timestamp;
        this.latencies.push(latency);
      }
    });
    
    this.socket.on('user_cursor', (data) => {
      this.messagesReceived++;
    });
    
    this.socket.on('batch_update', (data) => {
      this.messagesReceived += data.count || 1;
    });
    
    this.socket.on('error', (error) => {
      this.errors.push(`Socket错误: ${error.message || error}`);
    });
    
    this.socket.on('disconnect', (reason) => {
      this.isConnected = false;
      console.log(`用户 ${this.userId} 断开连接: ${reason}`);
    });
  }
  
  /**
   * 开始模拟用户行为
   */
  public startSimulation(): void {
    if (!this.isConnected) return;
    
    // 定期发送对象更新消息
    const updateInterval = setInterval(() => {
      if (!this.isConnected) {
        clearInterval(updateInterval);
        return;
      }
      
      this.sendObjectUpdate();
    }, this.config.messageInterval);
    
    // 定期发送光标移动消息
    const cursorInterval = setInterval(() => {
      if (!this.isConnected) {
        clearInterval(cursorInterval);
        return;
      }
      
      this.sendCursorUpdate();
    }, this.config.messageInterval / 2);
    
    // 偶尔发送场景操作
    const sceneInterval = setInterval(() => {
      if (!this.isConnected) {
        clearInterval(sceneInterval);
        return;
      }
      
      if (Math.random() < 0.1) { // 10%概率
        this.sendSceneOperation();
      }
    }, this.config.messageInterval * 5);
  }
  
  /**
   * 发送对象更新消息
   */
  private sendObjectUpdate(): void {
    const message = {
      objectId: `object_${Math.floor(Math.random() * 100)}`,
      changes: {
        position: [
          Math.random() * 100,
          Math.random() * 100,
          Math.random() * 100,
        ],
        rotation: [
          Math.random() * 360,
          Math.random() * 360,
          Math.random() * 360,
        ],
      },
      timestamp: Date.now(),
    };
    
    this.socket.emit('object_update', message);
    this.messagesSent++;
  }
  
  /**
   * 发送光标更新消息
   */
  private sendCursorUpdate(): void {
    const message = {
      x: Math.random() * 1920,
      y: Math.random() * 1080,
      timestamp: Date.now(),
    };
    
    this.socket.emit('user_cursor', message);
    this.messagesSent++;
  }
  
  /**
   * 发送场景操作消息
   */
  private sendSceneOperation(): void {
    const operations = ['create', 'delete', 'duplicate'];
    const operation = operations[Math.floor(Math.random() * operations.length)];
    
    const message = {
      operation,
      objectId: `object_${Math.floor(Math.random() * 100)}`,
      data: {
        type: 'mesh',
        name: `Object ${Date.now()}`,
      },
      timestamp: Date.now(),
    };
    
    this.socket.emit('scene_operation', message);
    this.messagesSent++;
  }
  
  /**
   * 断开连接
   */
  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
    }
  }
  
  /**
   * 获取统计信息
   */
  public getStats(): {
    userId: string;
    messagesSent: number;
    messagesReceived: number;
    averageLatency: number;
    maxLatency: number;
    minLatency: number;
    errorCount: number;
    errors: string[];
    uptime: number;
  } {
    const now = performance.now();
    const averageLatency = this.latencies.length > 0 
      ? this.latencies.reduce((a, b) => a + b, 0) / this.latencies.length 
      : 0;
    
    return {
      userId: this.userId,
      messagesSent: this.messagesSent,
      messagesReceived: this.messagesReceived,
      averageLatency,
      maxLatency: this.latencies.length > 0 ? Math.max(...this.latencies) : 0,
      minLatency: this.latencies.length > 0 ? Math.min(...this.latencies) : 0,
      errorCount: this.errors.length,
      errors: this.errors,
      uptime: now - this.startTime,
    };
  }
}

/**
 * 并发压力测试类
 */
export class ConcurrencyStressTest {
  private users: UserSimulator[] = [];
  private testStartTime: number;
  private testEndTime: number;
  private isRunning = false;
  
  constructor(private config: TestConfig) {}
  
  /**
   * 运行压力测试
   */
  public async run(): Promise<void> {
    console.log('开始并发压力测试...');
    console.log(`目标用户数: ${this.config.maxUsers}`);
    console.log(`增长时间: ${this.config.rampUpTime}秒`);
    console.log(`测试时长: ${this.config.testDuration}秒`);
    
    this.isRunning = true;
    this.testStartTime = performance.now();
    
    try {
      // 阶段1：逐步增加用户
      await this.rampUpUsers();
      
      // 阶段2：维持负载
      await this.maintainLoad();
      
      // 阶段3：收集结果
      await this.collectResults();
      
    } catch (error) {
      console.error('测试执行失败:', error);
    } finally {
      this.isRunning = false;
      await this.cleanup();
    }
  }
  
  /**
   * 逐步增加用户
   */
  private async rampUpUsers(): Promise<void> {
    console.log('阶段1: 逐步增加用户...');
    
    const userInterval = (this.config.rampUpTime * 1000) / this.config.maxUsers;
    
    for (let i = 0; i < this.config.maxUsers; i++) {
      if (!this.isRunning) break;
      
      try {
        const user = new UserSimulator(this.config, i);
        await user.connect();
        user.startSimulation();
        this.users.push(user);
        
        console.log(`用户 ${i + 1}/${this.config.maxUsers} 已连接`);
        
        // 等待下一个用户连接
        if (i < this.config.maxUsers - 1) {
          await this.sleep(userInterval);
        }
        
      } catch (error) {
        console.error(`用户 ${i} 连接失败:`, error.message);
      }
    }
    
    console.log(`成功连接 ${this.users.length} 个用户`);
  }
  
  /**
   * 维持负载
   */
  private async maintainLoad(): Promise<void> {
    console.log('阶段2: 维持负载...');
    
    const startTime = Date.now();
    const endTime = startTime + this.config.testDuration * 1000;
    
    // 定期输出统计信息
    const statsInterval = setInterval(() => {
      if (!this.isRunning || Date.now() >= endTime) {
        clearInterval(statsInterval);
        return;
      }
      
      this.printCurrentStats();
    }, 10000); // 每10秒输出一次
    
    // 等待测试完成
    while (this.isRunning && Date.now() < endTime) {
      await this.sleep(1000);
    }
    
    clearInterval(statsInterval);
  }
  
  /**
   * 收集测试结果
   */
  private async collectResults(): Promise<void> {
    console.log('阶段3: 收集测试结果...');
    
    this.testEndTime = performance.now();
    
    // 收集所有用户的统计信息
    const allStats = this.users.map(user => user.getStats());
    
    // 计算总体统计
    const totalStats = this.calculateTotalStats(allStats);
    
    // 输出结果
    this.printFinalResults(totalStats, allStats);
  }
  
  /**
   * 计算总体统计
   */
  private calculateTotalStats(allStats: any[]): any {
    const connectedUsers = allStats.filter(stats => stats.messagesSent > 0);
    
    const totalMessagesSent = allStats.reduce((sum, stats) => sum + stats.messagesSent, 0);
    const totalMessagesReceived = allStats.reduce((sum, stats) => sum + stats.messagesReceived, 0);
    const totalErrors = allStats.reduce((sum, stats) => sum + stats.errorCount, 0);
    
    const latencies = allStats
      .filter(stats => stats.averageLatency > 0)
      .map(stats => stats.averageLatency);
    
    const averageLatency = latencies.length > 0 
      ? latencies.reduce((a, b) => a + b, 0) / latencies.length 
      : 0;
    
    const maxLatency = allStats.reduce((max, stats) => 
      Math.max(max, stats.maxLatency), 0);
    
    const testDuration = (this.testEndTime - this.testStartTime) / 1000;
    const messagesPerSecond = totalMessagesSent / testDuration;
    
    return {
      totalUsers: this.config.maxUsers,
      connectedUsers: connectedUsers.length,
      connectionSuccessRate: (connectedUsers.length / this.config.maxUsers) * 100,
      totalMessagesSent,
      totalMessagesReceived,
      messagesPerSecond,
      averageLatency,
      maxLatency,
      totalErrors,
      errorRate: (totalErrors / totalMessagesSent) * 100,
      testDuration,
    };
  }
  
  /**
   * 输出当前统计信息
   */
  private printCurrentStats(): void {
    const connectedUsers = this.users.filter(user => user.getStats().messagesSent > 0);
    const totalMessages = this.users.reduce((sum, user) => sum + user.getStats().messagesSent, 0);
    const totalErrors = this.users.reduce((sum, user) => sum + user.getStats().errorCount, 0);
    
    console.log(`当前状态: ${connectedUsers.length} 用户在线, ${totalMessages} 消息已发送, ${totalErrors} 错误`);
  }
  
  /**
   * 输出最终结果
   */
  private printFinalResults(totalStats: any, allStats: any[]): void {
    console.log('\n=== 压力测试结果 ===');
    console.log(`测试时长: ${totalStats.testDuration.toFixed(2)} 秒`);
    console.log(`目标用户数: ${totalStats.totalUsers}`);
    console.log(`成功连接用户数: ${totalStats.connectedUsers}`);
    console.log(`连接成功率: ${totalStats.connectionSuccessRate.toFixed(2)}%`);
    console.log(`总发送消息数: ${totalStats.totalMessagesSent}`);
    console.log(`总接收消息数: ${totalStats.totalMessagesReceived}`);
    console.log(`消息发送速率: ${totalStats.messagesPerSecond.toFixed(2)} 消息/秒`);
    console.log(`平均延迟: ${totalStats.averageLatency.toFixed(2)} ms`);
    console.log(`最大延迟: ${totalStats.maxLatency.toFixed(2)} ms`);
    console.log(`总错误数: ${totalStats.totalErrors}`);
    console.log(`错误率: ${totalStats.errorRate.toFixed(2)}%`);
    
    // 性能评估
    console.log('\n=== 性能评估 ===');
    if (totalStats.connectionSuccessRate >= 95) {
      console.log('✅ 连接稳定性: 优秀');
    } else if (totalStats.connectionSuccessRate >= 90) {
      console.log('⚠️  连接稳定性: 良好');
    } else {
      console.log('❌ 连接稳定性: 需要改进');
    }
    
    if (totalStats.averageLatency <= 100) {
      console.log('✅ 响应延迟: 优秀');
    } else if (totalStats.averageLatency <= 300) {
      console.log('⚠️  响应延迟: 良好');
    } else {
      console.log('❌ 响应延迟: 需要改进');
    }
    
    if (totalStats.errorRate <= 1) {
      console.log('✅ 错误率: 优秀');
    } else if (totalStats.errorRate <= 5) {
      console.log('⚠️  错误率: 良好');
    } else {
      console.log('❌ 错误率: 需要改进');
    }
    
    // 输出详细错误信息
    const errorUsers = allStats.filter(stats => stats.errorCount > 0);
    if (errorUsers.length > 0) {
      console.log('\n=== 错误详情 ===');
      errorUsers.forEach(stats => {
        console.log(`用户 ${stats.userId}: ${stats.errorCount} 个错误`);
        stats.errors.forEach((error: string) => {
          console.log(`  - ${error}`);
        });
      });
    }
  }
  
  /**
   * 清理资源
   */
  private async cleanup(): Promise<void> {
    console.log('清理测试资源...');
    
    // 断开所有用户连接
    for (const user of this.users) {
      user.disconnect();
    }
    
    this.users = [];
    console.log('清理完成');
  }
  
  /**
   * 停止测试
   */
  public stop(): void {
    this.isRunning = false;
  }
  
  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 运行测试的主函数
export async function runConcurrencyTest(): Promise<void> {
  const config: TestConfig = {
    serverUrl: process.env.TEST_SERVER_URL || 'http://localhost:3005',
    maxUsers: parseInt(process.env.MAX_USERS || '100'),
    rampUpTime: parseInt(process.env.RAMP_UP_TIME || '60'), // 60秒
    testDuration: parseInt(process.env.TEST_DURATION || '300'), // 5分钟
    messageInterval: parseInt(process.env.MESSAGE_INTERVAL || '1000'), // 1秒
    roomId: process.env.ROOM_ID || 'test_room',
  };
  
  const test = new ConcurrencyStressTest(config);
  
  // 处理中断信号
  process.on('SIGINT', () => {
    console.log('\n收到中断信号，停止测试...');
    test.stop();
  });
  
  await test.run();
}

// 如果直接运行此文件
if (require.main === module) {
  runConcurrencyTest().catch(console.error);
}

/**
 * 性能基准测试
 */
export class PerformanceBenchmark {
  private results: Map<string, any> = new Map();

  /**
   * 运行完整的性能基准测试
   */
  public async runFullBenchmark(): Promise<void> {
    console.log('开始性能基准测试...');

    // 测试不同用户数量下的性能
    const userCounts = [10, 25, 50, 75, 100];

    for (const userCount of userCounts) {
      console.log(`\n测试 ${userCount} 个并发用户...`);

      const config: TestConfig = {
        serverUrl: process.env.TEST_SERVER_URL || 'http://localhost:3005',
        maxUsers: userCount,
        rampUpTime: 30, // 30秒增长
        testDuration: 120, // 2分钟测试
        messageInterval: 1000, // 1秒间隔
        roomId: `benchmark_room_${userCount}`,
      };

      const test = new ConcurrencyStressTest(config);
      await test.run();

      // 等待系统恢复
      await this.sleep(30000);
    }

    console.log('\n性能基准测试完成');
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
