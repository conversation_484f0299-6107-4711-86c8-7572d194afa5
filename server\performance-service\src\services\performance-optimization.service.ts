/**
 * 性能优化服务
 * 
 * 提供服务器端性能优化功能，包括：
 * - 分布式性能监控
 * - 自动性能调优
 * - 负载均衡优化
 * - 资源使用优化
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import Redis from 'ioredis';
import * as os from 'os';
import * as process from 'process';

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
  nodeId: string;
  timestamp: number;
  cpu: {
    usage: number;
    loadAverage: number[];
    cores: number;
  };
  memory: {
    used: number;
    total: number;
    usage: number;
    heapUsed: number;
    heapTotal: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    connectionsActive: number;
  };
  application: {
    requestsPerSecond: number;
    averageResponseTime: number;
    errorRate: number;
    activeConnections: number;
  };
  optimization: {
    cacheHitRate: number;
    poolUtilization: number;
    parallelEfficiency: number;
    gcPressure: number;
  };
}

/**
 * 优化配置接口
 */
export interface OptimizationConfig {
  nodeId: string;
  enableAutoTuning: boolean;
  enableLoadBalancing: boolean;
  enableCaching: boolean;
  enableCompression: boolean;
  maxMemoryUsage: number;
  maxCpuUsage: number;
  cacheSize: number;
  poolSize: number;
  parallelThreshold: number;
  gcThreshold: number;
}

/**
 * 优化建议接口
 */
export interface OptimizationRecommendation {
  type: 'critical' | 'warning' | 'info';
  category: 'memory' | 'cpu' | 'network' | 'application';
  title: string;
  description: string;
  action: string;
  impact: 'high' | 'medium' | 'low';
  priority: number;
  estimatedImprovement: string;
}

/**
 * 负载均衡状态
 */
interface LoadBalancingState {
  nodes: Map<string, NodeStatus>;
  totalRequests: number;
  distributionStrategy: 'round_robin' | 'least_connections' | 'cpu_based' | 'memory_based';
  lastRebalance: number;
}

/**
 * 节点状态
 */
interface NodeStatus {
  nodeId: string;
  isHealthy: boolean;
  load: number;
  capacity: number;
  activeConnections: number;
  lastHeartbeat: number;
  metrics: PerformanceMetrics;
}

/**
 * 性能优化服务
 */
@Injectable()
export class PerformanceOptimizationService {
  private readonly logger = new Logger(PerformanceOptimizationService.name);
  private readonly redis: Redis;
  
  private nodeId: string;
  private metricsHistory = new Map<string, PerformanceMetrics[]>();
  private optimizationConfigs = new Map<string, OptimizationConfig>();
  private loadBalancingState: LoadBalancingState;
  
  // 性能监控
  private performanceTimer?: NodeJS.Timeout;
  private requestCounter = 0;
  private responseTimeSum = 0;
  private errorCounter = 0;
  private lastMetricsTime = Date.now();
  
  // 优化状态
  private isAutoTuningEnabled = true;
  private lastOptimizationTime = 0;
  private optimizationCooldown = 30000; // 30秒冷却时间

  constructor(
    private readonly eventEmitter: EventEmitter2,
    redisConfig: any
  ) {
    this.redis = new Redis(redisConfig);
    this.nodeId = `node_${os.hostname()}_${process.pid}`;
    
    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private async initializeService(): Promise<void> {
    try {
      // 初始化负载均衡状态
      this.loadBalancingState = {
        nodes: new Map(),
        totalRequests: 0,
        distributionStrategy: 'cpu_based',
        lastRebalance: Date.now()
      };
      
      // 加载优化配置
      await this.loadOptimizationConfig();
      
      // 启动性能监控
      this.startPerformanceMonitoring();
      
      // 注册节点
      await this.registerNode();
      
      this.logger.log('性能优化服务已启动');
      
    } catch (error) {
      this.logger.error('服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    this.performanceTimer = setInterval(async () => {
      try {
        const metrics = await this.collectPerformanceMetrics();
        await this.processPerformanceMetrics(metrics);
        
        // 自动优化
        if (this.isAutoTuningEnabled) {
          await this.performAutoOptimization(metrics);
        }
        
      } catch (error) {
        this.logger.error('性能监控失败:', error);
      }
    }, 5000); // 5秒间隔
  }

  /**
   * 收集性能指标
   */
  private async collectPerformanceMetrics(): Promise<PerformanceMetrics> {
    const now = Date.now();
    const timeDelta = (now - this.lastMetricsTime) / 1000;
    
    // CPU指标
    const cpuUsage = await this.getCpuUsage();
    const loadAverage = os.loadavg();
    
    // 内存指标
    const memoryUsage = process.memoryUsage();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    
    // 应用指标
    const requestsPerSecond = this.requestCounter / timeDelta;
    const averageResponseTime = this.responseTimeSum / Math.max(this.requestCounter, 1);
    const errorRate = this.errorCounter / Math.max(this.requestCounter, 1);
    
    // 重置计数器
    this.requestCounter = 0;
    this.responseTimeSum = 0;
    this.errorCounter = 0;
    this.lastMetricsTime = now;
    
    const metrics: PerformanceMetrics = {
      nodeId: this.nodeId,
      timestamp: now,
      cpu: {
        usage: cpuUsage,
        loadAverage,
        cores: os.cpus().length
      },
      memory: {
        used: usedMemory,
        total: totalMemory,
        usage: usedMemory / totalMemory,
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal
      },
      network: {
        bytesIn: 0, // 需要从网络接口获取
        bytesOut: 0,
        connectionsActive: 0
      },
      application: {
        requestsPerSecond,
        averageResponseTime,
        errorRate,
        activeConnections: 0
      },
      optimization: {
        cacheHitRate: await this.getCacheHitRate(),
        poolUtilization: await this.getPoolUtilization(),
        parallelEfficiency: await this.getParallelEfficiency(),
        gcPressure: this.getGcPressure()
      }
    };
    
    return metrics;
  }

  /**
   * 处理性能指标
   */
  private async processPerformanceMetrics(metrics: PerformanceMetrics): Promise<void> {
    // 存储指标历史
    if (!this.metricsHistory.has(metrics.nodeId)) {
      this.metricsHistory.set(metrics.nodeId, []);
    }
    
    const history = this.metricsHistory.get(metrics.nodeId)!;
    history.push(metrics);
    
    // 限制历史长度
    if (history.length > 720) { // 保留1小时的数据（5秒间隔）
      history.shift();
    }
    
    // 存储到Redis
    await this.storeMetricsToRedis(metrics);
    
    // 更新节点状态
    await this.updateNodeStatus(metrics);
    
    // 发送事件
    this.eventEmitter.emit('performance.metrics.collected', metrics);
  }

  /**
   * 执行自动优化
   */
  private async performAutoOptimization(metrics: PerformanceMetrics): Promise<void> {
    const now = Date.now();
    
    // 检查冷却时间
    if (now - this.lastOptimizationTime < this.optimizationCooldown) {
      return;
    }
    
    const config = this.optimizationConfigs.get(this.nodeId);
    if (!config) return;
    
    let optimizationPerformed = false;
    
    // 内存优化
    if (metrics.memory.usage > config.maxMemoryUsage) {
      await this.optimizeMemoryUsage(metrics);
      optimizationPerformed = true;
    }
    
    // CPU优化
    if (metrics.cpu.usage > config.maxCpuUsage) {
      await this.optimizeCpuUsage(metrics);
      optimizationPerformed = true;
    }
    
    // 缓存优化
    if (metrics.optimization.cacheHitRate < 0.7) {
      await this.optimizeCaching(metrics);
      optimizationPerformed = true;
    }
    
    // GC优化
    if (metrics.optimization.gcPressure > config.gcThreshold) {
      await this.optimizeGarbageCollection(metrics);
      optimizationPerformed = true;
    }
    
    if (optimizationPerformed) {
      this.lastOptimizationTime = now;
      this.logger.log(`节点 ${this.nodeId} 执行了自动优化`);
    }
  }

  /**
   * 内存使用优化
   */
  private async optimizeMemoryUsage(metrics: PerformanceMetrics): Promise<void> {
    try {
      // 触发垃圾回收
      if (global.gc) {
        global.gc();
      }
      
      // 清理缓存
      await this.cleanupCache();
      
      // 调整对象池大小
      await this.adjustPoolSizes(metrics);
      
      this.logger.log('内存使用优化完成');
      
    } catch (error) {
      this.logger.error('内存优化失败:', error);
    }
  }

  /**
   * CPU使用优化
   */
  private async optimizeCpuUsage(metrics: PerformanceMetrics): Promise<void> {
    try {
      const config = this.optimizationConfigs.get(this.nodeId);
      if (!config) return;
      
      // 调整并行阈值
      if (metrics.cpu.usage > 0.8) {
        config.parallelThreshold = Math.max(1, config.parallelThreshold - 1);
      }
      
      // 启用负载均衡
      if (!config.enableLoadBalancing) {
        config.enableLoadBalancing = true;
        await this.enableLoadBalancing();
      }
      
      this.logger.log('CPU使用优化完成');
      
    } catch (error) {
      this.logger.error('CPU优化失败:', error);
    }
  }

  /**
   * 缓存优化
   */
  private async optimizeCaching(metrics: PerformanceMetrics): Promise<void> {
    try {
      const config = this.optimizationConfigs.get(this.nodeId);
      if (!config) return;
      
      // 增加缓存大小
      if (metrics.optimization.cacheHitRate < 0.5) {
        config.cacheSize = Math.min(config.cacheSize * 1.5, 50000);
      }
      
      // 启用压缩
      if (!config.enableCompression) {
        config.enableCompression = true;
      }
      
      this.logger.log('缓存优化完成');
      
    } catch (error) {
      this.logger.error('缓存优化失败:', error);
    }
  }

  /**
   * 垃圾回收优化
   */
  private async optimizeGarbageCollection(metrics: PerformanceMetrics): Promise<void> {
    try {
      // 强制垃圾回收
      if (global.gc) {
        global.gc();
      }
      
      // 调整GC阈值
      const config = this.optimizationConfigs.get(this.nodeId);
      if (config) {
        config.gcThreshold = Math.max(0.1, config.gcThreshold * 0.9);
      }
      
      this.logger.log('垃圾回收优化完成');
      
    } catch (error) {
      this.logger.error('GC优化失败:', error);
    }
  }

  /**
   * 生成优化建议
   */
  public async generateOptimizationRecommendations(
    nodeId: string
  ): Promise<OptimizationRecommendation[]> {
    const recommendations: OptimizationRecommendation[] = [];
    const history = this.metricsHistory.get(nodeId);
    
    if (!history || history.length === 0) {
      return recommendations;
    }
    
    const latestMetrics = history[history.length - 1];
    const config = this.optimizationConfigs.get(nodeId);
    
    // 内存建议
    if (latestMetrics.memory.usage > 0.8) {
      recommendations.push({
        type: 'critical',
        category: 'memory',
        title: '内存使用率过高',
        description: `当前内存使用率 ${(latestMetrics.memory.usage * 100).toFixed(1)}%`,
        action: '增加内存或优化内存使用',
        impact: 'high',
        priority: 5,
        estimatedImprovement: '减少30-50%内存使用'
      });
    }
    
    // CPU建议
    if (latestMetrics.cpu.usage > 0.7) {
      recommendations.push({
        type: 'warning',
        category: 'cpu',
        title: 'CPU使用率偏高',
        description: `当前CPU使用率 ${latestMetrics.cpu.usage.toFixed(1)}%`,
        action: '启用负载均衡或增加CPU核心',
        impact: 'medium',
        priority: 4,
        estimatedImprovement: '提升20-40%处理能力'
      });
    }
    
    // 缓存建议
    if (latestMetrics.optimization.cacheHitRate < 0.6) {
      recommendations.push({
        type: 'info',
        category: 'application',
        title: '缓存命中率偏低',
        description: `当前缓存命中率 ${(latestMetrics.optimization.cacheHitRate * 100).toFixed(1)}%`,
        action: '调整缓存策略或增加缓存大小',
        impact: 'medium',
        priority: 3,
        estimatedImprovement: '提升15-30%响应速度'
      });
    }
    
    // 网络建议
    if (latestMetrics.application.averageResponseTime > 100) {
      recommendations.push({
        type: 'warning',
        category: 'network',
        title: '响应时间过长',
        description: `平均响应时间 ${latestMetrics.application.averageResponseTime.toFixed(1)}ms`,
        action: '启用压缩或优化网络配置',
        impact: 'high',
        priority: 4,
        estimatedImprovement: '减少40-60%响应时间'
      });
    }
    
    return recommendations.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 应用优化配置
   */
  public async applyOptimizationConfig(
    nodeId: string,
    config: Partial<OptimizationConfig>
  ): Promise<void> {
    try {
      const existingConfig = this.optimizationConfigs.get(nodeId) || {
        nodeId,
        enableAutoTuning: true,
        enableLoadBalancing: false,
        enableCaching: true,
        enableCompression: false,
        maxMemoryUsage: 0.8,
        maxCpuUsage: 0.7,
        cacheSize: 10000,
        poolSize: 1000,
        parallelThreshold: 5,
        gcThreshold: 0.3
      };
      
      const newConfig = { ...existingConfig, ...config };
      this.optimizationConfigs.set(nodeId, newConfig);
      
      // 存储到Redis
      await this.redis.setex(
        `optimization:config:${nodeId}`,
        3600,
        JSON.stringify(newConfig)
      );
      
      this.eventEmitter.emit('optimization.config.applied', {
        nodeId,
        config: newConfig
      });
      
      this.logger.log(`节点 ${nodeId} 优化配置已应用`);
      
    } catch (error) {
      this.logger.error('应用优化配置失败:', error);
      throw error;
    }
  }

  /**
   * 获取性能指标
   */
  public getPerformanceMetrics(nodeId: string): PerformanceMetrics[] {
    return this.metricsHistory.get(nodeId) || [];
  }

  /**
   * 获取优化配置
   */
  public getOptimizationConfig(nodeId: string): OptimizationConfig | null {
    return this.optimizationConfigs.get(nodeId) || null;
  }

  /**
   * 记录请求
   */
  public recordRequest(responseTime: number, isError: boolean = false): void {
    this.requestCounter++;
    this.responseTimeSum += responseTime;
    
    if (isError) {
      this.errorCounter++;
    }
  }

  // 辅助方法
  private async getCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage();
      setTimeout(() => {
        const endUsage = process.cpuUsage(startUsage);
        const totalUsage = endUsage.user + endUsage.system;
        const usage = totalUsage / 1000000 / 0.1; // 100ms采样
        resolve(Math.min(usage, 1));
      }, 100);
    });
  }

  private async getCacheHitRate(): Promise<number> {
    // 简化实现，实际需要从缓存系统获取
    return Math.random() * 0.4 + 0.6;
  }

  private async getPoolUtilization(): Promise<number> {
    // 简化实现，实际需要从对象池获取
    return Math.random() * 0.3 + 0.5;
  }

  private async getParallelEfficiency(): Promise<number> {
    // 简化实现，实际需要从并行处理系统获取
    return Math.random() * 0.2 + 0.7;
  }

  private getGcPressure(): number {
    const memUsage = process.memoryUsage();
    return memUsage.heapUsed / memUsage.heapTotal;
  }

  private async loadOptimizationConfig(): Promise<void> {
    try {
      const configStr = await this.redis.get(`optimization:config:${this.nodeId}`);
      if (configStr) {
        const config = JSON.parse(configStr);
        this.optimizationConfigs.set(this.nodeId, config);
      }
    } catch (error) {
      this.logger.error('加载优化配置失败:', error);
    }
  }

  private async storeMetricsToRedis(metrics: PerformanceMetrics): Promise<void> {
    try {
      await this.redis.setex(
        `performance:metrics:${metrics.nodeId}:${metrics.timestamp}`,
        3600,
        JSON.stringify(metrics)
      );
    } catch (error) {
      this.logger.error('存储指标失败:', error);
    }
  }

  private async registerNode(): Promise<void> {
    try {
      await this.redis.setex(
        `performance:node:${this.nodeId}`,
        60,
        JSON.stringify({
          nodeId: this.nodeId,
          hostname: os.hostname(),
          pid: process.pid,
          startTime: Date.now()
        })
      );
    } catch (error) {
      this.logger.error('注册节点失败:', error);
    }
  }

  private async updateNodeStatus(metrics: PerformanceMetrics): Promise<void> {
    // 更新负载均衡状态
    const nodeStatus: NodeStatus = {
      nodeId: metrics.nodeId,
      isHealthy: metrics.cpu.usage < 0.9 && metrics.memory.usage < 0.9,
      load: (metrics.cpu.usage + metrics.memory.usage) / 2,
      capacity: 1.0,
      activeConnections: metrics.application.activeConnections,
      lastHeartbeat: Date.now(),
      metrics
    };
    
    this.loadBalancingState.nodes.set(metrics.nodeId, nodeStatus);
  }

  private async cleanupCache(): Promise<void> {
    // 实现缓存清理逻辑
  }

  private async adjustPoolSizes(metrics: PerformanceMetrics): Promise<void> {
    // 实现对象池大小调整逻辑
  }

  private async enableLoadBalancing(): Promise<void> {
    // 实现负载均衡启用逻辑
  }

  /**
   * 定期清理过期数据
   */
  @Cron(CronExpression.EVERY_HOUR)
  private async cleanupExpiredData(): Promise<void> {
    try {
      const cutoffTime = Date.now() - 24 * 60 * 60 * 1000; // 24小时前
      
      for (const [nodeId, history] of this.metricsHistory) {
        const filteredHistory = history.filter(m => m.timestamp > cutoffTime);
        this.metricsHistory.set(nodeId, filteredHistory);
      }
      
      this.logger.log('过期数据清理完成');
      
    } catch (error) {
      this.logger.error('数据清理失败:', error);
    }
  }

  /**
   * 关闭服务
   */
  public async shutdown(): Promise<void> {
    this.logger.log('正在关闭性能优化服务...');
    
    if (this.performanceTimer) {
      clearInterval(this.performanceTimer);
    }
    
    this.redis.disconnect();
    
    this.logger.log('性能优化服务已关闭');
  }
}
