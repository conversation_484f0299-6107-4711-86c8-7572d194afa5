/**
 * 优化的编辑器状态切片
 * 专为支持100+并发用户优化的状态管理
 */
import { createSlice, createSelector, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../index';

// 规范化的实体状态结构
interface NormalizedEntities<T> {
  byId: Record<string, T>;
  allIds: string[];
}

// 场景对象接口
interface SceneObject {
  id: string;
  name: string;
  type: string;
  parentId?: string;
  position: [number, number, number];
  rotation: [number, number, number];
  scale: [number, number, number];
  visible: boolean;
  locked: boolean;
  lastModified: number;
  modifiedBy?: string;
}

// 材质接口
interface Material {
  id: string;
  name: string;
  type: string;
  properties: Record<string, any>;
  textureIds: string[];
  lastModified: number;
}

// 场景接口
interface Scene {
  id: string;
  name: string;
  objectIds: string[];
  cameraIds: string[];
  lightIds: string[];
  lastModified: number;
}

// 视口设置
interface ViewportSettings {
  cameraId: string;
  renderMode: string;
  showGrid: boolean;
  showAxes: boolean;
  showStats: boolean;
  wireframe: boolean;
  shadows: boolean;
  postProcessing: boolean;
}

// 优化的编辑器状态
interface OptimizedEditorState {
  // 规范化的实体数据
  entities: {
    scenes: NormalizedEntities<Scene>;
    objects: NormalizedEntities<SceneObject>;
    materials: NormalizedEntities<Material>;
    cameras: NormalizedEntities<any>;
    lights: NormalizedEntities<any>;
  };
  
  // UI状态（分离业务逻辑）
  ui: {
    selectedObjectIds: string[];
    activeSceneId: string | null;
    activeCameraId: string | null;
    activePanel: string;
    viewportSettings: ViewportSettings;
    transformMode: string;
    transformSpace: string;
    snapMode: string;
    gridSize: number;
  };
  
  // 缓存的计算结果
  computed: {
    visibleObjectIds: string[];
    selectedObjectsCount: number;
    sceneObjectCount: number;
    renderStats: {
      drawCalls: number;
      triangles: number;
      vertices: number;
      frameTime: number;
      fps: number;
    };
  };
  
  // 协作状态
  collaboration: {
    connectedUsers: Record<string, {
      id: string;
      name: string;
      color: string;
      cursor: { x: number; y: number };
      selectedObjectIds: string[];
      lastActivity: number;
    }>;
    lockedObjects: Record<string, string>; // objectId -> userId
    editingZones: Record<string, {
      userId: string;
      bounds: { min: [number, number, number]; max: [number, number, number] };
    }>;
  };
  
  // 性能状态
  performance: {
    memoryUsage: number;
    cpuUsage: number;
    networkLatency: number;
    renderTime: number;
    updateTime: number;
    lastOptimization: number;
  };
  
  // 加载状态
  loading: {
    scenes: boolean;
    objects: boolean;
    materials: boolean;
    assets: boolean;
  };
  
  // 错误状态
  errors: {
    scenes: string | null;
    objects: string | null;
    materials: string | null;
    network: string | null;
  };
}

// 初始状态
const initialState: OptimizedEditorState = {
  entities: {
    scenes: { byId: {}, allIds: [] },
    objects: { byId: {}, allIds: [] },
    materials: { byId: {}, allIds: [] },
    cameras: { byId: {}, allIds: [] },
    lights: { byId: {}, allIds: [] },
  },
  ui: {
    selectedObjectIds: [],
    activeSceneId: null,
    activeCameraId: null,
    activePanel: 'hierarchy',
    viewportSettings: {
      cameraId: '',
      renderMode: 'textured',
      showGrid: true,
      showAxes: true,
      showStats: false,
      wireframe: false,
      shadows: true,
      postProcessing: false,
    },
    transformMode: 'translate',
    transformSpace: 'local',
    snapMode: 'disabled',
    gridSize: 1,
  },
  computed: {
    visibleObjectIds: [],
    selectedObjectsCount: 0,
    sceneObjectCount: 0,
    renderStats: {
      drawCalls: 0,
      triangles: 0,
      vertices: 0,
      frameTime: 0,
      fps: 0,
    },
  },
  collaboration: {
    connectedUsers: {},
    lockedObjects: {},
    editingZones: {},
  },
  performance: {
    memoryUsage: 0,
    cpuUsage: 0,
    networkLatency: 0,
    renderTime: 0,
    updateTime: 0,
    lastOptimization: 0,
  },
  loading: {
    scenes: false,
    objects: false,
    materials: false,
    assets: false,
  },
  errors: {
    scenes: null,
    objects: null,
    materials: null,
    network: null,
  },
};

// 异步操作
export const loadSceneData = createAsyncThunk(
  'optimizedEditor/loadSceneData',
  async (sceneId: string, { rejectWithValue }) => {
    try {
      // 这里应该调用实际的API
      const response = await fetch(`/api/scenes/${sceneId}`);
      if (!response.ok) {
        throw new Error('Failed to load scene data');
      }
      return await response.json();
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const updateObjectBatch = createAsyncThunk(
  'optimizedEditor/updateObjectBatch',
  async (updates: Array<{ id: string; changes: Partial<SceneObject> }>, { rejectWithValue }) => {
    try {
      // 批量更新对象
      const response = await fetch('/api/objects/batch', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ updates }),
      });
      if (!response.ok) {
        throw new Error('Failed to update objects');
      }
      return await response.json();
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

// 创建优化的状态切片
const optimizedEditorSlice = createSlice({
  name: 'optimizedEditor',
  initialState,
  reducers: {
    // 实体操作（使用规范化结构）
    addObject: (state, action: PayloadAction<SceneObject>) => {
      const object = action.payload;
      state.entities.objects.byId[object.id] = object;
      if (!state.entities.objects.allIds.includes(object.id)) {
        state.entities.objects.allIds.push(object.id);
      }
      // 更新计算缓存
      state.computed.sceneObjectCount = state.entities.objects.allIds.length;
    },
    
    updateObject: (state, action: PayloadAction<{ id: string; changes: Partial<SceneObject> }>) => {
      const { id, changes } = action.payload;
      if (state.entities.objects.byId[id]) {
        state.entities.objects.byId[id] = {
          ...state.entities.objects.byId[id],
          ...changes,
          lastModified: Date.now(),
        };
      }
    },
    
    removeObject: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      delete state.entities.objects.byId[id];
      state.entities.objects.allIds = state.entities.objects.allIds.filter(objId => objId !== id);
      // 从选择中移除
      state.ui.selectedObjectIds = state.ui.selectedObjectIds.filter(objId => objId !== id);
      // 更新计算缓存
      state.computed.sceneObjectCount = state.entities.objects.allIds.length;
      state.computed.selectedObjectsCount = state.ui.selectedObjectIds.length;
    },
    
    // UI操作（优化的选择管理）
    setSelectedObjects: (state, action: PayloadAction<string[]>) => {
      state.ui.selectedObjectIds = action.payload;
      state.computed.selectedObjectsCount = action.payload.length;
    },
    
    addToSelection: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      if (!state.ui.selectedObjectIds.includes(id)) {
        state.ui.selectedObjectIds.push(id);
        state.computed.selectedObjectsCount = state.ui.selectedObjectIds.length;
      }
    },
    
    removeFromSelection: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      state.ui.selectedObjectIds = state.ui.selectedObjectIds.filter(objId => objId !== id);
      state.computed.selectedObjectsCount = state.ui.selectedObjectIds.length;
    },
    
    clearSelection: (state) => {
      state.ui.selectedObjectIds = [];
      state.computed.selectedObjectsCount = 0;
    },
    
    // 视口设置
    updateViewportSettings: (state, action: PayloadAction<Partial<ViewportSettings>>) => {
      state.ui.viewportSettings = { ...state.ui.viewportSettings, ...action.payload };
    },
    
    // 协作操作
    updateUserCursor: (state, action: PayloadAction<{ userId: string; cursor: { x: number; y: number } }>) => {
      const { userId, cursor } = action.payload;
      if (state.collaboration.connectedUsers[userId]) {
        state.collaboration.connectedUsers[userId].cursor = cursor;
        state.collaboration.connectedUsers[userId].lastActivity = Date.now();
      }
    },
    
    lockObject: (state, action: PayloadAction<{ objectId: string; userId: string }>) => {
      const { objectId, userId } = action.payload;
      state.collaboration.lockedObjects[objectId] = userId;
    },
    
    unlockObject: (state, action: PayloadAction<string>) => {
      const objectId = action.payload;
      delete state.collaboration.lockedObjects[objectId];
    },
    
    // 性能监控
    updatePerformanceStats: (state, action: PayloadAction<Partial<typeof initialState.performance>>) => {
      state.performance = { ...state.performance, ...action.payload };
    },
    
    updateRenderStats: (state, action: PayloadAction<Partial<typeof initialState.computed.renderStats>>) => {
      state.computed.renderStats = { ...state.computed.renderStats, ...action.payload };
    },
    
    // 缓存更新
    updateVisibleObjects: (state, action: PayloadAction<string[]>) => {
      state.computed.visibleObjectIds = action.payload;
    },
    
    // 错误处理
    setError: (state, action: PayloadAction<{ type: keyof typeof initialState.errors; message: string }>) => {
      const { type, message } = action.payload;
      state.errors[type] = message;
    },
    
    clearError: (state, action: PayloadAction<keyof typeof initialState.errors>) => {
      state.errors[action.payload] = null;
    },
  },
  
  extraReducers: (builder) => {
    // 处理异步操作
    builder
      .addCase(loadSceneData.pending, (state) => {
        state.loading.scenes = true;
        state.errors.scenes = null;
      })
      .addCase(loadSceneData.fulfilled, (state, action) => {
        state.loading.scenes = false;
        // 处理加载的场景数据
        const { scene, objects, materials } = action.payload;
        
        // 更新场景
        if (scene) {
          state.entities.scenes.byId[scene.id] = scene;
          if (!state.entities.scenes.allIds.includes(scene.id)) {
            state.entities.scenes.allIds.push(scene.id);
          }
          state.ui.activeSceneId = scene.id;
        }
        
        // 批量更新对象
        if (objects) {
          objects.forEach((obj: SceneObject) => {
            state.entities.objects.byId[obj.id] = obj;
            if (!state.entities.objects.allIds.includes(obj.id)) {
              state.entities.objects.allIds.push(obj.id);
            }
          });
          state.computed.sceneObjectCount = state.entities.objects.allIds.length;
        }
        
        // 批量更新材质
        if (materials) {
          materials.forEach((mat: Material) => {
            state.entities.materials.byId[mat.id] = mat;
            if (!state.entities.materials.allIds.includes(mat.id)) {
              state.entities.materials.allIds.push(mat.id);
            }
          });
        }
      })
      .addCase(loadSceneData.rejected, (state, action) => {
        state.loading.scenes = false;
        state.errors.scenes = action.payload as string;
      })
      .addCase(updateObjectBatch.pending, (state) => {
        state.loading.objects = true;
      })
      .addCase(updateObjectBatch.fulfilled, (state, action) => {
        state.loading.objects = false;
        // 批量更新对象
        const updates = action.payload;
        updates.forEach((update: { id: string; changes: Partial<SceneObject> }) => {
          if (state.entities.objects.byId[update.id]) {
            state.entities.objects.byId[update.id] = {
              ...state.entities.objects.byId[update.id],
              ...update.changes,
              lastModified: Date.now(),
            };
          }
        });
      })
      .addCase(updateObjectBatch.rejected, (state, action) => {
        state.loading.objects = false;
        state.errors.objects = action.payload as string;
      });
  },
});

// 导出actions
export const {
  addObject,
  updateObject,
  removeObject,
  setSelectedObjects,
  addToSelection,
  removeFromSelection,
  clearSelection,
  updateViewportSettings,
  updateUserCursor,
  lockObject,
  unlockObject,
  updatePerformanceStats,
  updateRenderStats,
  updateVisibleObjects,
  setError,
  clearError,
} = optimizedEditorSlice.actions;

// 记忆化选择器
export const selectAllObjects = (state: RootState) => state.optimizedEditor?.entities.objects.byId || {};
export const selectAllObjectIds = (state: RootState) => state.optimizedEditor?.entities.objects.allIds || [];
export const selectSelectedObjectIds = (state: RootState) => state.optimizedEditor?.ui.selectedObjectIds || [];
export const selectViewportSettings = (state: RootState) => state.optimizedEditor?.ui.viewportSettings;

// 复杂的记忆化选择器
export const selectSelectedObjects = createSelector(
  [selectAllObjects, selectSelectedObjectIds],
  (objects, selectedIds) => selectedIds.map(id => objects[id]).filter(Boolean)
);

export const selectVisibleObjects = createSelector(
  [selectAllObjects, (state: RootState) => state.optimizedEditor?.computed.visibleObjectIds || []],
  (objects, visibleIds) => visibleIds.map(id => objects[id]).filter(Boolean)
);

export const selectObjectsByParent = createSelector(
  [selectAllObjects, (state: RootState, parentId: string) => parentId],
  (objects, parentId) => Object.values(objects).filter(obj => obj.parentId === parentId)
);

export const selectLockedObjects = createSelector(
  [selectAllObjects, (state: RootState) => state.optimizedEditor?.collaboration.lockedObjects || {}],
  (objects, lockedObjects) => {
    return Object.keys(lockedObjects).map(id => objects[id]).filter(Boolean);
  }
);

export const selectPerformanceStats = (state: RootState) => state.optimizedEditor?.performance;
export const selectRenderStats = (state: RootState) => state.optimizedEditor?.computed.renderStats;

export default optimizedEditorSlice.reducer;
