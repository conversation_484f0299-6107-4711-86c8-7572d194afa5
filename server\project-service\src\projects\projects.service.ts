/**
 * 项目服务
 */
import { Injectable, NotFoundException, ForbiddenException, BadRequestException, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { Project, ProjectVisibility } from './entities/project.entity';
import { ProjectMember, ProjectMemberRole } from './entities/project-member.entity';
import { ProjectSetting } from './entities/project-setting.entity';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { AddProjectMemberDto } from './dto/add-project-member.dto';
import { UpdateProjectMemberDto } from './dto/update-project-member.dto';
import { CreateProjectSettingDto } from './dto/create-project-setting.dto';

@Injectable()
export class ProjectsService {
  constructor(
    @InjectRepository(Project)
    private readonly projectRepository: Repository<Project>,
    @InjectRepository(ProjectMember)
    private readonly projectMemberRepository: Repository<ProjectMember>,
    @InjectRepository(ProjectSetting)
    private readonly projectSettingRepository: Repository<ProjectSetting>,
    @Inject('USER_SERVICE') private readonly userService: ClientProxy,
  ) {}

  /**
   * 创建项目
   */
  async create(userId: string, createProjectDto: CreateProjectDto): Promise<Project> {
    // 验证用户是否存在
    try {
      await firstValueFrom(this.userService.send({ cmd: 'findUserById' }, userId));
    } catch (error) {
      throw new BadRequestException('用户不存在');
    }

    // 创建项目
    const project = this.projectRepository.create({
      ...createProjectDto,
      ownerId: userId,
    });

    // 保存项目
    const savedProject = await this.projectRepository.save(project);

    // 添加创建者作为项目所有者
    const member = this.projectMemberRepository.create({
      projectId: savedProject.id,
      userId,
      role: ProjectMemberRole.OWNER,
    });
    await this.projectMemberRepository.save(member);

    // 添加项目设置
    if (createProjectDto.settings && createProjectDto.settings.length > 0) {
      for (const settingDto of createProjectDto.settings) {
        await this.createSetting(savedProject.id, settingDto);
      }
    }

    return this.findOne(savedProject.id);
  }

  /**
   * 查找所有项目
   */
  async findAll(userId: string): Promise<Project[]> {
    // 查找用户有权限的项目
    const memberProjects = await this.projectMemberRepository.find({
      where: { userId },
      relations: ['project'],
    });

    const projectIds = memberProjects.map(member => member.projectId);

    // 查找公开项目
    const publicProjects = await this.projectRepository.find({
      where: { visibility: ProjectVisibility.PUBLIC },
      relations: ['members', 'settings'],
    });

    // 合并结果并去重
    const memberProjectsData = projectIds.length > 0
      ? await this.projectRepository.find({
          where: { id: In(projectIds) },
          relations: ['members', 'settings'],
        })
      : [];

    const allProjects = [
      ...memberProjectsData,
      ...publicProjects.filter(project => !projectIds.includes(project.id)),
    ];

    return allProjects;
  }

  /**
   * 查找用户拥有的项目
   */
  async findUserProjects(userId: string): Promise<Project[]> {
    return this.projectRepository.find({
      where: { ownerId: userId },
      relations: ['members', 'settings'],
    });
  }

  /**
   * 查找单个项目
   */
  async findOne(id: string): Promise<Project> {
    const project = await this.projectRepository.findOne({
      where: { id },
      relations: ['members', 'settings'],
    });

    if (!project) {
      throw new NotFoundException(`项目ID ${id} 不存在`);
    }

    return project;
  }

  /**
   * 更新项目
   */
  async update(id: string, userId: string, updateProjectDto: UpdateProjectDto): Promise<Project> {
    const project = await this.findOne(id);

    // 检查权限
    await this.checkPermission(id, userId, [ProjectMemberRole.OWNER, ProjectMemberRole.ADMIN]);

    // 更新项目
    Object.assign(project, updateProjectDto);
    return this.projectRepository.save(project);
  }

  /**
   * 删除项目
   */
  async remove(id: string, userId: string): Promise<void> {
    const project = await this.findOne(id);

    // 检查权限
    await this.checkPermission(id, userId, [ProjectMemberRole.OWNER]);

    await this.projectRepository.remove(project);
  }

  /**
   * 添加项目成员
   */
  async addMember(projectId: string, userId: string, addMemberDto: AddProjectMemberDto): Promise<ProjectMember> {
    // 验证项目是否存在
    await this.findOne(projectId);

    // 检查权限
    await this.checkPermission(projectId, userId, [ProjectMemberRole.OWNER, ProjectMemberRole.ADMIN]);

    // 验证要添加的用户是否存在
    try {
      await firstValueFrom(this.userService.send({ cmd: 'findUserById' }, addMemberDto.userId));
    } catch (error) {
      throw new BadRequestException('要添加的用户不存在');
    }

    // 检查用户是否已经是项目成员
    const existingMember = await this.projectMemberRepository.findOne({
      where: {
        projectId,
        userId: addMemberDto.userId,
      },
    });

    if (existingMember) {
      throw new BadRequestException('用户已经是项目成员');
    }

    // 添加成员
    const member = this.projectMemberRepository.create({
      projectId,
      userId: addMemberDto.userId,
      role: addMemberDto.role,
    });

    return this.projectMemberRepository.save(member);
  }

  /**
   * 更新项目成员
   */
  async updateMember(projectId: string, memberId: string, userId: string, updateMemberDto: UpdateProjectMemberDto): Promise<ProjectMember> {
    // 验证项目是否存在
    await this.findOne(projectId);

    // 检查权限
    await this.checkPermission(projectId, userId, [ProjectMemberRole.OWNER, ProjectMemberRole.ADMIN]);

    // 查找成员
    const member = await this.projectMemberRepository.findOne({
      where: {
        id: memberId,
        projectId,
      },
    });

    if (!member) {
      throw new NotFoundException(`成员ID ${memberId} 不存在`);
    }

    // 不能修改项目所有者的角色
    if (member.role === ProjectMemberRole.OWNER) {
      throw new ForbiddenException('不能修改项目所有者的角色');
    }

    // 更新成员
    member.role = updateMemberDto.role;
    return this.projectMemberRepository.save(member);
  }

  /**
   * 删除项目成员
   */
  async removeMember(projectId: string, memberId: string, userId: string): Promise<void> {
    // 验证项目是否存在
    await this.findOne(projectId);

    // 检查权限
    await this.checkPermission(projectId, userId, [ProjectMemberRole.OWNER, ProjectMemberRole.ADMIN]);

    // 查找成员
    const member = await this.projectMemberRepository.findOne({
      where: {
        id: memberId,
        projectId,
      },
    });

    if (!member) {
      throw new NotFoundException(`成员ID ${memberId} 不存在`);
    }

    // 不能删除项目所有者
    if (member.role === ProjectMemberRole.OWNER) {
      throw new ForbiddenException('不能删除项目所有者');
    }

    await this.projectMemberRepository.remove(member);
  }

  /**
   * 创建项目设置
   */
  async createSetting(projectId: string, createSettingDto: CreateProjectSettingDto): Promise<ProjectSetting> {
    // 验证项目是否存在
    await this.findOne(projectId);

    // 检查设置是否已存在
    const existingSetting = await this.projectSettingRepository.findOne({
      where: {
        projectId,
        key: createSettingDto.key,
      },
    });

    if (existingSetting) {
      // 更新现有设置
      existingSetting.value = createSettingDto.value;
      return this.projectSettingRepository.save(existingSetting);
    }

    // 创建新设置
    const setting = this.projectSettingRepository.create({
      ...createSettingDto,
      projectId,
    });

    return this.projectSettingRepository.save(setting);
  }

  /**
   * 获取项目设置
   */
  async getSetting(projectId: string, key: string): Promise<ProjectSetting> {
    const setting = await this.projectSettingRepository.findOne({
      where: {
        projectId,
        key,
      },
    });

    if (!setting) {
      throw new NotFoundException(`设置 ${key} 不存在`);
    }

    return setting;
  }

  /**
   * 删除项目设置
   */
  async removeSetting(projectId: string, key: string): Promise<void> {
    const setting = await this.getSetting(projectId, key);
    await this.projectSettingRepository.remove(setting);
  }

  /**
   * 检查用户对项目的权限
   */
  async checkPermission(projectId: string, userId: string, roles: ProjectMemberRole[]): Promise<boolean> {
    const member = await this.projectMemberRepository.findOne({
      where: {
        projectId,
        userId,
      },
    });

    if (!member) {
      throw new ForbiddenException('您没有权限访问此项目');
    }

    if (!roles.includes(member.role)) {
      throw new ForbiddenException('您没有足够的权限执行此操作');
    }

    return true;
  }
}
