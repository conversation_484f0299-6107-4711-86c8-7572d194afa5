{"testSuites": {"basic": {"name": "基础并发测试", "description": "验证系统基本并发处理能力", "config": {"maxUsers": 50, "rampUpTime": 30, "testDuration": 180, "messageInterval": 1000, "roomId": "basic_test"}, "expectedResults": {"connectionSuccessRate": 95, "averageLatency": 200, "errorRate": 2}}, "stress": {"name": "压力测试", "description": "验证系统在高负载下的稳定性", "config": {"maxUsers": 100, "rampUpTime": 60, "testDuration": 300, "messageInterval": 500, "roomId": "stress_test"}, "expectedResults": {"connectionSuccessRate": 90, "averageLatency": 500, "errorRate": 5}}, "peak": {"name": "峰值测试", "description": "验证系统峰值处理能力", "config": {"maxUsers": 150, "rampUpTime": 90, "testDuration": 600, "messageInterval": 250, "roomId": "peak_test"}, "expectedResults": {"connectionSuccessRate": 85, "averageLatency": 1000, "errorRate": 10}}, "endurance": {"name": "耐久测试", "description": "验证系统长时间运行的稳定性", "config": {"maxUsers": 75, "rampUpTime": 45, "testDuration": 1800, "messageInterval": 2000, "roomId": "endurance_test"}, "expectedResults": {"connectionSuccessRate": 95, "averageLatency": 300, "errorRate": 3}}}, "scenarios": {"lightLoad": {"name": "轻负载场景", "description": "模拟正常工作时间的用户行为", "userBehavior": {"objectUpdateFrequency": 0.1, "cursorUpdateFrequency": 0.5, "sceneOperationFrequency": 0.01, "idleTime": 5000}}, "mediumLoad": {"name": "中等负载场景", "description": "模拟繁忙时段的用户行为", "userBehavior": {"objectUpdateFrequency": 0.3, "cursorUpdateFrequency": 0.8, "sceneOperationFrequency": 0.05, "idleTime": 2000}}, "heavyLoad": {"name": "重负载场景", "description": "模拟高强度协作的用户行为", "userBehavior": {"objectUpdateFrequency": 0.8, "cursorUpdateFrequency": 1.0, "sceneOperationFrequency": 0.1, "idleTime": 500}}}, "monitoring": {"metrics": [{"name": "连接成功率", "key": "connectionSuccessRate", "unit": "%", "thresholds": {"excellent": 98, "good": 95, "acceptable": 90, "poor": 85}}, {"name": "平均延迟", "key": "averageLatency", "unit": "ms", "thresholds": {"excellent": 50, "good": 100, "acceptable": 300, "poor": 1000}}, {"name": "最大延迟", "key": "maxLatency", "unit": "ms", "thresholds": {"excellent": 200, "good": 500, "acceptable": 1000, "poor": 3000}}, {"name": "错误率", "key": "errorRate", "unit": "%", "thresholds": {"excellent": 0.5, "good": 1, "acceptable": 3, "poor": 10}}, {"name": "消息吞吐量", "key": "messagesPerSecond", "unit": "msg/s", "thresholds": {"excellent": 1000, "good": 500, "acceptable": 200, "poor": 100}}, {"name": "CPU使用率", "key": "cpuUsage", "unit": "%", "thresholds": {"excellent": 50, "good": 70, "acceptable": 85, "poor": 95}}, {"name": "内存使用率", "key": "memoryUsage", "unit": "%", "thresholds": {"excellent": 60, "good": 75, "acceptable": 85, "poor": 95}}], "alerts": [{"name": "连接失败率过高", "condition": "connectionSuccessRate < 90", "severity": "critical"}, {"name": "延迟过高", "condition": "averageLatency > 1000", "severity": "warning"}, {"name": "错误率过高", "condition": "errorRate > 5", "severity": "error"}, {"name": "系统资源不足", "condition": "cpuUsage > 90 OR memoryUsage > 90", "severity": "critical"}]}, "reporting": {"formats": ["console", "json", "html"], "outputDir": "./test-results", "includeCharts": true, "includeRawData": false, "autoUpload": false}, "environment": {"development": {"serverUrl": "http://localhost:3005", "database": "postgresql://localhost:5432/gameserver_dev", "redis": "redis://localhost:6379/0"}, "staging": {"serverUrl": "https://staging-api.example.com", "database": "postgresql://staging-db:5432/gameserver", "redis": "redis://staging-redis:6379/0"}, "production": {"serverUrl": "https://api.example.com", "database": "postgresql://prod-db:5432/gameserver", "redis": "redis://prod-redis:6379/0"}}}