/**
 * UIThemeSystem.ts
 *
 * UI主题系统，管理UI组件的主题和样式
 */

import { System } from '../../core/System';
import { UIComponent } from '../components/UIComponent';

/**
 * 主题颜色配置
 */
export interface ThemeColors {
  primary: string;
  secondary: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  shadow: string;
}

/**
 * 主题字体配置
 */
export interface ThemeFonts {
  primary: string;
  secondary: string;
  monospace: string;
  sizes: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    xxl: string;
  };
  weights: {
    light: number;
    normal: number;
    medium: number;
    bold: number;
  };
}

/**
 * 主题间距配置
 */
export interface ThemeSpacing {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  xxl: number;
}

/**
 * 主题圆角配置
 */
export interface ThemeBorderRadius {
  none: number;
  sm: number;
  md: number;
  lg: number;
  full: number;
}

/**
 * 主题阴影配置
 */
export interface ThemeShadows {
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
}

/**
 * 主题配置接口
 */
export interface Theme {
  name: string;
  colors: ThemeColors;
  fonts: ThemeFonts;
  spacing: ThemeSpacing;
  borderRadius: ThemeBorderRadius;
  shadows: ThemeShadows;
  transitions: {
    duration: {
      fast: string;
      normal: string;
      slow: string;
    };
    easing: {
      ease: string;
      easeIn: string;
      easeOut: string;
      easeInOut: string;
    };
  };
}

/**
 * 预定义主题
 */
export const LIGHT_THEME: Theme = {
  name: 'light',
  colors: {
    primary: '#1890ff',
    secondary: '#722ed1',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#13c2c2',
    background: '#ffffff',
    surface: '#fafafa',
    text: '#000000',
    textSecondary: '#666666',
    border: '#d9d9d9',
    shadow: 'rgba(0, 0, 0, 0.15)'
  },
  fonts: {
    primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    secondary: 'Georgia, "Times New Roman", Times, serif',
    monospace: '"SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace',
    sizes: {
      xs: '12px',
      sm: '14px',
      md: '16px',
      lg: '18px',
      xl: '20px',
      xxl: '24px'
    },
    weights: {
      light: 300,
      normal: 400,
      medium: 500,
      bold: 700
    }
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48
  },
  borderRadius: {
    none: 0,
    sm: 2,
    md: 4,
    lg: 8,
    full: 9999
  },
  shadows: {
    none: 'none',
    sm: '0 1px 2px rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px rgba(0, 0, 0, 0.1)',
    xl: '0 20px 25px rgba(0, 0, 0, 0.1)'
  },
  transitions: {
    duration: {
      fast: '150ms',
      normal: '300ms',
      slow: '500ms'
    },
    easing: {
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out'
    }
  }
};

export const DARK_THEME: Theme = {
  ...LIGHT_THEME,
  name: 'dark',
  colors: {
    primary: '#1890ff',
    secondary: '#722ed1',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#13c2c2',
    background: '#141414',
    surface: '#1f1f1f',
    text: '#ffffff',
    textSecondary: '#a0a0a0',
    border: '#434343',
    shadow: 'rgba(0, 0, 0, 0.3)'
  }
};

/**
 * UI主题系统
 */
export class UIThemeSystem extends System {
  // 当前主题
  private currentTheme: Theme = LIGHT_THEME;
  
  // 注册的组件
  private registeredComponents: Set<UIComponent> = new Set();
  
  // 主题变量样式表
  private themeStyleSheet?: HTMLStyleElement;

  /**
   * 构造函数
   */
  constructor() {
    super(100); // 低优先级，在其他系统之后运行
    
    // 创建主题样式表
    this.createThemeStyleSheet();
    
    // 应用默认主题
    this.applyTheme(this.currentTheme);
  }

  /**
   * 创建主题样式表
   */
  private createThemeStyleSheet(): void {
    this.themeStyleSheet = document.createElement('style');
    this.themeStyleSheet.id = 'ui-theme-variables';
    document.head.appendChild(this.themeStyleSheet);
  }

  /**
   * 应用主题
   * @param theme 主题配置
   */
  applyTheme(theme: Theme): void {
    this.currentTheme = theme;
    
    // 更新CSS变量
    this.updateCSSVariables();
    
    // 更新所有注册的组件
    this.updateRegisteredComponents();
    
    // 触发主题变更事件
    this.emit('themeChanged', theme);
  }

  /**
   * 更新CSS变量
   */
  private updateCSSVariables(): void {
    if (!this.themeStyleSheet) return;
    
    const theme = this.currentTheme;
    
    const cssVariables = `
      :root {
        /* 颜色变量 */
        --ui-color-primary: ${theme.colors.primary};
        --ui-color-secondary: ${theme.colors.secondary};
        --ui-color-success: ${theme.colors.success};
        --ui-color-warning: ${theme.colors.warning};
        --ui-color-error: ${theme.colors.error};
        --ui-color-info: ${theme.colors.info};
        --ui-color-background: ${theme.colors.background};
        --ui-color-surface: ${theme.colors.surface};
        --ui-color-text: ${theme.colors.text};
        --ui-color-text-secondary: ${theme.colors.textSecondary};
        --ui-color-border: ${theme.colors.border};
        --ui-color-shadow: ${theme.colors.shadow};
        
        /* 字体变量 */
        --ui-font-primary: ${theme.fonts.primary};
        --ui-font-secondary: ${theme.fonts.secondary};
        --ui-font-monospace: ${theme.fonts.monospace};
        --ui-font-size-xs: ${theme.fonts.sizes.xs};
        --ui-font-size-sm: ${theme.fonts.sizes.sm};
        --ui-font-size-md: ${theme.fonts.sizes.md};
        --ui-font-size-lg: ${theme.fonts.sizes.lg};
        --ui-font-size-xl: ${theme.fonts.sizes.xl};
        --ui-font-size-xxl: ${theme.fonts.sizes.xxl};
        --ui-font-weight-light: ${theme.fonts.weights.light};
        --ui-font-weight-normal: ${theme.fonts.weights.normal};
        --ui-font-weight-medium: ${theme.fonts.weights.medium};
        --ui-font-weight-bold: ${theme.fonts.weights.bold};
        
        /* 间距变量 */
        --ui-spacing-xs: ${theme.spacing.xs}px;
        --ui-spacing-sm: ${theme.spacing.sm}px;
        --ui-spacing-md: ${theme.spacing.md}px;
        --ui-spacing-lg: ${theme.spacing.lg}px;
        --ui-spacing-xl: ${theme.spacing.xl}px;
        --ui-spacing-xxl: ${theme.spacing.xxl}px;
        
        /* 圆角变量 */
        --ui-border-radius-none: ${theme.borderRadius.none}px;
        --ui-border-radius-sm: ${theme.borderRadius.sm}px;
        --ui-border-radius-md: ${theme.borderRadius.md}px;
        --ui-border-radius-lg: ${theme.borderRadius.lg}px;
        --ui-border-radius-full: ${theme.borderRadius.full}px;
        
        /* 阴影变量 */
        --ui-shadow-none: ${theme.shadows.none};
        --ui-shadow-sm: ${theme.shadows.sm};
        --ui-shadow-md: ${theme.shadows.md};
        --ui-shadow-lg: ${theme.shadows.lg};
        --ui-shadow-xl: ${theme.shadows.xl};
        
        /* 过渡变量 */
        --ui-transition-duration-fast: ${theme.transitions.duration.fast};
        --ui-transition-duration-normal: ${theme.transitions.duration.normal};
        --ui-transition-duration-slow: ${theme.transitions.duration.slow};
        --ui-transition-easing-ease: ${theme.transitions.easing.ease};
        --ui-transition-easing-ease-in: ${theme.transitions.easing.easeIn};
        --ui-transition-easing-ease-out: ${theme.transitions.easing.easeOut};
        --ui-transition-easing-ease-in-out: ${theme.transitions.easing.easeInOut};
      }
    `;
    
    this.themeStyleSheet.textContent = cssVariables;
  }

  /**
   * 注册组件
   * @param component UI组件
   */
  registerComponent(component: UIComponent): void {
    this.registeredComponents.add(component);
    this.applyThemeToComponent(component);
  }

  /**
   * 注销组件
   * @param component UI组件
   */
  unregisterComponent(component: UIComponent): void {
    this.registeredComponents.delete(component);
  }

  /**
   * 更新所有注册的组件
   */
  private updateRegisteredComponents(): void {
    for (const component of this.registeredComponents) {
      this.applyThemeToComponent(component);
    }
  }

  /**
   * 应用主题到组件
   * @param component UI组件
   */
  private applyThemeToComponent(component: UIComponent): void {
    if (!component.htmlElement) return;
    
    const element = component.htmlElement;
    const theme = this.currentTheme;
    
    // 应用基础样式
    element.style.fontFamily = theme.fonts.primary;
    element.style.fontSize = theme.fonts.sizes.md;
    element.style.color = theme.colors.text;
    
    // 根据组件类型应用特定样式
    this.applyComponentSpecificStyles(component, theme);
  }

  /**
   * 应用组件特定样式
   * @param component UI组件
   * @param theme 主题配置
   */
  private applyComponentSpecificStyles(component: UIComponent, theme: Theme): void {
    if (!component.htmlElement) return;
    
    const element = component.htmlElement;
    
    // 根据组件类型设置样式
    switch (component.uiType) {
      case 'button':
        element.style.backgroundColor = theme.colors.primary;
        element.style.color = '#ffffff';
        element.style.border = 'none';
        element.style.borderRadius = `${theme.borderRadius.md}px`;
        element.style.padding = `${theme.spacing.sm}px ${theme.spacing.md}px`;
        element.style.cursor = 'pointer';
        element.style.transition = `all ${theme.transitions.duration.normal} ${theme.transitions.easing.ease}`;
        break;
        
      case 'input':
        element.style.backgroundColor = theme.colors.background;
        element.style.border = `1px solid ${theme.colors.border}`;
        element.style.borderRadius = `${theme.borderRadius.md}px`;
        element.style.padding = `${theme.spacing.sm}px ${theme.spacing.md}px`;
        element.style.transition = `border-color ${theme.transitions.duration.normal} ${theme.transitions.easing.ease}`;
        break;
        
      case 'panel':
      case 'window':
        element.style.backgroundColor = theme.colors.surface;
        element.style.border = `1px solid ${theme.colors.border}`;
        element.style.borderRadius = `${theme.borderRadius.lg}px`;
        element.style.boxShadow = theme.shadows.md;
        break;
        
      case 'text':
        element.style.color = theme.colors.text;
        break;
    }
  }

  /**
   * 获取当前主题
   * @returns 当前主题配置
   */
  getCurrentTheme(): Theme {
    return this.currentTheme;
  }

  /**
   * 获取主题颜色
   * @param colorName 颜色名称
   * @returns 颜色值
   */
  getThemeColor(colorName: keyof ThemeColors): string {
    return this.currentTheme.colors[colorName];
  }

  /**
   * 获取主题字体
   * @param fontName 字体名称
   * @returns 字体值
   */
  getThemeFont(fontName: keyof ThemeFonts): string | number | object {
    return this.currentTheme.fonts[fontName];
  }

  /**
   * 获取主题间距
   * @param spacingName 间距名称
   * @returns 间距值
   */
  getThemeSpacing(spacingName: keyof ThemeSpacing): number {
    return this.currentTheme.spacing[spacingName];
  }

  /**
   * 切换主题
   * @param themeName 主题名称
   */
  switchTheme(themeName: string): void {
    let theme: Theme;
    
    switch (themeName) {
      case 'dark':
        theme = DARK_THEME;
        break;
      case 'light':
      default:
        theme = LIGHT_THEME;
        break;
    }
    
    this.applyTheme(theme);
  }

  /**
   * 创建自定义主题
   * @param name 主题名称
   * @param customizations 自定义配置
   * @returns 自定义主题
   */
  createCustomTheme(name: string, customizations: Partial<Theme>): Theme {
    return {
      ...this.currentTheme,
      ...customizations,
      name,
      colors: {
        ...this.currentTheme.colors,
        ...customizations.colors
      },
      fonts: {
        ...this.currentTheme.fonts,
        ...customizations.fonts
      },
      spacing: {
        ...this.currentTheme.spacing,
        ...customizations.spacing
      },
      borderRadius: {
        ...this.currentTheme.borderRadius,
        ...customizations.borderRadius
      },
      shadows: {
        ...this.currentTheme.shadows,
        ...customizations.shadows
      },
      transitions: {
        ...this.currentTheme.transitions,
        ...customizations.transitions
      }
    };
  }

  /**
   * 更新系统
   * @param deltaTime 时间增量
   */
  update(deltaTime: number): void {
    // 主题系统不需要定期更新
  }

  /**
   * 销毁系统
   */
  dispose(): void {
    // 移除样式表
    if (this.themeStyleSheet && this.themeStyleSheet.parentNode) {
      this.themeStyleSheet.parentNode.removeChild(this.themeStyleSheet);
    }
    
    // 清理注册的组件
    this.registeredComponents.clear();
    
    super.dispose();
  }
}
