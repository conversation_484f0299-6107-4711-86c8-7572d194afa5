/**
 * PluginSDK.ts
 * 
 * 插件开发工具包，为插件开发者提供便捷的API
 */

import React from 'react';
import { pluginSystem, Plugin, PluginContext, ComponentType, ToolbarButton, Panel, MenuItem, Shortcut } from '../core/PluginSystem';

/**
 * 插件基类
 */
export abstract class BasePlugin {
  protected context: PluginContext;
  protected config: Record<string, any> = {};

  constructor(context: PluginContext) {
    this.context = context;
    this.config = context.plugin.config || {};
  }

  /**
   * 插件初始化
   */
  abstract initialize(): Promise<void> | void;

  /**
   * 插件清理
   */
  abstract cleanup(): Promise<void> | void;

  /**
   * 获取配置值
   */
  protected getConfig<T = any>(key: string, defaultValue?: T): T {
    return this.config[key] ?? defaultValue;
  }

  /**
   * 设置配置值
   */
  protected setConfig(key: string, value: any): void {
    this.config[key] = value;
    this.context.storage.set('config', this.config);
  }

  /**
   * 记录日志
   */
  protected log = {
    info: (message: string, ...args: any[]) => this.context.logger.info(message, ...args),
    warn: (message: string, ...args: any[]) => this.context.logger.warn(message, ...args),
    error: (message: string, ...args: any[]) => this.context.logger.error(message, ...args),
    debug: (message: string, ...args: any[]) => this.context.logger.debug(message, ...args)
  };

  /**
   * 显示通知
   */
  protected notify(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): void {
    this.context.editor.showNotification(message, type);
  }

  /**
   * 注册组件类型
   */
  protected registerComponent(componentType: ComponentType): void {
    this.context.editor.registerComponent(componentType);
  }

  /**
   * 注册工具栏按钮
   */
  protected registerToolbarButton(button: ToolbarButton): void {
    this.context.editor.registerToolbarButton(button);
  }

  /**
   * 注册面板
   */
  protected registerPanel(panel: Panel): void {
    this.context.editor.registerPanel(panel);
  }

  /**
   * 注册菜单项
   */
  protected registerMenuItem(menuItem: MenuItem): void {
    this.context.editor.registerMenuItem(menuItem);
  }

  /**
   * 注册快捷键
   */
  protected registerShortcut(shortcut: Shortcut): void {
    this.context.editor.registerShortcut(shortcut);
  }

  /**
   * 获取选中的组件
   */
  protected getSelectedComponents(): any[] {
    return this.context.editor.getSelectedComponents();
  }

  /**
   * 设置选中的组件
   */
  protected setSelectedComponents(components: any[]): void {
    this.context.editor.setSelectedComponents(components);
  }

  /**
   * 添加组件
   */
  protected addComponent(component: any): void {
    this.context.editor.addComponent(component);
  }

  /**
   * 更新组件
   */
  protected updateComponent(componentId: string, updates: any): void {
    this.context.editor.updateComponent(componentId, updates);
  }

  /**
   * 删除组件
   */
  protected deleteComponent(componentId: string): void {
    this.context.editor.deleteComponent(componentId);
  }
}

/**
 * 组件插件基类
 */
export abstract class ComponentPlugin extends BasePlugin {
  /**
   * 获取组件类型定义
   */
  abstract getComponentType(): ComponentType;

  /**
   * 初始化组件插件
   */
  async initialize(): Promise<void> {
    const componentType = this.getComponentType();
    this.registerComponent(componentType);
    this.log.info(`组件插件 ${componentType.name} 已注册`);
  }

  /**
   * 清理组件插件
   */
  async cleanup(): Promise<void> {
    // 组件插件清理逻辑
  }
}

/**
 * 工具插件基类
 */
export abstract class ToolPlugin extends BasePlugin {
  /**
   * 获取工具配置
   */
  abstract getToolConfig(): {
    toolbarButton?: ToolbarButton;
    panel?: Panel;
    menuItem?: MenuItem;
    shortcuts?: Shortcut[];
  };

  /**
   * 初始化工具插件
   */
  async initialize(): Promise<void> {
    const config = this.getToolConfig();

    if (config.toolbarButton) {
      this.registerToolbarButton(config.toolbarButton);
    }

    if (config.panel) {
      this.registerPanel(config.panel);
    }

    if (config.menuItem) {
      this.registerMenuItem(config.menuItem);
    }

    if (config.shortcuts) {
      config.shortcuts.forEach(shortcut => this.registerShortcut(shortcut));
    }

    this.log.info('工具插件已初始化');
  }

  /**
   * 清理工具插件
   */
  async cleanup(): Promise<void> {
    // 工具插件清理逻辑
  }
}

/**
 * 插件装饰器
 */
export function PluginDecorator(metadata: Omit<Plugin, 'enabled' | 'hooks'>) {
  return function <T extends new (...args: any[]) => BasePlugin>(constructor: T) {
    return class extends constructor {
      static metadata = metadata;
    };
  };
}

/**
 * 组件装饰器
 */
export function Component(config: {
  name: string;
  category: string;
  icon: string;
  defaultProps?: Record<string, any>;
}) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    target._componentConfig = config;
  };
}

/**
 * 工具栏按钮装饰器
 */
export function ToolbarButton(config: {
  label: string;
  icon: string;
  tooltip?: string;
  position?: 'left' | 'center' | 'right';
  group?: string;
}) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    target._toolbarConfig = config;
  };
}

/**
 * 快捷键装饰器
 */
export function Shortcut(config: {
  keys: string[];
  description: string;
  scope?: string;
}) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    target._shortcutConfig = config;
  };
}

/**
 * 插件工厂函数
 */
export class PluginFactory {
  /**
   * 创建组件插件
   */
  static createComponentPlugin(config: {
    id: string;
    name: string;
    version: string;
    description: string;
    author: string;
    componentType: ComponentType;
    dependencies?: string[];
  }): Plugin {
    return {
      id: config.id,
      name: config.name,
      version: config.version,
      description: config.description,
      author: config.author,
      dependencies: config.dependencies,
      main: 'index.js',
      enabled: false,
      hooks: {
        onEnable: async (context: PluginContext) => {
          context.editor.registerComponent(config.componentType);
        }
      }
    };
  }

  /**
   * 创建工具插件
   */
  static createToolPlugin(config: {
    id: string;
    name: string;
    version: string;
    description: string;
    author: string;
    toolbarButton?: ToolbarButton;
    panel?: Panel;
    menuItem?: MenuItem;
    shortcuts?: Shortcut[];
    dependencies?: string[];
  }): Plugin {
    return {
      id: config.id,
      name: config.name,
      version: config.version,
      description: config.description,
      author: config.author,
      dependencies: config.dependencies,
      main: 'index.js',
      enabled: false,
      hooks: {
        onEnable: async (context: PluginContext) => {
          if (config.toolbarButton) {
            context.editor.registerToolbarButton(config.toolbarButton);
          }
          if (config.panel) {
            context.editor.registerPanel(config.panel);
          }
          if (config.menuItem) {
            context.editor.registerMenuItem(config.menuItem);
          }
          if (config.shortcuts) {
            config.shortcuts.forEach(shortcut => context.editor.registerShortcut(shortcut));
          }
        }
      }
    };
  }
}

/**
 * 插件开发工具
 */
export class PluginDevTools {
  /**
   * 验证插件配置
   */
  static validatePlugin(plugin: Plugin): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!plugin.id) {
      errors.push('插件ID不能为空');
    }

    if (!plugin.name) {
      errors.push('插件名称不能为空');
    }

    if (!plugin.version) {
      errors.push('插件版本不能为空');
    } else if (!/^\d+\.\d+\.\d+$/.test(plugin.version)) {
      errors.push('插件版本格式不正确，应为 x.y.z');
    }

    if (!plugin.author) {
      errors.push('插件作者不能为空');
    }

    if (!plugin.main) {
      errors.push('插件入口文件不能为空');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 生成插件模板
   */
  static generatePluginTemplate(type: 'component' | 'tool', config: {
    id: string;
    name: string;
    version: string;
    description: string;
    author: string;
  }): string {
    if (type === 'component') {
      return `
import React from 'react';
import { ComponentPlugin, Component } from '@editor/plugin-sdk';

export default class ${config.name}Plugin extends ComponentPlugin {
  getComponentType() {
    return {
      id: '${config.id}',
      name: '${config.name}',
      category: 'custom',
      icon: 'component',
      defaultProps: {},
      render: (props) => {
        return React.createElement('div', props, '${config.name} Component');
      }
    };
  }
}
      `.trim();
    } else {
      return `
import { ToolPlugin } from '@editor/plugin-sdk';

export default class ${config.name}Plugin extends ToolPlugin {
  getToolConfig() {
    return {
      toolbarButton: {
        id: '${config.id}',
        label: '${config.name}',
        icon: 'tool',
        onClick: () => {
          this.notify('${config.name} 工具已激活');
        }
      }
    };
  }
}
      `.trim();
    }
  }

  /**
   * 生成插件清单文件
   */
  static generateManifest(plugin: Plugin): string {
    return JSON.stringify({
      id: plugin.id,
      name: plugin.name,
      version: plugin.version,
      description: plugin.description,
      author: plugin.author,
      main: plugin.main,
      dependencies: plugin.dependencies || [],
      config: plugin.config || {}
    }, null, 2);
  }
}

/**
 * 插件事件系统
 */
export class PluginEventBus {
  private static events: Map<string, Function[]> = new Map();

  /**
   * 监听事件
   */
  static on(event: string, handler: Function): void {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)!.push(handler);
  }

  /**
   * 移除事件监听
   */
  static off(event: string, handler: Function): void {
    const handlers = this.events.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  static emit(event: string, ...args: any[]): void {
    const handlers = this.events.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args);
        } catch (error) {
          console.error(`插件事件处理错误 [${event}]:`, error);
        }
      });
    }
  }

  /**
   * 清空所有事件监听
   */
  static clear(): void {
    this.events.clear();
  }
}

// 导出常用类型
export type {
  Plugin,
  PluginContext,
  ComponentType,
  ToolbarButton,
  Panel,
  MenuItem,
  Shortcut
} from '../core/PluginSystem';
