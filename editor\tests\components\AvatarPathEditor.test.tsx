/**
 * 数字人路径编辑器组件测试
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { AvatarPathEditor } from '../../src/components/AvatarPathEditor/AvatarPathEditor';

// Mock antd组件
jest.mock('antd', () => ({
  Card: ({ children, title, extra }: any) => (
    <div data-testid="card">
      <div data-testid="card-title">{title}</div>
      <div data-testid="card-extra">{extra}</div>
      {children}
    </div>
  ),
  Button: ({ children, onClick, icon, type, ...props }: any) => (
    <button onClick={onClick} data-testid={`button-${type || 'default'}`} {...props}>
      {icon}
      {children}
    </button>
  ),
  Tabs: ({ children, activeKey, onChange }: any) => (
    <div data-testid="tabs" data-active-key={activeKey}>
      {children}
    </div>
  ),
  'Tabs.TabPane': ({ children, tab, key }: any) => (
    <div data-testid={`tab-pane-${key}`} data-tab={tab}>
      {children}
    </div>
  ),
  Space: ({ children }: any) => <div data-testid="space">{children}</div>,
  message: {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn()
  },
  Modal: {
    confirm: jest.fn()
  },
  Tag: ({ children, color }: any) => (
    <span data-testid="tag" data-color={color}>{children}</span>
  ),
  Progress: ({ percent }: any) => (
    <div data-testid="progress" data-percent={percent}></div>
  ),
  Tooltip: ({ children, title }: any) => (
    <div data-testid="tooltip" title={title}>{children}</div>
  ),
  Popconfirm: ({ children, onConfirm }: any) => (
    <div data-testid="popconfirm" onClick={onConfirm}>{children}</div>
  )
}));

// Mock react-i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}));

// Mock 子组件
jest.mock('../../src/components/AvatarPathEditor/PathCanvas', () => ({
  PathCanvas: React.forwardRef(({ path, onPointAdd, onPointSelect }: any, ref: any) => (
    <div data-testid="path-canvas">
      <button 
        data-testid="add-point-btn"
        onClick={() => onPointAdd?.({ x: 5, y: 0, z: 5 })}
      >
        Add Point
      </button>
      <button 
        data-testid="select-point-btn"
        onClick={() => onPointSelect?.(0)}
      >
        Select Point
      </button>
    </div>
  ))
}));

jest.mock('../../src/components/AvatarPathEditor/PathPropertiesPanel', () => ({
  PathPropertiesPanel: ({ path, onChange }: any) => (
    <div data-testid="path-properties-panel">
      <input 
        data-testid="path-name-input"
        value={path?.name || ''}
        onChange={(e) => onChange?.({ name: e.target.value })}
      />
    </div>
  )
}));

jest.mock('../../src/components/AvatarPathEditor/PathPreview', () => ({
  PathPreview: React.forwardRef(({ path, onProgressChange }: any, ref: any) => (
    <div data-testid="path-preview">
      <button 
        data-testid="preview-play-btn"
        onClick={() => onProgressChange?.(0.5)}
      >
        Play Preview
      </button>
    </div>
  ))
}));

jest.mock('../../src/components/AvatarPathEditor/PathValidator', () => ({
  PathValidator: ({ path, onValidate }: any) => (
    <div data-testid="path-validator">
      <button 
        data-testid="validate-btn"
        onClick={onValidate}
      >
        Validate
      </button>
    </div>
  )
}));

describe('AvatarPathEditor', () => {
  const mockPath = {
    id: 'test-path-1',
    name: '测试路径',
    avatarId: 'avatar-1',
    points: [
      {
        id: 'point-1',
        position: { x: 0, y: 0, z: 0 },
        waitTime: 1,
        speed: 1.5,
        animation: 'idle',
        triggers: []
      },
      {
        id: 'point-2',
        position: { x: 10, y: 0, z: 0 },
        waitTime: 0,
        speed: 2.0,
        animation: 'walk',
        triggers: []
      }
    ],
    loopMode: 'none',
    interpolation: 'linear',
    enabled: true,
    totalDuration: 10,
    metadata: {
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z',
      creator: 'test-user',
      version: 1,
      description: '测试路径描述',
      tags: ['test']
    }
  };

  const defaultProps = {
    entityId: 'entity-1',
    initialPath: mockPath,
    readonly: false,
    onPathChange: jest.fn(),
    onPathSave: jest.fn(),
    onPathDelete: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('基本渲染', () => {
    test('应该正确渲染编辑器', () => {
      render(<AvatarPathEditor {...defaultProps} />);
      
      expect(screen.getByTestId('card')).toBeInTheDocument();
      expect(screen.getByTestId('tabs')).toBeInTheDocument();
    });

    test('应该显示路径验证状态', () => {
      render(<AvatarPathEditor {...defaultProps} />);
      
      // 应该显示验证相关的UI元素
      expect(screen.getByTestId('card-title')).toBeInTheDocument();
    });

    test('应该在只读模式下禁用编辑功能', () => {
      render(<AvatarPathEditor {...defaultProps} readonly={true} />);
      
      // 验证只读模式的行为
      const buttons = screen.getAllByRole('button');
      // 在只读模式下，某些按钮应该被禁用
      expect(buttons.length).toBeGreaterThan(0);
    });
  });

  describe('路径操作', () => {
    test('应该能够添加路径点', async () => {
      render(<AvatarPathEditor {...defaultProps} />);
      
      const addPointBtn = screen.getByTestId('add-point-btn');
      fireEvent.click(addPointBtn);
      
      await waitFor(() => {
        expect(defaultProps.onPathChange).toHaveBeenCalled();
      });
    });

    test('应该能够选择路径点', async () => {
      render(<AvatarPathEditor {...defaultProps} />);
      
      const selectPointBtn = screen.getByTestId('select-point-btn');
      fireEvent.click(selectPointBtn);
      
      // 验证路径点选择逻辑
      await waitFor(() => {
        // 选择路径点后应该有相应的UI变化
        expect(screen.getByTestId('path-canvas')).toBeInTheDocument();
      });
    });

    test('应该能够修改路径属性', async () => {
      render(<AvatarPathEditor {...defaultProps} />);
      
      const nameInput = screen.getByTestId('path-name-input');
      fireEvent.change(nameInput, { target: { value: '新路径名称' } });
      
      await waitFor(() => {
        expect(defaultProps.onPathChange).toHaveBeenCalledWith(
          expect.objectContaining({
            name: '新路径名称'
          })
        );
      });
    });
  });

  describe('预览功能', () => {
    test('应该能够播放路径预览', async () => {
      render(<AvatarPathEditor {...defaultProps} />);
      
      const playBtn = screen.getByTestId('preview-play-btn');
      fireEvent.click(playBtn);
      
      // 验证预览播放逻辑
      await waitFor(() => {
        expect(screen.getByTestId('path-preview')).toBeInTheDocument();
      });
    });

    test('应该显示预览进度', () => {
      render(<AvatarPathEditor {...defaultProps} />);
      
      // 验证进度显示
      expect(screen.getByTestId('path-preview')).toBeInTheDocument();
    });
  });

  describe('验证功能', () => {
    test('应该能够验证路径', async () => {
      const mockOnValidate = jest.fn().mockResolvedValue({
        valid: true,
        errors: [],
        warnings: [],
        score: 95
      });

      render(<AvatarPathEditor {...defaultProps} />);
      
      const validateBtn = screen.getByTestId('validate-btn');
      fireEvent.click(validateBtn);
      
      // 验证路径验证逻辑
      await waitFor(() => {
        expect(screen.getByTestId('path-validator')).toBeInTheDocument();
      });
    });

    test('应该显示验证结果', async () => {
      const pathWithValidation = {
        ...mockPath,
        validationResult: {
          valid: false,
          errors: ['路径点不足'],
          warnings: ['速度过快'],
          score: 60
        }
      };

      render(<AvatarPathEditor {...defaultProps} initialPath={pathWithValidation} />);
      
      // 验证结果应该显示在UI中
      expect(screen.getByTestId('path-validator')).toBeInTheDocument();
    });
  });

  describe('保存和删除', () => {
    test('应该能够保存路径', async () => {
      render(<AvatarPathEditor {...defaultProps} />);
      
      const saveBtn = screen.getByTestId('button-primary');
      fireEvent.click(saveBtn);
      
      await waitFor(() => {
        expect(defaultProps.onPathSave).toHaveBeenCalledWith(mockPath);
      });
    });

    test('应该能够删除路径', async () => {
      render(<AvatarPathEditor {...defaultProps} />);
      
      const deleteBtn = screen.getByTestId('popconfirm');
      fireEvent.click(deleteBtn);
      
      await waitFor(() => {
        expect(defaultProps.onPathDelete).toHaveBeenCalledWith(mockPath.id);
      });
    });

    test('在只读模式下应该禁用保存和删除', () => {
      render(<AvatarPathEditor {...defaultProps} readonly={true} />);
      
      // 在只读模式下，保存和删除按钮应该被禁用
      const buttons = screen.getAllByRole('button');
      const saveBtn = buttons.find(btn => btn.getAttribute('data-testid') === 'button-primary');
      expect(saveBtn).toBeDisabled();
    });
  });

  describe('标签页切换', () => {
    test('应该能够切换到不同的标签页', () => {
      render(<AvatarPathEditor {...defaultProps} />);
      
      const tabs = screen.getByTestId('tabs');
      expect(tabs).toBeInTheDocument();
      
      // 验证标签页内容
      expect(screen.getByTestId('path-canvas')).toBeInTheDocument();
    });

    test('每个标签页应该显示正确的内容', () => {
      render(<AvatarPathEditor {...defaultProps} />);
      
      // 画布标签页
      expect(screen.getByTestId('path-canvas')).toBeInTheDocument();
      
      // 属性标签页
      expect(screen.getByTestId('path-properties-panel')).toBeInTheDocument();
      
      // 预览标签页
      expect(screen.getByTestId('path-preview')).toBeInTheDocument();
      
      // 验证标签页
      expect(screen.getByTestId('path-validator')).toBeInTheDocument();
    });
  });

  describe('错误处理', () => {
    test('应该处理保存错误', async () => {
      const onPathSave = jest.fn().mockRejectedValue(new Error('保存失败'));
      
      render(<AvatarPathEditor {...defaultProps} onPathSave={onPathSave} />);
      
      const saveBtn = screen.getByTestId('button-primary');
      fireEvent.click(saveBtn);
      
      await waitFor(() => {
        expect(onPathSave).toHaveBeenCalled();
        // 应该显示错误消息
      });
    });

    test('应该处理验证错误', async () => {
      render(<AvatarPathEditor {...defaultProps} />);
      
      const validateBtn = screen.getByTestId('validate-btn');
      fireEvent.click(validateBtn);
      
      // 验证错误处理逻辑
      await waitFor(() => {
        expect(screen.getByTestId('path-validator')).toBeInTheDocument();
      });
    });
  });

  describe('无路径状态', () => {
    test('应该显示创建新路径的提示', () => {
      render(<AvatarPathEditor {...defaultProps} initialPath={undefined} />);
      
      // 应该显示创建新路径的UI
      expect(screen.getByTestId('card')).toBeInTheDocument();
    });

    test('应该能够创建新路径', async () => {
      render(<AvatarPathEditor {...defaultProps} initialPath={undefined} />);
      
      // 验证创建新路径的逻辑
      expect(screen.getByTestId('card')).toBeInTheDocument();
    });
  });

  describe('性能测试', () => {
    test('应该能够处理大量路径点', () => {
      const largePathData = {
        ...mockPath,
        points: Array.from({ length: 1000 }, (_, i) => ({
          id: `point-${i}`,
          position: { x: i, y: 0, z: 0 },
          waitTime: 0,
          speed: 1.0,
          animation: 'walk',
          triggers: []
        }))
      };

      const startTime = performance.now();
      render(<AvatarPathEditor {...defaultProps} initialPath={largePathData} />);
      const renderTime = performance.now() - startTime;
      
      expect(renderTime).toBeLessThan(1000); // 应该在1秒内渲染完成
      expect(screen.getByTestId('card')).toBeInTheDocument();
    });

    test('路径更新应该高效', async () => {
      render(<AvatarPathEditor {...defaultProps} />);
      
      const startTime = performance.now();
      
      // 执行多次路径更新
      for (let i = 0; i < 100; i++) {
        const nameInput = screen.getByTestId('path-name-input');
        fireEvent.change(nameInput, { target: { value: `路径${i}` } });
      }
      
      const updateTime = performance.now() - startTime;
      expect(updateTime).toBeLessThan(1000); // 应该在1秒内完成
    });
  });
});
