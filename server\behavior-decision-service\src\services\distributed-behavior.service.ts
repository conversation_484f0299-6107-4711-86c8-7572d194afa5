/**
 * 分布式行为决策服务
 * 
 * 提供大规模分布式环境下的行为决策和协调功能。
 * 支持多实例协调、负载均衡、故障恢复等企业级特性。
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import Redis from 'ioredis';
import { 
  DecisionContext, 
  DecisionOption, 
  DecisionResult,
  DecisionStrategy 
} from '../../../engine/src/ai/behavior/IntelligentDecisionSystem';

/**
 * 分布式决策请求
 */
export interface DistributedDecisionRequest {
  requestId: string;
  entityId: string;
  sessionId: string;
  context: DecisionContext;
  options: DecisionOption[];
  strategy?: string;
  priority: number;
  timeout: number;
  timestamp: number;
}

/**
 * 分布式决策响应
 */
export interface DistributedDecisionResponse {
  requestId: string;
  entityId: string;
  result: DecisionResult;
  processingTime: number;
  nodeId: string;
  timestamp: number;
}

/**
 * 节点状态
 */
export interface NodeStatus {
  nodeId: string;
  isActive: boolean;
  load: number;
  capacity: number;
  lastHeartbeat: number;
  processedRequests: number;
  averageResponseTime: number;
  errorRate: number;
}

/**
 * 协调策略
 */
export enum CoordinationStrategy {
  ROUND_ROBIN = 'round_robin',
  LEAST_LOAD = 'least_load',
  GEOGRAPHIC = 'geographic',
  CAPABILITY_BASED = 'capability_based'
}

/**
 * 分布式行为决策服务
 */
@Injectable()
export class DistributedBehaviorService {
  private readonly logger = new Logger(DistributedBehaviorService.name);
  private readonly nodeId: string;
  private readonly redis: Redis;
  private readonly pubsub: Redis;
  
  private isActive = false;
  private currentLoad = 0;
  private maxCapacity = 100;
  private processedRequests = 0;
  private totalResponseTime = 0;
  private errorCount = 0;
  
  private pendingRequests = new Map<string, DistributedDecisionRequest>();
  private nodeStatuses = new Map<string, NodeStatus>();
  private coordinationStrategy = CoordinationStrategy.LEAST_LOAD;
  
  // 决策策略实例
  private decisionStrategies = new Map<string, DecisionStrategy>();

  constructor(
    private readonly eventEmitter: EventEmitter2,
    redisConfig: any
  ) {
    this.nodeId = `behavior-node-${process.env.NODE_ID || Date.now()}`;
    this.redis = new Redis(redisConfig);
    this.pubsub = new Redis(redisConfig);
    
    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private async initializeService(): Promise<void> {
    try {
      // 注册节点
      await this.registerNode();
      
      // 设置消息监听
      await this.setupMessageHandlers();
      
      // 启动心跳
      this.startHeartbeat();
      
      // 初始化决策策略
      this.initializeDecisionStrategies();
      
      this.isActive = true;
      this.logger.log(`分布式行为决策服务已启动，节点ID: ${this.nodeId}`);
      
    } catch (error) {
      this.logger.error('服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 注册节点
   */
  private async registerNode(): Promise<void> {
    const nodeInfo: NodeStatus = {
      nodeId: this.nodeId,
      isActive: true,
      load: 0,
      capacity: this.maxCapacity,
      lastHeartbeat: Date.now(),
      processedRequests: 0,
      averageResponseTime: 0,
      errorRate: 0
    };
    
    await this.redis.hset(
      'behavior:nodes',
      this.nodeId,
      JSON.stringify(nodeInfo)
    );
    
    this.logger.log(`节点已注册: ${this.nodeId}`);
  }

  /**
   * 设置消息处理器
   */
  private async setupMessageHandlers(): Promise<void> {
    // 监听决策请求
    this.pubsub.subscribe('behavior:requests');
    this.pubsub.subscribe(`behavior:requests:${this.nodeId}`);
    
    // 监听协调消息
    this.pubsub.subscribe('behavior:coordination');
    
    this.pubsub.on('message', async (channel, message) => {
      try {
        const data = JSON.parse(message);
        
        switch (channel) {
          case 'behavior:requests':
            await this.handleDecisionRequest(data);
            break;
          case `behavior:requests:${this.nodeId}`:
            await this.handleDirectRequest(data);
            break;
          case 'behavior:coordination':
            await this.handleCoordinationMessage(data);
            break;
        }
      } catch (error) {
        this.logger.error('消息处理失败:', error);
      }
    });
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    setInterval(async () => {
      try {
        await this.sendHeartbeat();
      } catch (error) {
        this.logger.error('心跳发送失败:', error);
      }
    }, 5000); // 5秒心跳间隔
  }

  /**
   * 发送心跳
   */
  private async sendHeartbeat(): Promise<void> {
    const nodeInfo: NodeStatus = {
      nodeId: this.nodeId,
      isActive: this.isActive,
      load: this.currentLoad,
      capacity: this.maxCapacity,
      lastHeartbeat: Date.now(),
      processedRequests: this.processedRequests,
      averageResponseTime: this.processedRequests > 0 ? this.totalResponseTime / this.processedRequests : 0,
      errorRate: this.processedRequests > 0 ? this.errorCount / this.processedRequests : 0
    };
    
    await this.redis.hset(
      'behavior:nodes',
      this.nodeId,
      JSON.stringify(nodeInfo)
    );
    
    // 发布心跳事件
    await this.redis.publish('behavior:heartbeat', JSON.stringify({
      nodeId: this.nodeId,
      timestamp: Date.now(),
      status: nodeInfo
    }));
  }

  /**
   * 初始化决策策略
   */
  private initializeDecisionStrategies(): void {
    // 这里应该加载各种决策策略
    // 暂时使用模拟实现
    this.logger.log('决策策略已初始化');
  }

  /**
   * 处理决策请求
   */
  public async makeDecision(request: DistributedDecisionRequest): Promise<DistributedDecisionResponse> {
    const startTime = Date.now();
    
    try {
      // 检查负载
      if (this.currentLoad >= this.maxCapacity) {
        throw new Error('节点负载已满');
      }
      
      // 增加负载
      this.currentLoad++;
      this.pendingRequests.set(request.requestId, request);
      
      // 执行决策
      const result = await this.executeDecision(request);
      
      // 构建响应
      const response: DistributedDecisionResponse = {
        requestId: request.requestId,
        entityId: request.entityId,
        result,
        processingTime: Date.now() - startTime,
        nodeId: this.nodeId,
        timestamp: Date.now()
      };
      
      // 更新统计
      this.updateStatistics(response.processingTime, false);
      
      // 发布结果
      await this.publishDecisionResult(response);
      
      return response;
      
    } catch (error) {
      this.logger.error(`决策执行失败 [${request.requestId}]:`, error);
      this.updateStatistics(Date.now() - startTime, true);
      throw error;
      
    } finally {
      // 减少负载
      this.currentLoad--;
      this.pendingRequests.delete(request.requestId);
    }
  }

  /**
   * 执行决策
   */
  private async executeDecision(request: DistributedDecisionRequest): Promise<DecisionResult> {
    // 选择决策策略
    const strategy = this.decisionStrategies.get(request.strategy || 'default');
    if (!strategy) {
      throw new Error(`未找到决策策略: ${request.strategy}`);
    }
    
    // 执行决策（模拟实现）
    const result: DecisionResult = {
      selectedOption: request.options[0] || {
        id: 'default',
        name: '默认选项',
        description: '默认决策选项',
        type: 'default',
        cost: 0,
        benefit: 0.5,
        risk: 0.1,
        duration: 1000,
        requirements: [],
        consequences: [],
        confidence: 0.5
      },
      reasoning: '基于当前上下文的最优选择',
      confidence: 0.8,
      alternatives: request.options.slice(1),
      executionPlan: [{
        id: 'step_1',
        action: 'execute',
        parameters: {},
        duration: 1000,
        dependencies: [],
        priority: 1
      }],
      timestamp: Date.now()
    };
    
    return result;
  }

  /**
   * 处理决策请求消息
   */
  private async handleDecisionRequest(data: any): Promise<void> {
    const request = data as DistributedDecisionRequest;
    
    // 检查是否应该处理这个请求
    if (!this.shouldHandleRequest(request)) {
      return;
    }
    
    try {
      const response = await this.makeDecision(request);
      this.logger.log(`决策完成 [${request.requestId}]: ${response.result.selectedOption.name}`);
      
    } catch (error) {
      this.logger.error(`决策失败 [${request.requestId}]:`, error);
      
      // 发布错误响应
      await this.publishErrorResponse(request, error.message);
    }
  }

  /**
   * 处理直接请求
   */
  private async handleDirectRequest(data: any): Promise<void> {
    // 直接分配给当前节点的请求
    await this.handleDecisionRequest(data);
  }

  /**
   * 处理协调消息
   */
  private async handleCoordinationMessage(data: any): Promise<void> {
    switch (data.type) {
      case 'load_balance':
        await this.handleLoadBalance(data);
        break;
      case 'node_status_update':
        await this.handleNodeStatusUpdate(data);
        break;
      case 'strategy_update':
        await this.handleStrategyUpdate(data);
        break;
    }
  }

  /**
   * 处理负载均衡
   */
  private async handleLoadBalance(data: any): Promise<void> {
    // 实现负载均衡逻辑
    this.logger.log('处理负载均衡请求');
  }

  /**
   * 处理节点状态更新
   */
  private async handleNodeStatusUpdate(data: any): Promise<void> {
    const nodeStatus = data.status as NodeStatus;
    this.nodeStatuses.set(nodeStatus.nodeId, nodeStatus);
  }

  /**
   * 处理策略更新
   */
  private async handleStrategyUpdate(data: any): Promise<void> {
    this.coordinationStrategy = data.strategy;
    this.logger.log(`协调策略已更新: ${this.coordinationStrategy}`);
  }

  /**
   * 判断是否应该处理请求
   */
  private shouldHandleRequest(request: DistributedDecisionRequest): boolean {
    // 检查负载
    if (this.currentLoad >= this.maxCapacity) {
      return false;
    }
    
    // 检查优先级
    if (request.priority < 1) {
      return false;
    }
    
    // 根据协调策略决定
    switch (this.coordinationStrategy) {
      case CoordinationStrategy.LEAST_LOAD:
        return this.isLeastLoadedNode();
      case CoordinationStrategy.ROUND_ROBIN:
        return this.isMyTurn(request);
      default:
        return true;
    }
  }

  /**
   * 检查是否是负载最小的节点
   */
  private isLeastLoadedNode(): boolean {
    let minLoad = this.currentLoad;
    
    for (const status of this.nodeStatuses.values()) {
      if (status.isActive && status.load < minLoad) {
        minLoad = status.load;
      }
    }
    
    return this.currentLoad === minLoad;
  }

  /**
   * 检查是否轮到当前节点
   */
  private isMyTurn(request: DistributedDecisionRequest): boolean {
    // 简单的轮询实现
    const hash = this.hashString(request.entityId);
    const activeNodes = Array.from(this.nodeStatuses.values())
      .filter(status => status.isActive)
      .map(status => status.nodeId)
      .sort();
    
    if (activeNodes.length === 0) return true;
    
    const index = hash % activeNodes.length;
    return activeNodes[index] === this.nodeId;
  }

  /**
   * 字符串哈希
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  /**
   * 发布决策结果
   */
  private async publishDecisionResult(response: DistributedDecisionResponse): Promise<void> {
    await this.redis.publish('behavior:results', JSON.stringify(response));
    
    // 存储结果到缓存
    await this.redis.setex(
      `behavior:result:${response.requestId}`,
      300, // 5分钟过期
      JSON.stringify(response)
    );
  }

  /**
   * 发布错误响应
   */
  private async publishErrorResponse(request: DistributedDecisionRequest, error: string): Promise<void> {
    const errorResponse = {
      requestId: request.requestId,
      entityId: request.entityId,
      error,
      nodeId: this.nodeId,
      timestamp: Date.now()
    };
    
    await this.redis.publish('behavior:errors', JSON.stringify(errorResponse));
  }

  /**
   * 更新统计信息
   */
  private updateStatistics(responseTime: number, isError: boolean): void {
    this.processedRequests++;
    this.totalResponseTime += responseTime;
    
    if (isError) {
      this.errorCount++;
    }
  }

  /**
   * 获取节点状态
   */
  public getNodeStatus(): NodeStatus {
    return {
      nodeId: this.nodeId,
      isActive: this.isActive,
      load: this.currentLoad,
      capacity: this.maxCapacity,
      lastHeartbeat: Date.now(),
      processedRequests: this.processedRequests,
      averageResponseTime: this.processedRequests > 0 ? this.totalResponseTime / this.processedRequests : 0,
      errorRate: this.processedRequests > 0 ? this.errorCount / this.processedRequests : 0
    };
  }

  /**
   * 获取所有节点状态
   */
  public async getAllNodeStatuses(): Promise<NodeStatus[]> {
    const nodeData = await this.redis.hgetall('behavior:nodes');
    const statuses: NodeStatus[] = [];
    
    for (const [nodeId, data] of Object.entries(nodeData)) {
      try {
        const status = JSON.parse(data) as NodeStatus;
        statuses.push(status);
      } catch (error) {
        this.logger.error(`解析节点状态失败 [${nodeId}]:`, error);
      }
    }
    
    return statuses;
  }

  /**
   * 设置协调策略
   */
  public async setCoordinationStrategy(strategy: CoordinationStrategy): Promise<void> {
    this.coordinationStrategy = strategy;
    
    // 广播策略更新
    await this.redis.publish('behavior:coordination', JSON.stringify({
      type: 'strategy_update',
      strategy,
      nodeId: this.nodeId,
      timestamp: Date.now()
    }));
    
    this.logger.log(`协调策略已设置: ${strategy}`);
  }

  /**
   * 定期清理过期数据
   */
  @Cron(CronExpression.EVERY_HOUR)
  private async cleanupExpiredData(): Promise<void> {
    try {
      // 清理过期的节点状态
      const nodeData = await this.redis.hgetall('behavior:nodes');
      const now = Date.now();
      
      for (const [nodeId, data] of Object.entries(nodeData)) {
        try {
          const status = JSON.parse(data) as NodeStatus;
          
          // 如果节点超过1分钟没有心跳，标记为不活跃
          if (now - status.lastHeartbeat > 60000) {
            status.isActive = false;
            await this.redis.hset('behavior:nodes', nodeId, JSON.stringify(status));
            this.logger.warn(`节点 ${nodeId} 已标记为不活跃`);
          }
        } catch (error) {
          // 删除无效的节点数据
          await this.redis.hdel('behavior:nodes', nodeId);
          this.logger.warn(`删除无效节点数据: ${nodeId}`);
        }
      }
      
      this.logger.log('过期数据清理完成');
      
    } catch (error) {
      this.logger.error('数据清理失败:', error);
    }
  }

  /**
   * 关闭服务
   */
  public async shutdown(): Promise<void> {
    this.logger.log('正在关闭分布式行为决策服务...');
    
    this.isActive = false;
    
    // 注销节点
    await this.redis.hdel('behavior:nodes', this.nodeId);
    
    // 关闭连接
    this.redis.disconnect();
    this.pubsub.disconnect();
    
    this.logger.log('分布式行为决策服务已关闭');
  }
}
