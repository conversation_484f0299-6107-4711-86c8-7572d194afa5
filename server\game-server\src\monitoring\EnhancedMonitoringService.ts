/**
 * 增强的性能监控服务
 * 专为支持100+并发用户优化的全面监控系统
 */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as os from 'os';
import * as process from 'process';

// 系统指标接口
interface SystemMetrics {
  cpu: {
    usage: number;
    loadAverage: number[];
    cores: number;
  };
  memory: {
    total: number;
    used: number;
    free: number;
    usage: number;
    heapUsed: number;
    heapTotal: number;
  };
  network: {
    bytesReceived: number;
    bytesSent: number;
    packetsReceived: number;
    packetsSent: number;
    connectionsActive: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
}

// 应用指标接口
interface ApplicationMetrics {
  users: {
    total: number;
    active: number;
    peak: number;
    averageSessionDuration: number;
  };
  requests: {
    total: number;
    successful: number;
    failed: number;
    averageResponseTime: number;
    requestsPerSecond: number;
  };
  websocket: {
    connections: number;
    messagesPerSecond: number;
    averageLatency: number;
    errorRate: number;
  };
  webrtc: {
    activeTransports: number;
    producers: number;
    consumers: number;
    averageBitrate: number;
    packetLoss: number;
  };
  database: {
    connections: number;
    queriesPerSecond: number;
    averageQueryTime: number;
    slowQueries: number;
    cacheHitRate: number;
  };
}

// 告警级别
enum AlertLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

// 告警规则
interface AlertRule {
  id: string;
  name: string;
  metric: string;
  operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
  threshold: number;
  level: AlertLevel;
  enabled: boolean;
  cooldown: number; // 冷却时间（秒）
  lastTriggered?: number;
}

// 告警事件
interface AlertEvent {
  id: string;
  ruleId: string;
  level: AlertLevel;
  message: string;
  value: number;
  threshold: number;
  timestamp: number;
  resolved: boolean;
}

// 性能趋势数据
interface PerformanceTrend {
  timestamp: number;
  systemMetrics: SystemMetrics;
  applicationMetrics: ApplicationMetrics;
}

/**
 * 增强的性能监控服务类
 */
@Injectable()
export class EnhancedMonitoringService {
  private readonly logger = new Logger(EnhancedMonitoringService.name);
  
  // 当前指标
  private currentSystemMetrics: SystemMetrics;
  private currentApplicationMetrics: ApplicationMetrics;
  
  // 历史数据
  private performanceHistory: PerformanceTrend[] = [];
  private maxHistorySize: number;
  
  // 告警系统
  private alertRules: Map<string, AlertRule> = new Map();
  private activeAlerts: Map<string, AlertEvent> = new Map();
  
  // 基线数据
  private baselineMetrics: {
    cpu: number;
    memory: number;
    responseTime: number;
    throughput: number;
  };
  
  // 配置
  private readonly monitoringInterval: number;
  private readonly enableAlerting: boolean;
  private readonly enableTrending: boolean;
  private readonly enableAutoScaling: boolean;
  
  // 定时器
  private metricsTimer?: NodeJS.Timeout;
  private alertTimer?: NodeJS.Timeout;
  
  // 网络统计
  private networkStats = {
    lastBytesReceived: 0,
    lastBytesSent: 0,
    lastTimestamp: Date.now(),
  };
  
  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    // 读取配置
    this.monitoringInterval = this.configService.get<number>('MONITORING_INTERVAL', 5000);
    this.enableAlerting = this.configService.get<boolean>('ENABLE_ALERTING', true);
    this.enableTrending = this.configService.get<boolean>('ENABLE_TRENDING', true);
    this.enableAutoScaling = this.configService.get<boolean>('ENABLE_AUTO_SCALING', true);
    this.maxHistorySize = this.configService.get<number>('MAX_HISTORY_SIZE', 1440); // 24小时（每分钟一个点）
    
    this.initialize();
  }
  
  /**
   * 初始化监控服务
   */
  private async initialize(): Promise<void> {
    try {
      // 初始化告警规则
      this.initializeAlertRules();
      
      // 收集基线指标
      await this.collectBaselineMetrics();
      
      // 启动监控
      this.startMonitoring();
      
      this.logger.log('增强性能监控服务初始化完成');
      
    } catch (error) {
      this.logger.error('监控服务初始化失败:', error);
      throw error;
    }
  }
  
  /**
   * 初始化告警规则
   */
  private initializeAlertRules(): void {
    const defaultRules: AlertRule[] = [
      {
        id: 'cpu_high',
        name: 'CPU使用率过高',
        metric: 'system.cpu.usage',
        operator: 'gt',
        threshold: 80,
        level: AlertLevel.WARNING,
        enabled: true,
        cooldown: 300, // 5分钟
      },
      {
        id: 'cpu_critical',
        name: 'CPU使用率严重过高',
        metric: 'system.cpu.usage',
        operator: 'gt',
        threshold: 95,
        level: AlertLevel.CRITICAL,
        enabled: true,
        cooldown: 60, // 1分钟
      },
      {
        id: 'memory_high',
        name: '内存使用率过高',
        metric: 'system.memory.usage',
        operator: 'gt',
        threshold: 85,
        level: AlertLevel.WARNING,
        enabled: true,
        cooldown: 300,
      },
      {
        id: 'memory_critical',
        name: '内存使用率严重过高',
        metric: 'system.memory.usage',
        operator: 'gt',
        threshold: 95,
        level: AlertLevel.CRITICAL,
        enabled: true,
        cooldown: 60,
      },
      {
        id: 'response_time_high',
        name: '响应时间过长',
        metric: 'application.requests.averageResponseTime',
        operator: 'gt',
        threshold: 1000, // 1秒
        level: AlertLevel.WARNING,
        enabled: true,
        cooldown: 180, // 3分钟
      },
      {
        id: 'error_rate_high',
        name: '错误率过高',
        metric: 'application.websocket.errorRate',
        operator: 'gt',
        threshold: 0.05, // 5%
        level: AlertLevel.ERROR,
        enabled: true,
        cooldown: 120, // 2分钟
      },
      {
        id: 'users_approaching_limit',
        name: '用户数接近限制',
        metric: 'application.users.total',
        operator: 'gt',
        threshold: 90, // 90个用户
        level: AlertLevel.WARNING,
        enabled: true,
        cooldown: 600, // 10分钟
      },
    ];
    
    for (const rule of defaultRules) {
      this.alertRules.set(rule.id, rule);
    }
  }
  
  /**
   * 收集基线指标
   */
  private async collectBaselineMetrics(): Promise<void> {
    // 收集一段时间的指标作为基线
    const samples: any[] = [];
    
    for (let i = 0; i < 10; i++) {
      const systemMetrics = await this.collectSystemMetrics();
      const applicationMetrics = await this.collectApplicationMetrics();
      
      samples.push({
        cpu: systemMetrics.cpu.usage,
        memory: systemMetrics.memory.usage,
        responseTime: applicationMetrics.requests.averageResponseTime,
        throughput: applicationMetrics.requests.requestsPerSecond,
      });
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 计算基线值
    this.baselineMetrics = {
      cpu: samples.reduce((sum, s) => sum + s.cpu, 0) / samples.length,
      memory: samples.reduce((sum, s) => sum + s.memory, 0) / samples.length,
      responseTime: samples.reduce((sum, s) => sum + s.responseTime, 0) / samples.length,
      throughput: samples.reduce((sum, s) => sum + s.throughput, 0) / samples.length,
    };
    
    this.logger.log('基线指标收集完成:', this.baselineMetrics);
  }
  
  /**
   * 启动监控
   */
  private startMonitoring(): void {
    // 启动指标收集
    this.metricsTimer = setInterval(async () => {
      await this.collectMetrics();
    }, this.monitoringInterval);
    
    // 启动告警检查
    if (this.enableAlerting) {
      this.alertTimer = setInterval(() => {
        this.checkAlerts();
      }, 10000); // 每10秒检查一次告警
    }
  }
  
  /**
   * 收集所有指标
   */
  private async collectMetrics(): Promise<void> {
    try {
      // 收集系统指标
      this.currentSystemMetrics = await this.collectSystemMetrics();
      
      // 收集应用指标
      this.currentApplicationMetrics = await this.collectApplicationMetrics();
      
      // 存储历史数据
      if (this.enableTrending) {
        this.storeHistoricalData();
      }
      
      // 发出指标更新事件
      this.eventEmitter.emit('metrics.updated', {
        system: this.currentSystemMetrics,
        application: this.currentApplicationMetrics,
        timestamp: Date.now(),
      });
      
    } catch (error) {
      this.logger.error('收集指标失败:', error);
    }
  }
  
  /**
   * 收集系统指标
   */
  private async collectSystemMetrics(): Promise<SystemMetrics> {
    const cpus = os.cpus();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;
    
    // 计算CPU使用率
    const cpuUsage = await this.calculateCPUUsage();
    
    // 获取网络统计
    const networkStats = this.getNetworkStats();
    
    // 获取磁盘统计（简化版）
    const diskStats = this.getDiskStats();
    
    return {
      cpu: {
        usage: cpuUsage,
        loadAverage: os.loadavg(),
        cores: cpus.length,
      },
      memory: {
        total: totalMem,
        used: usedMem,
        free: freeMem,
        usage: (usedMem / totalMem) * 100,
        heapUsed: process.memoryUsage().heapUsed,
        heapTotal: process.memoryUsage().heapTotal,
      },
      network: networkStats,
      disk: diskStats,
    };
  }
  
  /**
   * 计算CPU使用率
   */
  private async calculateCPUUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startMeasure = process.cpuUsage();
      const startTime = process.hrtime();
      
      setTimeout(() => {
        const endMeasure = process.cpuUsage(startMeasure);
        const endTime = process.hrtime(startTime);
        
        const totalTime = endTime[0] * 1000000 + endTime[1] / 1000; // 微秒
        const cpuTime = endMeasure.user + endMeasure.system; // 微秒
        
        const usage = (cpuTime / totalTime) * 100;
        resolve(Math.min(100, Math.max(0, usage)));
      }, 100);
    });
  }
  
  /**
   * 获取网络统计
   */
  private getNetworkStats(): SystemMetrics['network'] {
    // 这里应该从系统获取实际的网络统计
    // 简化实现，返回模拟数据
    const now = Date.now();
    const timeDiff = now - this.networkStats.lastTimestamp;
    
    // 模拟网络流量
    const bytesReceived = this.networkStats.lastBytesReceived + Math.random() * 1000000;
    const bytesSent = this.networkStats.lastBytesSent + Math.random() * 1000000;
    
    this.networkStats = {
      lastBytesReceived: bytesReceived,
      lastBytesSent: bytesSent,
      lastTimestamp: now,
    };
    
    return {
      bytesReceived,
      bytesSent,
      packetsReceived: Math.floor(bytesReceived / 1500), // 假设平均包大小1500字节
      packetsSent: Math.floor(bytesSent / 1500),
      connectionsActive: 0, // 需要从连接池获取
    };
  }
  
  /**
   * 获取磁盘统计
   */
  private getDiskStats(): SystemMetrics['disk'] {
    // 简化实现，返回模拟数据
    const total = 100 * 1024 * 1024 * 1024; // 100GB
    const used = total * 0.6; // 60%使用率
    const free = total - used;
    
    return {
      total,
      used,
      free,
      usage: (used / total) * 100,
    };
  }
  
  /**
   * 收集应用指标
   */
  private async collectApplicationMetrics(): Promise<ApplicationMetrics> {
    // 这些指标需要从各个服务获取
    // 这里提供一个框架，实际实现需要集成各个服务的统计接口
    
    return {
      users: {
        total: 0, // 从用户服务获取
        active: 0, // 从会话管理获取
        peak: 0, // 从历史数据获取
        averageSessionDuration: 0, // 计算平均会话时长
      },
      requests: {
        total: 0, // 从HTTP服务获取
        successful: 0,
        failed: 0,
        averageResponseTime: 0,
        requestsPerSecond: 0,
      },
      websocket: {
        connections: 0, // 从WebSocket网关获取
        messagesPerSecond: 0,
        averageLatency: 0,
        errorRate: 0,
      },
      webrtc: {
        activeTransports: 0, // 从WebRTC服务获取
        producers: 0,
        consumers: 0,
        averageBitrate: 0,
        packetLoss: 0,
      },
      database: {
        connections: 0, // 从数据库管理器获取
        queriesPerSecond: 0,
        averageQueryTime: 0,
        slowQueries: 0,
        cacheHitRate: 0,
      },
    };
  }
  
  /**
   * 存储历史数据
   */
  private storeHistoricalData(): void {
    const trend: PerformanceTrend = {
      timestamp: Date.now(),
      systemMetrics: { ...this.currentSystemMetrics },
      applicationMetrics: { ...this.currentApplicationMetrics },
    };
    
    this.performanceHistory.push(trend);
    
    // 限制历史数据大小
    if (this.performanceHistory.length > this.maxHistorySize) {
      this.performanceHistory.shift();
    }
  }
  
  /**
   * 检查告警
   */
  private checkAlerts(): void {
    if (!this.currentSystemMetrics || !this.currentApplicationMetrics) {
      return;
    }
    
    for (const rule of this.alertRules.values()) {
      if (!rule.enabled) continue;
      
      // 检查冷却时间
      if (rule.lastTriggered && Date.now() - rule.lastTriggered < rule.cooldown * 1000) {
        continue;
      }
      
      const value = this.getMetricValue(rule.metric);
      if (value === null) continue;
      
      const triggered = this.evaluateCondition(value, rule.operator, rule.threshold);
      
      if (triggered) {
        this.triggerAlert(rule, value);
      }
    }
  }
  
  /**
   * 获取指标值
   */
  private getMetricValue(metric: string): number | null {
    const parts = metric.split('.');
    let obj: any = {
      system: this.currentSystemMetrics,
      application: this.currentApplicationMetrics,
    };
    
    for (const part of parts) {
      if (obj && typeof obj === 'object' && part in obj) {
        obj = obj[part];
      } else {
        return null;
      }
    }
    
    return typeof obj === 'number' ? obj : null;
  }
  
  /**
   * 评估条件
   */
  private evaluateCondition(value: number, operator: string, threshold: number): boolean {
    switch (operator) {
      case 'gt': return value > threshold;
      case 'lt': return value < threshold;
      case 'eq': return value === threshold;
      case 'gte': return value >= threshold;
      case 'lte': return value <= threshold;
      default: return false;
    }
  }
  
  /**
   * 触发告警
   */
  private triggerAlert(rule: AlertRule, value: number): void {
    const alertId = `${rule.id}_${Date.now()}`;
    
    const alert: AlertEvent = {
      id: alertId,
      ruleId: rule.id,
      level: rule.level,
      message: `${rule.name}: 当前值 ${value.toFixed(2)}, 阈值 ${rule.threshold}`,
      value,
      threshold: rule.threshold,
      timestamp: Date.now(),
      resolved: false,
    };
    
    this.activeAlerts.set(alertId, alert);
    rule.lastTriggered = Date.now();
    
    // 发出告警事件
    this.eventEmitter.emit('alert.triggered', alert);
    
    this.logger.warn(`告警触发: ${alert.message}`);
    
    // 如果启用自动扩缩容，触发扩容检查
    if (this.enableAutoScaling && rule.level === AlertLevel.CRITICAL) {
      this.eventEmitter.emit('autoscaling.check', {
        reason: 'critical_alert',
        metric: rule.metric,
        value,
        threshold: rule.threshold,
      });
    }
  }
  
  /**
   * 定时任务：每分钟收集趋势数据
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async collectTrendData(): Promise<void> {
    if (this.enableTrending) {
      await this.collectMetrics();
    }
  }
  
  /**
   * 定时任务：每小时清理过期告警
   */
  @Cron(CronExpression.EVERY_HOUR)
  cleanupExpiredAlerts(): void {
    const now = Date.now();
    const expireTime = 24 * 60 * 60 * 1000; // 24小时
    
    for (const [alertId, alert] of this.activeAlerts) {
      if (now - alert.timestamp > expireTime) {
        this.activeAlerts.delete(alertId);
      }
    }
  }
  
  /**
   * 获取当前指标
   */
  public getCurrentMetrics(): {
    system: SystemMetrics;
    application: ApplicationMetrics;
  } {
    return {
      system: this.currentSystemMetrics,
      application: this.currentApplicationMetrics,
    };
  }
  
  /**
   * 获取历史数据
   */
  public getHistoricalData(hours: number = 1): PerformanceTrend[] {
    const cutoff = Date.now() - hours * 60 * 60 * 1000;
    return this.performanceHistory.filter(trend => trend.timestamp >= cutoff);
  }
  
  /**
   * 获取活跃告警
   */
  public getActiveAlerts(): AlertEvent[] {
    return Array.from(this.activeAlerts.values()).filter(alert => !alert.resolved);
  }
  
  /**
   * 获取基线指标
   */
  public getBaselineMetrics(): typeof this.baselineMetrics {
    return { ...this.baselineMetrics };
  }
  
  /**
   * 添加自定义告警规则
   */
  public addAlertRule(rule: AlertRule): void {
    this.alertRules.set(rule.id, rule);
  }
  
  /**
   * 移除告警规则
   */
  public removeAlertRule(ruleId: string): void {
    this.alertRules.delete(ruleId);
  }
  
  /**
   * 解决告警
   */
  public resolveAlert(alertId: string): void {
    const alert = this.activeAlerts.get(alertId);
    if (alert) {
      alert.resolved = true;
      this.eventEmitter.emit('alert.resolved', alert);
    }
  }

  /**
   * 获取健康状态
   */
  public getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    score: number;
    issues: string[];
  } {
    const issues: string[] = [];
    let score = 100;

    if (this.currentSystemMetrics) {
      // 检查CPU
      if (this.currentSystemMetrics.cpu.usage > 90) {
        issues.push('CPU使用率过高');
        score -= 30;
      } else if (this.currentSystemMetrics.cpu.usage > 70) {
        issues.push('CPU使用率较高');
        score -= 15;
      }

      // 检查内存
      if (this.currentSystemMetrics.memory.usage > 90) {
        issues.push('内存使用率过高');
        score -= 30;
      } else if (this.currentSystemMetrics.memory.usage > 70) {
        issues.push('内存使用率较高');
        score -= 15;
      }
    }

    // 检查活跃告警
    const criticalAlerts = Array.from(this.activeAlerts.values())
      .filter(alert => !alert.resolved && alert.level === AlertLevel.CRITICAL);

    if (criticalAlerts.length > 0) {
      issues.push(`${criticalAlerts.length}个严重告警`);
      score -= criticalAlerts.length * 20;
    }

    const warningAlerts = Array.from(this.activeAlerts.values())
      .filter(alert => !alert.resolved && alert.level === AlertLevel.WARNING);

    if (warningAlerts.length > 0) {
      issues.push(`${warningAlerts.length}个警告告警`);
      score -= warningAlerts.length * 10;
    }

    // 确定状态
    let status: 'healthy' | 'warning' | 'critical';
    if (score >= 80) {
      status = 'healthy';
    } else if (score >= 50) {
      status = 'warning';
    } else {
      status = 'critical';
    }

    return { status, score: Math.max(0, score), issues };
  }
}
