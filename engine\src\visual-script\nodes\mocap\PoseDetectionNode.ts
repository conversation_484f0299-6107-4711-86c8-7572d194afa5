/**
 * 姿态检测节点
 * 使用MediaPipe进行人体姿态检测
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 关键点数据接口
 */
export interface LandmarkData {
  x: number;
  y: number;
  z?: number;
  visibility?: number;
}

/**
 * 世界坐标关键点数据接口
 */
export interface WorldLandmarkData {
  x: number;
  y: number;
  z: number;
  visibility?: number;
}

/**
 * MediaPipe配置接口
 */
export interface MediaPipeConfig {
  modelComplexity: number;
  smoothLandmarks: boolean;
  enableSegmentation: boolean;
  minDetectionConfidence: number;
  minTrackingConfidence: number;
  enableWorldLandmarks: boolean;
}

/**
 * 姿态检测结果接口
 */
export interface PoseResults {
  landmarks?: LandmarkData[];
  worldLandmarks?: WorldLandmarkData[];
  segmentationMask?: ImageData;
  confidence: number;
}

/**
 * 简化的MediaPipe姿态检测器接口
 */
interface SimplePoseDetector {
  initialize(): Promise<void>;
  destroy(): void;
  detectPose(imageData: ImageData): Promise<PoseResults>;
  updateConfig(config: Partial<MediaPipeConfig>): void;
  on(event: string, callback: Function): void;
}

/**
 * 姿态检测节点配置
 */
export interface PoseDetectionNodeConfig {
  /** 模型复杂度 (0-2) */
  modelComplexity: number;
  /** 是否启用平滑 */
  smoothLandmarks: boolean;
  /** 是否启用分割 */
  enableSegmentation: boolean;
  /** 最小检测置信度 */
  minDetectionConfidence: number;
  /** 最小跟踪置信度 */
  minTrackingConfidence: number;
  /** 是否启用世界坐标 */
  enableWorldLandmarks: boolean;
  /** 是否自动初始化 */
  autoInitialize: boolean;
}

/**
 * 姿态检测节点
 */
export class PoseDetectionNode extends VisualScriptNode {
  /** 节点类型 */
  public static readonly TYPE = 'PoseDetection';

  /** 节点名称 */
  public static readonly NAME = '姿态检测';

  /** 节点描述 */
  public static readonly DESCRIPTION = '使用MediaPipe检测人体姿态关键点';

  private poseDetector: SimplePoseDetector | null = null;
  private config: PoseDetectionNodeConfig;
  private isInitialized = false;
  private lastResults: PoseResults | null = null;
  private processingCount = 0;
  private successCount = 0;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: PoseDetectionNodeConfig = {
    modelComplexity: 1,
    smoothLandmarks: true,
    enableSegmentation: false,
    minDetectionConfidence: 0.5,
    minTrackingConfidence: 0.5,
    enableWorldLandmarks: true,
    autoInitialize: true
  };

  constructor(nodeType: string = PoseDetectionNode.TYPE, name: string = PoseDetectionNode.NAME, id?: string) {
    super(nodeType, name, id);

    this.config = { ...PoseDetectionNode.DEFAULT_CONFIG };
    this.setupPorts();
  }

  /**
   * 设置输入输出端口
   */
  private setupPorts(): void {
    // 输入端口
    this.addInput('imageData', 'object', '图像数据');
    this.addInput('detect', 'trigger', '检测');
    this.addInput('initialize', 'trigger', '初始化');
    this.addInput('modelComplexity', 'number', '模型复杂度');
    this.addInput('minDetectionConfidence', 'number', '检测置信度');
    this.addInput('minTrackingConfidence', 'number', '跟踪置信度');

    // 输出端口
    this.addOutput('landmarks', 'array', '2D关键点');
    this.addOutput('worldLandmarks', 'array', '3D关键点');
    this.addOutput('segmentationMask', 'object', '分割掩码');
    this.addOutput('confidence', 'number', '置信度');
    this.addOutput('isDetected', 'boolean', '检测成功');
    this.addOutput('processingTime', 'number', '处理时间');
    this.addOutput('successRate', 'number', '成功率');
    this.addOutput('onDetected', 'trigger', '检测完成');
    this.addOutput('onInitialized', 'trigger', '初始化完成');
    this.addOutput('onError', 'trigger', '错误');

    // 特定关键点输出
    this.addOutput('nose', 'object', '鼻子');
    this.addOutput('leftEye', 'object', '左眼');
    this.addOutput('rightEye', 'object', '右眼');
    this.addOutput('leftShoulder', 'object', '左肩');
    this.addOutput('rightShoulder', 'object', '右肩');
    this.addOutput('leftElbow', 'object', '左肘');
    this.addOutput('rightElbow', 'object', '右肘');
    this.addOutput('leftWrist', 'object', '左腕');
    this.addOutput('rightWrist', 'object', '右腕');
    this.addOutput('leftHip', 'object', '左髋');
    this.addOutput('rightHip', 'object', '右髋');
    this.addOutput('leftKnee', 'object', '左膝');
    this.addOutput('rightKnee', 'object', '右膝');
    this.addOutput('leftAnkle', 'object', '左踝');
    this.addOutput('rightAnkle', 'object', '右踝');
  }

  /**
   * 执行节点
   */
  public execute(inputs?: any): any {
    try {
      // 检查输入
      const imageData = inputs?.imageData as ImageData;
      const detectTrigger = inputs?.detect;
      const initializeTrigger = inputs?.initialize;
      const modelComplexity = inputs?.modelComplexity as number;
      const minDetectionConfidence = inputs?.minDetectionConfidence as number;
      const minTrackingConfidence = inputs?.minTrackingConfidence as number;

      // 更新配置
      if (modelComplexity !== undefined) {
        this.config.modelComplexity = Math.max(0, Math.min(2, Math.floor(modelComplexity)));
      }
      if (minDetectionConfidence !== undefined) {
        this.config.minDetectionConfidence = Math.max(0, Math.min(1, minDetectionConfidence));
      }
      if (minTrackingConfidence !== undefined) {
        this.config.minTrackingConfidence = Math.max(0, Math.min(1, minTrackingConfidence));
      }

      // 处理初始化触发
      if (initializeTrigger || (!this.isInitialized && this.config.autoInitialize)) {
        this.initializeDetector();
      }

      // 处理检测触发
      if (detectTrigger && imageData && this.isInitialized) {
        this.detectPose(imageData);
      }

      // 返回输出
      return this.getNodeOutputs();

    } catch (error) {
      Debug.error('PoseDetectionNode', '节点执行失败', String(error));
      return { onError: true };
    }
  }

  /**
   * 获取节点输出值
   */
  private getNodeOutputs(): any {
    if (!this.lastResults) {
      return {
        landmarks: [],
        worldLandmarks: [],
        segmentationMask: null,
        confidence: 0,
        isDetected: false,
        processingTime: 0,
        successRate: 0,
        onDetected: false,
        onInitialized: false,
        onError: false
      };
    }

    const results = this.lastResults;
    const successRate = this.processingCount > 0 ? this.successCount / this.processingCount : 0;

    // 提取特定关键点
    const specificLandmarks: any = {};
    if (results.landmarks && results.landmarks.length >= 33) {
      const landmarks = results.landmarks;
      specificLandmarks.nose = landmarks[0];
      specificLandmarks.leftEye = landmarks[2];
      specificLandmarks.rightEye = landmarks[5];
      specificLandmarks.leftShoulder = landmarks[11];
      specificLandmarks.rightShoulder = landmarks[12];
      specificLandmarks.leftElbow = landmarks[13];
      specificLandmarks.rightElbow = landmarks[14];
      specificLandmarks.leftWrist = landmarks[15];
      specificLandmarks.rightWrist = landmarks[16];
      specificLandmarks.leftHip = landmarks[23];
      specificLandmarks.rightHip = landmarks[24];
      specificLandmarks.leftKnee = landmarks[25];
      specificLandmarks.rightKnee = landmarks[26];
      specificLandmarks.leftAnkle = landmarks[27];
      specificLandmarks.rightAnkle = landmarks[28];
    }

    return {
      landmarks: results.landmarks || [],
      worldLandmarks: results.worldLandmarks || [],
      segmentationMask: results.segmentationMask || null,
      confidence: results.confidence,
      isDetected: results.confidence > this.config.minDetectionConfidence,
      processingTime: 0,
      successRate,
      onDetected: false,
      onInitialized: false,
      onError: false,
      ...specificLandmarks
    };
  }

  /**
   * 初始化检测器
   */
  private initializeDetector(): void {
    try {
      // 模拟初始化
      this.isInitialized = true;
      this.processingCount = 0;
      this.successCount = 0;

      Debug.log('PoseDetectionNode', '姿态检测器初始化成功');

    } catch (error) {
      Debug.error('PoseDetectionNode', '初始化检测器失败', String(error));
      this.isInitialized = false;
    }
  }

  /**
   * 检测姿态
   */
  private detectPose(imageData: ImageData): void {
    if (!this.isInitialized) {
      Debug.error('PoseDetectionNode', '检测器未初始化');
      return;
    }

    try {
      this.processingCount++;

      // 模拟检测结果
      const mockResults: PoseResults = {
        landmarks: this.generateMockLandmarks(),
        worldLandmarks: this.generateMockWorldLandmarks(),
        segmentationMask: undefined,
        confidence: 0.8
      };

      this.lastResults = mockResults;
      this.successCount++;

      Debug.log('PoseDetectionNode', '姿态检测完成');

    } catch (error) {
      Debug.error('PoseDetectionNode', '姿态检测失败', String(error));
    }
  }

  /**
   * 生成模拟2D关键点
   */
  private generateMockLandmarks(): LandmarkData[] {
    const landmarks: LandmarkData[] = [];
    for (let i = 0; i < 33; i++) {
      landmarks.push({
        x: Math.random(),
        y: Math.random(),
        z: Math.random() * 0.1,
        visibility: 0.9
      });
    }
    return landmarks;
  }

  /**
   * 生成模拟3D关键点
   */
  private generateMockWorldLandmarks(): WorldLandmarkData[] {
    const landmarks: WorldLandmarkData[] = [];
    for (let i = 0; i < 33; i++) {
      landmarks.push({
        x: (Math.random() - 0.5) * 2,
        y: (Math.random() - 0.5) * 2,
        z: (Math.random() - 0.5) * 2,
        visibility: 0.9
      });
    }
    return landmarks;
  }



  /**
   * 获取节点配置
   */
  public getConfig(): PoseDetectionNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新节点配置
   */
  public updateConfig(newConfig: Partial<PoseDetectionNodeConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取最后检测结果
   */
  public getLastResults(): PoseResults | null {
    return this.lastResults;
  }

  /**
   * 获取检测成功率
   */
  public getSuccessRate(): number {
    return this.processingCount > 0 ? this.successCount / this.processingCount : 0;
  }

  /**
   * 是否已初始化
   */
  public get initialized(): boolean {
    return this.isInitialized;
  }
}
