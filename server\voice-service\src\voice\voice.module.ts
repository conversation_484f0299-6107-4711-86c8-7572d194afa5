import { <PERSON>du<PERSON> } from '@nestjs/common';
import { VoiceController } from './voice.controller';
import { VoiceService } from './voice.service';
import { VoiceGateway } from './voice.gateway';
import { SpeechRecognitionModule } from '../speech-recognition/speech-recognition.module';
import { SpeechSynthesisModule } from '../speech-synthesis/speech-synthesis.module';
import { AudioProcessingModule } from '../audio-processing/audio-processing.module';
import { LipSyncModule } from '../lip-sync/lip-sync.module';

@Module({
  imports: [
    SpeechRecognitionModule,
    SpeechSynthesisModule,
    AudioProcessingModule,
    LipSyncModule,
  ],
  controllers: [VoiceController],
  providers: [VoiceService, VoiceGateway],
  exports: [VoiceService],
})
export class VoiceModule {}
