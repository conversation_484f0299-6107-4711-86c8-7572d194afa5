/**
 * 编辑器UI组件测试套件
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import '@testing-library/jest-dom';

import { UIVisualEditor, UIElementType } from '../../src/components/ui/UIVisualEditor';
import { UIComponentLibrary, ComponentCategory } from '../../src/components/ui/UIComponentLibrary';
import { undoRedoService, ActionType } from '../../src/services/UndoRedoService';

// 模拟antd组件
jest.mock('antd', () => ({
  Card: ({ children, ...props }: any) => <div data-testid="card" {...props}>{children}</div>,
  Button: ({ children, onClick, ...props }: any) => (
    <button data-testid="button" onClick={onClick} {...props}>{children}</button>
  ),
  Input: ({ onChange, ...props }: any) => (
    <input data-testid="input" onChange={onChange} {...props} />
  ),
  Select: ({ children, onChange, ...props }: any) => (
    <select data-testid="select" onChange={onChange} {...props}>{children}</select>
  ),
  Space: ({ children }: any) => <div data-testid="space">{children}</div>,
  Tooltip: ({ children, title }: any) => (
    <div data-testid="tooltip" title={title}>{children}</div>
  ),
  Modal: ({ children, open, onCancel, ...props }: any) => 
    open ? <div data-testid="modal" {...props}>{children}</div> : null,
  Form: ({ children, onFinish }: any) => (
    <form data-testid="form" onSubmit={onFinish}>{children}</form>
  ),
  Row: ({ children }: any) => <div data-testid="row">{children}</div>,
  Col: ({ children }: any) => <div data-testid="col">{children}</div>,
  Slider: ({ onChange, ...props }: any) => (
    <input type="range" data-testid="slider" onChange={onChange} {...props} />
  ),
  ColorPicker: ({ onChange, ...props }: any) => (
    <input type="color" data-testid="color-picker" onChange={onChange} {...props} />
  ),
  List: ({ dataSource, renderItem }: any) => (
    <div data-testid="list">
      {dataSource?.map((item: any, index: number) => (
        <div key={index}>{renderItem(item)}</div>
      ))}
    </div>
  ),
  Avatar: ({ src, icon }: any) => (
    <div data-testid="avatar">{src ? <img src={src} alt="" /> : icon}</div>
  ),
  Tag: ({ children }: any) => <span data-testid="tag">{children}</span>,
  Empty: ({ description }: any) => <div data-testid="empty">{description}</div>,
  Spin: ({ children, spinning }: any) => (
    <div data-testid="spin" data-spinning={spinning}>{children}</div>
  ),
  Divider: () => <hr data-testid="divider" />,
  Popconfirm: ({ children, onConfirm }: any) => (
    <div data-testid="popconfirm" onClick={onConfirm}>{children}</div>
  ),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warning: jest.fn()
  }
}));

// 模拟图标
jest.mock('@ant-design/icons', () => ({
  AppstoreOutlined: () => <span data-testid="appstore-icon">AppstoreOutlined</span>,
  EditOutlined: () => <span data-testid="edit-icon">EditOutlined</span>,
  DeleteOutlined: () => <span data-testid="delete-icon">DeleteOutlined</span>,
  CopyOutlined: () => <span data-testid="copy-icon">CopyOutlined</span>,
  UndoOutlined: () => <span data-testid="undo-icon">UndoOutlined</span>,
  RedoOutlined: () => <span data-testid="redo-icon">RedoOutlined</span>,
  SaveOutlined: () => <span data-testid="save-icon">SaveOutlined</span>,
  EyeOutlined: () => <span data-testid="eye-icon">EyeOutlined</span>,
  SearchOutlined: () => <span data-testid="search-icon">SearchOutlined</span>,
  PlusOutlined: () => <span data-testid="plus-icon">PlusOutlined</span>,
  StarOutlined: () => <span data-testid="star-icon">StarOutlined</span>,
  StarFilled: () => <span data-testid="star-filled-icon">StarFilled</span>,
  DownloadOutlined: () => <span data-testid="download-icon">DownloadOutlined</span>,
  SettingOutlined: () => <span data-testid="setting-icon">SettingOutlined</span>
}));

// 模拟国际化
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}));

// 模拟引擎服务
jest.mock('../../src/services/EngineService', () => ({
  default: {
    createUIElement: jest.fn(),
    updateUIElement: jest.fn(),
    deleteUIElement: jest.fn()
  }
}));

// 测试工具函数
const renderWithDnd = (component: React.ReactElement) => {
  return render(
    <DndProvider backend={HTML5Backend}>
      {component}
    </DndProvider>
  );
};

describe('UI可视化编辑器测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('应该正确渲染编辑器界面', () => {
    renderWithDnd(<UIVisualEditor />);
    
    // 验证主要界面元素
    expect(screen.getByTestId('undo-icon')).toBeInTheDocument();
    expect(screen.getByTestId('redo-icon')).toBeInTheDocument();
    expect(screen.getByTestId('save-icon')).toBeInTheDocument();
    expect(screen.getByTestId('eye-icon')).toBeInTheDocument();
    
    // 验证组件库
    expect(screen.getByText('组件库')).toBeInTheDocument();
    
    // 验证设计画布
    expect(screen.getByText('请选择一个元素')).toBeInTheDocument();
  });

  test('应该支持添加UI元素', async () => {
    renderWithDnd(<UIVisualEditor />);
    
    // 模拟拖拽添加按钮组件
    const buttonComponent = screen.getByText('按钮');
    expect(buttonComponent).toBeInTheDocument();
    
    // 这里可以添加更多的拖拽测试逻辑
    // 由于react-dnd的测试比较复杂，这里主要验证组件渲染
  });

  test('应该支持撤销和重做操作', () => {
    renderWithDnd(<UIVisualEditor />);
    
    const undoButton = screen.getByTestId('undo-icon').closest('button');
    const redoButton = screen.getByTestId('redo-icon').closest('button');
    
    expect(undoButton).toBeDisabled();
    expect(redoButton).toBeDisabled();
  });

  test('应该支持保存和预览功能', () => {
    renderWithDnd(<UIVisualEditor />);
    
    const saveButton = screen.getByTestId('save-icon').closest('button');
    const previewButton = screen.getByTestId('eye-icon').closest('button');
    
    fireEvent.click(saveButton!);
    fireEvent.click(previewButton!);
    
    // 验证按钮可以点击
    expect(saveButton).toBeInTheDocument();
    expect(previewButton).toBeInTheDocument();
  });
});

describe('UI组件库测试', () => {
  const mockOnComponentSelect = jest.fn();
  const mockOnComponentAdd = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('应该正确渲染组件库界面', () => {
    render(
      <UIComponentLibrary
        onComponentSelect={mockOnComponentSelect}
        onComponentAdd={mockOnComponentAdd}
      />
    );
    
    // 验证搜索框
    expect(screen.getByTestId('input')).toBeInTheDocument();
    
    // 验证分类选择器
    expect(screen.getByTestId('select')).toBeInTheDocument();
    
    // 验证组件卡片
    expect(screen.getByText('基础按钮')).toBeInTheDocument();
    expect(screen.getByText('输入框')).toBeInTheDocument();
    expect(screen.getByText('进度条')).toBeInTheDocument();
  });

  test('应该支持组件搜索', async () => {
    render(
      <UIComponentLibrary
        onComponentSelect={mockOnComponentSelect}
        onComponentAdd={mockOnComponentAdd}
      />
    );
    
    const searchInput = screen.getByTestId('input');
    fireEvent.change(searchInput, { target: { value: '按钮' } });
    
    // 验证搜索功能
    expect(searchInput).toHaveValue('按钮');
  });

  test('应该支持组件分类筛选', () => {
    render(
      <UIComponentLibrary
        onComponentSelect={mockOnComponentSelect}
        onComponentAdd={mockOnComponentAdd}
      />
    );
    
    const categorySelect = screen.getByTestId('select');
    fireEvent.change(categorySelect, { target: { value: ComponentCategory.BASIC } });
    
    // 验证分类筛选
    expect(categorySelect).toBeInTheDocument();
  });

  test('应该支持组件收藏功能', () => {
    render(
      <UIComponentLibrary
        onComponentSelect={mockOnComponentSelect}
        onComponentAdd={mockOnComponentAdd}
      />
    );
    
    // 查找收藏按钮
    const favoriteButton = screen.getByTestId('star-icon').closest('button');
    expect(favoriteButton).toBeInTheDocument();
  });

  test('应该支持组件预览功能', () => {
    render(
      <UIComponentLibrary
        onComponentSelect={mockOnComponentSelect}
        onComponentAdd={mockOnComponentAdd}
      />
    );
    
    // 查找预览按钮
    const previewButton = screen.getByTestId('eye-icon').closest('button');
    expect(previewButton).toBeInTheDocument();
  });

  test('应该支持创建新组件', () => {
    render(
      <UIComponentLibrary
        onComponentSelect={mockOnComponentSelect}
        onComponentAdd={mockOnComponentAdd}
        mode="manage"
      />
    );
    
    // 查找创建按钮
    const createButton = screen.getByTestId('plus-icon').closest('button');
    expect(createButton).toBeInTheDocument();
    
    fireEvent.click(createButton!);
    
    // 验证模态框打开
    expect(screen.getByTestId('modal')).toBeInTheDocument();
  });
});

describe('撤销重做服务测试', () => {
  beforeEach(() => {
    undoRedoService.clear();
  });

  test('应该正确执行操作', () => {
    const mockExecute = jest.fn();
    const mockUndo = jest.fn();
    
    const action = {
      id: 'test-action',
      type: ActionType.CREATE_ELEMENT,
      data: {
        type: ActionType.CREATE_ELEMENT,
        elementId: 'element-1',
        timestamp: Date.now(),
        description: '创建元素'
      },
      execute: mockExecute,
      undo: mockUndo,
      redo: mockExecute
    };
    
    undoRedoService.execute(action);
    
    expect(mockExecute).toHaveBeenCalled();
    expect(undoRedoService.canUndo()).toBe(true);
    expect(undoRedoService.canRedo()).toBe(false);
  });

  test('应该正确撤销操作', () => {
    const mockExecute = jest.fn();
    const mockUndo = jest.fn();
    
    const action = {
      id: 'test-action',
      type: ActionType.CREATE_ELEMENT,
      data: {
        type: ActionType.CREATE_ELEMENT,
        elementId: 'element-1',
        timestamp: Date.now(),
        description: '创建元素'
      },
      execute: mockExecute,
      undo: mockUndo,
      redo: mockExecute
    };
    
    undoRedoService.execute(action);
    undoRedoService.undo();
    
    expect(mockUndo).toHaveBeenCalled();
    expect(undoRedoService.canUndo()).toBe(false);
    expect(undoRedoService.canRedo()).toBe(true);
  });

  test('应该正确重做操作', () => {
    const mockExecute = jest.fn();
    const mockUndo = jest.fn();
    
    const action = {
      id: 'test-action',
      type: ActionType.CREATE_ELEMENT,
      data: {
        type: ActionType.CREATE_ELEMENT,
        elementId: 'element-1',
        timestamp: Date.now(),
        description: '创建元素'
      },
      execute: mockExecute,
      undo: mockUndo,
      redo: mockExecute
    };
    
    undoRedoService.execute(action);
    undoRedoService.undo();
    undoRedoService.redo();
    
    expect(mockExecute).toHaveBeenCalledTimes(2);
    expect(undoRedoService.canUndo()).toBe(true);
    expect(undoRedoService.canRedo()).toBe(false);
  });

  test('应该支持批量操作', () => {
    const mockExecute1 = jest.fn();
    const mockExecute2 = jest.fn();
    const mockUndo1 = jest.fn();
    const mockUndo2 = jest.fn();
    
    const actions = [
      {
        id: 'action-1',
        type: ActionType.CREATE_ELEMENT,
        data: {
          type: ActionType.CREATE_ELEMENT,
          elementId: 'element-1',
          timestamp: Date.now(),
          description: '创建元素1'
        },
        execute: mockExecute1,
        undo: mockUndo1,
        redo: mockExecute1
      },
      {
        id: 'action-2',
        type: ActionType.CREATE_ELEMENT,
        data: {
          type: ActionType.CREATE_ELEMENT,
          elementId: 'element-2',
          timestamp: Date.now(),
          description: '创建元素2'
        },
        execute: mockExecute2,
        undo: mockUndo2,
        redo: mockExecute2
      }
    ];
    
    undoRedoService.executeGroup(actions, '批量创建');
    
    expect(mockExecute1).toHaveBeenCalled();
    expect(mockExecute2).toHaveBeenCalled();
    expect(undoRedoService.canUndo()).toBe(true);
  });

  test('应该正确清空历史记录', () => {
    const mockExecute = jest.fn();
    const mockUndo = jest.fn();
    
    const action = {
      id: 'test-action',
      type: ActionType.CREATE_ELEMENT,
      data: {
        type: ActionType.CREATE_ELEMENT,
        elementId: 'element-1',
        timestamp: Date.now(),
        description: '创建元素'
      },
      execute: mockExecute,
      undo: mockUndo,
      redo: mockExecute
    };
    
    undoRedoService.execute(action);
    undoRedoService.clear();
    
    expect(undoRedoService.canUndo()).toBe(false);
    expect(undoRedoService.canRedo()).toBe(false);
    expect(undoRedoService.getHistory()).toHaveLength(0);
  });
});
