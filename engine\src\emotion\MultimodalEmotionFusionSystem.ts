/**
 * 多模态情感融合系统
 * 融合语音、表情、动作等多种模态的情感信息
 */

import { System } from '../core/System';
import { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 情感模态类型
 */
export enum EmotionModalityType {
  VOICE = 'voice',
  FACIAL = 'facial',
  GESTURE = 'gesture',
  POSTURE = 'posture',
  TEXT = 'text',
}

/**
 * 模态情感数据
 */
export interface ModalityEmotionData {
  /** 模态类型 */
  modalityType: EmotionModalityType;
  /** 情感类型 */
  emotionType: string;
  /** 情感强度 */
  intensity: number;
  /** 置信度 */
  confidence: number;
  /** 时间戳 */
  timestamp: number;
  /** 持续时间 */
  duration: number;
  /** 原始特征数据 */
  rawFeatures: any;
  /** 额外元数据 */
  metadata: any;
}

/**
 * 融合情感结果
 */
export interface FusedEmotionResult {
  /** 主要情感类型 */
  primaryEmotion: string;
  /** 情感强度 */
  intensity: number;
  /** 总体置信度 */
  confidence: number;
  /** 各模态贡献度 */
  modalityContributions: Map<EmotionModalityType, number>;
  /** 情感分数分布 */
  emotionScores: Map<string, number>;
  /** 融合时间戳 */
  timestamp: number;
  /** 融合质量评分 */
  fusionQuality: number;
}

/**
 * 模态权重配置
 */
export interface ModalityWeightConfig {
  /** 语音权重 */
  voiceWeight: number;
  /** 面部表情权重 */
  facialWeight: number;
  /** 手势权重 */
  gestureWeight: number;
  /** 姿态权重 */
  postureWeight: number;
  /** 文本权重 */
  textWeight: number;
  /** 动态权重调整 */
  enableDynamicWeighting: boolean;
}

/**
 * 融合策略配置
 */
export interface FusionStrategyConfig {
  /** 融合方法 */
  fusionMethod: 'weighted_average' | 'maximum_confidence' | 'neural_fusion' | 'adaptive_fusion';
  /** 时间窗口大小（毫秒） */
  timeWindowSize: number;
  /** 最小模态数量 */
  minModalityCount: number;
  /** 置信度阈值 */
  confidenceThreshold: number;
  /** 是否启用时间对齐 */
  enableTimeAlignment: boolean;
  /** 是否启用冲突检测 */
  enableConflictDetection: boolean;
}

/**
 * 多模态情感融合系统配置
 */
export interface MultimodalEmotionFusionConfig {
  /** 模态权重配置 */
  modalityWeights: ModalityWeightConfig;
  /** 融合策略配置 */
  fusionStrategy: FusionStrategyConfig;
  /** 最大历史记录数 */
  maxHistorySize: number;
  /** 数据清理间隔（毫秒） */
  cleanupInterval: number;
  /** 调试模式 */
  debug: boolean;
}

/**
 * 模态数据缓冲区
 */
interface ModalityDataBuffer {
  /** 数据列表 */
  data: ModalityEmotionData[];
  /** 最后更新时间 */
  lastUpdate: number;
  /** 缓冲区大小限制 */
  maxSize: number;
}

/**
 * 多模态情感融合系统
 */
export class MultimodalEmotionFusionSystem extends System {
  /** 系统名称 */
  static readonly NAME = 'MultimodalEmotionFusionSystem';

  /** 配置 */
  private config: MultimodalEmotionFusionConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 模态数据缓冲区 */
  private modalityBuffers: Map<EmotionModalityType, ModalityDataBuffer> = new Map();

  /** 融合历史记录 */
  private fusionHistory: FusedEmotionResult[] = [];

  /** 模态权重历史（用于动态调整） */
  private weightHistory: Map<EmotionModalityType, number[]> = new Map();

  /** 最后清理时间 */
  private lastCleanupTime: number = 0;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: Partial<MultimodalEmotionFusionConfig> = {}) {
    super();

    this.config = {
      modalityWeights: {
        voiceWeight: 0.3,
        facialWeight: 0.4,
        gestureWeight: 0.15,
        postureWeight: 0.1,
        textWeight: 0.05,
        enableDynamicWeighting: true,
      },
      fusionStrategy: {
        fusionMethod: 'adaptive_fusion',
        timeWindowSize: 2000, // 2秒
        minModalityCount: 2,
        confidenceThreshold: 0.5,
        enableTimeAlignment: true,
        enableConflictDetection: true,
      },
      maxHistorySize: 100,
      cleanupInterval: 30000, // 30秒
      debug: false,
      ...config,
    };
  }

  /**
   * 初始化系统
   */
  public async initialize(): Promise<void> {
    if (this.initialized) return;

    // 初始化模态缓冲区
    this.initializeModalityBuffers();

    // 初始化权重历史
    this.initializeWeightHistory();

    this.initialized = true;

    if (this.config.debug) {
      console.log('多模态情感融合系统初始化完成');
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized) return;

    const currentTime = Date.now();

    // 执行情感融合
    this.performEmotionFusion();

    // 更新动态权重
    if (this.config.modalityWeights.enableDynamicWeighting) {
      this.updateDynamicWeights();
    }

    // 定期清理过期数据
    if (currentTime - this.lastCleanupTime > this.config.cleanupInterval) {
      this.cleanupExpiredData();
      this.lastCleanupTime = currentTime;
    }
  }

  /**
   * 添加模态情感数据
   * @param modalityData 模态情感数据
   */
  public addModalityEmotionData(modalityData: ModalityEmotionData): void {
    const buffer = this.modalityBuffers.get(modalityData.modalityType);
    if (!buffer) {
      console.warn(`未知的模态类型: ${modalityData.modalityType}`);
      return;
    }

    // 添加数据到缓冲区
    buffer.data.push(modalityData);
    buffer.lastUpdate = Date.now();

    // 限制缓冲区大小
    if (buffer.data.length > buffer.maxSize) {
      buffer.data.shift();
    }

    // 触发数据添加事件
    this.eventEmitter.emit('modalityDataAdded', modalityData);

    if (this.config.debug) {
      console.log(`添加${modalityData.modalityType}模态数据:`, modalityData);
    }
  }

  /**
   * 获取最新的融合情感结果
   * @returns 融合情感结果
   */
  public getLatestFusedEmotion(): FusedEmotionResult | null {
    return this.fusionHistory.length > 0 ? this.fusionHistory[this.fusionHistory.length - 1] : null;
  }

  /**
   * 获取融合历史
   * @param count 获取数量
   * @returns 融合历史
   */
  public getFusionHistory(count: number = 10): FusedEmotionResult[] {
    return this.fusionHistory.slice(-count);
  }

  /**
   * 设置模态权重
   * @param modalityType 模态类型
   * @param weight 权重
   */
  public setModalityWeight(modalityType: EmotionModalityType, weight: number): void {
    switch (modalityType) {
      case EmotionModalityType.VOICE:
        this.config.modalityWeights.voiceWeight = weight;
        break;
      case EmotionModalityType.FACIAL:
        this.config.modalityWeights.facialWeight = weight;
        break;
      case EmotionModalityType.GESTURE:
        this.config.modalityWeights.gestureWeight = weight;
        break;
      case EmotionModalityType.POSTURE:
        this.config.modalityWeights.postureWeight = weight;
        break;
      case EmotionModalityType.TEXT:
        this.config.modalityWeights.textWeight = weight;
        break;
    }

    // 记录权重变化
    this.recordWeightChange(modalityType, weight);

    if (this.config.debug) {
      console.log(`设置${modalityType}模态权重为: ${weight}`);
    }
  }

  /**
   * 初始化模态缓冲区
   */
  private initializeModalityBuffers(): void {
    const bufferSize = Math.ceil(this.config.fusionStrategy.timeWindowSize / 100); // 假设100ms一个数据点

    for (const modalityType of Object.values(EmotionModalityType)) {
      this.modalityBuffers.set(modalityType, {
        data: [],
        lastUpdate: 0,
        maxSize: bufferSize,
      });
    }
  }

  /**
   * 初始化权重历史
   */
  private initializeWeightHistory(): void {
    for (const modalityType of Object.values(EmotionModalityType)) {
      this.weightHistory.set(modalityType, []);
    }
  }

  /**
   * 执行情感融合
   */
  private performEmotionFusion(): void {
    const currentTime = Date.now();
    const timeWindow = this.config.fusionStrategy.timeWindowSize;

    // 收集时间窗口内的所有模态数据
    const windowData: ModalityEmotionData[] = [];
    
    for (const [modalityType, buffer] of this.modalityBuffers) {
      const recentData = buffer.data.filter(
        data => currentTime - data.timestamp <= timeWindow
      );
      windowData.push(...recentData);
    }

    // 检查是否有足够的模态数据
    const uniqueModalities = new Set(windowData.map(data => data.modalityType));
    if (uniqueModalities.size < this.config.fusionStrategy.minModalityCount) {
      return; // 模态数量不足，跳过融合
    }

    // 时间对齐
    let alignedData = windowData;
    if (this.config.fusionStrategy.enableTimeAlignment) {
      alignedData = this.performTimeAlignment(windowData);
    }

    // 冲突检测
    if (this.config.fusionStrategy.enableConflictDetection) {
      const conflicts = this.detectConflicts(alignedData);
      if (conflicts.length > 0 && this.config.debug) {
        console.log('检测到情感冲突:', conflicts);
      }
    }

    // 执行融合
    const fusedResult = this.fuseEmotionData(alignedData);
    
    if (fusedResult) {
      // 添加到历史记录
      this.fusionHistory.push(fusedResult);
      
      // 限制历史记录大小
      if (this.fusionHistory.length > this.config.maxHistorySize) {
        this.fusionHistory.shift();
      }

      // 触发融合完成事件
      this.eventEmitter.emit('emotionFused', fusedResult);

      if (this.config.debug) {
        console.log('情感融合完成:', fusedResult);
      }
    }
  }

  /**
   * 融合情感数据
   * @param data 模态数据列表
   * @returns 融合结果
   */
  private fuseEmotionData(data: ModalityEmotionData[]): FusedEmotionResult | null {
    if (data.length === 0) return null;

    switch (this.config.fusionStrategy.fusionMethod) {
      case 'weighted_average':
        return this.weightedAverageFusion(data);
      case 'maximum_confidence':
        return this.maximumConfidenceFusion(data);
      case 'neural_fusion':
        return this.neuralFusion(data);
      case 'adaptive_fusion':
      default:
        return this.adaptiveFusion(data);
    }
  }

  /**
   * 加权平均融合
   * @param data 模态数据列表
   * @returns 融合结果
   */
  private weightedAverageFusion(data: ModalityEmotionData[]): FusedEmotionResult {
    const emotionScores = new Map<string, number>();
    const modalityContributions = new Map<EmotionModalityType, number>();
    let totalWeight = 0;
    let totalConfidence = 0;

    // 计算加权情感分数
    for (const modalityData of data) {
      const weight = this.getModalityWeight(modalityData.modalityType);
      const weightedScore = modalityData.intensity * modalityData.confidence * weight;
      
      // 累加情感分数
      const currentScore = emotionScores.get(modalityData.emotionType) || 0;
      emotionScores.set(modalityData.emotionType, currentScore + weightedScore);
      
      // 累加模态贡献度
      const currentContribution = modalityContributions.get(modalityData.modalityType) || 0;
      modalityContributions.set(modalityData.modalityType, currentContribution + weight);
      
      totalWeight += weight;
      totalConfidence += modalityData.confidence * weight;
    }

    // 归一化
    if (totalWeight > 0) {
      for (const [emotion, score] of emotionScores) {
        emotionScores.set(emotion, score / totalWeight);
      }
      totalConfidence /= totalWeight;
    }

    // 找出主要情感
    let primaryEmotion = '';
    let maxScore = -1;
    for (const [emotion, score] of emotionScores) {
      if (score > maxScore) {
        maxScore = score;
        primaryEmotion = emotion;
      }
    }

    return {
      primaryEmotion,
      intensity: maxScore,
      confidence: totalConfidence,
      modalityContributions,
      emotionScores,
      timestamp: Date.now(),
      fusionQuality: this.calculateFusionQuality(data, modalityContributions),
    };
  }

  /**
   * 最大置信度融合
   * @param data 模态数据列表
   * @returns 融合结果
   */
  private maximumConfidenceFusion(data: ModalityEmotionData[]): FusedEmotionResult {
    // 找出置信度最高的数据
    let maxConfidenceData = data[0];
    for (const modalityData of data) {
      if (modalityData.confidence > maxConfidenceData.confidence) {
        maxConfidenceData = modalityData;
      }
    }

    const emotionScores = new Map<string, number>();
    emotionScores.set(maxConfidenceData.emotionType, maxConfidenceData.intensity);

    const modalityContributions = new Map<EmotionModalityType, number>();
    modalityContributions.set(maxConfidenceData.modalityType, 1.0);

    return {
      primaryEmotion: maxConfidenceData.emotionType,
      intensity: maxConfidenceData.intensity,
      confidence: maxConfidenceData.confidence,
      modalityContributions,
      emotionScores,
      timestamp: Date.now(),
      fusionQuality: maxConfidenceData.confidence,
    };
  }

  /**
   * 神经网络融合（简化实现）
   * @param data 模态数据列表
   * @returns 融合结果
   */
  private neuralFusion(data: ModalityEmotionData[]): FusedEmotionResult {
    // 这里是简化的神经网络融合实现
    // 实际应用中应该使用训练好的神经网络模型
    return this.weightedAverageFusion(data);
  }

  /**
   * 自适应融合
   * @param data 模态数据列表
   * @returns 融合结果
   */
  private adaptiveFusion(data: ModalityEmotionData[]): FusedEmotionResult {
    // 根据历史表现动态调整融合策略
    const recentHistory = this.fusionHistory.slice(-10);
    
    if (recentHistory.length < 5) {
      // 历史数据不足，使用加权平均
      return this.weightedAverageFusion(data);
    }

    // 分析历史融合质量，选择最佳策略
    const avgQuality = recentHistory.reduce((sum, result) => sum + result.fusionQuality, 0) / recentHistory.length;
    
    if (avgQuality > 0.8) {
      // 质量较高，继续使用当前策略
      return this.weightedAverageFusion(data);
    } else {
      // 质量较低，尝试最大置信度策略
      return this.maximumConfidenceFusion(data);
    }
  }

  /**
   * 获取模态权重
   * @param modalityType 模态类型
   * @returns 权重值
   */
  private getModalityWeight(modalityType: EmotionModalityType): number {
    switch (modalityType) {
      case EmotionModalityType.VOICE:
        return this.config.modalityWeights.voiceWeight;
      case EmotionModalityType.FACIAL:
        return this.config.modalityWeights.facialWeight;
      case EmotionModalityType.GESTURE:
        return this.config.modalityWeights.gestureWeight;
      case EmotionModalityType.POSTURE:
        return this.config.modalityWeights.postureWeight;
      case EmotionModalityType.TEXT:
        return this.config.modalityWeights.textWeight;
      default:
        return 0.1; // 默认权重
    }
  }

  /**
   * 时间对齐
   * @param data 模态数据列表
   * @returns 对齐后的数据
   */
  private performTimeAlignment(data: ModalityEmotionData[]): ModalityEmotionData[] {
    // 简化的时间对齐实现
    // 实际应用中可能需要更复杂的时间同步算法
    return data.sort((a, b) => a.timestamp - b.timestamp);
  }

  /**
   * 检测冲突
   * @param data 模态数据列表
   * @returns 冲突列表
   */
  private detectConflicts(data: ModalityEmotionData[]): string[] {
    const conflicts: string[] = [];
    
    // 检查不同模态是否表达了相反的情感
    const emotionTypes = data.map(d => d.emotionType);
    const uniqueEmotions = new Set(emotionTypes);
    
    if (uniqueEmotions.size > 1) {
      // 简化的冲突检测：如果有多种不同的情感类型，认为存在冲突
      conflicts.push(`检测到多种情感类型: ${Array.from(uniqueEmotions).join(', ')}`);
    }
    
    return conflicts;
  }

  /**
   * 计算融合质量
   * @param data 原始数据
   * @param contributions 模态贡献度
   * @returns 融合质量评分
   */
  private calculateFusionQuality(
    data: ModalityEmotionData[],
    contributions: Map<EmotionModalityType, number>
  ): number {
    // 基于数据一致性和模态覆盖度计算融合质量
    const modalityCount = contributions.size;
    const maxModalityCount = Object.values(EmotionModalityType).length;
    
    const coverageScore = modalityCount / maxModalityCount;
    const avgConfidence = data.reduce((sum, d) => sum + d.confidence, 0) / data.length;
    
    return (coverageScore * 0.4 + avgConfidence * 0.6);
  }

  /**
   * 记录权重变化
   * @param modalityType 模态类型
   * @param weight 新权重
   */
  private recordWeightChange(modalityType: EmotionModalityType, weight: number): void {
    const history = this.weightHistory.get(modalityType);
    if (history) {
      history.push(weight);
      
      // 限制历史记录大小
      if (history.length > 50) {
        history.shift();
      }
    }
  }

  /**
   * 更新动态权重
   */
  private updateDynamicWeights(): void {
    // 基于历史表现动态调整模态权重
    // 这里是简化实现，实际应用中可能需要更复杂的算法
    
    for (const [modalityType, buffer] of this.modalityBuffers) {
      if (buffer.data.length > 0) {
        const avgConfidence = buffer.data.reduce((sum, d) => sum + d.confidence, 0) / buffer.data.length;
        
        // 根据平均置信度调整权重
        const currentWeight = this.getModalityWeight(modalityType);
        const adjustmentFactor = 0.1; // 调整幅度
        const newWeight = currentWeight + (avgConfidence - 0.5) * adjustmentFactor;
        
        // 限制权重范围
        const clampedWeight = Math.max(0.05, Math.min(0.8, newWeight));
        
        if (Math.abs(clampedWeight - currentWeight) > 0.01) {
          this.setModalityWeight(modalityType, clampedWeight);
        }
      }
    }
  }

  /**
   * 清理过期数据
   */
  private cleanupExpiredData(): void {
    const currentTime = Date.now();
    const maxAge = this.config.fusionStrategy.timeWindowSize * 2; // 保留2倍时间窗口的数据

    // 清理模态缓冲区中的过期数据
    for (const [modalityType, buffer] of this.modalityBuffers) {
      buffer.data = buffer.data.filter(
        data => currentTime - data.timestamp <= maxAge
      );
    }

    if (this.config.debug) {
      console.log('清理过期数据完成');
    }
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    this.modalityBuffers.clear();
    this.fusionHistory = [];
    this.weightHistory.clear();
    this.eventEmitter.removeAllListeners();
    this.initialized = false;
  }
}
