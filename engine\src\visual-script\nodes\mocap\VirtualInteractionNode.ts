/**
 * 虚拟交互节点
 * 将检测到的手势和动作映射到虚拟环境中的交互
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3 } from 'three';

/**
 * 手势类型枚举
 */
export enum GestureType {
  GRAB = 'grab',
  OPEN_HAND = 'open_hand',
  POINTING = 'pointing',
  THUMBS_UP = 'thumbs_up',
  PEACE_SIGN = 'peace',
  FIST = 'fist'
}

/**
 * 交互动作枚举
 */
export enum InteractionAction {
  GRAB_OBJECT = 'grab_object',
  RELEASE_OBJECT = 'release_object',
  POINT_AT_OBJECT = 'point_at_object',
  GESTURE_COMMAND = 'gesture_command',
  WAVE_HAND = 'wave_hand'
}

/**
 * 手势识别结果
 */
export interface GestureResult {
  type: GestureType;
  confidence: number;
  hand: 'left' | 'right';
  position: Vector3;
  timestamp: number;
}

/**
 * 简化的实体接口
 */
export interface Entity {
  id: string;
  getComponent(name: string): any;
}

/**
 * 虚拟交互节点配置
 */
export interface VirtualInteractionNodeConfig {
  /** 是否启用物体交互 */
  enableObjectInteraction: boolean;
  /** 是否启用手势命令 */
  enableGestureCommands: boolean;
  /** 交互距离阈值 */
  interactionDistance: number;
  /** 抓取阈值 */
  grabThreshold: number;
  /** 释放阈值 */
  releaseThreshold: number;
  /** 手势置信度阈值 */
  gestureConfidenceThreshold: number;
  /** 是否启用调试模式 */
  debug: boolean;
}

/**
 * 交互事件数据
 */
export interface InteractionEventData {
  action: InteractionAction;
  targetEntity?: Entity;
  position: Vector3;
  gesture?: GestureResult;
  confidence: number;
  timestamp: number;
}

/**
 * 虚拟交互节点
 */
export class VirtualInteractionNode extends VisualScriptNode {
  /** 节点类型 */
  public static readonly TYPE = 'VirtualInteraction';

  /** 节点名称 */
  public static readonly NAME = '虚拟交互';

  /** 节点描述 */
  public static readonly DESCRIPTION = '将手势和动作映射到虚拟环境交互';

  private config: VirtualInteractionNodeConfig;
  private lastGestures: { left?: GestureResult; right?: GestureResult } = {};
  private grabbedObjects: Map<string, Entity> = new Map(); // hand -> grabbed object
  private interactionHistory: InteractionEventData[] = [];

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: VirtualInteractionNodeConfig = {
    enableObjectInteraction: true,
    enableGestureCommands: true,
    interactionDistance: 2.0,
    grabThreshold: 0.7,
    releaseThreshold: 0.3,
    gestureConfidenceThreshold: 0.6,
    debug: false
  };

  constructor(nodeType: string = VirtualInteractionNode.TYPE, name: string = VirtualInteractionNode.NAME, id?: string) {
    super(nodeType, name, id);

    this.config = { ...VirtualInteractionNode.DEFAULT_CONFIG };
    this.setupPorts();
  }

  /**
   * 设置输入输出端口
   */
  private setupPorts(): void {
    // 输入端口
    this.addInput('leftGesture', 'object', '左手手势');
    this.addInput('rightGesture', 'object', '右手手势');
    this.addInput('leftHandPosition', 'object', '左手位置');
    this.addInput('rightHandPosition', 'object', '右手位置');
    this.addInput('avatarEntity', 'object', '化身实体');
    this.addInput('targetObjects', 'array', '目标物体');
    this.addInput('process', 'trigger', '处理');

    // 配置输入端口
    this.addInput('interactionDistance', 'number', '交互距离');
    this.addInput('grabThreshold', 'number', '抓取阈值');
    this.addInput('enableObjectInteraction', 'boolean', '启用物体交互');

    // 输出端口
    this.addOutput('grabbedObject', 'object', '被抓取物体');
    this.addOutput('interactionAction', 'string', '交互动作');
    this.addOutput('interactionTarget', 'object', '交互目标');
    this.addOutput('interactionPosition', 'object', '交互位置');
    this.addOutput('isGrabbing', 'boolean', '正在抓取');
    this.addOutput('leftHandGrabbing', 'boolean', '左手抓取');
    this.addOutput('rightHandGrabbing', 'boolean', '右手抓取');

    // 事件输出端口
    this.addOutput('onGrab', 'trigger', '抓取事件');
    this.addOutput('onRelease', 'trigger', '释放事件');
    this.addOutput('onGestureCommand', 'trigger', '手势命令');
    this.addOutput('onInteraction', 'trigger', '交互事件');

    // 手势输出端口
    this.addOutput('gestureType', 'string', '手势类型');
    this.addOutput('gestureConfidence', 'number', '手势置信度');
    this.addOutput('gestureHand', 'string', '手势手部');
  }

  /**
   * 执行节点
   */
  public execute(inputs?: any): any {
    try {
      // 获取输入
      const leftGesture = inputs?.leftGesture as GestureResult;
      const rightGesture = inputs?.rightGesture as GestureResult;
      const leftHandPosition = inputs?.leftHandPosition as Vector3;
      const rightHandPosition = inputs?.rightHandPosition as Vector3;
      const avatarEntity = inputs?.avatarEntity as Entity;
      const targetObjects = inputs?.targetObjects as Entity[];
      const processTrigger = inputs?.process;

      // 更新配置
      const interactionDistance = inputs?.interactionDistance as number;
      const grabThreshold = inputs?.grabThreshold as number;
      const enableObjectInteraction = inputs?.enableObjectInteraction as boolean;

      if (interactionDistance !== undefined) {
        this.config.interactionDistance = interactionDistance;
      }
      if (grabThreshold !== undefined) {
        this.config.grabThreshold = grabThreshold;
      }
      if (enableObjectInteraction !== undefined) {
        this.config.enableObjectInteraction = enableObjectInteraction;
      }

      // 处理交互
      if (processTrigger && avatarEntity) {
        this.processInteractions(
          leftGesture,
          rightGesture,
          leftHandPosition,
          rightHandPosition,
          avatarEntity,
          targetObjects || []
        );
      }

      // 返回输出
      return this.getNodeOutputs();

    } catch (error) {
      Debug.error('VirtualInteractionNode', '节点执行失败', String(error));
      return { onError: true };
    }
  }

  /**
   * 获取节点输出值
   */
  private getNodeOutputs(): any {
    const leftGrabbing = this.grabbedObjects.has('left');
    const rightGrabbing = this.grabbedObjects.has('right');
    const isGrabbing = leftGrabbing || rightGrabbing;
    const grabbedObject = this.grabbedObjects.get('right') || this.grabbedObjects.get('left');

    let lastInteraction = null;
    if (this.interactionHistory.length > 0) {
      lastInteraction = this.interactionHistory[this.interactionHistory.length - 1];
    }

    return {
      grabbedObject: grabbedObject || null,
      interactionAction: lastInteraction?.action || '',
      interactionTarget: lastInteraction?.targetEntity || null,
      interactionPosition: lastInteraction?.position || new Vector3(),
      isGrabbing,
      leftHandGrabbing: leftGrabbing,
      rightHandGrabbing: rightGrabbing,
      onGrab: false,
      onRelease: false,
      onGestureCommand: false,
      onInteraction: false,
      gestureType: '',
      gestureConfidence: 0,
      gestureHand: ''
    };
  }

  /**
   * 处理交互
   */
  private processInteractions(
    leftGesture: GestureResult,
    rightGesture: GestureResult,
    leftHandPosition: Vector3,
    rightHandPosition: Vector3,
    avatarEntity: Entity,
    targetObjects: Entity[]
  ): void {
    // 处理左手交互
    if (leftGesture && leftHandPosition) {
      this.processHandInteraction(
        'left',
        leftGesture,
        leftHandPosition,
        avatarEntity,
        targetObjects
      );
    }

    // 处理右手交互
    if (rightGesture && rightHandPosition) {
      this.processHandInteraction(
        'right',
        rightGesture,
        rightHandPosition,
        avatarEntity,
        targetObjects
      );
    }

    // 检查手势变化
    this.checkGestureChanges(leftGesture, rightGesture);
  }

  /**
   * 处理单手交互
   */
  private processHandInteraction(
    hand: 'left' | 'right',
    gesture: GestureResult,
    handPosition: Vector3,
    avatarEntity: Entity,
    targetObjects: Entity[]
  ): void {
    const lastGesture = this.lastGestures[hand];
    const handKey = hand;

    // 检测手势变化
    if (!lastGesture || lastGesture.type !== gesture.type) {
      this.handleGestureChange(hand, gesture, lastGesture, handPosition, avatarEntity, targetObjects);
    }

    // 处理持续交互
    if (gesture.type === GestureType.GRAB && this.grabbedObjects.has(handKey)) {
      this.handleContinuousGrab(hand, handPosition, avatarEntity);
    }

    // 更新手势状态
    this.lastGestures[hand] = gesture;
  }

  /**
   * 处理手势变化
   */
  private handleGestureChange(
    hand: 'left' | 'right',
    newGesture: GestureResult,
    lastGesture: GestureResult | undefined,
    handPosition: Vector3,
    avatarEntity: Entity,
    targetObjects: Entity[]
  ): void {
    const handKey = hand;

    // 处理抓取手势
    if (newGesture.type === GestureType.GRAB &&
        newGesture.confidence > this.config.grabThreshold &&
        (!lastGesture || lastGesture.type !== GestureType.GRAB)) {

      this.handleGrabGesture(hand, handPosition, avatarEntity, targetObjects);
    }

    // 处理释放手势
    if (newGesture.type === GestureType.OPEN_HAND &&
        lastGesture?.type === GestureType.GRAB &&
        this.grabbedObjects.has(handKey)) {

      this.handleReleaseGesture(hand, handPosition, avatarEntity);
    }

    // 处理手势命令
    if (this.config.enableGestureCommands) {
      this.handleGestureCommand(newGesture, handPosition, avatarEntity);
    }

    // 记录交互事件
    const interactionEvent: InteractionEventData = {
      action: this.gestureToAction(newGesture.type),
      position: handPosition,
      gesture: newGesture,
      confidence: newGesture.confidence,
      timestamp: Date.now()
    };

    this.interactionHistory.push(interactionEvent);
    if (this.interactionHistory.length > 100) {
      this.interactionHistory.shift(); // 保持历史记录在合理范围内
    }

    if (this.config.debug) {
      Debug.log('VirtualInteractionNode', `手势变化: ${hand} ${lastGesture?.type || 'none'} -> ${newGesture.type}`);
    }
  }

  /**
   * 处理抓取手势
   */
  private handleGrabGesture(
    hand: 'left' | 'right',
    handPosition: Vector3,
    avatarEntity: Entity,
    targetObjects: Entity[]
  ): void {
    if (!this.config.enableObjectInteraction) {
      return;
    }

    // 查找附近的可抓取物体
    const nearbyObjects = this.findNearbyObjects(handPosition, targetObjects);

    if (nearbyObjects.length > 0) {
      const targetObject = nearbyObjects[0];
      const handKey = hand;

      // 检查是否已经被抓取
      if (!this.grabbedObjects.has(handKey)) {
        // 模拟抓取成功
        this.grabbedObjects.set(handKey, targetObject);

        if (this.config.debug) {
          Debug.log('VirtualInteractionNode', `${hand}手抓取物体: ${targetObject.id}`);
        }
      }
    }
  }

  /**
   * 处理释放手势
   */
  private handleReleaseGesture(
    hand: 'left' | 'right',
    handPosition: Vector3,
    avatarEntity: Entity
  ): void {
    const handKey = hand;
    const grabbedObject = this.grabbedObjects.get(handKey);

    if (grabbedObject) {
      // 模拟释放
      this.grabbedObjects.delete(handKey);

      if (this.config.debug) {
        Debug.log('VirtualInteractionNode', `${hand}手释放物体: ${grabbedObject.id}`);
      }
    }
  }

  /**
   * 处理持续抓取
   */
  private handleContinuousGrab(
    hand: 'left' | 'right',
    handPosition: Vector3,
    avatarEntity: Entity
  ): void {
    const handKey = hand;
    const grabbedObject = this.grabbedObjects.get(handKey);

    if (grabbedObject) {
      // 模拟更新被抓取物体的位置
      const transform = grabbedObject.getComponent('Transform');
      if (transform) {
        // 将手部位置转换为世界坐标
        const worldPosition = this.handPositionToWorldPosition(avatarEntity, handPosition);
        transform.position.copy(worldPosition);
      }
    }
  }

  /**
   * 处理手势命令
   */
  private handleGestureCommand(
    gesture: GestureResult,
    handPosition: Vector3,
    avatarEntity: Entity
  ): void {
    // 处理特殊手势命令
    switch (gesture.type) {
      case GestureType.THUMBS_UP:
        // 竖拇指手势 - 可以用作确认命令
        if (this.config.debug) {
          Debug.log('VirtualInteractionNode', '检测到竖拇指手势命令');
        }
        break;

      case GestureType.PEACE_SIGN:
        // 胜利手势 - 可以用作特殊命令
        if (this.config.debug) {
          Debug.log('VirtualInteractionNode', '检测到胜利手势命令');
        }
        break;

      case GestureType.POINTING:
        // 指向手势 - 可以用作选择命令
        if (this.config.debug) {
          Debug.log('VirtualInteractionNode', '检测到指向手势命令');
        }
        break;
    }
  }

  /**
   * 检查手势变化
   */
  private checkGestureChanges(leftGesture: GestureResult, rightGesture: GestureResult): void {
    let gestureChanged = false;

    // 检查左手手势变化
    if (leftGesture && (!this.lastGestures.left || this.lastGestures.left.type !== leftGesture.type)) {
      gestureChanged = true;
    }

    // 检查右手手势变化
    if (rightGesture && (!this.lastGestures.right || this.lastGestures.right.type !== rightGesture.type)) {
      gestureChanged = true;
    }

    if (gestureChanged && this.config.debug) {
      const currentGesture = rightGesture || leftGesture;
      if (currentGesture) {
        Debug.log('VirtualInteractionNode', `手势变化: ${currentGesture.hand} ${currentGesture.type}`);
      }
    }
  }

  /**
   * 查找附近的物体
   */
  private findNearbyObjects(position: Vector3, targetObjects: Entity[]): Entity[] {
    const nearbyObjects: Entity[] = [];

    for (const entity of targetObjects) {
      const transform = entity.getComponent('Transform');
      const grabbableComponent = entity.getComponent('GrabbableComponent');

      if (transform && grabbableComponent) {
        const distance = position.distanceTo(transform.position);
        
        if (distance <= this.config.interactionDistance) {
          nearbyObjects.push(entity);
        }
      }
    }

    // 按距离排序
    nearbyObjects.sort((a, b) => {
      const transformA = a.getComponent('Transform');
      const transformB = b.getComponent('Transform');
      
      if (!transformA || !transformB) return 0;
      
      const distanceA = position.distanceTo(transformA.position);
      const distanceB = position.distanceTo(transformB.position);
      
      return distanceA - distanceB;
    });

    return nearbyObjects;
  }

  /**
   * 将手部位置转换为世界坐标
   */
  private handPositionToWorldPosition(avatarEntity: Entity, handPosition: Vector3): Vector3 {
    const entityTransform = avatarEntity.getComponent('Transform');
    if (entityTransform) {
      // 简单的相对位置转换
      return entityTransform.position.clone().add(handPosition);
    }
    
    return handPosition.clone();
  }

  /**
   * 将手势类型转换为交互动作
   */
  private gestureToAction(gestureType: GestureType): InteractionAction {
    switch (gestureType) {
      case GestureType.GRAB:
        return InteractionAction.GRAB_OBJECT;
      case GestureType.OPEN_HAND:
        return InteractionAction.RELEASE_OBJECT;
      case GestureType.POINTING:
        return InteractionAction.POINT_AT_OBJECT;
      case GestureType.THUMBS_UP:
      case GestureType.PEACE_SIGN:
        return InteractionAction.GESTURE_COMMAND;
      default:
        return InteractionAction.WAVE_HAND;
    }
  }



  /**
   * 获取节点配置
   */
  public getConfig(): VirtualInteractionNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新节点配置
   */
  public updateConfig(newConfig: Partial<VirtualInteractionNodeConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }



  /**
   * 获取交互历史
   */
  public getInteractionHistory(): InteractionEventData[] {
    return [...this.interactionHistory];
  }

  /**
   * 清除交互历史
   */
  public clearInteractionHistory(): void {
    this.interactionHistory = [];
  }

  /**
   * 获取当前抓取的物体
   */
  public getGrabbedObjects(): Map<string, Entity> {
    return new Map(this.grabbedObjects);
  }
}
