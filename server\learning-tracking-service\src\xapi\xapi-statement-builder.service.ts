import { Injectable } from '@nestjs/common';
import {
  XAPIStatement,
  Actor,
  Verb,
  Activity,
  Result,
  Context,
  Extensions,
  LEARNING_VERBS,
  DL_ENGINE_EXTENSIONS
} from './interfaces/xapi.interface';

/**
 * xAPI语句构建器服务
 * 提供便捷的方法来构建标准化的xAPI语句
 */
@Injectable()
export class XAPIStatementBuilderService {

  /**
   * 创建基础Actor
   * @param userId 用户ID
   * @param userName 用户名
   * @param email 邮箱
   * @returns Actor对象
   */
  createActor(userId: string, userName?: string, email?: string): Actor {
    const actor: Actor = {
      objectType: 'Agent'
    };

    if (email) {
      actor.mbox = `mailto:${email}`;
    } else {
      actor.account = {
        homePage: 'http://dl-engine.com',
        name: userId
      };
    }

    if (userName) {
      actor.name = userName;
    }

    return actor;
  }

  /**
   * 创建动词
   * @param verbId 动词IRI
   * @param displayName 显示名称
   * @returns Verb对象
   */
  createVerb(verbId: string, displayName?: { [lang: string]: string }): Verb {
    const verb: Verb = {
      id: verbId
    };

    if (displayName) {
      verb.display = displayName;
    } else {
      // 根据常用动词提供默认显示名称
      verb.display = this.getDefaultVerbDisplay(verbId);
    }

    return verb;
  }

  /**
   * 创建活动对象
   * @param activityId 活动IRI
   * @param name 活动名称
   * @param description 活动描述
   * @param type 活动类型
   * @returns Activity对象
   */
  createActivity(
    activityId: string, 
    name?: { [lang: string]: string },
    description?: { [lang: string]: string },
    type?: string
  ): Activity {
    const activity: Activity = {
      objectType: 'Activity',
      id: activityId
    };

    if (name || description || type) {
      activity.definition = {};
      
      if (name) {
        activity.definition.name = name;
      }
      
      if (description) {
        activity.definition.description = description;
      }
      
      if (type) {
        activity.definition.type = type;
      }
    }

    return activity;
  }

  /**
   * 创建数字人活动
   * @param avatarId 数字人ID
   * @param avatarName 数字人名称
   * @returns Activity对象
   */
  createAvatarActivity(avatarId: string, avatarName?: string): Activity {
    return this.createActivity(
      `http://dl-engine.com/avatars/${avatarId}`,
      avatarName ? { 'zh-CN': avatarName, 'en-US': avatarName } : undefined,
      { 'zh-CN': '数字人交互', 'en-US': 'Avatar Interaction' },
      'http://dl-engine.com/activity-types/avatar'
    );
  }

  /**
   * 创建学习路径活动
   * @param pathId 路径ID
   * @param pathName 路径名称
   * @returns Activity对象
   */
  createPathActivity(pathId: string, pathName?: string): Activity {
    return this.createActivity(
      `http://dl-engine.com/paths/${pathId}`,
      pathName ? { 'zh-CN': pathName, 'en-US': pathName } : undefined,
      { 'zh-CN': '学习路径', 'en-US': 'Learning Path' },
      'http://dl-engine.com/activity-types/learning-path'
    );
  }

  /**
   * 创建推荐活动
   * @param recommendationId 推荐ID
   * @param contentTitle 内容标题
   * @returns Activity对象
   */
  createRecommendationActivity(recommendationId: string, contentTitle?: string): Activity {
    return this.createActivity(
      `http://dl-engine.com/recommendations/${recommendationId}`,
      contentTitle ? { 'zh-CN': contentTitle, 'en-US': contentTitle } : undefined,
      { 'zh-CN': '知识推荐', 'en-US': 'Knowledge Recommendation' },
      'http://dl-engine.com/activity-types/recommendation'
    );
  }

  /**
   * 创建场景活动
   * @param sceneId 场景ID
   * @param sceneName 场景名称
   * @returns Activity对象
   */
  createSceneActivity(sceneId: string, sceneName?: string): Activity {
    return this.createActivity(
      `http://dl-engine.com/scenes/${sceneId}`,
      sceneName ? { 'zh-CN': sceneName, 'en-US': sceneName } : undefined,
      { 'zh-CN': '3D场景', 'en-US': '3D Scene' },
      'http://dl-engine.com/activity-types/scene'
    );
  }

  /**
   * 创建情感活动
   * @param emotion 情感类型
   * @returns Activity对象
   */
  createEmotionActivity(emotion: string): Activity {
    return this.createActivity(
      `http://dl-engine.com/emotions/${emotion}`,
      { 'zh-CN': emotion, 'en-US': emotion },
      { 'zh-CN': '情感表达', 'en-US': 'Emotion Expression' },
      'http://dl-engine.com/activity-types/emotion'
    );
  }

  /**
   * 创建结果对象
   * @param options 结果选项
   * @returns Result对象
   */
  createResult(options: {
    score?: { scaled?: number; raw?: number; min?: number; max?: number };
    success?: boolean;
    completion?: boolean;
    response?: string;
    duration?: number; // 毫秒
    extensions?: Extensions;
  }): Result {
    const result: Result = {};

    if (options.score) {
      result.score = options.score;
    }

    if (options.success !== undefined) {
      result.success = options.success;
    }

    if (options.completion !== undefined) {
      result.completion = options.completion;
    }

    if (options.response) {
      result.response = options.response;
    }

    if (options.duration) {
      result.duration = this.formatDuration(options.duration);
    }

    if (options.extensions) {
      result.extensions = options.extensions;
    }

    return result;
  }

  /**
   * 创建上下文对象
   * @param options 上下文选项
   * @returns Context对象
   */
  createContext(options: {
    sessionId?: string;
    sceneId?: string;
    avatarId?: string;
    knowledgeArea?: string;
    platform?: string;
    language?: string;
    extensions?: Extensions;
  } = {}): Context {
    const context: Context = {
      platform: options.platform || 'DL-Engine',
      language: options.language || 'zh-CN'
    };

    const extensions: Extensions = {};

    if (options.sessionId) {
      extensions[DL_ENGINE_EXTENSIONS.SESSION_ID] = options.sessionId;
    }

    if (options.sceneId) {
      extensions[DL_ENGINE_EXTENSIONS.SCENE_ID] = options.sceneId;
    }

    if (options.avatarId) {
      extensions[DL_ENGINE_EXTENSIONS.AVATAR_ID] = options.avatarId;
    }

    if (options.knowledgeArea) {
      extensions[DL_ENGINE_EXTENSIONS.KNOWLEDGE_AREA] = options.knowledgeArea;
    }

    if (options.extensions) {
      Object.assign(extensions, options.extensions);
    }

    if (Object.keys(extensions).length > 0) {
      context.extensions = extensions;
    }

    return context;
  }

  /**
   * 构建数字人对话语句
   * @param userId 用户ID
   * @param avatarId 数字人ID
   * @param question 问题
   * @param answer 回答
   * @param options 其他选项
   * @returns XAPIStatement
   */
  buildAvatarInteractionStatement(
    userId: string,
    avatarId: string,
    question: string,
    answer: string,
    options: {
      userName?: string;
      avatarName?: string;
      emotion?: string;
      satisfaction?: number; // 1-5
      duration?: number; // 毫秒
      knowledgeArea?: string;
      sessionId?: string;
      sceneId?: string;
    } = {}
  ): XAPIStatement {
    const extensions: Extensions = {
      [DL_ENGINE_EXTENSIONS.QUESTION]: question
    };

    if (options.emotion) {
      extensions[DL_ENGINE_EXTENSIONS.EMOTION] = options.emotion;
    }

    return {
      actor: this.createActor(userId, options.userName),
      verb: this.createVerb(LEARNING_VERBS.TALKED_WITH_AVATAR),
      object: this.createAvatarActivity(avatarId, options.avatarName),
      result: this.createResult({
        response: answer,
        score: options.satisfaction ? { scaled: options.satisfaction / 5 } : undefined,
        success: options.satisfaction ? options.satisfaction >= 3 : undefined,
        duration: options.duration,
        extensions
      }),
      context: this.createContext({
        sessionId: options.sessionId,
        sceneId: options.sceneId,
        avatarId,
        knowledgeArea: options.knowledgeArea
      }),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 构建路径跟踪语句
   * @param userId 用户ID
   * @param pathId 路径ID
   * @param options 选项
   * @returns XAPIStatement
   */
  buildPathFollowingStatement(
    userId: string,
    pathId: string,
    options: {
      userName?: string;
      pathName?: string;
      startTime?: Date;
      endTime?: Date;
      completionRate?: number; // 0-1
      stoppingPoints?: any[];
      difficulty?: string;
      sessionId?: string;
      sceneId?: string;
    } = {}
  ): XAPIStatement {
    const extensions: Extensions = {};

    if (options.stoppingPoints) {
      extensions[DL_ENGINE_EXTENSIONS.STOPPING_POINTS] = options.stoppingPoints;
    }

    if (options.difficulty) {
      extensions[DL_ENGINE_EXTENSIONS.DIFFICULTY] = options.difficulty;
    }

    const duration = options.startTime && options.endTime 
      ? options.endTime.getTime() - options.startTime.getTime()
      : undefined;

    return {
      actor: this.createActor(userId, options.userName),
      verb: this.createVerb(LEARNING_VERBS.FOLLOWED_PATH),
      object: this.createPathActivity(pathId, options.pathName),
      result: this.createResult({
        completion: options.completionRate ? options.completionRate >= 1.0 : undefined,
        score: options.completionRate ? { scaled: options.completionRate } : undefined,
        duration,
        extensions
      }),
      context: this.createContext({
        sessionId: options.sessionId,
        sceneId: options.sceneId
      }),
      timestamp: (options.endTime || new Date()).toISOString()
    };
  }

  /**
   * 格式化持续时间为ISO 8601格式
   * @param milliseconds 毫秒
   * @returns ISO 8601持续时间字符串
   */
  private formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    const remainingMinutes = minutes % 60;
    const remainingSeconds = seconds % 60;

    let duration = 'PT';
    
    if (hours > 0) {
      duration += `${hours}H`;
    }
    
    if (remainingMinutes > 0) {
      duration += `${remainingMinutes}M`;
    }
    
    if (remainingSeconds > 0 || duration === 'PT') {
      duration += `${remainingSeconds}S`;
    }

    return duration;
  }

  /**
   * 获取默认动词显示名称
   * @param verbId 动词IRI
   * @returns 显示名称映射
   */
  private getDefaultVerbDisplay(verbId: string): { [lang: string]: string } {
    const verbDisplayMap: { [key: string]: { [lang: string]: string } } = {
      [LEARNING_VERBS.TALKED_WITH_AVATAR]: { 'zh-CN': '与数字人对话', 'en-US': 'talked with avatar' },
      [LEARNING_VERBS.ASKED_AVATAR]: { 'zh-CN': '询问数字人', 'en-US': 'asked avatar' },
      [LEARNING_VERBS.FOLLOWED_PATH]: { 'zh-CN': '跟随路径', 'en-US': 'followed path' },
      [LEARNING_VERBS.EXPLORED_SCENE]: { 'zh-CN': '探索场景', 'en-US': 'explored scene' },
      [LEARNING_VERBS.RECEIVED_RECOMMENDATION]: { 'zh-CN': '接收推荐', 'en-US': 'received recommendation' },
      [LEARNING_VERBS.EXPRESSED_EMOTION]: { 'zh-CN': '表达情感', 'en-US': 'expressed emotion' },
      [LEARNING_VERBS.COMPLETED]: { 'zh-CN': '完成', 'en-US': 'completed' },
      [LEARNING_VERBS.ATTEMPTED]: { 'zh-CN': '尝试', 'en-US': 'attempted' },
      [LEARNING_VERBS.EXPERIENCED]: { 'zh-CN': '体验', 'en-US': 'experienced' }
    };

    return verbDisplayMap[verbId] || { 'zh-CN': '学习', 'en-US': 'learned' };
  }
}
