{"dashboard": {"id": null, "title": "DL引擎RAG应用系统监控", "tags": ["dl-engine", "rag", "monitoring"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "系统概览", "type": "stat", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "targets": [{"expr": "up{job=~\".*-service\"}", "legendFormat": "{{job}} 状态"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}}, {"id": 2, "title": "请求速率", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "{{job}} - {{method}} {{status}}"}], "yAxes": [{"label": "请求/秒", "min": 0}, {"show": false}]}, {"id": 3, "title": "响应时间", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "{{job}} - 95th percentile"}, {"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "{{job}} - 50th percentile"}], "yAxes": [{"label": "秒", "min": 0}, {"show": false}]}, {"id": 4, "title": "知识库指标", "type": "graph", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 16}, "targets": [{"expr": "knowledge_base_total", "legendFormat": "知识库总数"}, {"expr": "knowledge_base_documents_total", "legendFormat": "文档总数"}, {"expr": "knowledge_base_vectors_total", "legendFormat": "向量总数"}]}, {"id": 5, "title": "RAG对话指标", "type": "graph", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 16}, "targets": [{"expr": "rate(rag_conversations_total[5m])", "legendFormat": "对话速率"}, {"expr": "rag_active_sessions", "legendFormat": "活跃会话"}, {"expr": "rate(rag_messages_total[5m])", "legendFormat": "消息速率"}]}, {"id": 6, "title": "数字人指标", "type": "graph", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 16}, "targets": [{"expr": "avatar_total", "legendFormat": "数字人总数"}, {"expr": "avatar_active", "legendFormat": "活跃数字人"}, {"expr": "avatar_in_conversation", "legendFormat": "对话中数字人"}]}, {"id": 7, "title": "语音服务指标", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "rate(voice_synthesis_requests_total[5m])", "legendFormat": "语音合成请求"}, {"expr": "rate(voice_recognition_requests_total[5m])", "legendFormat": "语音识别请求"}, {"expr": "voice_synthesis_duration_seconds", "legendFormat": "合成耗时"}]}, {"id": 8, "title": "错误率", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m])", "legendFormat": "{{job}} - 5xx错误率"}, {"expr": "rate(http_requests_total{status=~\"4..\"}[5m]) / rate(http_requests_total[5m])", "legendFormat": "{{job}} - 4xx错误率"}], "yAxes": [{"label": "错误率", "min": 0, "max": 1}, {"show": false}]}, {"id": 9, "title": "资源使用情况", "type": "graph", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 32}, "targets": [{"expr": "process_resident_memory_bytes / 1024 / 1024", "legendFormat": "{{job}} - 内存使用(MB)"}]}, {"id": 10, "title": "CPU使用率", "type": "graph", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 32}, "targets": [{"expr": "rate(process_cpu_seconds_total[5m]) * 100", "legendFormat": "{{job}} - CPU使用率(%)"}]}, {"id": 11, "title": "数据库连接", "type": "graph", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 32}, "targets": [{"expr": "mysql_global_status_threads_connected", "legendFormat": "MySQL连接数"}, {"expr": "redis_connected_clients", "legendFormat": "Redis连接数"}]}, {"id": 12, "title": "业务指标", "type": "table", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}, "targets": [{"expr": "rag_applications_total", "legendFormat": "RAG应用总数", "format": "table"}, {"expr": "rag_applications_active", "legendFormat": "活跃RAG应用", "format": "table"}, {"expr": "sum(rag_sessions_total)", "legendFormat": "总会话数", "format": "table"}, {"expr": "sum(rag_messages_total)", "legendFormat": "总消息数", "format": "table"}]}], "templating": {"list": [{"name": "service", "type": "query", "query": "label_values(up, job)", "refresh": 1, "includeAll": true, "multi": true}]}, "annotations": {"list": [{"name": "部署事件", "datasource": "Prometheus", "expr": "changes(up[1m]) > 0", "titleFormat": "服务重启", "textFormat": "{{job}} 服务重启"}]}}}