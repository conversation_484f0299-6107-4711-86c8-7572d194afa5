/**
 * 区块链服务主入口
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // 全局拦截器
  app.useGlobalInterceptors(new LoggingInterceptor());

  // 全局异常过滤器
  app.useGlobalFilters(new HttpExceptionFilter());

  // CORS配置
  app.enableCors({
    origin: configService.get('CORS_ORIGIN', '*'),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
  });

  // API前缀
  app.setGlobalPrefix('api/v1');

  // Swagger文档配置
  const config = new DocumentBuilder()
    .setTitle('DL引擎区块链服务API')
    .setDescription('提供NFT管理、智能合约交互、数字资产管理等区块链相关功能')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('blockchain', '区块链基础功能')
    .addTag('nft', 'NFT管理')
    .addTag('contracts', '智能合约')
    .addTag('assets', '数字资产')
    .addTag('marketplace', '数字资产市场')
    .addTag('transactions', '交易管理')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  // 健康检查端点
  app.getHttpAdapter().get('/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'blockchain-service',
      version: '1.0.0',
    });
  });

  const port = configService.get('PORT', 3006);
  await app.listen(port);

  console.log(`🚀 区块链服务已启动，端口: ${port}`);
  console.log(`📚 API文档地址: http://localhost:${port}/api/docs`);
  console.log(`❤️ 健康检查: http://localhost:${port}/health`);
}

bootstrap().catch((error) => {
  console.error('启动区块链服务失败:', error);
  process.exit(1);
});
