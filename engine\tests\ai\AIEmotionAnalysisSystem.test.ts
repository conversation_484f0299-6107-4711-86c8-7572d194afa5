/**
 * AI情感分析系统测试
 */
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { 
  AIEmotionAnalysisSystem, 
  EmotionType, 
  AnalysisMethod,
  EmotionAnalysisOptions 
} from '../../src/ai/AIEmotionAnalysisSystem';

describe('AIEmotionAnalysisSystem', () => {
  let emotionSystem: AIEmotionAnalysisSystem;

  beforeEach(() => {
    emotionSystem = new AIEmotionAnalysisSystem({
      debug: false,
      analysisMethod: AnalysisMethod.HYBRID,
      enableHistory: true,
      maxHistoryLength: 10
    });
  });

  afterEach(() => {
    emotionSystem.dispose();
  });

  describe('基础功能测试', () => {
    it('应该正确初始化系统', () => {
      expect(emotionSystem).toBeDefined();
      expect(emotionSystem.getSupportedEmotions()).toContain(EmotionType.HAPPY);
      expect(emotionSystem.getSupportedEmotions()).toContain(EmotionType.SAD);
    });

    it('应该正确分析快乐情感', async () => {
      const result = await emotionSystem.analyzeEmotion('我今天非常开心！');
      
      expect(result).toBeDefined();
      expect(result!.primaryEmotion).toBe(EmotionType.HAPPY);
      expect(result!.primaryIntensity).toBeGreaterThan(0);
      expect(result!.confidence).toBeGreaterThan(0);
    });

    it('应该正确分析悲伤情感', async () => {
      const result = await emotionSystem.analyzeEmotion('我感到很难过和失望');
      
      expect(result).toBeDefined();
      expect(result!.primaryEmotion).toBe(EmotionType.SAD);
      expect(result!.primaryIntensity).toBeGreaterThan(0);
    });

    it('应该正确分析愤怒情感', async () => {
      const result = await emotionSystem.analyzeEmotion('这让我非常愤怒！');
      
      expect(result).toBeDefined();
      expect(result!.primaryEmotion).toBe(EmotionType.ANGRY);
      expect(result!.primaryIntensity).toBeGreaterThan(0);
    });

    it('应该正确处理中性文本', async () => {
      const result = await emotionSystem.analyzeEmotion('今天是星期一');
      
      expect(result).toBeDefined();
      expect(result!.primaryEmotion).toBe(EmotionType.NEUTRAL);
    });
  });

  describe('表情符号分析测试', () => {
    it('应该正确识别快乐表情符号', async () => {
      const result = await emotionSystem.analyzeEmotion('今天不错 😊');
      
      expect(result).toBeDefined();
      expect(result!.primaryEmotion).toBe(EmotionType.HAPPY);
    });

    it('应该正确识别悲伤表情符号', async () => {
      const result = await emotionSystem.analyzeEmotion('感觉不好 😢');
      
      expect(result).toBeDefined();
      expect(result!.primaryEmotion).toBe(EmotionType.SAD);
    });

    it('应该正确识别爱心表情符号', async () => {
      const result = await emotionSystem.analyzeEmotion('我爱你 ❤️');
      
      expect(result).toBeDefined();
      expect(result!.primaryEmotion).toBe(EmotionType.LOVE);
    });
  });

  describe('程度副词测试', () => {
    it('应该正确处理强化副词', async () => {
      const normalResult = await emotionSystem.analyzeEmotion('我开心');
      const intensifiedResult = await emotionSystem.analyzeEmotion('我非常开心');
      
      expect(normalResult).toBeDefined();
      expect(intensifiedResult).toBeDefined();
      expect(intensifiedResult!.primaryIntensity).toBeGreaterThan(normalResult!.primaryIntensity);
    });

    it('应该正确处理减弱副词', async () => {
      const normalResult = await emotionSystem.analyzeEmotion('我开心');
      const weakenedResult = await emotionSystem.analyzeEmotion('我有点开心');
      
      expect(normalResult).toBeDefined();
      expect(weakenedResult).toBeDefined();
      expect(weakenedResult!.primaryIntensity).toBeLessThan(normalResult!.primaryIntensity);
    });
  });

  describe('否定词测试', () => {
    it('应该正确处理否定词', async () => {
      const positiveResult = await emotionSystem.analyzeEmotion('我开心');
      const negativeResult = await emotionSystem.analyzeEmotion('我不开心');
      
      expect(positiveResult).toBeDefined();
      expect(negativeResult).toBeDefined();
      expect(positiveResult!.primaryEmotion).toBe(EmotionType.HAPPY);
      expect(negativeResult!.primaryEmotion).not.toBe(EmotionType.HAPPY);
    });
  });

  describe('详细分析测试', () => {
    it('应该返回详细分析信息', async () => {
      const options: EmotionAnalysisOptions = {
        detailed: true,
        includeSecondary: true
      };
      
      const result = await emotionSystem.analyzeEmotion('我很开心但也有点担心', options);
      
      expect(result).toBeDefined();
      expect(result!.detailedEmotions).toBeDefined();
      expect(result!.secondaryEmotion).toBeDefined();
      expect(result!.secondaryIntensity).toBeDefined();
    });
  });

  describe('批量分析测试', () => {
    it('应该正确处理批量分析', async () => {
      const texts = [
        '我很开心',
        '我很难过',
        '我很愤怒'
      ];
      
      const results = await emotionSystem.batchAnalyzeEmotions(texts);
      
      expect(results).toHaveLength(3);
      expect(results[0].primaryEmotion).toBe(EmotionType.HAPPY);
      expect(results[1].primaryEmotion).toBe(EmotionType.SAD);
      expect(results[2].primaryEmotion).toBe(EmotionType.ANGRY);
    });
  });

  describe('历史记录测试', () => {
    it('应该正确记录分析历史', async () => {
      await emotionSystem.analyzeEmotion('我很开心');
      await emotionSystem.analyzeEmotion('我很难过');
      
      const history = emotionSystem.getEmotionHistory();
      expect(history).toHaveLength(2);
      expect(history[0].result.primaryEmotion).toBe(EmotionType.HAPPY);
      expect(history[1].result.primaryEmotion).toBe(EmotionType.SAD);
    });

    it('应该正确限制历史记录长度', async () => {
      // 创建一个历史记录长度限制为2的系统
      const limitedSystem = new AIEmotionAnalysisSystem({
        enableHistory: true,
        maxHistoryLength: 2
      });

      await limitedSystem.analyzeEmotion('文本1');
      await limitedSystem.analyzeEmotion('文本2');
      await limitedSystem.analyzeEmotion('文本3');
      
      const history = limitedSystem.getEmotionHistory();
      expect(history).toHaveLength(2);
      
      limitedSystem.dispose();
    });

    it('应该正确清除历史记录', async () => {
      await emotionSystem.analyzeEmotion('我很开心');
      expect(emotionSystem.getEmotionHistory()).toHaveLength(1);
      
      emotionSystem.clearEmotionHistory();
      expect(emotionSystem.getEmotionHistory()).toHaveLength(0);
    });
  });

  describe('统计功能测试', () => {
    it('应该正确生成情感统计', async () => {
      await emotionSystem.analyzeEmotion('我很开心');
      await emotionSystem.analyzeEmotion('我很开心');
      await emotionSystem.analyzeEmotion('我很难过');
      
      const stats = emotionSystem.getEmotionStatistics();
      
      expect(stats.totalAnalyses).toBe(3);
      expect(stats.emotionCounts[EmotionType.HAPPY]).toBe(2);
      expect(stats.emotionCounts[EmotionType.SAD]).toBe(1);
      expect(stats.averageIntensities[EmotionType.HAPPY]).toBeGreaterThan(0);
    });
  });

  describe('配置测试', () => {
    it('应该正确获取和更新配置', () => {
      const originalConfig = emotionSystem.getConfig();
      expect(originalConfig.analysisMethod).toBe(AnalysisMethod.HYBRID);
      
      emotionSystem.updateConfig({
        analysisMethod: AnalysisMethod.KEYWORD_MATCHING
      });
      
      const updatedConfig = emotionSystem.getConfig();
      expect(updatedConfig.analysisMethod).toBe(AnalysisMethod.KEYWORD_MATCHING);
    });
  });

  describe('事件系统测试', () => {
    it('应该正确触发情感分析事件', async () => {
      let eventTriggered = false;
      let eventData: any = null;
      
      emotionSystem.addEventListener('emotionAnalyzed', (data) => {
        eventTriggered = true;
        eventData = data;
      });
      
      await emotionSystem.analyzeEmotion('我很开心');
      
      expect(eventTriggered).toBe(true);
      expect(eventData).toBeDefined();
      expect(eventData.result.primaryEmotion).toBe(EmotionType.HAPPY);
    });
  });

  describe('错误处理测试', () => {
    it('应该正确处理空文本', async () => {
      const result = await emotionSystem.analyzeEmotion('');
      
      expect(result).toBeDefined();
      expect(result!.primaryEmotion).toBe(EmotionType.NEUTRAL);
    });

    it('应该正确处理特殊字符', async () => {
      const result = await emotionSystem.analyzeEmotion('!@#$%^&*()');
      
      expect(result).toBeDefined();
      expect(result!.primaryEmotion).toBe(EmotionType.NEUTRAL);
    });
  });
});
