/**
 * 增强的内存管理器
 * 专为支持100+并发用户优化的内存管理系统
 */
import * as THREE from 'three';
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';

// 内存池类型
enum PoolType {
  VECTOR3 = 'vector3',
  MATRIX4 = 'matrix4',
  QUATERNION = 'quaternion',
  COLOR = 'color',
  BUFFER_GEOMETRY = 'bufferGeometry',
  MATERIAL = 'material',
  TEXTURE = 'texture'
}

// 内存统计信息
interface MemoryStats {
  totalAllocated: number;
  totalReleased: number;
  currentUsage: number;
  poolStats: Map<PoolType, {
    allocated: number;
    available: number;
    totalCreated: number;
  }>;
  gcStats: {
    lastGCTime: number;
    gcCount: number;
    totalGCTime: number;
  };
}

// 对象池接口
interface ObjectPool<T> {
  acquire(): T;
  release(obj: T): void;
  clear(): void;
  size(): number;
  available(): number;
}

/**
 * 通用对象池实现
 */
class GenericObjectPool<T> implements ObjectPool<T> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn?: (obj: T) => void;
  private maxSize: number;
  private totalCreated = 0;

  constructor(
    createFn: () => T,
    resetFn?: (obj: T) => void,
    maxSize: number = 1000
  ) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    this.maxSize = maxSize;
  }

  acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }

    this.totalCreated++;
    return this.createFn();
  }

  release(obj: T): void {
    if (this.pool.length < this.maxSize) {
      if (this.resetFn) {
        this.resetFn(obj);
      }
      this.pool.push(obj);
    }
  }

  clear(): void {
    this.pool.length = 0;
  }

  size(): number {
    return this.totalCreated;
  }

  available(): number {
    return this.pool.length;
  }

  getTotalCreated(): number {
    return this.totalCreated;
  }
}

/**
 * 增强的内存管理器类
 */
export class EnhancedMemoryManager extends EventEmitter {
  private static instance: EnhancedMemoryManager;
  
  // 对象池
  private pools: Map<PoolType, ObjectPool<any>> = new Map();
  
  // 内存统计
  private stats: MemoryStats = {
    totalAllocated: 0,
    totalReleased: 0,
    currentUsage: 0,
    poolStats: new Map(),
    gcStats: {
      lastGCTime: 0,
      gcCount: 0,
      totalGCTime: 0
    }
  };
  
  // 配置
  private config = {
    enableObjectPools: true,
    enableAutoGC: true,
    gcInterval: 30000, // 30秒
    memoryThreshold: 0.8, // 80%内存阈值
    maxPoolSize: 1000,
    enableMemoryMonitoring: true,
    monitoringInterval: 5000 // 5秒
  };
  
  // 监控定时器
  private gcTimer?: NodeJS.Timeout;
  private monitoringTimer?: NodeJS.Timeout;
  
  private constructor() {
    super();
    this.initialize();
  }
  
  /**
   * 获取单例实例
   */
  public static getInstance(): EnhancedMemoryManager {
    if (!EnhancedMemoryManager.instance) {
      EnhancedMemoryManager.instance = new EnhancedMemoryManager();
    }
    return EnhancedMemoryManager.instance;
  }
  
  /**
   * 初始化内存管理器
   */
  private initialize(): void {
    if (this.config.enableObjectPools) {
      this.initializeObjectPools();
    }
    
    if (this.config.enableAutoGC) {
      this.startAutoGC();
    }
    
    if (this.config.enableMemoryMonitoring) {
      this.startMemoryMonitoring();
    }
    
    Debug.log('EnhancedMemoryManager', '增强内存管理器已初始化');
  }
  
  /**
   * 初始化对象池
   */
  private initializeObjectPools(): void {
    // Vector3池
    this.pools.set(PoolType.VECTOR3, new GenericObjectPool(
      () => new THREE.Vector3(),
      (v) => v.set(0, 0, 0),
      this.config.maxPoolSize
    ));
    
    // Matrix4池
    this.pools.set(PoolType.MATRIX4, new GenericObjectPool(
      () => new THREE.Matrix4(),
      (m) => m.identity(),
      this.config.maxPoolSize
    ));
    
    // Quaternion池
    this.pools.set(PoolType.QUATERNION, new GenericObjectPool(
      () => new THREE.Quaternion(),
      (q) => q.set(0, 0, 0, 1),
      this.config.maxPoolSize
    ));
    
    // Color池
    this.pools.set(PoolType.COLOR, new GenericObjectPool(
      () => new THREE.Color(),
      (c) => c.setRGB(1, 1, 1),
      this.config.maxPoolSize
    ));
    
    Debug.log('EnhancedMemoryManager', '对象池已初始化');
  }
  
  /**
   * 从池中获取对象
   */
  public acquire<T>(type: PoolType): T {
    const pool = this.pools.get(type);
    if (!pool) {
      throw new Error(`未找到类型为 ${type} 的对象池`);
    }
    
    const obj = pool.acquire();
    this.stats.totalAllocated++;
    this.updatePoolStats(type);
    
    return obj;
  }
  
  /**
   * 将对象返回到池中
   */
  public release(type: PoolType, obj: any): void {
    const pool = this.pools.get(type);
    if (!pool) {
      Debug.warn('EnhancedMemoryManager', `未找到类型为 ${type} 的对象池`);
      return;
    }
    
    pool.release(obj);
    this.stats.totalReleased++;
    this.updatePoolStats(type);
  }
  
  /**
   * 更新池统计信息
   */
  private updatePoolStats(type: PoolType): void {
    const pool = this.pools.get(type);
    if (!pool) return;
    
    this.stats.poolStats.set(type, {
      allocated: (pool as GenericObjectPool<any>).getTotalCreated(),
      available: pool.available(),
      totalCreated: (pool as GenericObjectPool<any>).getTotalCreated()
    });
  }
  
  /**
   * 启动自动垃圾回收
   */
  private startAutoGC(): void {
    this.gcTimer = setInterval(() => {
      this.performGarbageCollection();
    }, this.config.gcInterval);
  }
  
  /**
   * 执行垃圾回收
   */
  public performGarbageCollection(): void {
    const startTime = performance.now();
    
    try {
      // 清理未使用的对象池
      this.cleanupObjectPools();
      
      // 强制垃圾回收（如果支持）
      if (typeof (window as any).gc === 'function') {
        (window as any).gc();
      }
      
      const gcTime = performance.now() - startTime;
      this.stats.gcStats.lastGCTime = Date.now();
      this.stats.gcStats.gcCount++;
      this.stats.gcStats.totalGCTime += gcTime;
      
      this.emit('gc-completed', {
        duration: gcTime,
        memoryBefore: this.getMemoryUsage(),
        memoryAfter: this.getMemoryUsage()
      });
      
      Debug.log('EnhancedMemoryManager', `垃圾回收完成，耗时: ${gcTime.toFixed(2)}ms`);
      
    } catch (error) {
      Debug.error('EnhancedMemoryManager', '垃圾回收失败:', error);
    }
  }
  
  /**
   * 清理对象池
   */
  private cleanupObjectPools(): void {
    for (const [type, pool] of this.pools) {
      // 如果池中可用对象过多，清理一部分
      const available = pool.available();
      const maxRetain = Math.floor(this.config.maxPoolSize * 0.5);
      
      if (available > maxRetain) {
        // 清理多余的对象
        const toRemove = available - maxRetain;
        for (let i = 0; i < toRemove; i++) {
          pool.acquire(); // 获取并丢弃
        }
        
        Debug.log('EnhancedMemoryManager', `清理了 ${type} 池中的 ${toRemove} 个对象`);
      }
    }
  }
  
  /**
   * 启动内存监控
   */
  private startMemoryMonitoring(): void {
    this.monitoringTimer = setInterval(() => {
      this.updateMemoryStats();
      this.checkMemoryThreshold();
    }, this.config.monitoringInterval);
  }
  
  /**
   * 更新内存统计
   */
  private updateMemoryStats(): void {
    this.stats.currentUsage = this.getMemoryUsage();
    
    // 更新所有池的统计信息
    for (const type of this.pools.keys()) {
      this.updatePoolStats(type);
    }
    
    this.emit('memory-stats-updated', this.stats);
  }
  
  /**
   * 检查内存阈值
   */
  private checkMemoryThreshold(): void {
    const memoryUsage = this.getMemoryUsagePercentage();
    
    if (memoryUsage > this.config.memoryThreshold) {
      Debug.warn('EnhancedMemoryManager', `内存使用率过高: ${(memoryUsage * 100).toFixed(1)}%`);
      
      this.emit('memory-threshold-exceeded', {
        usage: memoryUsage,
        threshold: this.config.memoryThreshold
      });
      
      // 触发紧急垃圾回收
      this.performGarbageCollection();
    }
  }
  
  /**
   * 获取内存使用量（字节）
   */
  public getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    
    // 如果不支持，返回估算值
    return this.stats.totalAllocated - this.stats.totalReleased;
  }
  
  /**
   * 获取内存使用百分比
   */
  public getMemoryUsagePercentage(): number {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return memory.usedJSHeapSize / memory.jsHeapSizeLimit;
    }
    
    // 如果不支持，返回估算值
    return 0.5; // 假设50%
  }
  
  /**
   * 获取内存统计信息
   */
  public getStats(): MemoryStats {
    this.updateMemoryStats();
    return { ...this.stats };
  }
  
  /**
   * 清理所有资源
   */
  public cleanup(): void {
    // 清理所有对象池
    for (const pool of this.pools.values()) {
      pool.clear();
    }
    
    // 清除定时器
    if (this.gcTimer) {
      clearInterval(this.gcTimer);
      this.gcTimer = undefined;
    }
    
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.monitoringTimer = undefined;
    }
    
    // 执行最后一次垃圾回收
    this.performGarbageCollection();
    
    Debug.log('EnhancedMemoryManager', '内存管理器已清理');
  }
  
  /**
   * 获取配置
   */
  public getConfig() {
    return { ...this.config };
  }
  
  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<typeof this.config>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 重新启动相关服务
    if (newConfig.gcInterval && this.gcTimer) {
      clearInterval(this.gcTimer);
      this.startAutoGC();
    }
    
    if (newConfig.monitoringInterval && this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.startMemoryMonitoring();
    }
  }
}

// 导出便捷函数
export const memoryManager = EnhancedMemoryManager.getInstance();

// 便捷的对象获取和释放函数
export function acquireVector3(): THREE.Vector3 {
  return memoryManager.acquire<THREE.Vector3>(PoolType.VECTOR3);
}

export function releaseVector3(vector: THREE.Vector3): void {
  memoryManager.release(PoolType.VECTOR3, vector);
}

export function acquireMatrix4(): THREE.Matrix4 {
  return memoryManager.acquire<THREE.Matrix4>(PoolType.MATRIX4);
}

export function releaseMatrix4(matrix: THREE.Matrix4): void {
  memoryManager.release(PoolType.MATRIX4, matrix);
}

export function acquireQuaternion(): THREE.Quaternion {
  return memoryManager.acquire<THREE.Quaternion>(PoolType.QUATERNION);
}

export function releaseQuaternion(quaternion: THREE.Quaternion): void {
  memoryManager.release(PoolType.QUATERNION, quaternion);
}

export function acquireColor(): THREE.Color {
  return memoryManager.acquire<THREE.Color>(PoolType.COLOR);
}

export function releaseColor(color: THREE.Color): void {
  memoryManager.release(PoolType.COLOR, color);
}
