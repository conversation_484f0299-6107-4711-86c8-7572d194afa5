/**
 * 推荐系统相关接口定义
 */

// 推荐类型
export type RecommendationType = 'concept' | 'exercise' | 'video' | 'article' | 'interactive' | 'path' | 'resource';

// 推荐难度
export type RecommendationDifficulty = 'easy' | 'medium' | 'hard';

// 推荐状态
export type RecommendationStatus = 'pending' | 'accepted' | 'rejected' | 'ignored' | 'completed';

// 推荐反馈动作
export type FeedbackAction = 'accepted' | 'rejected' | 'ignored' | 'completed' | 'bookmarked';

// 推荐内容接口
export interface RecommendationContent {
  id: string;
  title: string;
  description: string;
  type: RecommendationType;
  difficulty: RecommendationDifficulty;
  duration: number;                          // 预计学习时长(分钟)
  tags: string[];
  knowledgeArea: string;
  prerequisites: string[];                   // 前置知识
  learningObjectives: string[];              // 学习目标
  contentUrl?: string;                       // 内容链接
  thumbnailUrl?: string;                     // 缩略图
  authorId?: string;                         // 作者ID
  createdAt: Date;
  updatedAt: Date;
  metadata: {
    views: number;                           // 浏览次数
    completions: number;                     // 完成次数
    averageRating: number;                   // 平均评分
    ratingCount: number;                     // 评分人数
    successRate: number;                     // 成功率
  };
}

// 推荐记录接口
export interface Recommendation {
  id: string;
  userId: string;
  contentId: string;
  content?: RecommendationContent;           // 关联的内容
  type: RecommendationType;
  title: string;
  description: string;
  relevanceScore: number;                    // 相关性分数 0-1
  difficulty: RecommendationDifficulty;
  estimatedDuration: number;                 // 预计学习时长(分钟)
  reason: string;                            // 推荐理由
  tags: string[];
  knowledgeArea: string;
  prerequisites: string[];
  learningObjectives: string[];
  status: RecommendationStatus;
  feedbackRating?: number;                   // 用户反馈评分 1-5
  feedbackComment?: string;                  // 用户反馈评论
  createdAt: Date;
  expiresAt?: Date;                          // 过期时间
  viewedAt?: Date;                           // 查看时间
  acceptedAt?: Date;                         // 接受时间
  completedAt?: Date;                        // 完成时间
  metadata: {
    algorithm: string;                       // 推荐算法
    version: string;                         // 算法版本
    factors: string[];                       // 推荐因子
    confidence: number;                      // 推荐置信度
    context: any;                            // 推荐上下文
  };
}

// 推荐请求参数
export interface RecommendationRequest {
  userId: string;
  limit?: number;                            // 推荐数量限制
  types?: RecommendationType[];              // 推荐类型过滤
  knowledgeAreas?: string[];                 // 知识领域过滤
  difficulty?: RecommendationDifficulty;     // 难度过滤
  maxDuration?: number;                      // 最大时长过滤
  excludeCompleted?: boolean;                // 排除已完成内容
  includeReasons?: boolean;                  // 包含推荐理由
  context?: {
    currentActivity?: string;                // 当前活动
    sessionId?: string;                      // 会话ID
    timeOfDay?: string;                      // 时间段
    deviceType?: string;                     // 设备类型
  };
}

// 推荐反馈
export interface RecommendationFeedback {
  recommendationId: string;
  userId: string;
  action: FeedbackAction;
  rating?: number;                           // 评分 1-5
  comment?: string;                          // 评论
  timeToDecision?: number;                   // 决策时间(毫秒)
  context?: {
    sessionId?: string;
    deviceType?: string;
    location?: string;
  };
  timestamp: Date;
}

// 推荐算法配置
export interface RecommendationAlgorithmConfig {
  name: string;
  version: string;
  enabled: boolean;
  weight: number;                            // 算法权重
  parameters: {
    [key: string]: any;
  };
  filters: {
    minRelevanceScore?: number;
    maxResults?: number;
    diversityFactor?: number;
  };
}

// 推荐策略
export interface RecommendationStrategy {
  id: string;
  name: string;
  description: string;
  algorithms: RecommendationAlgorithmConfig[];
  rules: RecommendationRule[];
  enabled: boolean;
  priority: number;
  applicableUserTypes: string[];             // 适用用户类型
  applicableContexts: string[];              // 适用上下文
}

// 推荐规则
export interface RecommendationRule {
  id: string;
  name: string;
  condition: string;                         // 条件表达式
  action: string;                            // 动作
  priority: number;
  enabled: boolean;
}

// 推荐结果
export interface RecommendationResult {
  recommendations: Recommendation[];
  totalCount: number;
  strategy: string;                          // 使用的策略
  executionTime: number;                     // 执行时间(毫秒)
  metadata: {
    algorithms: string[];                    // 使用的算法
    filters: string[];                       // 应用的过滤器
    diversityScore: number;                  // 多样性分数
    noveltyScore: number;                    // 新颖性分数
  };
}

// 推荐统计
export interface RecommendationStats {
  totalRecommendations: number;
  acceptanceRate: number;                    // 接受率
  completionRate: number;                    // 完成率
  averageRating: number;                     // 平均评分
  clickThroughRate: number;                  // 点击率
  conversionRate: number;                    // 转化率
  byType: {
    [type: string]: {
      count: number;
      acceptanceRate: number;
      completionRate: number;
    };
  };
  byKnowledgeArea: {
    [area: string]: {
      count: number;
      acceptanceRate: number;
      completionRate: number;
    };
  };
  timeRange: {
    start: Date;
    end: Date;
  };
}

// 内容相似度
export interface ContentSimilarity {
  contentId1: string;
  contentId2: string;
  similarity: number;                        // 相似度分数 0-1
  factors: {
    [factor: string]: number;                // 各因子的相似度
  };
  computedAt: Date;
}

// 用户偏好
export interface UserPreference {
  userId: string;
  contentTypes: {
    [type: string]: number;                  // 类型偏好权重
  };
  knowledgeAreas: {
    [area: string]: number;                  // 领域偏好权重
  };
  difficulties: {
    [difficulty: string]: number;           // 难度偏好权重
  };
  authors: {
    [authorId: string]: number;             // 作者偏好权重
  };
  tags: {
    [tag: string]: number;                  // 标签偏好权重
  };
  timePreferences: {
    duration: {
      min: number;
      max: number;
      preferred: number;
    };
    timeOfDay: string[];                    // 偏好时间段
  };
  updatedAt: Date;
}

// 推荐解释
export interface RecommendationExplanation {
  recommendationId: string;
  mainReason: string;                       // 主要原因
  factors: {
    factor: string;
    weight: number;
    explanation: string;
  }[];
  userProfile: {
    relevantTraits: string[];
    matchingPreferences: string[];
  };
  contentFeatures: {
    relevantFeatures: string[];
    matchingCriteria: string[];
  };
  confidence: number;                       // 解释置信度
}

// A/B测试配置
export interface ABTestConfig {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  startDate: Date;
  endDate: Date;
  trafficAllocation: number;                // 流量分配比例
  variants: {
    id: string;
    name: string;
    strategy: string;
    allocation: number;                     // 变体分配比例
  }[];
  metrics: string[];                        // 监控指标
  successCriteria: {
    metric: string;
    threshold: number;
    comparison: 'greater' | 'less' | 'equal';
  }[];
}

// 推荐性能指标
export interface RecommendationMetrics {
  precision: number;                        // 精确率
  recall: number;                           // 召回率
  f1Score: number;                          // F1分数
  diversity: number;                        // 多样性
  novelty: number;                          // 新颖性
  coverage: number;                         // 覆盖率
  serendipity: number;                      // 意外发现度
  userSatisfaction: number;                 // 用户满意度
  businessImpact: {
    engagementIncrease: number;             // 参与度提升
    learningOutcomeImprovement: number;     // 学习效果改善
    retentionImprovement: number;           // 留存率改善
  };
  computedAt: Date;
}
