﻿/**
 * 网络系统
 * 负责管理网络连接、数据同步和多用户支持
 */
import * as THREE from 'three';
import { System } from '../core/System';
import { Engine } from '../core/Engine';
import type { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { EventEmitter } from '../utils/EventEmitter';
import { NetworkManager } from './NetworkManager';
import { NetworkConnection } from './NetworkConnection';
import { NetworkEvent, NetworkEventType } from './NetworkEvent';
import { NetworkEntityComponent  } from './components/NetworkEntityComponent';
import { NetworkTransformComponent  } from './components/NetworkTransformComponent';
import { NetworkUserComponent } from './components/NetworkUserComponent';
import { Debug } from '../utils/Debug';
import { WebRTCConnectionManager } from './WebRTCConnectionManager';
import { MediaStreamManager, MediaStreamType } from './MediaStreamManager';
import { EntitySyncManager } from './EntitySyncManager';
import { UserSessionManager, UserRole, UserPermission } from './UserSessionManager';
import { NetworkEventDispatcher } from './NetworkEventDispatcher';
import { NetworkEventBuffer, EventPriority } from './NetworkEventBuffer';
import { BandwidthController, BandwidthControlStrategy } from './BandwidthController';
import { NetworkQualityMonitor } from './NetworkQualityMonitor';
import { DataCompressor, CompressionAlgorithm, CompressionLevel } from './DataCompressor';
import { ServiceDiscoveryClient, ServiceInstance } from './ServiceDiscoveryClient';
import { MicroserviceClient } from './MicroserviceClient';

/**
 * 网络系统配置选项
 */
export interface NetworkSystemOptions {
  /** 是否自动连接到服务器 */
  autoConnect?: boolean;
  /** 服务器URL */
  serverUrl?: string;
  /** 房间ID */
  roomId?: string;
  /** 用户ID */
  userId?: string;
  /** 用户名 */
  username?: string;
  /** 是否启用WebRTC */
  enableWebRTC?: boolean;
  /** ICE服务器配置 */
  iceServers?: RTCIceServer[];
  /** 最大重连次数 */
  maxReconnectAttempts?: number;
  /** 重连间隔（毫秒） */
  reconnectInterval?: number;
  /** 数据同步间隔（毫秒） */
  syncInterval?: number;
  /** 是否启用数据压缩 */
  enableCompression?: boolean;
  /** 是否启用媒体流 */
  enableMediaStream?: boolean;
  /** 是否启用音频 */
  enableAudio?: boolean;
  /** 是否启用视频 */
  enableVideo?: boolean;
  /** 是否启用屏幕共享 */
  enableScreenShare?: boolean;
  /** 是否启用网络质量监控 */
  enableNetworkQualityMonitor?: boolean;
  /** 是否启用带宽控制 */
  enableBandwidthControl?: boolean;
  /** 带宽控制策略 */
  bandwidthControlStrategy?: BandwidthControlStrategy;
  /** 最大上行带宽（字节/秒） */
  maxUploadBandwidth?: number;
  /** 最大下行带宽（字节/秒） */
  maxDownloadBandwidth?: number;
  /** 压缩算法 */
  compressionAlgorithm?: CompressionAlgorithm;
  /** 压缩级别 */
  compressionLevel?: CompressionLevel;
  /** 是否启用实体同步 */
  enableEntitySync?: boolean;
  /** 是否启用用户会话管理 */
  enableUserSessionManagement?: boolean;
  /** 默认用户角色 */
  defaultUserRole?: UserRole;
  /** 是否启用权限检查 */
  enablePermissionCheck?: boolean;
  /** 是否启用事件缓冲 */
  enableEventBuffer?: boolean;
  /** 是否启用事件日志 */
  enableEventLogging?: boolean;
  /** 是否启用服务发现 */
  enableServiceDiscovery?: boolean;
  /** 服务注册中心URL */
  serviceRegistryUrl?: string;
  /** 是否启用微服务通信 */
  enableMicroserviceClient?: boolean;
  /** API网关URL */
  apiGatewayUrl?: string;
  /** 是否使用API网关 */
  useApiGateway?: boolean;
}

/**
 * 网络系统状态
 */
export enum NetworkState {
  /** 已断开连接 */
  DISCONNECTED = 'disconnected',
  /** 正在连接 */
  CONNECTING = 'connecting',
  /** 已连接 */
  CONNECTED = 'connected',
  /** 正在断开连接 */
  DISCONNECTING = 'disconnecting',
  /** 连接错误 */
  ERROR = 'error',
}

/**
 * 网络系统
 * 负责管理网络连接、数据同步和多用户支持
 */
export class NetworkSystem extends System {
  /** 网络管理器 */
  private networkManager: NetworkManager;

  /** 网络连接状态 */
  private connectionState: NetworkState = NetworkState.DISCONNECTED;

  /** 配置选项 */
  private networkOptions: NetworkSystemOptions;

  /** 重连尝试次数 */
  private reconnectAttempts: number = 0;

  /** 重连定时器ID */
  private reconnectTimerId: number | null = null;

  /** 同步定时器ID */
  private syncTimerId: number | null = null;

  /** 网络实体映射表 */
  private networkEntities: Map<string, Entity> = new Map();

  /** 本地用户ID */
  private localUserId: string | null = null;

  /** 远程用户映射表 */
  private remoteUsers: Map<string, Entity> = new Map();

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** WebRTC连接管理器 */
  private webRTCConnectionManager: WebRTCConnectionManager | null = null;

  /** 媒体流管理器 */
  private mediaStreamManager: MediaStreamManager | null = null;

  /** 实体同步管理器 */
  private entitySyncManager: EntitySyncManager | null = null;

  /** 用户会话管理器 */
  private userSessionManager: UserSessionManager | null = null;

  /** 网络事件分发器 */
  private networkEventDispatcher: NetworkEventDispatcher | null = null;

  /** 网络事件缓冲器 */
  private networkEventBuffer: NetworkEventBuffer | null = null;

  /** 带宽控制器 */
  private bandwidthController: BandwidthController | null = null;

  /** 网络质量监控器 */
  private networkQualityMonitor: NetworkQualityMonitor | null = null;

  /** 数据压缩器 */
  private dataCompressor: DataCompressor | null = null;

  /** 服务发现客户端 */
  private serviceDiscoveryClient: ServiceDiscoveryClient | null = null;

  /** 微服务客户端 */
  private microserviceClient: MicroserviceClient | null = null;



  /**
   * 创建网络系统
   * @param options 配置选项
   */
  constructor(options: NetworkSystemOptions = {}) {
    super(0, {
      priority: 0,
      enabled: true,
      enablePerformanceMonitoring: false,
      enableErrorHandling: true
    });

    this.networkOptions = {
      autoConnect: false,
      serverUrl: 'wss://localhost:8080',
      enableWebRTC: true,
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
      ],
      maxReconnectAttempts: 5,
      reconnectInterval: 3000,
      syncInterval: 100,
      enableCompression: true,
      enableMediaStream: true,
      enableAudio: false,
      enableVideo: false,
      enableScreenShare: false,
      enableNetworkQualityMonitor: true,
      enableBandwidthControl: true,
      bandwidthControlStrategy: BandwidthControlStrategy.ADAPTIVE,
      maxUploadBandwidth: 1024 * 1024, // 1MB/s
      maxDownloadBandwidth: 1024 * 1024, // 1MB/s
      compressionAlgorithm: CompressionAlgorithm.LZ_STRING,
      compressionLevel: CompressionLevel.MEDIUM,
      enableEntitySync: true,
      enableUserSessionManagement: true,
      defaultUserRole: UserRole.USER,
      enablePermissionCheck: true,
      enableEventBuffer: true,
      enableEventLogging: false,
      enableServiceDiscovery: true,
      serviceRegistryUrl: 'http://localhost:4010/api/registry',
      enableMicroserviceClient: true,
      apiGatewayUrl: 'http://localhost:3000/api',
      useApiGateway: true,
      ...options,
    };

    this.initializeComponents();
  }

  /**
   * 初始化组件
   */
  private initializeComponents(): void {
    // 创建数据压缩器
    if (this.networkOptions.enableCompression) {
      this.dataCompressor = new DataCompressor({
        algorithm: this.networkOptions.compressionAlgorithm,
        level: this.networkOptions.compressionLevel,
        adaptive: true,
      });
    }

    // 创建网络质量监控器
    if (this.networkOptions.enableNetworkQualityMonitor) {
      this.networkQualityMonitor = new NetworkQualityMonitor();
    }

    // 创建带宽控制器
    if (this.networkOptions.enableBandwidthControl) {
      this.bandwidthController = new BandwidthController({
        maxUploadBandwidth: this.networkOptions.maxUploadBandwidth,
        maxDownloadBandwidth: this.networkOptions.maxDownloadBandwidth,
        strategy: this.networkOptions.bandwidthControlStrategy,
      });
    }

    // 创建服务发现客户端
    if (this.networkOptions.enableServiceDiscovery) {
      this.serviceDiscoveryClient = new ServiceDiscoveryClient({
        registryUrl: this.networkOptions.serviceRegistryUrl,
      });
    }

    // 创建微服务客户端
    if (this.networkOptions.enableMicroserviceClient) {
      this.microserviceClient = new MicroserviceClient({
        serviceDiscoveryClient: this.serviceDiscoveryClient,
        apiGatewayUrl: this.networkOptions.apiGatewayUrl,
        useApiGateway: this.networkOptions.useApiGateway,
        useServiceDiscovery: this.networkOptions.enableServiceDiscovery,
      });
    }

    // 创建网络事件缓冲器
    if (this.networkOptions.enableEventBuffer) {
      this.networkEventBuffer = new NetworkEventBuffer({
        maxBufferSize: 1000,
        processInterval: 50,
        autoProcess: true,
        maxEventsPerProcess: 10,
      });
    }

    // 创建网络事件分发器
    this.networkEventDispatcher = new NetworkEventDispatcher({
      useEventBuffer: this.networkOptions.enableEventBuffer,
      enableEventLogging: this.networkOptions.enableEventLogging,
      eventBufferConfig: {
        maxBufferSize: 1000,
        processInterval: 50,
        autoProcess: true,
        maxEventsPerProcess: 10,
      },
    });

    // 创建网络管理器
    this.networkManager = new NetworkManager(this.networkOptions);

    // 设置网络事件监听器
    this.setupEventListeners();

    // 如果设置了自动连接，则连接到服务器
    if (this.networkOptions.autoConnect && this.networkOptions.serverUrl) {
      this.connect(this.networkOptions.serverUrl, this.networkOptions.roomId);
    }
  }

  /**
   * 设置引擎实例
   * @param engine 引擎实例
   */
  public setEngine(engine: Engine): void {
    super.setEngine(engine);
    this.world = engine.getWorld();
  }

  /**
   * 设置世界实例
   * @param world 世界实例
   */
  public setWorld(world: any): void {
    super.setWorld(world);
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    Debug.log('NetworkSystem', 'Initializing network system');

    // 网络组件会在使用时自动注册，无需手动注册

    // 初始化本地用户ID
    if (this.networkOptions.userId) {
      this.localUserId = this.networkOptions.userId;
    } else {
      // 生成随机用户ID
      this.localUserId = `user_${Math.random().toString(36).substring(2, 9)}`;
    }

    // 初始化服务发现客户端
    if (this.serviceDiscoveryClient) {
      this.serviceDiscoveryClient.initialize();
    }

    // 初始化微服务客户端
    if (this.microserviceClient) {
      this.microserviceClient.initialize();
    }

    // 初始化用户会话管理器
    if (this.networkOptions.enableUserSessionManagement) {
      this.userSessionManager = new UserSessionManager({
        defaultRole: this.networkOptions.defaultUserRole,
        enablePermissionCheck: this.networkOptions.enablePermissionCheck,
      });

      // 创建本地用户会话
      if (this.localUserId) {
        this.userSessionManager.createSession(
          this.localUserId,
          this.networkOptions.username || `User_${this.localUserId.substring(5)}`,
          this.networkOptions.defaultUserRole,
          true
        );
      }
    }

    // 初始化WebRTC连接管理器
    if (this.networkOptions.enableWebRTC) {
      this.webRTCConnectionManager = new WebRTCConnectionManager({
        iceServers: this.networkOptions.iceServers,
        enableDataChannel: true,
        enableAudio: this.networkOptions.enableAudio,
        enableVideo: this.networkOptions.enableVideo,
        enableScreenShare: this.networkOptions.enableScreenShare,
        useCompression: this.networkOptions.enableCompression,
        maxReconnectAttempts: this.networkOptions.maxReconnectAttempts,
        reconnectInterval: this.networkOptions.reconnectInterval,
        useNetworkQualityMonitor: this.networkOptions.enableNetworkQualityMonitor,
        useBandwidthController: this.networkOptions.enableBandwidthControl,
      });

      // 初始化WebRTC连接管理器
      this.webRTCConnectionManager.initialize(this.localUserId);
    }

    // 初始化媒体流管理器
    if (this.networkOptions.enableMediaStream) {
      this.mediaStreamManager = new MediaStreamManager({
        enableDeviceEnumeration: true,
        enableDeviceChangeDetection: true,
        enableAudioProcessing: true,
        enableVideoProcessing: true,
        enableAutoPlay: true,
        enableAudioLevelMonitoring: true,
      });
    }

    // 初始化实体同步管理器
    if (this.networkOptions.enableEntitySync) {
      this.entitySyncManager = new EntitySyncManager({
        defaultSyncInterval: this.networkOptions.syncInterval,
        useSpatialPartitioning: true,
        useInterpolation: true,
        useExtrapolation: true,
        useCompression: this.networkOptions.enableCompression,
        useDeltaSync: true,
        usePrioritySync: true,
        useAdaptiveSync: true,
      });

      // 初始化实体同步管理器
      if (this.localUserId && this.bandwidthController) {
        this.entitySyncManager.initialize(this.localUserId, this.bandwidthController);
      }
    }

    // 连接网络质量监控器和带宽控制器
    if (this.networkQualityMonitor && this.bandwidthController) {
      // 监听网络质量更新事件
      this.networkQualityMonitor.on('qualityUpdate', (quality) => {
        // 更新带宽控制器的网络质量数据
        this.bandwidthController?.setNetworkQuality(quality);
      });
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (this.connectionState !== NetworkState.CONNECTED) {
      return;
    }

    // 更新网络管理器
    this.networkManager.update(deltaTime);

    // 更新WebRTC连接管理器
    if (this.webRTCConnectionManager) {
      // WebRTC连接管理器没有update方法，但可以在这里添加自定义逻辑
    }

    // 更新实体同步管理器
    if (this.entitySyncManager) {
      // 实体同步管理器通过定时器自动同步，不需要在这里调用
    }

    // 更新网络质量监控器
    if (this.networkQualityMonitor) {
      // 网络质量监控器通过定时器自动采样，不需要在这里调用
    }

    // 更新带宽控制器
    if (this.bandwidthController) {
      // 带宽控制器通过定时器自动调整，不需要在这里调用
    }
  }

  /**
   * 连接到服务器
   * @param serverUrl 服务器URL
   * @param roomId 房间ID
   */
  public connect(serverUrl: string, roomId?: string): void {
    if (this.connectionState === NetworkState.CONNECTING || this.connectionState === NetworkState.CONNECTED) {
      Debug.warn('NetworkSystem', 'Already connected or connecting to server');
      return;
    }

    this.connectionState = NetworkState.CONNECTING;
    this.eventEmitter.emit('connecting');

    this.networkManager.connect(serverUrl, roomId)
      .then(() => {
        this.connectionState = NetworkState.CONNECTED;
        this.reconnectAttempts = 0;
        this.eventEmitter.emit('connected');

        // 启动同步定时器
        this.startSyncTimer();
      })
      .catch((error) => {
        this.connectionState = NetworkState.ERROR;
        this.eventEmitter.emit('error', error);

        // 尝试重连
        this.attemptReconnect();
      });
  }

  /**
   * 断开连接
   */
  public disconnect(): void {
    if (this.connectionState === NetworkState.DISCONNECTED || this.connectionState === NetworkState.DISCONNECTING) {
      return;
    }

    this.connectionState = NetworkState.DISCONNECTING;
    this.eventEmitter.emit('disconnecting');

    // 停止同步定时器
    this.stopSyncTimer();

    // 停止重连定时器
    this.stopReconnectTimer();

    this.networkManager.disconnect()
      .then(() => {
        this.connectionState = NetworkState.DISCONNECTED;
        this.eventEmitter.emit('disconnected');
      })
      .catch((error) => {
        this.connectionState = NetworkState.ERROR;
        this.eventEmitter.emit('error', error);
      });
  }

  /**
   * 获取网络连接状态
   * @returns 网络连接状态
   */
  public getConnectionState(): NetworkState {
    return this.connectionState;
  }

  /**
   * 获取本地用户ID
   * @returns 本地用户ID
   */
  public getLocalUserId(): string | null {
    return this.localUserId;
  }

  /**
   * 设置本地用户ID
   * @param userId 用户ID
   */
  public setLocalUserId(userId: string): void {
    this.localUserId = userId;
  }

  /**
   * 获取远程用户
   * @param userId 用户ID
   * @returns 用户实体
   */
  public getRemoteUser(userId: string): Entity | undefined {
    return this.remoteUsers.get(userId);
  }

  /**
   * 获取所有远程用户
   * @returns 用户实体映射表
   */
  public getRemoteUsers(): Map<string, Entity> {
    return this.remoteUsers;
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  public on(event: string, listener: (...args: any[]) => void): this {
    this.eventEmitter.on(event, listener);
    return this;
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  public off(event: string, listener: (...args: any[]) => void): this {
    this.eventEmitter.off(event, listener);
    return this;
  }

  /**
   * 设置网络事件监听器
   */
  private setupEventListeners(): void {
    // 监听用户加入事件
    this.networkManager.on('userJoined', (userId: string, username: string) => {
      this.handleUserJoined(userId, username);
    });

    // 监听用户离开事件
    this.networkManager.on('userLeft', (userId: string) => {
      this.handleUserLeft(userId);
    });

    // 监听实体创建事件
    this.networkManager.on('entityCreated', (entityId: string, data: any) => {
      this.handleEntityCreated(entityId, data);
    });

    // 监听实体更新事件
    this.networkManager.on('entityUpdated', (entityId: string, data: any) => {
      this.handleEntityUpdated(entityId, data);
    });

    // 监听实体删除事件
    this.networkManager.on('entityDeleted', (entityId: string) => {
      this.handleEntityDeleted(entityId);
    });
  }

  /**
   * 处理用户加入事件
   * @param userId 用户ID
   * @param username 用户名
   */
  private handleUserJoined(userId: string, username: string): void {
    Debug.log('NetworkSystem', `User joined: ${username} (${userId})`);

    // 创建用户会话
    if (this.userSessionManager) {
      this.userSessionManager.createSession(
        userId,
        username,
        this.networkOptions.defaultUserRole || UserRole.USER,
        false
      );
    }

    this.eventEmitter.emit('userJoined', userId, username);
  }

  /**
   * 处理用户离开事件
   * @param userId 用户ID
   */
  private handleUserLeft(userId: string): void {
    Debug.log('NetworkSystem', `User left: ${userId}`);

    // 移除用户会话
    if (this.userSessionManager) {
      this.userSessionManager.removeSession(userId);
    }

    // 断开WebRTC连接
    if (this.webRTCConnectionManager) {
      this.webRTCConnectionManager.closeConnection(userId);
    }

    this.remoteUsers.delete(userId);
    this.eventEmitter.emit('userLeft', userId);
  }

  /**
   * 处理实体创建事件
   * @param entityId 实体ID
   * @param data 实体数据
   */
  private handleEntityCreated(entityId: string, data: any): void {
    Debug.log('NetworkSystem', `Entity created: ${entityId}`);

    // 如果有实体同步管理器，注册实体
    if (this.entitySyncManager && this.world) {
      // 这里可以根据data创建实际的实体对象
      // const entity = this.world.createEntity(data);
      // this.networkEntities.set(entityId, entity);
    }

    this.eventEmitter.emit('entityCreated', entityId, data);
  }

  /**
   * 处理实体更新事件
   * @param entityId 实体ID
   * @param data 实体数据
   */
  private handleEntityUpdated(entityId: string, data: any): void {
    // 更新本地实体
    const entity = this.networkEntities.get(entityId);
    if (entity && this.entitySyncManager) {
      // 应用更新数据到实体
      // this.entitySyncManager.applyEntityUpdate(entity, data);
    }

    this.eventEmitter.emit('entityUpdated', entityId, data);
  }

  /**
   * 处理实体删除事件
   * @param entityId 实体ID
   */
  private handleEntityDeleted(entityId: string): void {
    Debug.log('NetworkSystem', `Entity deleted: ${entityId}`);

    // 从实体同步管理器中移除
    if (this.entitySyncManager) {
      this.entitySyncManager.removeEntity(entityId);
    }

    // 从世界中移除实体
    const entity = this.networkEntities.get(entityId);
    if (entity && this.world) {
      // this.world.removeEntity(entity);
    }

    this.networkEntities.delete(entityId);
    this.eventEmitter.emit('entityDeleted', entityId);
  }

  /**
   * 启动同步定时器
   */
  private startSyncTimer(): void {
    if (this.syncTimerId) {
      return;
    }

    this.syncTimerId = window.setInterval(() => {
      this.syncNetworkEntities();
    }, this.networkOptions.syncInterval || 100);
  }

  /**
   * 停止同步定时器
   */
  private stopSyncTimer(): void {
    if (this.syncTimerId) {
      window.clearInterval(this.syncTimerId);
      this.syncTimerId = null;
    }
  }

  /**
   * 同步网络实体
   */
  private syncNetworkEntities(): void {
    // 实体同步逻辑由EntitySyncManager处理
    if (this.entitySyncManager) {
      // EntitySyncManager会自动处理实体同步
    }
  }

  /**
   * 尝试重连
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= (this.networkOptions.maxReconnectAttempts || 5)) {
      Debug.error('NetworkSystem', 'Max reconnect attempts reached');
      return;
    }

    this.reconnectAttempts++;
    Debug.log('NetworkSystem', `Attempting to reconnect (${this.reconnectAttempts}/${this.networkOptions.maxReconnectAttempts})`);

    this.reconnectTimerId = window.setTimeout(() => {
      if (this.networkOptions.serverUrl) {
        this.connect(this.networkOptions.serverUrl, this.networkOptions.roomId);
      }
    }, this.networkOptions.reconnectInterval || 3000);
  }

  /**
   * 停止重连定时器
   */
  private stopReconnectTimer(): void {
    if (this.reconnectTimerId) {
      window.clearTimeout(this.reconnectTimerId);
      this.reconnectTimerId = null;
    }
  }

  /**
   * 发送消息到所有用户
   * @param type 消息类型
   * @param data 消息数据
   */
  public async sendToAll(type: string, data: any): Promise<void> {
    let messageData = data;

    // 如果启用了数据压缩，则压缩数据
    if (this.networkOptions.enableCompression && this.dataCompressor) {
      try {
        const compressedResult = await this.dataCompressor.compress(data);
        messageData = compressedResult.data;
      } catch (error) {
        Debug.warn('NetworkSystem', 'Failed to compress data, sending uncompressed', error);
      }
    }

    this.networkManager.sendToAll(type, messageData);

    // 如果启用了WebRTC，则通过WebRTC发送
    if (this.networkOptions.enableWebRTC && this.webRTCConnectionManager) {
      const message = { type, data: messageData };
      this.webRTCConnectionManager.broadcastMessage(message);
    }
  }

  /**
   * 发送消息到特定用户
   * @param userId 用户ID
   * @param type 消息类型
   * @param data 消息数据
   */
  public async sendToUser(userId: string, type: string, data: any): Promise<void> {
    let messageData = data;

    // 如果启用了数据压缩，则压缩数据
    if (this.networkOptions.enableCompression && this.dataCompressor) {
      try {
        const compressedResult = await this.dataCompressor.compress(data);
        messageData = compressedResult.data;
      } catch (error) {
        Debug.warn('NetworkSystem', 'Failed to compress data, sending uncompressed', error);
      }
    }

    this.networkManager.sendToUser(userId, type, messageData);

    // 如果启用了WebRTC，则通过WebRTC发送
    if (this.networkOptions.enableWebRTC && this.webRTCConnectionManager) {
      const message = { type, data: messageData };
      this.webRTCConnectionManager.sendMessage(userId, message);
    }
  }

  /**
   * 创建WebRTC连接
   * @param userId 远程用户ID
   * @returns WebRTC连接
   */
  public createWebRTCConnection(userId: string): any {
    if (!this.networkOptions.enableWebRTC || !this.webRTCConnectionManager) {
      Debug.warn('NetworkSystem', 'WebRTC is not enabled');
      return null;
    }

    return this.webRTCConnectionManager.createConnection(userId);
  }

  /**
   * 处理WebRTC提议
   * @param userId 远程用户ID
   * @param offer 提议
   */
  public handleWebRTCOffer(userId: string, offer: RTCSessionDescriptionInit): void {
    if (!this.networkOptions.enableWebRTC || !this.webRTCConnectionManager) {
      Debug.warn('NetworkSystem', 'WebRTC is not enabled');
      return;
    }

    this.webRTCConnectionManager.handleOffer(userId, offer);
  }

  /**
   * 处理WebRTC应答
   * @param userId 远程用户ID
   * @param answer 应答
   */
  public handleWebRTCAnswer(userId: string, answer: RTCSessionDescriptionInit): void {
    if (!this.networkOptions.enableWebRTC || !this.webRTCConnectionManager) {
      Debug.warn('NetworkSystem', 'WebRTC is not enabled');
      return;
    }

    this.webRTCConnectionManager.handleAnswer(userId, answer);
  }

  /**
   * 处理WebRTC ICE候选
   * @param userId 远程用户ID
   * @param candidate ICE候选
   */
  public handleWebRTCIceCandidate(userId: string, candidate: RTCIceCandidateInit): void {
    if (!this.networkOptions.enableWebRTC || !this.webRTCConnectionManager) {
      Debug.warn('NetworkSystem', 'WebRTC is not enabled');
      return;
    }

    this.webRTCConnectionManager.handleIceCandidate(userId, candidate);
  }

  /**
   * 获取本地媒体流
   * @param type 媒体流类型
   * @param config 媒体流配置
   * @returns 媒体流信息
   */
  public async getLocalMediaStream(type: MediaStreamType, config: any = {}): Promise<any> {
    if (!this.networkOptions.enableMediaStream || !this.mediaStreamManager) {
      Debug.warn('NetworkSystem', 'Media stream is not enabled');
      return null;
    }

    return this.mediaStreamManager.getLocalStream(type, config);
  }

  /**
   * 停止本地媒体流
   * @param streamId 流ID
   * @returns 是否成功停止
   */
  public stopLocalMediaStream(streamId: string): boolean {
    if (!this.networkOptions.enableMediaStream || !this.mediaStreamManager) {
      Debug.warn('NetworkSystem', 'Media stream is not enabled');
      return false;
    }

    return this.mediaStreamManager.stopLocalStream(streamId);
  }

  /**
   * 添加远程媒体流
   * @param stream 媒体流
   * @param userId 用户ID
   * @param type 媒体流类型
   * @param config 媒体流配置
   * @returns 媒体流信息
   */
  public addRemoteMediaStream(stream: MediaStream, userId: string, type: MediaStreamType, config: any = {}): any {
    if (!this.networkOptions.enableMediaStream || !this.mediaStreamManager) {
      Debug.warn('NetworkSystem', 'Media stream is not enabled');
      return null;
    }

    return this.mediaStreamManager.addRemoteStream(stream, userId, type, config);
  }

  /**
   * 移除远程媒体流
   * @param streamId 流ID
   * @returns 是否成功移除
   */
  public removeRemoteMediaStream(streamId: string): boolean {
    if (!this.networkOptions.enableMediaStream || !this.mediaStreamManager) {
      Debug.warn('NetworkSystem', 'Media stream is not enabled');
      return false;
    }

    return this.mediaStreamManager.removeRemoteStream(streamId);
  }

  /**
   * 注册网络实体
   * @param entityId 实体ID
   * @param entity 实体对象
   */
  public registerNetworkEntity(entityId: string, entity: Entity): void {
    this.networkEntities.set(entityId, entity);

    // 如果启用了实体同步，注册到同步管理器
    if (this.entitySyncManager) {
      this.entitySyncManager.addEntity(entityId, entity);
    }

    Debug.log('NetworkSystem', `Registered network entity: ${entityId}`);
  }

  /**
   * 注销网络实体
   * @param entityId 实体ID
   */
  public unregisterNetworkEntity(entityId: string): void {
    const entity = this.networkEntities.get(entityId);
    if (!entity) {
      return;
    }

    // 从实体同步管理器中移除
    if (this.entitySyncManager) {
      this.entitySyncManager.removeEntity(entityId);
    }

    this.networkEntities.delete(entityId);
    Debug.log('NetworkSystem', `Unregistered network entity: ${entityId}`);
  }

  /**
   * 获取网络实体
   * @param entityId 实体ID
   * @returns 实体对象
   */
  public getNetworkEntity(entityId: string): Entity | undefined {
    return this.networkEntities.get(entityId);
  }

  /**
   * 获取所有网络实体
   * @returns 网络实体映射表
   */
  public getNetworkEntities(): Map<string, Entity> {
    return this.networkEntities;
  }

  /**
   * 获取网络质量数据
   * @returns 网络质量数据
   */
  public getNetworkQuality(): any {
    if (!this.networkQualityMonitor) {
      return null;
    }

    return this.networkQualityMonitor.getCurrentQuality();
  }

  /**
   * 获取带宽使用情况
   * @returns 带宽使用数据
   */
  public getBandwidthUsage(): any {
    if (!this.bandwidthController) {
      return null;
    }

    return this.bandwidthController.getBandwidthUsage();
  }

  /**
   * 设置带宽限制
   * @param uploadLimit 上行带宽限制（字节/秒）
   * @param downloadLimit 下行带宽限制（字节/秒）
   */
  public setBandwidthLimits(uploadLimit: number, downloadLimit: number): void {
    if (!this.bandwidthController) {
      Debug.warn('NetworkSystem', 'Bandwidth controller is not enabled');
      return;
    }

    this.bandwidthController.setMaxUploadBandwidth(uploadLimit);
    this.bandwidthController.setMaxDownloadBandwidth(downloadLimit);
  }

  /**
   * 获取用户会话信息
   * @param userId 用户ID
   * @returns 用户会话信息
   */
  public getUserSession(userId: string): any {
    if (!this.userSessionManager) {
      return null;
    }

    return this.userSessionManager.getSession(userId);
  }

  /**
   * 获取所有用户会话
   * @returns 用户会话列表
   */
  public getAllUserSessions(): any {
    if (!this.userSessionManager) {
      return null;
    }

    return this.userSessionManager.getAllUsers();
  }

  /**
   * 检查用户权限
   * @param userId 用户ID
   * @param permission 权限
   * @returns 是否有权限
   */
  public checkUserPermission(userId: string, permission: UserPermission): boolean {
    if (!this.userSessionManager) {
      return false;
    }

    return this.userSessionManager.hasPermission(userId, permission);
  }

  /**
   * 设置用户角色
   * @param userId 用户ID
   * @param role 用户角色
   */
  public setUserRole(userId: string, role: UserRole): void {
    if (!this.userSessionManager) {
      Debug.warn('NetworkSystem', 'User session management is not enabled');
      return;
    }

    this.userSessionManager.setUserRole(userId, role);
  }

  /**
   * 销毁网络系统
   */
  public destroy(): void {
    Debug.log('NetworkSystem', 'Destroying network system');

    // 断开连接
    this.disconnect();

    // 停止定时器
    this.stopSyncTimer();
    this.stopReconnectTimer();

    // 销毁WebRTC连接管理器
    if (this.webRTCConnectionManager) {
      this.webRTCConnectionManager.closeAllConnections();
      this.webRTCConnectionManager = null;
    }

    // 销毁媒体流管理器
    if (this.mediaStreamManager) {
      // 停止所有本地媒体流
      this.mediaStreamManager.stopAllLocalStreams();
      this.mediaStreamManager = null;
    }

    // 销毁实体同步管理器
    if (this.entitySyncManager) {
      this.entitySyncManager = null;
    }

    // 销毁用户会话管理器
    if (this.userSessionManager) {
      this.userSessionManager.clearSessions();
      this.userSessionManager = null;
    }

    // 销毁网络事件分发器
    if (this.networkEventDispatcher) {
      this.networkEventDispatcher = null;
    }

    // 销毁网络事件缓冲器
    if (this.networkEventBuffer) {
      this.networkEventBuffer = null;
    }

    // 销毁带宽控制器
    if (this.bandwidthController) {
      this.bandwidthController = null;
    }

    // 销毁网络质量监控器
    if (this.networkQualityMonitor) {
      this.networkQualityMonitor = null;
    }

    // 销毁数据压缩器
    if (this.dataCompressor) {
      this.dataCompressor = null;
    }

    // 销毁服务发现客户端
    if (this.serviceDiscoveryClient) {
      this.serviceDiscoveryClient = null;
    }

    // 销毁微服务客户端
    if (this.microserviceClient) {
      this.microserviceClient = null;
    }

    // 清空映射表
    this.networkEntities.clear();
    this.remoteUsers.clear();

    // 移除所有事件监听器
    this.eventEmitter.removeAllListeners();

    // 重置状态
    this.connectionState = NetworkState.DISCONNECTED;
    this.localUserId = null;
    this.reconnectAttempts = 0;

    Debug.log('NetworkSystem', 'Network system destroyed');
  }

  /**
   * 获取系统状态信息
   * @returns 系统状态信息
   */
  public getSystemStatus(): any {
    return {
      connectionState: this.connectionState,
      localUserId: this.localUserId,
      connectedUsers: this.remoteUsers.size,
      networkEntities: this.networkEntities.size,
      reconnectAttempts: this.reconnectAttempts,
      webRTCEnabled: this.networkOptions.enableWebRTC,
      mediaStreamEnabled: this.networkOptions.enableMediaStream,
      compressionEnabled: this.networkOptions.enableCompression,
      entitySyncEnabled: this.networkOptions.enableEntitySync,
      userSessionManagementEnabled: this.networkOptions.enableUserSessionManagement,
      networkQualityMonitorEnabled: this.networkOptions.enableNetworkQualityMonitor,
      bandwidthControlEnabled: this.networkOptions.enableBandwidthControl,
      serviceDiscoveryEnabled: this.networkOptions.enableServiceDiscovery,
      microserviceClientEnabled: this.networkOptions.enableMicroserviceClient,
    };
  }
}
