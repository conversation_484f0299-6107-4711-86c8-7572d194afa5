/**
 * 视觉脚本函数节点
 * 函数节点用于执行纯函数，不影响执行流程
 */
import { Node, NodeCategory, NodeOptions, NodeType, SocketDirection, SocketType } from './Node';
import { AsyncNode } from './AsyncNode';

/**
 * 函数类型枚举
 */
export enum FunctionType {
  /** 纯函数 */
  PURE = 'pure',
  /** 异步函数 */
  ASYNC = 'async',
  /** 高阶函数 */
  HIGHER_ORDER = 'higherOrder',
  /** 组合函数 */
  COMPOSED = 'composed',
  /** 内置函数 */
  BUILTIN = 'builtin',
  /** 用户定义函数 */
  USER_DEFINED = 'userDefined'
}

/**
 * 函数执行模式
 */
export enum FunctionExecutionMode {
  /** 同步执行 */
  SYNC = 'sync',
  /** 异步执行 */
  ASYNC = 'async',
  /** 延迟执行 */
  LAZY = 'lazy',
  /** 并行执行 */
  PARALLEL = 'parallel'
}

/**
 * 函数参数定义
 */
export interface FunctionParameter {
  /** 参数名称 */
  name: string;
  /** 参数类型 */
  type: string;
  /** 是否可选 */
  optional?: boolean;
  /** 默认值 */
  defaultValue?: any;
  /** 参数描述 */
  description?: string;
  /** 验证函数 */
  validator?: (value: any) => boolean;
  /** 最小值（数值类型） */
  min?: number;
  /** 最大值（数值类型） */
  max?: number;
}

/**
 * 函数返回值定义
 */
export interface FunctionReturn {
  /** 返回值名称 */
  name: string;
  /** 返回值类型 */
  type: string;
  /** 返回值描述 */
  description?: string;
}

/**
 * 函数签名定义
 */
export interface FunctionSignature {
  /** 函数名称 */
  name: string;
  /** 函数描述 */
  description?: string;
  /** 输入参数 */
  parameters: FunctionParameter[];
  /** 返回值 */
  returns: FunctionReturn[];
  /** 是否为纯函数 */
  pure?: boolean;
  /** 是否支持缓存 */
  cacheable?: boolean;
  /** 函数类型 */
  type?: FunctionType;
  /** 执行模式 */
  executionMode?: FunctionExecutionMode;
  /** 函数版本 */
  version?: string;
  /** 函数标签 */
  tags?: string[];
  /** 函数分类 */
  category?: string;
  /** 最小引擎版本 */
  minEngineVersion?: string;
  /** 依赖项 */
  dependencies?: string[];
}

/**
 * 函数重载定义
 */
export interface FunctionOverload {
  /** 重载签名 */
  signature: FunctionSignature;
  /** 重载优先级 */
  priority: number;
  /** 匹配条件 */
  matcher?: (inputs: Record<string, any>) => boolean;
}

/**
 * 函数库定义
 */
export interface FunctionLibrary {
  /** 库名称 */
  name: string;
  /** 库版本 */
  version: string;
  /** 库描述 */
  description?: string;
  /** 函数列表 */
  functions: Map<string, FunctionSignature[]>;
  /** 库依赖 */
  dependencies?: string[];
  /** 库作者 */
  author?: string;
  /** 库许可证 */
  license?: string;
}

/**
 * 函数执行上下文
 */
export interface FunctionContext {
  /** 输入值 */
  inputs: Record<string, any>;
  /** 执行时间戳 */
  timestamp: number;
  /** 执行ID */
  executionId: string;
}

/**
 * 函数节点选项
 */
export interface FunctionNodeOptions extends NodeOptions {
  /** 函数名称 */
  functionName?: string;
  /** 函数签名 */
  signature?: FunctionSignature;
  /** 函数重载列表 */
  overloads?: FunctionOverload[];
  /** 是否启用缓存 */
  enableCache?: boolean;
  /** 缓存大小限制 */
  cacheSize?: number;
  /** 是否启用输入验证 */
  enableValidation?: boolean;
  /** 是否启用性能分析 */
  enableProfiling?: boolean;
  /** 是否启用调试 */
  enableDebugging?: boolean;
  /** 执行超时时间（毫秒） */
  timeout?: number;
  /** 最大重试次数 */
  maxRetries?: number;
  /** 函数库引用 */
  libraryRef?: string;
}

/**
 * 函数节点基类
 */
export class FunctionNode extends Node {
  /** 节点类型 */
  public readonly nodeType: NodeType = NodeType.FUNCTION;

  /** 节点类别 */
  public readonly category: NodeCategory = NodeCategory.FUNCTION;

  /** 函数名称 */
  protected functionName: string;

  /** 函数签名 */
  protected signature: FunctionSignature | null = null;

  /** 函数重载列表 */
  protected overloads: FunctionOverload[] = [];

  /** 是否启用缓存 */
  protected enableCache: boolean;

  /** 缓存大小限制 */
  protected cacheSize: number;

  /** 是否启用输入验证 */
  protected enableValidation: boolean;

  /** 是否启用性能分析 */
  protected enableProfiling: boolean;

  /** 是否启用调试 */
  protected enableDebugging: boolean;

  /** 执行超时时间 */
  protected timeout: number;

  /** 最大重试次数 */
  protected maxRetries: number;

  /** 函数库引用 */
  protected libraryRef?: string;

  /** 结果缓存 */
  protected cache: Map<string, any> = new Map();

  /** 缓存键的访问顺序（用于LRU） */
  protected cacheOrder: string[] = [];

  /** 是否已执行 */
  protected executed: boolean = false;

  /** 执行结果 */
  protected result: any = null;

  /** 最后一次输入的哈希值 */
  protected lastInputHash: string = '';

  /** 执行计数器 */
  protected executionCount: number = 0;

  /** 性能分析数据 */
  protected performanceData: {
    totalExecutionTime: number;
    averageExecutionTime: number;
    minExecutionTime: number;
    maxExecutionTime: number;
    cacheHitRate: number;
  } = {
    totalExecutionTime: 0,
    averageExecutionTime: 0,
    minExecutionTime: Infinity,
    maxExecutionTime: 0,
    cacheHitRate: 0
  };

  /** 调试断点 */
  protected breakpoints: Set<string> = new Set();

  /** 当前重试次数 */
  protected currentRetries: number = 0;
  
  /**
   * 创建函数节点
   * @param options 节点选项
   */
  constructor(options: FunctionNodeOptions) {
    super(options);

    this.functionName = options.functionName || '';
    this.signature = options.signature || null;
    this.overloads = options.overloads || [];
    this.enableCache = options.enableCache ?? true;
    this.cacheSize = options.cacheSize || 100;
    this.enableValidation = options.enableValidation ?? true;
    this.enableProfiling = options.enableProfiling ?? false;
    this.enableDebugging = options.enableDebugging ?? false;
    this.timeout = options.timeout || 5000;
    this.maxRetries = options.maxRetries || 0;
    this.libraryRef = options.libraryRef;
  }
  
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 如果有函数签名，根据签名自动创建插槽
    if (this.signature) {
      this.createSocketsFromSignature();
    }
    // 否则子类需要手动添加具体的数据插槽
  }

  /**
   * 根据函数签名创建插槽
   */
  protected createSocketsFromSignature(): void {
    if (!this.signature) return;

    // 创建输入插槽
    for (const param of this.signature.parameters) {
      this.addInput({
        name: param.name,
        type: SocketType.DATA,
        dataType: param.type,
        description: param.description || param.name,
        defaultValue: param.defaultValue,
        optional: param.optional
      });
    }

    // 创建输出插槽
    for (const returnValue of this.signature.returns) {
      this.addOutput({
        name: returnValue.name,
        type: SocketType.DATA,
        dataType: returnValue.type,
        description: returnValue.description || returnValue.name
      });
    }
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const startTime = this.enableProfiling ? performance.now() : 0;

    try {
      // 获取所有输入值
      const inputs: Record<string, any> = {};

      for (const [name] of this.inputs.entries()) {
        inputs[name] = this.getInputValue(name);
      }

      // 选择最佳重载
      const selectedSignature = this.selectBestOverload(inputs);

      // 输入验证
      if (this.enableValidation) {
        const validationResult = this.validateInputs(inputs, selectedSignature);
        if (!validationResult.valid) {
          throw new Error(`输入验证失败: ${validationResult.error}`);
        }
      }

      // 调试断点检查
      if (this.enableDebugging && this.hasSpecificBreakpoint('beforeExecution')) {
        this.handleBreakpoint('beforeExecution', { inputs });
      }

      // 生成输入哈希值用于缓存
      const inputHash = this.generateInputHash(inputs);

      // 检查缓存
      if (this.enableCache && this.cache.has(inputHash)) {
        this.result = this.cache.get(inputHash);
        this.updateCacheOrder(inputHash);
        this.updatePerformanceData(startTime, true);
      } else {
        // 执行函数计算（带重试机制）
        this.result = this.executeWithRetry(inputs, selectedSignature);
        this.executed = true;
        this.executionCount++;

        // 缓存结果
        if (this.enableCache && this.isPure(selectedSignature)) {
          this.cacheResult(inputHash, this.result);
        }

        this.updatePerformanceData(startTime, false);
      }

      // 调试断点检查
      if (this.enableDebugging && this.hasSpecificBreakpoint('afterExecution')) {
        this.handleBreakpoint('afterExecution', { inputs, result: this.result });
      }

      // 设置输出值
      this.setOutputValues(this.result);

      // 记录最后一次输入哈希
      this.lastInputHash = inputHash;

      return this.result;
    } catch (error) {
      this.updatePerformanceData(startTime, false);
      console.error(`函数节点执行失败 [${this.functionName}]:`, error);
      this.emit('executionError', error);
      throw error;
    }
  }
  
  /**
   * 选择最佳重载
   * @param inputs 输入值
   * @returns 选择的函数签名
   */
  protected selectBestOverload(inputs: Record<string, any>): FunctionSignature {
    // 如果没有重载，返回默认签名
    if (this.overloads.length === 0) {
      return this.signature || this.createDefaultSignature();
    }

    // 按优先级排序重载
    const sortedOverloads = [...this.overloads].sort((a, b) => b.priority - a.priority);

    // 查找匹配的重载
    for (const overload of sortedOverloads) {
      if (this.matchesOverload(inputs, overload)) {
        return overload.signature;
      }
    }

    // 如果没有匹配的重载，返回默认签名
    return this.signature || this.createDefaultSignature();
  }

  /**
   * 检查输入是否匹配重载
   * @param inputs 输入值
   * @param overload 重载定义
   * @returns 是否匹配
   */
  protected matchesOverload(inputs: Record<string, any>, overload: FunctionOverload): boolean {
    // 如果有自定义匹配器，使用它
    if (overload.matcher) {
      return overload.matcher(inputs);
    }

    // 默认匹配逻辑：检查参数类型和数量
    const signature = overload.signature;

    // 检查必需参数
    for (const param of signature.parameters) {
      if (!param.optional && !(param.name in inputs)) {
        return false;
      }

      if (param.name in inputs && !this.validateType(inputs[param.name], param.type)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 创建默认签名
   * @returns 默认函数签名
   */
  protected createDefaultSignature(): FunctionSignature {
    return {
      name: this.functionName,
      parameters: [],
      returns: [{ name: 'result', type: 'any' }],
      pure: true,
      cacheable: true
    };
  }

  /**
   * 带重试机制的执行
   * @param inputs 输入值
   * @param signature 函数签名
   * @returns 执行结果
   */
  protected executeWithRetry(inputs: Record<string, any>, signature: FunctionSignature): any {
    this.currentRetries = 0;

    while (this.currentRetries <= this.maxRetries) {
      try {
        return this.compute(inputs, signature);
      } catch (error) {
        this.currentRetries++;

        if (this.currentRetries > this.maxRetries) {
          throw error;
        }

        // 延迟重试
        const delay = Math.pow(2, this.currentRetries - 1) * 100; // 指数退避
        setTimeout(() => {}, delay);
      }
    }

    return null;
  }

  /**
   * 计算函数结果
   * @param inputs 输入值
   * @param signature 函数签名
   * @returns 计算结果
   */
  protected compute(_inputs: Record<string, any>, _signature?: FunctionSignature): any {
    // 子类实现
    return null;
  }

  /**
   * 更新性能数据
   * @param startTime 开始时间
   * @param cacheHit 是否缓存命中
   */
  protected updatePerformanceData(startTime: number, cacheHit: boolean): void {
    if (!this.enableProfiling) return;

    const executionTime = performance.now() - startTime;

    this.performanceData.totalExecutionTime += executionTime;
    this.performanceData.minExecutionTime = Math.min(this.performanceData.minExecutionTime, executionTime);
    this.performanceData.maxExecutionTime = Math.max(this.performanceData.maxExecutionTime, executionTime);
    this.performanceData.averageExecutionTime = this.performanceData.totalExecutionTime / this.executionCount;

    // 更新缓存命中率
    const totalCacheChecks = this.executionCount;
    const cacheHits = cacheHit ? 1 : 0;
    this.performanceData.cacheHitRate = (this.performanceData.cacheHitRate * (totalCacheChecks - 1) + cacheHits) / totalCacheChecks;
  }

  /**
   * 检查是否有指定断点
   * @param breakpointId 断点ID
   * @returns 是否有断点
   */
  protected hasSpecificBreakpoint(breakpointId: string): boolean {
    return this.breakpoints.has(breakpointId);
  }

  /**
   * 处理断点
   * @param breakpointId 断点ID
   * @param context 上下文数据
   */
  protected handleBreakpoint(breakpointId: string, context: any): void {
    console.log(`断点触发 [${this.functionName}:${breakpointId}]:`, context);
    this.emit('breakpointHit', { breakpointId, context });

    // 在实际实现中，这里可能会暂停执行等待调试器
    if (this.enableDebugging) {
      debugger; // 触发浏览器调试器
    }
  }

  /**
   * 验证输入参数
   * @param inputs 输入值
   * @param signature 函数签名（可选）
   * @returns 验证结果
   */
  protected validateInputs(inputs: Record<string, any>, signature?: FunctionSignature): { valid: boolean; error?: string } {
    const targetSignature = signature || this.signature;
    if (!targetSignature) {
      return { valid: true };
    }

    for (const param of targetSignature.parameters) {
      const value = inputs[param.name];

      // 检查必需参数
      if (!param.optional && (value === undefined || value === null)) {
        return { valid: false, error: `缺少必需参数: ${param.name}` };
      }

      // 跳过可选参数的验证（如果值为undefined）
      if (param.optional && value === undefined) {
        continue;
      }

      // 类型检查
      if (!this.validateType(value, param.type)) {
        return { valid: false, error: `参数 ${param.name} 类型错误，期望: ${param.type}` };
      }

      // 数值范围检查
      if (param.type === 'number' && typeof value === 'number') {
        if (param.min !== undefined && value < param.min) {
          return { valid: false, error: `参数 ${param.name} 小于最小值: ${param.min}` };
        }
        if (param.max !== undefined && value > param.max) {
          return { valid: false, error: `参数 ${param.name} 大于最大值: ${param.max}` };
        }
      }

      // 自定义验证器
      if (param.validator && !param.validator(value)) {
        return { valid: false, error: `参数 ${param.name} 验证失败` };
      }
    }

    return { valid: true };
  }

  /**
   * 验证值的类型
   * @param value 值
   * @param expectedType 期望类型
   * @returns 是否匹配
   */
  protected validateType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'object':
        return typeof value === 'object' && value !== null;
      case 'array':
        return Array.isArray(value);
      case 'function':
        return typeof value === 'function';
      case 'any':
        return true;
      default:
        // 对于自定义类型，简单检查是否为对象
        return typeof value === 'object' && value !== null;
    }
  }

  /**
   * 生成输入哈希值
   * @param inputs 输入值
   * @returns 哈希值
   */
  protected generateInputHash(inputs: Record<string, any>): string {
    // 简单的哈希实现，实际项目中可能需要更复杂的哈希算法
    const sortedKeys = Object.keys(inputs).sort();
    const hashString = sortedKeys.map(key => `${key}:${JSON.stringify(inputs[key])}`).join('|');

    // 简单的字符串哈希
    let hash = 0;
    for (let i = 0; i < hashString.length; i++) {
      const char = hashString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }

    return hash.toString();
  }

  /**
   * 缓存结果
   * @param inputHash 输入哈希
   * @param result 结果
   */
  protected cacheResult(inputHash: string, result: any): void {
    // 如果缓存已满，删除最旧的条目（LRU）
    if (this.cache.size >= this.cacheSize) {
      const oldestKey = this.cacheOrder.shift();
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(inputHash, result);
    this.cacheOrder.push(inputHash);
  }

  /**
   * 更新缓存访问顺序
   * @param inputHash 输入哈希
   */
  protected updateCacheOrder(inputHash: string): void {
    const index = this.cacheOrder.indexOf(inputHash);
    if (index > -1) {
      this.cacheOrder.splice(index, 1);
      this.cacheOrder.push(inputHash);
    }
  }

  /**
   * 设置输出值
   * @param result 结果
   */
  protected setOutputValues(result: any): void {
    if (result === null || result === undefined) {
      return;
    }

    // 如果结果是对象，分别设置各个输出
    if (typeof result === 'object' && !Array.isArray(result)) {
      for (const [key, value] of Object.entries(result)) {
        if (this.outputs.has(key)) {
          this.setOutputValue(key, value);
        }
      }
    }
    // 如果只有一个输出，直接设置
    else if (this.outputs.size === 1) {
      const outputName = Array.from(this.outputs.keys())[0];
      this.setOutputValue(outputName, result);
    }
    // 如果有多个输出但结果不是对象，设置第一个输出
    else if (this.outputs.size > 1) {
      const outputName = Array.from(this.outputs.keys())[0];
      this.setOutputValue(outputName, result);
    }
  }

  /**
   * 判断是否为纯函数
   * @param signature 函数签名（可选）
   * @returns 是否为纯函数
   */
  protected isPure(signature?: FunctionSignature): boolean {
    const targetSignature = signature || this.signature;
    return targetSignature?.pure ?? true;
  }

  /**
   * 清空缓存
   */
  public clearCache(): void {
    this.cache.clear();
    this.cacheOrder = [];
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计
   */
  public getCacheStats(): { size: number; maxSize: number; hitRate: number } {
    const hitRate = this.executionCount > 0 ?
      (this.executionCount - this.cache.size) / this.executionCount : 0;

    return {
      size: this.cache.size,
      maxSize: this.cacheSize,
      hitRate: Math.max(0, hitRate)
    };
  }

  /**
   * 设置函数签名
   * @param signature 函数签名
   */
  public setSignature(signature: FunctionSignature): void {
    this.signature = signature;
    this.functionName = signature.name;

    // 重新创建插槽
    this.inputs.clear();
    this.outputs.clear();
    this.createSocketsFromSignature();
  }

  /**
   * 获取函数签名
   * @returns 函数签名
   */
  public getSignature(): FunctionSignature | null {
    return this.signature;
  }

  /**
   * 获取执行统计信息
   * @returns 执行统计
   */
  public getExecutionStats(): {
    executionCount: number;
    lastInputHash: string;
    cacheEnabled: boolean;
    validationEnabled: boolean;
  } {
    return {
      executionCount: this.executionCount,
      lastInputHash: this.lastInputHash,
      cacheEnabled: this.enableCache,
      validationEnabled: this.enableValidation
    };
  }

  /**
   * 添加函数重载
   * @param overload 重载定义
   */
  public addOverload(overload: FunctionOverload): void {
    this.overloads.push(overload);
    // 按优先级排序
    this.overloads.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 移除函数重载
   * @param index 重载索引
   */
  public removeOverload(index: number): void {
    if (index >= 0 && index < this.overloads.length) {
      this.overloads.splice(index, 1);
    }
  }

  /**
   * 获取所有重载
   * @returns 重载列表
   */
  public getOverloads(): FunctionOverload[] {
    return [...this.overloads];
  }

  /**
   * 添加调试断点
   * @param breakpointId 断点ID
   */
  public addBreakpoint(breakpointId: string): void {
    this.breakpoints.add(breakpointId);
  }

  /**
   * 移除调试断点
   * @param breakpointId 断点ID
   */
  public removeBreakpoint(breakpointId: string): void {
    this.breakpoints.delete(breakpointId);
  }

  /**
   * 清除所有断点
   */
  public clearBreakpoints(): void {
    this.breakpoints.clear();
  }

  /**
   * 获取性能分析数据
   * @returns 性能数据
   */
  public getPerformanceData(): typeof this.performanceData {
    return { ...this.performanceData };
  }

  /**
   * 重置性能数据
   */
  public resetPerformanceData(): void {
    this.performanceData = {
      totalExecutionTime: 0,
      averageExecutionTime: 0,
      minExecutionTime: Infinity,
      maxExecutionTime: 0,
      cacheHitRate: 0
    };
  }

  /**
   * 启用性能分析
   */
  public setProfilingEnabled(enabled: boolean): void {
    this.enableProfiling = enabled;
  }

  /**
   * 启用调试
   */
  public setDebuggingEnabled(enabled: boolean): void {
    this.enableDebugging = enabled;
  }

  /**
   * 获取函数库引用
   * @returns 函数库引用
   */
  public getLibraryRef(): string | undefined {
    return this.libraryRef;
  }

  /**
   * 设置函数库引用
   * @param libraryRef 函数库引用
   */
  public setLibraryRef(libraryRef: string): void {
    this.libraryRef = libraryRef;
  }

  /**
   * 重置执行状态
   */
  public reset(): void {
    this.executed = false;
    this.result = null;
    this.lastInputHash = '';
    this.executionCount = 0;
    this.currentRetries = 0;
    this.clearCache();
    this.resetPerformanceData();
  }
}

/**
 * 异步函数节点
 * 支持异步函数执行
 */
export class AsyncFunctionNode extends FunctionNode {
  /** 异步执行状态 */
  protected isExecuting: boolean = false;

  /** 取消控制器 */
  protected abortController?: AbortController;

  constructor(options: FunctionNodeOptions) {
    super(options);

    // 异步函数节点默认不是纯函数
    if (this.signature) {
      this.signature.pure = false;
      this.signature.executionMode = FunctionExecutionMode.ASYNC;
    }
  }

  /**
   * 异步执行节点
   * @returns Promise执行结果
   */
  public async executeAsync(): Promise<any> {
    if (this.isExecuting) {
      throw new Error('异步函数节点已在执行中');
    }

    this.isExecuting = true;
    this.abortController = new AbortController();

    try {
      const startTime = this.enableProfiling ? performance.now() : 0;

      // 获取所有输入值
      const inputs: Record<string, any> = {};
      for (const [name] of this.inputs.entries()) {
        inputs[name] = this.getInputValue(name);
      }

      // 选择最佳重载
      const selectedSignature = this.selectBestOverload(inputs);

      // 输入验证
      if (this.enableValidation) {
        const validationResult = this.validateInputs(inputs, selectedSignature);
        if (!validationResult.valid) {
          throw new Error(`输入验证失败: ${validationResult.error}`);
        }
      }

      // 执行异步计算
      this.result = await this.computeAsync(inputs, selectedSignature);
      this.executed = true;
      this.executionCount++;

      // 设置输出值
      this.setOutputValues(this.result);

      this.updatePerformanceData(startTime, false);

      return this.result;
    } catch (error) {
      console.error(`异步函数节点执行失败 [${this.functionName}]:`, error);
      throw error;
    } finally {
      this.isExecuting = false;
      this.abortController = undefined;
    }
  }

  /**
   * 异步计算函数结果
   * @param inputs 输入值
   * @param signature 函数签名
   * @returns Promise计算结果
   */
  protected async computeAsync(_inputs: Record<string, any>, _signature?: FunctionSignature): Promise<any> {
    // 子类实现
    return null;
  }

  /**
   * 取消异步执行
   */
  public cancel(): void {
    if (this.abortController) {
      this.abortController.abort();
    }
    this.isExecuting = false;
  }

  /**
   * 检查是否被取消
   * @returns 是否被取消
   */
  protected isCancelled(): boolean {
    return this.abortController?.signal.aborted ?? false;
  }
}

/**
 * 函数调用节点
 * 用于调用其他函数节点或外部函数
 */
export class FunctionCallNode extends FunctionNode {
  /** 目标函数引用 */
  protected targetFunction?: FunctionNode | Function;

  /** 函数名称或路径 */
  protected functionPath: string;

  constructor(options: FunctionNodeOptions & { functionPath: string }) {
    super(options);
    this.functionPath = options.functionPath;
  }

  /**
   * 设置目标函数
   * @param targetFunction 目标函数
   */
  public setTargetFunction(targetFunction: FunctionNode | Function): void {
    this.targetFunction = targetFunction;
  }

  /**
   * 计算函数结果
   * @param inputs 输入值
   * @param signature 函数签名
   * @returns 计算结果
   */
  protected compute(inputs: Record<string, any>, signature?: FunctionSignature): any {
    if (!this.targetFunction) {
      throw new Error(`未找到目标函数: ${this.functionPath}`);
    }

    if (this.targetFunction instanceof FunctionNode) {
      // 调用其他函数节点
      return this.callFunctionNode(this.targetFunction, inputs);
    } else if (typeof this.targetFunction === 'function') {
      // 调用JavaScript函数
      return this.callJavaScriptFunction(this.targetFunction, inputs, signature);
    }

    throw new Error(`不支持的函数类型: ${typeof this.targetFunction}`);
  }

  /**
   * 调用函数节点
   * @param functionNode 函数节点
   * @param inputs 输入值
   * @returns 执行结果
   */
  protected callFunctionNode(functionNode: FunctionNode, inputs: Record<string, any>): any {
    // 设置输入值
    for (const [name, value] of Object.entries(inputs)) {
      try {
        // 使用公共方法设置输入值
        (functionNode as any).setInputValue?.(name, value);
      } catch (error) {
        console.warn(`无法设置输入值 ${name}:`, error);
      }
    }

    // 执行函数节点
    return functionNode.execute();
  }

  /**
   * 调用JavaScript函数
   * @param func JavaScript函数
   * @param inputs 输入值
   * @param signature 函数签名
   * @returns 执行结果
   */
  protected callJavaScriptFunction(func: Function, inputs: Record<string, any>, signature?: FunctionSignature): any {
    if (signature) {
      // 根据签名参数顺序调用
      const args = signature.parameters.map(param => inputs[param.name]);
      return func(...args);
    } else {
      // 直接传递输入对象
      return func(inputs);
    }
  }
}

/**
 * 函数库管理器
 * 管理函数库的注册、加载和版本控制
 */
export class FunctionLibraryManager {
  /** 已注册的函数库 */
  private libraries: Map<string, FunctionLibrary> = new Map();

  /** 函数缓存 */
  private functionCache: Map<string, FunctionSignature> = new Map();

  /** 依赖关系图 */
  private dependencyGraph: Map<string, Set<string>> = new Map();

  /**
   * 注册函数库
   * @param library 函数库
   */
  public registerLibrary(library: FunctionLibrary): void {
    // 检查依赖
    this.validateDependencies(library);

    // 注册库
    this.libraries.set(library.name, library);

    // 更新依赖图
    this.updateDependencyGraph(library);

    // 缓存函数
    this.cacheFunctions(library);
  }

  /**
   * 获取函数库
   * @param name 库名称
   * @returns 函数库
   */
  public getLibrary(name: string): FunctionLibrary | undefined {
    return this.libraries.get(name);
  }

  /**
   * 获取函数签名
   * @param functionPath 函数路径（格式：库名.函数名）
   * @returns 函数签名
   */
  public getFunctionSignature(functionPath: string): FunctionSignature | undefined {
    return this.functionCache.get(functionPath);
  }

  /**
   * 获取函数重载
   * @param functionPath 函数路径
   * @returns 函数重载列表
   */
  public getFunctionOverloads(functionPath: string): FunctionSignature[] {
    const [libraryName, functionName] = functionPath.split('.');
    const library = this.libraries.get(libraryName);

    if (!library || !functionName) {
      return [];
    }

    return library.functions.get(functionName) || [];
  }

  /**
   * 创建函数节点
   * @param functionPath 函数路径
   * @param options 节点选项
   * @returns 函数节点
   */
  public createFunctionNode(functionPath: string, options: Partial<FunctionNodeOptions> = {}): FunctionNode | null {
    const signatures = this.getFunctionOverloads(functionPath);

    if (signatures.length === 0) {
      return null;
    }

    const primarySignature = signatures[0];
    const overloads = signatures.slice(1).map((sig, index) => ({
      signature: sig,
      priority: signatures.length - index - 1,
      matcher: undefined
    }));

    return new FunctionNode({
      id: options.id || `func_${Date.now()}`,
      type: functionPath,
      functionName: primarySignature.name,
      signature: primarySignature,
      overloads,
      libraryRef: functionPath.split('.')[0],
      ...options
    } as FunctionNodeOptions);
  }

  /**
   * 验证依赖关系
   * @param library 函数库
   */
  private validateDependencies(library: FunctionLibrary): void {
    if (!library.dependencies) return;

    for (const dep of library.dependencies) {
      if (!this.libraries.has(dep)) {
        throw new Error(`缺少依赖库: ${dep}`);
      }
    }
  }

  /**
   * 更新依赖关系图
   * @param library 函数库
   */
  private updateDependencyGraph(library: FunctionLibrary): void {
    const deps = new Set(library.dependencies || []);
    this.dependencyGraph.set(library.name, deps);
  }

  /**
   * 缓存函数
   * @param library 函数库
   */
  private cacheFunctions(library: FunctionLibrary): void {
    for (const [functionName, signatures] of library.functions.entries()) {
      const functionPath = `${library.name}.${functionName}`;

      // 缓存主签名
      if (signatures.length > 0) {
        this.functionCache.set(functionPath, signatures[0]);
      }
    }
  }

  /**
   * 卸载函数库
   * @param name 库名称
   */
  public unregisterLibrary(name: string): void {
    const library = this.libraries.get(name);
    if (!library) return;

    // 检查是否有其他库依赖此库
    for (const [libName, deps] of this.dependencyGraph.entries()) {
      if (deps.has(name) && libName !== name) {
        throw new Error(`无法卸载库 ${name}，库 ${libName} 依赖于它`);
      }
    }

    // 清除缓存
    for (const functionName of library.functions.keys()) {
      const functionPath = `${name}.${functionName}`;
      this.functionCache.delete(functionPath);
    }

    // 移除库
    this.libraries.delete(name);
    this.dependencyGraph.delete(name);
  }

  /**
   * 获取所有已注册的库
   * @returns 库名称列表
   */
  public getRegisteredLibraries(): string[] {
    return Array.from(this.libraries.keys());
  }

  /**
   * 导出函数库
   * @param name 库名称
   * @returns 库的JSON表示
   */
  public exportLibrary(name: string): string | null {
    const library = this.libraries.get(name);
    if (!library) return null;

    // 转换Map为普通对象以便序列化
    const exportData = {
      ...library,
      functions: Object.fromEntries(library.functions.entries())
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * 导入函数库
   * @param libraryJson 库的JSON字符串
   */
  public importLibrary(libraryJson: string): void {
    try {
      const libraryData = JSON.parse(libraryJson);

      // 重建Map
      const library: FunctionLibrary = {
        ...libraryData,
        functions: new Map(Object.entries(libraryData.functions))
      };

      this.registerLibrary(library);
    } catch (error) {
      throw new Error(`导入函数库失败: ${error.message}`);
    }
  }
}
