version: '3.8'

services:
  # 边缘游戏服务器
  edge-game-server:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: edge-game-server
    hostname: edge-${EDGE_NODE_ID:-node}
    restart: unless-stopped
    ports:
      - "${EDGE_HTTP_PORT:-8080}:8080"
      - "${EDGE_TCP_PORT:-3030}:3030"
      - "${EDGE_WEBRTC_PORT:-10000}:10000/udp"
    environment:
      # 节点配置
      - EDGE_NODE_ID=${EDGE_NODE_ID:-edge-$(hostname)-$(date +%s)}
      - EDGE_REGION=${EDGE_REGION:-default}
      - CENTRAL_HUB_URL=${CENTRAL_HUB_URL:-http://central-hub:3000}
      
      # 性能配置
      - MAX_USERS_PER_EDGE=${MAX_USERS_PER_EDGE:-50}
      - EDGE_CACHE_MAX_SIZE=${EDGE_CACHE_MAX_SIZE:-10000}
      - EDGE_CACHE_MAX_MEMORY=${EDGE_CACHE_MAX_MEMORY:-512000000}
      - EDGE_CACHE_DEFAULT_TTL=${EDGE_CACHE_DEFAULT_TTL:-3600}
      
      # 同步配置
      - EDGE_SYNC_BATCH_SIZE=${EDGE_SYNC_BATCH_SIZE:-100}
      - EDGE_SYNC_INTERVAL=${EDGE_SYNC_INTERVAL:-30000}
      - EDGE_SYNC_MAX_RETRIES=${EDGE_SYNC_MAX_RETRIES:-3}
      - EDGE_SYNC_COMPRESSION=${EDGE_SYNC_COMPRESSION:-true}
      
      # 网络配置
      - EDGE_HEARTBEAT_INTERVAL=${EDGE_HEARTBEAT_INTERVAL:-30000}
      - EDGE_HEALTH_CHECK_INTERVAL=${EDGE_HEALTH_CHECK_INTERVAL:-30000}
      
      # 日志配置
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - LOG_FORMAT=${LOG_FORMAT:-json}
      
      # Redis配置（本地缓存）
      - REDIS_HOST=edge-redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    volumes:
      - edge-cache:/app/cache
      - edge-logs:/app/logs
      - edge-config:/app/config
      - /etc/localtime:/etc/localtime:ro
    networks:
      - edge-network
    depends_on:
      - edge-redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/edge/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # 本地Redis缓存
  edge-redis:
    image: redis:7-alpine
    container_name: edge-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: >
      redis-server
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --appendonly yes
      --appendfsync everysec
    volumes:
      - edge-redis-data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - edge-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 256M

  # Prometheus监控
  edge-prometheus:
    image: prom/prometheus:latest
    container_name: edge-prometheus
    restart: unless-stopped
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=7d'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/rules:/etc/prometheus/rules:ro
      - prometheus-data:/prometheus
    networks:
      - edge-network
    depends_on:
      - edge-game-server
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 256M

  # Node Exporter系统监控
  node-exporter:
    image: prom/node-exporter:latest
    container_name: edge-node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    networks:
      - edge-network
    deploy:
      resources:
        limits:
          cpus: '0.2'
          memory: 128M
        reservations:
          cpus: '0.05'
          memory: 64M

  # Grafana仪表板（可选）
  edge-grafana:
    image: grafana/grafana:latest
    container_name: edge-grafana
    restart: unless-stopped
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - edge-network
    depends_on:
      - edge-prometheus
    profiles:
      - monitoring
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 256M

  # 日志收集器（可选）
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: edge-filebeat
    restart: unless-stopped
    user: root
    volumes:
      - ./monitoring/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - edge-logs:/var/log/edge:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - ELASTICSEARCH_HOSTS=${ELASTICSEARCH_HOSTS:-http://elasticsearch:9200}
      - EDGE_NODE_ID=${EDGE_NODE_ID:-edge-node}
      - EDGE_REGION=${EDGE_REGION:-default}
    networks:
      - edge-network
    profiles:
      - logging
    deploy:
      resources:
        limits:
          cpus: '0.2'
          memory: 256M
        reservations:
          cpus: '0.05'
          memory: 128M

# 数据卷
volumes:
  edge-cache:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${EDGE_CACHE_PATH:-./data/cache}
  
  edge-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${EDGE_LOGS_PATH:-./data/logs}
  
  edge-config:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${EDGE_CONFIG_PATH:-./config}
  
  edge-redis-data:
    driver: local
  
  prometheus-data:
    driver: local
  
  grafana-data:
    driver: local

# 网络
networks:
  edge-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
