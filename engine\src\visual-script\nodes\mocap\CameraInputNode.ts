/**
 * 摄像头输入节点
 * 提供摄像头视频流输入功能
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 摄像头输入节点配置
 */
export interface CameraInputNodeConfig {
  /** 摄像头设备ID */
  deviceId?: string;
  /** 分辨率 */
  resolution: { width: number; height: number };
  /** 帧率 */
  frameRate: number;
  /** 是否自动启动 */
  autoStart: boolean;
}

/**
 * 摄像头状态枚举
 */
export enum CameraState {
  IDLE = 'idle',
  INITIALIZING = 'initializing',
  ACTIVE = 'active',
  ERROR = 'error'
}

/**
 * 简化的摄像头管理器接口
 */
interface SimpleCameraManager {
  initialize(deviceId?: string): Promise<void>;
  stop(): Promise<void>;
  destroy(): void;
  getCurrentFrame(): ImageData | null;
  getActualFPS(): number;
  getState(): CameraState;
  on(event: string, callback: Function): void;
}

/**
 * 摄像头输入节点
 */
export class CameraInputNode extends VisualScriptNode {
  /** 节点类型 */
  public static readonly TYPE = 'CameraInput';

  /** 节点名称 */
  public static readonly NAME = '摄像头输入';

  /** 节点描述 */
  public static readonly DESCRIPTION = '从摄像头获取视频流数据';

  private cameraManager: SimpleCameraManager | null = null;
  private config: CameraInputNodeConfig;
  private isActive = false;
  private lastFrame: ImageData | null = null;
  private frameCount = 0;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: CameraInputNodeConfig = {
    resolution: { width: 640, height: 480 },
    frameRate: 30,
    autoStart: false
  };

  constructor(nodeType: string = CameraInputNode.TYPE, name: string = CameraInputNode.NAME, id?: string) {
    super(nodeType, name, id);

    this.config = { ...CameraInputNode.DEFAULT_CONFIG };
    this.setupPorts();
  }

  /**
   * 设置输入输出端口
   */
  private setupPorts(): void {
    // 输入端口
    this.addInput('start', 'trigger', '启动');
    this.addInput('stop', 'trigger', '停止');
    this.addInput('deviceId', 'string', '设备ID');
    this.addInput('resolution', 'object', '分辨率');
    this.addInput('frameRate', 'number', '帧率');

    // 输出端口
    this.addOutput('frame', 'object', '视频帧');
    this.addOutput('imageData', 'object', '图像数据');
    this.addOutput('isActive', 'boolean', '是否活跃');
    this.addOutput('frameCount', 'number', '帧计数');
    this.addOutput('fps', 'number', 'FPS');
    this.addOutput('onStarted', 'trigger', '启动完成');
    this.addOutput('onStopped', 'trigger', '停止完成');
    this.addOutput('onError', 'trigger', '错误');
    this.addOutput('onFrame', 'trigger', '新帧');
  }

  /**
   * 执行节点
   */
  public execute(inputs?: any): any {
    try {
      // 检查输入
      const startTrigger = inputs?.start;
      const stopTrigger = inputs?.stop;
      const deviceId = inputs?.deviceId as string;
      const resolution = inputs?.resolution as { width: number; height: number };
      const frameRate = inputs?.frameRate as number;

      // 更新配置
      if (deviceId !== undefined) {
        this.config.deviceId = deviceId;
      }
      if (resolution) {
        this.config.resolution = resolution;
      }
      if (frameRate !== undefined) {
        this.config.frameRate = frameRate;
      }

      // 处理启动触发
      if (startTrigger && !this.isActive) {
        this.startCamera();
      }

      // 处理停止触发
      if (stopTrigger && this.isActive) {
        this.stopCamera();
      }

      // 返回输出
      return this.getOutputs();

    } catch (error) {
      Debug.error('CameraInputNode', '节点执行失败', String(error));
      return { onError: true };
    }
  }

  /**
   * 获取输出值
   */
  public getOutputs(): any {
    return {
      frame: this.lastFrame,
      imageData: this.lastFrame,
      isActive: this.isActive,
      frameCount: this.frameCount,
      fps: this.cameraManager?.getActualFPS() || 0,
      onStarted: false,
      onStopped: false,
      onError: false,
      onFrame: false
    };
  }

  /**
   * 启动摄像头
   */
  private startCamera(): void {
    try {
      // 模拟摄像头启动
      this.isActive = true;
      this.frameCount = 0;

      Debug.log('CameraInputNode', '摄像头启动成功');

    } catch (error) {
      Debug.error('CameraInputNode', '启动摄像头失败', String(error));
      this.isActive = false;
    }
  }

  /**
   * 停止摄像头
   */
  private stopCamera(): void {
    try {
      this.isActive = false;
      this.lastFrame = null;
      this.cameraManager = null;

      Debug.log('CameraInputNode', '摄像头已停止');

    } catch (error) {
      Debug.error('CameraInputNode', '停止摄像头失败', String(error));
    }
  }

  /**
   * 获取节点配置
   */
  public getConfig(): CameraInputNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新节点配置
   */
  public updateConfig(newConfig: Partial<CameraInputNodeConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前帧
   */
  public getCurrentFrame(): ImageData | null {
    return this.lastFrame;
  }

  /**
   * 获取摄像头状态
   */
  public getCameraState(): CameraState {
    return this.cameraManager?.getState() || CameraState.IDLE;
  }

  /**
   * 获取帧计数
   */
  public getFrameCount(): number {
    return this.frameCount;
  }

  /**
   * 是否正在运行
   */
  public get active(): boolean {
    return this.isActive;
  }
}
