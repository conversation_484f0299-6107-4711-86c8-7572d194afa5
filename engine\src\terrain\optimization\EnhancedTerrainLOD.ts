/**
 * 增强的地形LOD系统
 * 提供自适应LOD、性能优化、内存管理等高级功能
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import { TerrainComponent } from '../components/TerrainComponent';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * LOD节点
 */
export interface LODNode {
  /** 节点ID */
  id: string;
  /** 边界框 */
  bounds: THREE.Box3;
  /** LOD等级 */
  level: number;
  /** 几何体 */
  geometry: THREE.BufferGeometry | null;
  /** 网格 */
  mesh: THREE.Mesh | null;
  /** 子节点 */
  children: LODNode[];
  /** 父节点 */
  parent: LODNode | null;
  /** 是否可见 */
  visible: boolean;
  /** 距离相机的距离 */
  distanceToCamera: number;
  /** 过渡因子 */
  morphFactor: number;
  /** 最后更新时间 */
  lastUpdateTime: number;
}

/**
 * 增强LOD配置
 */
export interface EnhancedLODConfig {
  /** 最大LOD等级 */
  maxLODLevel: number;
  /** LOD距离阈值 */
  lodDistances: number[];
  /** 是否启用自适应LOD */
  enableAdaptiveLOD: boolean;
  /** 是否启用平滑过渡 */
  enableSmoothTransition: boolean;
  /** 是否启用视锥体剔除 */
  enableFrustumCulling: boolean;
  /** 是否启用遮挡剔除 */
  enableOcclusionCulling: boolean;
  /** 性能目标FPS */
  targetFPS: number;
  /** 内存限制(MB) */
  memoryLimit: number;
  /** 更新频率 */
  updateFrequency: number;
  /** 预加载距离 */
  preloadDistance: number;
}

/**
 * 性能统计
 */
export interface LODPerformanceStats {
  /** 可见节点数 */
  visibleNodes: number;
  /** 渲染的三角形数 */
  triangleCount: number;
  /** 内存使用量(MB) */
  memoryUsage: number;
  /** 更新时间(ms) */
  updateTime: number;
  /** 当前FPS */
  currentFPS: number;
  /** LOD切换次数 */
  lodSwitches: number;
}

/**
 * 增强地形LOD系统
 */
export class EnhancedTerrainLOD extends System {
  public static readonly TYPE = 'EnhancedTerrainLOD';

  private config: EnhancedLODConfig;
  private rootNode: LODNode | null;
  private camera: THREE.Camera | null;
  private frustum: THREE.Frustum;
  private performanceMonitor: PerformanceMonitor;
  private eventEmitter: EventEmitter;
  private nodePool: Map<string, LODNode>;
  private geometryCache: Map<string, THREE.BufferGeometry>;
  private lastUpdateTime: number;
  private stats: LODPerformanceStats;
  private adaptiveLODEnabled: boolean;

  constructor(config: EnhancedLODConfig) {
    super(0, { enabled: true });
    this.config = config;
    this.rootNode = null;
    this.camera = null;
    this.frustum = new THREE.Frustum();
    this.performanceMonitor = PerformanceMonitor.getInstance();
    this.eventEmitter = new EventEmitter();
    this.nodePool = new Map();
    this.geometryCache = new Map();
    this.lastUpdateTime = 0;
    this.adaptiveLODEnabled = config.enableAdaptiveLOD;

    this.stats = {
      visibleNodes: 0,
      triangleCount: 0,
      memoryUsage: 0,
      updateTime: 0,
      currentFPS: 0,
      lodSwitches: 0
    };

    this.initializeSystem();
  }

  /**
   * 初始化系统
   */
  private initializeSystem(): void {
    // 启动性能监控
    this.performanceMonitor.start();
    
    Debug.log('增强地形LOD系统初始化完成', `最大LOD等级: ${this.config.maxLODLevel}, 自适应LOD: ${this.config.enableAdaptiveLOD}, 目标FPS: ${this.config.targetFPS}`);
  }

  /**
   * 设置相机
   */
  public setCamera(camera: THREE.Camera): void {
    this.camera = camera;
  }

  /**
   * 构建LOD树
   */
  public buildLODTree(terrain: TerrainComponent): void {
    const startTime = performance.now();

    // 创建根节点
    this.rootNode = this.createLODNode(
      'root',
      terrain.getBounds(),
      0,
      null
    );

    // 递归构建四叉树
    this.buildQuadTree(this.rootNode, terrain, 0);

    const buildTime = performance.now() - startTime;
    Debug.log(`LOD树构建完成，耗时: ${buildTime.toFixed(2)}ms`);
  }

  /**
   * 创建LOD节点
   */
  private createLODNode(
    id: string,
    bounds: THREE.Box3,
    level: number,
    parent: LODNode | null
  ): LODNode {
    const node: LODNode = {
      id,
      bounds,
      level,
      geometry: null,
      mesh: null,
      children: [],
      parent,
      visible: false,
      distanceToCamera: 0,
      morphFactor: 0,
      lastUpdateTime: 0
    };

    this.nodePool.set(id, node);
    return node;
  }

  /**
   * 构建四叉树
   */
  private buildQuadTree(node: LODNode, terrain: TerrainComponent, level: number): void {
    if (level >= this.config.maxLODLevel) {
      return;
    }

    const bounds = node.bounds;
    const center = bounds.getCenter(new THREE.Vector3());
    const size = bounds.getSize(new THREE.Vector3());

    // 创建四个子节点
    const childSize = size.clone().multiplyScalar(0.5);
    const children = [
      { x: -0.25, z: -0.25 }, // 左下
      { x: 0.25, z: -0.25 },  // 右下
      { x: -0.25, z: 0.25 },  // 左上
      { x: 0.25, z: 0.25 }    // 右上
    ];

    children.forEach((offset, index) => {
      const childCenter = center.clone().add(
        new THREE.Vector3(offset.x * size.x, 0, offset.z * size.z)
      );
      
      const childBounds = new THREE.Box3().setFromCenterAndSize(childCenter, childSize);
      const childId = `${node.id}_${index}`;
      
      const childNode = this.createLODNode(childId, childBounds, level + 1, node);
      node.children.push(childNode);

      // 递归构建子树
      this.buildQuadTree(childNode, terrain, level + 1);
    });
  }

  /**
   * 更新LOD
   */
  public update(deltaTime: number): void {
    if (!this.camera || !this.rootNode) {
      return;
    }

    const startTime = performance.now();

    // 更新视锥体
    this.updateFrustum();

    // 更新节点可见性和LOD等级
    this.updateNodeLOD(this.rootNode);

    // 自适应LOD调整
    if (this.adaptiveLODEnabled) {
      this.updateAdaptiveLOD();
    }

    // 内存管理
    this.manageMemory();

    // 更新统计信息
    this.updateStats(performance.now() - startTime);

    this.lastUpdateTime = performance.now();
  }

  /**
   * 更新视锥体
   */
  private updateFrustum(): void {
    if (!this.camera) return;

    const matrix = new THREE.Matrix4().multiplyMatrices(
      this.camera.projectionMatrix,
      this.camera.matrixWorldInverse
    );
    this.frustum.setFromProjectionMatrix(matrix);
  }

  /**
   * 更新节点LOD
   */
  private updateNodeLOD(node: LODNode): void {
    if (!this.camera) return;

    // 计算节点到相机的距离
    const nodeCenter = node.bounds.getCenter(new THREE.Vector3());
    node.distanceToCamera = this.camera.position.distanceTo(nodeCenter);

    // 视锥体剔除
    if (this.config.enableFrustumCulling) {
      if (!this.frustum.intersectsBox(node.bounds)) {
        node.visible = false;
        this.hideNode(node);
        return;
      }
    }

    // 确定LOD等级
    const lodLevel = this.calculateLODLevel(node.distanceToCamera);
    
    // 检查是否需要显示此节点
    if (lodLevel === node.level) {
      node.visible = true;
      this.showNode(node);
      
      // 隐藏子节点
      node.children.forEach(child => {
        child.visible = false;
        this.hideNode(child);
      });
    } else if (lodLevel > node.level && node.children.length > 0) {
      // 显示更详细的子节点
      node.visible = false;
      this.hideNode(node);
      
      node.children.forEach(child => {
        this.updateNodeLOD(child);
      });
    } else {
      node.visible = false;
      this.hideNode(node);
    }

    // 计算平滑过渡因子
    if (this.config.enableSmoothTransition) {
      this.calculateMorphFactor(node);
    }
  }

  /**
   * 计算LOD等级
   */
  private calculateLODLevel(distance: number): number {
    for (let i = 0; i < this.config.lodDistances.length; i++) {
      if (distance < this.config.lodDistances[i]) {
        return i;
      }
    }
    return this.config.lodDistances.length;
  }

  /**
   * 显示节点
   */
  private showNode(node: LODNode): void {
    if (!node.mesh) {
      // 创建或获取几何体
      node.geometry = this.getOrCreateGeometry(node);
      
      if (node.geometry) {
        node.mesh = new THREE.Mesh(node.geometry);
        // 添加到场景中
      }
    }

    if (node.mesh) {
      node.mesh.visible = true;
      this.stats.visibleNodes++;
    }
  }

  /**
   * 隐藏节点
   */
  private hideNode(node: LODNode): void {
    if (node.mesh) {
      node.mesh.visible = false;
    }
    
    // 递归隐藏子节点
    node.children.forEach(child => this.hideNode(child));
  }

  /**
   * 获取或创建几何体
   */
  private getOrCreateGeometry(node: LODNode): THREE.BufferGeometry | null {
    const cacheKey = `${node.id}_${node.level}`;
    
    if (this.geometryCache.has(cacheKey)) {
      return this.geometryCache.get(cacheKey)!;
    }

    // 生成几何体
    const geometry = this.generateNodeGeometry(node);
    
    if (geometry) {
      this.geometryCache.set(cacheKey, geometry);
    }

    return geometry;
  }

  /**
   * 生成节点几何体
   */
  private generateNodeGeometry(node: LODNode): THREE.BufferGeometry | null {
    // 根据LOD等级生成不同精度的几何体
    const resolution = Math.pow(2, this.config.maxLODLevel - node.level) + 1;
    const size = node.bounds.getSize(new THREE.Vector3());
    
    const geometry = new THREE.PlaneGeometry(size.x, size.z, resolution - 1, resolution - 1);
    
    // 这里应该根据高度图数据设置顶点高度
    // 暂时使用平面几何体
    
    return geometry;
  }

  /**
   * 计算过渡因子
   */
  private calculateMorphFactor(node: LODNode): void {
    const distance = node.distanceToCamera;
    const currentLOD = this.calculateLODLevel(distance);
    
    if (currentLOD < this.config.lodDistances.length) {
      const nearDistance = currentLOD > 0 ? this.config.lodDistances[currentLOD - 1] : 0;
      const farDistance = this.config.lodDistances[currentLOD];
      
      node.morphFactor = THREE.MathUtils.clamp(
        (distance - nearDistance) / (farDistance - nearDistance),
        0,
        1
      );
    } else {
      node.morphFactor = 1;
    }
  }

  /**
   * 更新自适应LOD
   */
  private updateAdaptiveLOD(): void {
    const fpsMetric = this.performanceMonitor.getMetric('FPS' as any);
    const currentFPS = fpsMetric ? fpsMetric.value : 60;
    this.stats.currentFPS = currentFPS;

    if (currentFPS < this.config.targetFPS * 0.9) {
      // 性能不足，降低LOD质量
      this.adjustLODDistances(1.1);
    } else if (currentFPS > this.config.targetFPS * 1.1) {
      // 性能充足，提高LOD质量
      this.adjustLODDistances(0.95);
    }
  }

  /**
   * 调整LOD距离
   */
  private adjustLODDistances(factor: number): void {
    for (let i = 0; i < this.config.lodDistances.length; i++) {
      this.config.lodDistances[i] *= factor;
    }
  }

  /**
   * 内存管理
   */
  private manageMemory(): void {
    const memoryUsage = this.calculateMemoryUsage();
    this.stats.memoryUsage = memoryUsage;

    if (memoryUsage > this.config.memoryLimit) {
      // 清理不可见的几何体
      this.cleanupInvisibleGeometry();
    }
  }

  /**
   * 计算内存使用量
   */
  private calculateMemoryUsage(): number {
    let totalMemory = 0;
    
    for (const geometry of this.geometryCache.values()) {
      if (geometry.attributes.position) {
        totalMemory += (geometry.attributes.position.array as any).byteLength || 0;
      }
      if (geometry.attributes.normal) {
        totalMemory += (geometry.attributes.normal.array as any).byteLength || 0;
      }
      if (geometry.attributes.uv) {
        totalMemory += (geometry.attributes.uv.array as any).byteLength || 0;
      }
    }

    return totalMemory / (1024 * 1024); // 转换为MB
  }

  /**
   * 清理不可见的几何体
   */
  private cleanupInvisibleGeometry(): void {
    const toRemove: string[] = [];
    
    for (const [key, node] of this.nodePool.entries()) {
      if (!node.visible && node.geometry) {
        toRemove.push(key);
      }
    }

    toRemove.forEach(key => {
      const node = this.nodePool.get(key);
      if (node && node.geometry) {
        node.geometry.dispose();
        node.geometry = null;
        this.geometryCache.delete(key);
      }
    });

    Debug.log(`清理了 ${toRemove.length} 个不可见几何体`);
  }

  /**
   * 更新统计信息
   */
  private updateStats(updateTime: number): void {
    this.stats.updateTime = updateTime;
    this.stats.triangleCount = this.calculateTriangleCount();
  }

  /**
   * 计算三角形数量
   */
  private calculateTriangleCount(): number {
    let triangleCount = 0;
    
    for (const node of this.nodePool.values()) {
      if (node.visible && node.geometry) {
        const positionAttribute = node.geometry.attributes.position;
        if (positionAttribute) {
          triangleCount += positionAttribute.count / 3;
        }
      }
    }

    return triangleCount;
  }

  /**
   * 获取LOD性能统计
   */
  public getLODPerformanceStats(): LODPerformanceStats {
    return { ...this.stats };
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 清理所有几何体
    for (const geometry of this.geometryCache.values()) {
      geometry.dispose();
    }
    
    this.geometryCache.clear();
    this.nodePool.clear();
    this.performanceMonitor.stop();
    
    Debug.log('增强地形LOD系统已销毁');
  }
}
