import { Injectable, BadRequestException } from '@nestjs/common';
import { VectorStoreService, SearchResult } from '../vector-store/vector-store.service';
import { EmbeddingsService } from '../embeddings/embeddings.service';

export interface SearchOptions {
  topK?: number;
  threshold?: number;
  filter?: Record<string, any>;
  rerank?: boolean;
  expandQuery?: boolean;
}

export interface EnhancedSearchResult extends SearchResult {
  relevanceScore?: number;
  snippet?: string;
  highlights?: string[];
}

@Injectable()
export class SearchService {
  constructor(
    private vectorStoreService: VectorStoreService,
    private embeddingsService: EmbeddingsService,
  ) {}

  /**
   * 执行语义搜索
   */
  async search(
    knowledgeBaseId: string,
    query: string,
    options: SearchOptions = {},
  ): Promise<EnhancedSearchResult[]> {
    try {
      // 查询扩展
      const expandedQuery = options.expandQuery ? await this.expandQuery(query) : query;
      
      // 执行向量搜索
      const vectorResults = await this.vectorStoreService.search(
        knowledgeBaseId,
        expandedQuery,
        {
          topK: options.topK || 10,
          threshold: options.threshold || 0.7,
          filter: options.filter,
        },
      );

      // 增强搜索结果
      let enhancedResults = await this.enhanceResults(vectorResults, query);

      // 重排序
      if (options.rerank && enhancedResults.length > 1) {
        enhancedResults = await this.rerankResults(enhancedResults, query);
      }

      // 限制返回数量
      const finalTopK = options.topK || 5;
      return enhancedResults.slice(0, finalTopK);
    } catch (error) {
      throw new BadRequestException(`搜索失败: ${error.message}`);
    }
  }

  /**
   * 混合搜索（向量搜索 + 关键词搜索）
   */
  async hybridSearch(
    knowledgeBaseId: string,
    query: string,
    options: SearchOptions = {},
  ): Promise<EnhancedSearchResult[]> {
    try {
      // 向量搜索
      const vectorResults = await this.vectorStoreService.search(
        knowledgeBaseId,
        query,
        {
          topK: (options.topK || 5) * 2, // 获取更多结果用于融合
          threshold: options.threshold || 0.6,
          filter: options.filter,
        },
      );

      // 关键词搜索
      const keywordResults = await this.keywordSearch(knowledgeBaseId, query, options);

      // 融合结果
      const fusedResults = this.fuseResults(vectorResults, keywordResults);

      // 增强和重排序
      let enhancedResults = await this.enhanceResults(fusedResults, query);
      
      if (options.rerank) {
        enhancedResults = await this.rerankResults(enhancedResults, query);
      }

      return enhancedResults.slice(0, options.topK || 5);
    } catch (error) {
      throw new BadRequestException(`混合搜索失败: ${error.message}`);
    }
  }

  /**
   * 关键词搜索
   */
  private async keywordSearch(
    knowledgeBaseId: string,
    query: string,
    options: SearchOptions,
  ): Promise<SearchResult[]> {
    // 这里实现基于关键词的搜索
    // 可以使用Elasticsearch或其他全文搜索引擎
    // 暂时返回空结果
    return [];
  }

  /**
   * 查询扩展
   */
  private async expandQuery(query: string): Promise<string> {
    // 简单的查询扩展实现
    // 可以使用同义词词典、词向量相似词等方法
    const synonyms = this.getSynonyms(query);
    
    if (synonyms.length > 0) {
      return `${query} ${synonyms.join(' ')}`;
    }
    
    return query;
  }

  /**
   * 获取同义词
   */
  private getSynonyms(query: string): string[] {
    // 简单的同义词映射
    const synonymMap: Record<string, string[]> = {
      '高血压': ['高血压病', '血压高', '高血压症'],
      '糖尿病': ['糖尿病症', 'DM', '血糖高'],
      '心脏病': ['心脏疾病', '心血管疾病', '冠心病'],
      '感冒': ['流感', '上呼吸道感染', '风寒'],
    };

    const synonyms: string[] = [];
    
    for (const [key, values] of Object.entries(synonymMap)) {
      if (query.includes(key)) {
        synonyms.push(...values);
      }
    }

    return synonyms;
  }

  /**
   * 增强搜索结果
   */
  private async enhanceResults(
    results: SearchResult[],
    query: string,
  ): Promise<EnhancedSearchResult[]> {
    return Promise.all(
      results.map(async (result) => {
        // 生成摘要片段
        const snippet = this.generateSnippet(result.content, query);
        
        // 生成高亮
        const highlights = this.generateHighlights(result.content, query);
        
        // 计算相关性分数
        const relevanceScore = await this.calculateRelevanceScore(result, query);

        return {
          ...result,
          snippet,
          highlights,
          relevanceScore,
        };
      }),
    );
  }

  /**
   * 生成摘要片段
   */
  private generateSnippet(content: string, query: string, maxLength: number = 200): string {
    const queryTerms = query.toLowerCase().split(/\s+/);
    const sentences = content.split(/[。！？.!?]/);
    
    // 找到包含查询词的句子
    let bestSentence = '';
    let maxMatches = 0;
    
    for (const sentence of sentences) {
      const lowerSentence = sentence.toLowerCase();
      const matches = queryTerms.filter(term => lowerSentence.includes(term)).length;
      
      if (matches > maxMatches) {
        maxMatches = matches;
        bestSentence = sentence;
      }
    }
    
    if (bestSentence.length <= maxLength) {
      return bestSentence.trim();
    }
    
    // 截断长句子
    return bestSentence.substring(0, maxLength - 3).trim() + '...';
  }

  /**
   * 生成高亮
   */
  private generateHighlights(content: string, query: string): string[] {
    const queryTerms = query.toLowerCase().split(/\s+/);
    const highlights: string[] = [];
    
    for (const term of queryTerms) {
      const regex = new RegExp(`(${term})`, 'gi');
      const matches = content.match(regex);
      
      if (matches) {
        highlights.push(...matches);
      }
    }
    
    return [...new Set(highlights)]; // 去重
  }

  /**
   * 计算相关性分数
   */
  private async calculateRelevanceScore(result: SearchResult, query: string): Promise<number> {
    // 基于多个因素计算相关性分数
    let score = result.score; // 基础向量相似度分数
    
    // 查询词匹配度
    const queryTerms = query.toLowerCase().split(/\s+/);
    const content = result.content.toLowerCase();
    const matchCount = queryTerms.filter(term => content.includes(term)).length;
    const matchRatio = matchCount / queryTerms.length;
    
    // 内容长度因子（适中长度的内容可能更相关）
    const lengthFactor = Math.min(1, result.content.length / 500);
    
    // 综合计算
    score = score * 0.7 + matchRatio * 0.2 + lengthFactor * 0.1;
    
    return Math.min(1, score);
  }

  /**
   * 重排序结果
   */
  private async rerankResults(
    results: EnhancedSearchResult[],
    query: string,
  ): Promise<EnhancedSearchResult[]> {
    // 基于相关性分数重新排序
    return results.sort((a, b) => {
      const scoreA = a.relevanceScore || a.score;
      const scoreB = b.relevanceScore || b.score;
      return scoreB - scoreA;
    });
  }

  /**
   * 融合多个搜索结果
   */
  private fuseResults(
    vectorResults: SearchResult[],
    keywordResults: SearchResult[],
  ): SearchResult[] {
    const fusedMap = new Map<string, SearchResult>();
    
    // 添加向量搜索结果
    vectorResults.forEach(result => {
      fusedMap.set(result.id, {
        ...result,
        score: result.score * 0.7, // 向量搜索权重
      });
    });
    
    // 融合关键词搜索结果
    keywordResults.forEach(result => {
      if (fusedMap.has(result.id)) {
        // 如果已存在，融合分数
        const existing = fusedMap.get(result.id)!;
        existing.score = existing.score + result.score * 0.3; // 关键词搜索权重
      } else {
        fusedMap.set(result.id, {
          ...result,
          score: result.score * 0.3,
        });
      }
    });
    
    return Array.from(fusedMap.values()).sort((a, b) => b.score - a.score);
  }

  /**
   * 获取搜索建议
   */
  async getSearchSuggestions(
    knowledgeBaseId: string,
    partialQuery: string,
    limit: number = 5,
  ): Promise<string[]> {
    // 这里可以实现搜索建议功能
    // 基于历史查询、知识库内容等生成建议
    return [];
  }

  /**
   * 搜索统计
   */
  async getSearchStatistics(knowledgeBaseId: string): Promise<any> {
    return {
      totalVectors: await this.vectorStoreService.getVectorCount(knowledgeBaseId),
      collectionInfo: await this.vectorStoreService.getCollectionInfo(knowledgeBaseId),
    };
  }
}
