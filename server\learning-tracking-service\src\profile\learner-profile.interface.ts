/**
 * 学习者画像相关接口定义
 */

// 学习偏好
export interface LearningPreferences {
  preferredContentTypes: string[];           // 偏好的内容类型
  learningPace: 'slow' | 'medium' | 'fast'; // 学习节奏
  interactionStyle: 'passive' | 'active' | 'exploratory'; // 交互风格
  attentionSpan: number;                     // 注意力持续时间(分钟)
  preferredDifficulty: 'easy' | 'medium' | 'hard'; // 偏好难度
  sessionDuration: number;                   // 平均会话时长(分钟)
  preferredTimeOfDay: 'morning' | 'afternoon' | 'evening' | 'night'; // 偏好学习时间
  breakFrequency: number;                    // 休息频率(分钟)
}

// 知识领域掌握情况
export interface KnowledgeArea {
  level: 'beginner' | 'intermediate' | 'advanced';
  confidence: number;                        // 置信度 0-1
  lastAssessed: Date;
  weakPoints: string[];                      // 薄弱环节
  strengths: string[];                       // 优势领域
  masteredConcepts: string[];                // 已掌握概念
  strugglingConcepts: string[];              // 困难概念
  progressTrend: 'improving' | 'stable' | 'declining'; // 进步趋势
  timeSpent: number;                         // 在该领域花费的时间(分钟)
}

// 行为模式
export interface BehaviorPatterns {
  sessionFrequency: number;                  // 会话频率(次/周)
  questionsPerSession: number;               // 每次会话平均提问数
  emotionalStates: { [emotion: string]: number }; // 情感状态频率
  pathCompletionRate: number;                // 路径完成率
  recommendationAcceptanceRate: number;      // 推荐接受率
  helpSeekingBehavior: 'frequent' | 'moderate' | 'rare'; // 求助行为
  explorationTendency: number;               // 探索倾向 0-1
  persistenceLevel: number;                  // 坚持程度 0-1
  collaborationPreference: number;           // 协作偏好 0-1
  feedbackSensitivity: number;               // 反馈敏感度 0-1
}

// 学习目标
export interface LearningGoals {
  shortTerm: string[];                       // 短期目标
  longTerm: string[];                        // 长期目标
  priority: 'knowledge' | 'skills' | 'certification'; // 优先级
  targetCompletionDate?: Date;               // 目标完成日期
  motivationType: 'intrinsic' | 'extrinsic' | 'mixed'; // 动机类型
  difficultyPreference: 'challenge' | 'comfort' | 'mixed'; // 难度偏好
}

// 社交学习特征
export interface SocialLearning {
  collaborationPreference: 'individual' | 'small_group' | 'large_group';
  communicationStyle: 'formal' | 'informal' | 'mixed';
  feedbackPreference: 'immediate' | 'delayed' | 'periodic';
  leadershipTendency: number;                // 领导倾向 0-1
  mentorshipInterest: number;                // 指导兴趣 0-1
  peerLearningEffectiveness: number;         // 同伴学习效果 0-1
}

// 认知特征
export interface CognitiveTraits {
  processingSpeed: 'slow' | 'medium' | 'fast';
  memoryRetention: 'poor' | 'average' | 'excellent';
  abstractThinking: number;                  // 抽象思维能力 0-1
  analyticalSkills: number;                  // 分析能力 0-1
  creativityLevel: number;                   // 创造力水平 0-1
  problemSolvingStyle: 'systematic' | 'intuitive' | 'mixed';
  learningStyle: 'visual' | 'auditory' | 'kinesthetic' | 'mixed';
}

// 情感特征
export interface EmotionalTraits {
  dominantEmotions: string[];                // 主导情感
  emotionalStability: number;                // 情感稳定性 0-1
  stressResilience: number;                  // 抗压能力 0-1
  motivationLevel: number;                   // 动机水平 0-1
  confidenceLevel: number;                   // 自信水平 0-1
  frustrationTolerance: number;              // 挫折容忍度 0-1
  curiosityLevel: number;                    // 好奇心水平 0-1
}

// 学习成果
export interface LearningOutcomes {
  totalActivitiesCompleted: number;          // 完成的活动总数
  averageScore: number;                      // 平均分数
  improvementRate: number;                   // 改进率
  skillsAcquired: string[];                  // 获得的技能
  certificationsEarned: string[];            // 获得的认证
  milestonesAchieved: string[];              // 达成的里程碑
  timeToMastery: { [concept: string]: number }; // 掌握概念所需时间
}

// 学习者画像主接口
export interface LearnerProfile {
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  
  // 基本信息
  demographics: {
    age?: number;
    education?: string;
    profession?: string;
    location?: string;
    timezone?: string;
    language?: string;
  };
  
  // 学习偏好
  learningPreferences: LearningPreferences;
  
  // 知识领域掌握情况
  knowledgeAreas: { [subject: string]: KnowledgeArea };
  
  // 行为模式
  behaviorPatterns: BehaviorPatterns;
  
  // 学习目标
  learningGoals: LearningGoals;
  
  // 社交学习特征
  socialLearning: SocialLearning;
  
  // 认知特征
  cognitiveTraits: CognitiveTraits;
  
  // 情感特征
  emotionalTraits: EmotionalTraits;
  
  // 学习成果
  learningOutcomes: LearningOutcomes;
  
  // 元数据
  metadata: {
    profileVersion: string;
    dataQuality: number;                     // 数据质量评分 0-1
    lastAnalysisDate: Date;
    analysisCount: number;
    confidenceScore: number;                 // 画像置信度 0-1
  };
}

// 画像分析选项
export interface ProfileAnalysisOptions {
  includeHistoricalData: boolean;            // 包含历史数据
  timeRange?: {
    start: Date;
    end: Date;
  };
  minDataPoints: number;                     // 最少数据点
  analysisDepth: 'basic' | 'detailed' | 'comprehensive'; // 分析深度
  updateExisting: boolean;                   // 更新现有画像
}

// 画像更新事件
export interface ProfileUpdateEvent {
  userId: string;
  updateType: 'incremental' | 'full' | 'correction';
  changedFields: string[];
  previousValues: Partial<LearnerProfile>;
  newValues: Partial<LearnerProfile>;
  confidence: number;
  timestamp: Date;
}

// 画像比较结果
export interface ProfileComparison {
  userId1: string;
  userId2: string;
  similarity: number;                        // 相似度 0-1
  differences: {
    field: string;
    value1: any;
    value2: any;
    significance: number;                    // 差异显著性 0-1
  }[];
  recommendations: string[];                 // 基于比较的建议
}

// 画像聚类结果
export interface ProfileCluster {
  clusterId: string;
  name: string;
  description: string;
  memberCount: number;
  characteristics: {
    [trait: string]: {
      average: number;
      range: [number, number];
      significance: number;
    };
  };
  representativeProfile: Partial<LearnerProfile>;
}

// 画像预测结果
export interface ProfilePrediction {
  userId: string;
  predictionType: 'performance' | 'behavior' | 'preference' | 'outcome';
  predictions: {
    field: string;
    predictedValue: any;
    confidence: number;
    timeframe: string;
    factors: string[];
  }[];
  accuracy: number;                          // 预测准确性
  generatedAt: Date;
}

// 薄弱环节识别结果
export interface WeakArea {
  subject: string;
  confidence: number;
  weakPoints: string[];
  priority: number;                          // 优先级分数
  urgency: 'low' | 'medium' | 'high';
  recommendedActions: string[];
  estimatedImprovementTime: number;          // 预计改进时间(天)
}

// 学习路径建议
export interface LearningPathSuggestion {
  pathId: string;
  name: string;
  description: string;
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedDuration: number;                 // 预计时长(小时)
  prerequisites: string[];
  learningObjectives: string[];
  suitabilityScore: number;                  // 适合度分数 0-1
  reasons: string[];                         // 推荐理由
}

// 个性化建议
export interface PersonalizationSuggestion {
  type: 'content' | 'pace' | 'method' | 'schedule' | 'difficulty';
  suggestion: string;
  rationale: string;
  expectedImpact: number;                    // 预期影响 0-1
  implementationDifficulty: 'easy' | 'medium' | 'hard';
  priority: number;                          // 优先级 1-10
}

// 画像分析结果
export interface ProfileAnalysisResult {
  profile: LearnerProfile;
  weakAreas: WeakArea[];
  pathSuggestions: LearningPathSuggestion[];
  personalizations: PersonalizationSuggestion[];
  insights: string[];
  confidence: number;
  analysisDate: Date;
}
