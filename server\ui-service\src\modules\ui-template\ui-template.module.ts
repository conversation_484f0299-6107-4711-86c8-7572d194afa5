import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UITemplateController } from './ui-template.controller';
import { UITemplateService } from './ui-template.service';
import { UITemplate, UITemplateSchema } from './schemas/ui-template.schema';
import { UITemplateVersion, UITemplateVersionSchema } from './schemas/ui-template-version.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UITemplate.name, schema: UITemplateSchema },
      { name: UITemplateVersion.name, schema: UITemplateVersionSchema },
    ]),
  ],
  controllers: [UITemplateController],
  providers: [UITemplateService],
  exports: [UITemplateService],
})
export class UITemplateModule {}
