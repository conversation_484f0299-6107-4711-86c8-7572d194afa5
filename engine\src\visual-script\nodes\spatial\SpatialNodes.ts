/**
 * 空间信息系统视觉脚本节点
 * 为视觉脚本系统提供空间信息相关的节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { ExecutionContext } from '../../../visualscript/execution/ExecutionContext';
import {
  GeographicCoordinate,
  CoordinateSystemType,
  CoordinateTransformer,
  CoordinateSystemManager
} from '../../../spatial/coordinate/CoordinateSystem';
import { GeospatialComponent } from '../../../spatial/components/GeospatialComponent';
import { SpatialAnalysisEngine, BufferAnalysisParams } from '../../../spatial/analysis/SpatialAnalysisEngine';
import { Entity } from '../../../core/Entity';

/**
 * 节点类别枚举
 */
export enum NodeCategory {
  SPATIAL = 'spatial',
  CORE = 'core',
  MATH = 'math',
  LOGIC = 'logic',
  UI = 'ui',
  ANIMATION = 'animation',
  PHYSICS = 'physics',
  AUDIO = 'audio',
  NETWORK = 'network',
  AI = 'ai'
}

/**
 * 空间节点基类
 * 扩展基础视觉脚本节点，提供空间相关的功能
 */
export abstract class SpatialVisualScriptNode extends VisualScriptNode {
  constructor(nodeType: string, name: string, category: NodeCategory) {
    super(nodeType, name);
  }

  /**
   * 获取输入值（适配ExecutionContext）
   */
  protected getInputValueFromContext(name: string, context: ExecutionContext): any {
    const input = this.inputs.get(name);
    if (!input) return undefined;

    // 从执行上下文获取输入值
    const value = context.getVariable(`input_${this.id}_${name}`);
    return value !== undefined ? value : input.defaultValue;
  }

  /**
   * 设置输出值（适配ExecutionContext）
   */
  protected setOutputValue(name: string, value: any, context: ExecutionContext): void {
    context.setVariable(`output_${this.id}_${name}`, value);
  }

  /**
   * 执行输出连接
   */
  protected executeOutput(name: string, context: ExecutionContext): void {
    // 触发输出执行
    context.setVariable(`exec_${this.id}_${name}`, true);
  }

  /**
   * 执行节点（重写基类方法）
   */
  public execute(inputs?: any): any {
    // 基类方法实现
    return {};
  }

  /**
   * 执行节点（使用ExecutionContext）
   */
  public abstract executeWithContext(context: ExecutionContext): void;
}

/**
 * 创建地理坐标节点
 */
export class CreateGeographicCoordinateNode extends SpatialVisualScriptNode {
  constructor() {
    super('CreateGeographicCoordinate', '创建地理坐标', NodeCategory.SPATIAL);

    this.addInput('longitude', 'number', '经度', 0);
    this.addInput('latitude', 'number', '纬度', 0);
    this.addInput('altitude', 'number', '海拔', 0);

    this.addOutput('coordinate', 'GeographicCoordinate', '坐标');
  }

  executeWithContext(context: ExecutionContext): void {
    const longitude = this.getInputValueFromContext('longitude', context) as number;
    const latitude = this.getInputValueFromContext('latitude', context) as number;
    const altitude = this.getInputValueFromContext('altitude', context) as number;

    const coordinate: GeographicCoordinate = {
      longitude,
      latitude,
      altitude: altitude || undefined
    };

    this.setOutputValue('coordinate', coordinate, context);
  }
}

/**
 * 坐标转换节点
 */
export class CoordinateTransformNode extends SpatialVisualScriptNode {
  constructor() {
    super('CoordinateTransform', '坐标转换', NodeCategory.SPATIAL);

    this.addInput('coordinate', 'GeographicCoordinate', '输入坐标');
    this.addInput('sourceSystem', 'string', '源坐标系', 'WGS84');
    this.addInput('targetSystem', 'string', '目标坐标系', 'GCJ02');

    this.addOutput('transformedCoordinate', 'GeographicCoordinate', '转换后坐标');
    this.addOutput('success', 'boolean', '转换成功');
  }

  executeWithContext(context: ExecutionContext): void {
    const coordinate = this.getInputValueFromContext('coordinate', context) as GeographicCoordinate;
    const sourceSystem = this.getInputValueFromContext('sourceSystem', context) as string;
    const targetSystem = this.getInputValueFromContext('targetSystem', context) as string;

    try {
      const manager = CoordinateSystemManager.getInstance();
      const result = manager.transform(
        coordinate,
        sourceSystem as CoordinateSystemType,
        targetSystem as CoordinateSystemType
      );

      this.setOutputValue('transformedCoordinate', result, context);
      this.setOutputValue('success', true, context);
    } catch (error) {
      console.error('坐标转换失败:', error);
      this.setOutputValue('transformedCoordinate', coordinate, context);
      this.setOutputValue('success', false, context);
    }
  }
}

/**
 * 创建地理空间组件节点
 */
export class CreateGeospatialComponentNode extends SpatialVisualScriptNode {
  constructor() {
    super('CreateGeospatialComponent', '创建地理空间组件', NodeCategory.SPATIAL);

    this.addInput('coordinate', 'GeographicCoordinate', '地理坐标');
    this.addInput('geometryType', 'string', '几何类型', 'Point');
    this.addInput('coordinateSystem', 'string', '坐标系', 'WGS84');
    this.addInput('properties', 'object', '属性', {});

    this.addOutput('component', 'GeospatialComponent', '地理空间组件');
  }

  executeWithContext(context: ExecutionContext): void {
    const coordinate = this.getInputValueFromContext('coordinate', context) as GeographicCoordinate;
    const geometryType = this.getInputValueFromContext('geometryType', context) as 'Point' | 'LineString' | 'Polygon';
    const coordinateSystem = this.getInputValueFromContext('coordinateSystem', context) as CoordinateSystemType;
    const properties = this.getInputValueFromContext('properties', context) as any;

    const component = new GeospatialComponent(coordinate, coordinateSystem, geometryType);
    component.setProperties(properties);

    this.setOutputValue('component', component, context);
  }
}

/**
 * 添加地理空间组件到实体节点
 */
export class AddGeospatialComponentNode extends SpatialVisualScriptNode {
  constructor() {
    super('AddGeospatialComponent', '添加地理空间组件', NodeCategory.SPATIAL);

    this.addInput('entity', 'Entity', '实体');
    this.addInput('component', 'GeospatialComponent', '地理空间组件');
    this.addInput('execute', 'exec', '执行');

    this.addOutput('onComplete', 'exec', '完成');
    this.addOutput('success', 'boolean', '成功');
  }

  executeWithContext(context: ExecutionContext): void {
    const entity = this.getInputValueFromContext('entity', context) as Entity;
    const component = this.getInputValueFromContext('component', context) as GeospatialComponent;

    try {
      entity.addComponent(component);
      this.setOutputValue('success', true, context);
    } catch (error) {
      console.error('添加地理空间组件失败:', error);
      this.setOutputValue('success', false, context);
    }

    this.executeOutput('onComplete', context);
  }
}

/**
 * 获取地理坐标节点
 */
export class GetGeographicCoordinateNode extends SpatialVisualScriptNode {
  constructor() {
    super('GetGeographicCoordinate', '获取地理坐标', NodeCategory.SPATIAL);

    this.addInput('component', 'GeospatialComponent', '地理空间组件');

    this.addOutput('coordinate', 'GeographicCoordinate', '地理坐标');
    this.addOutput('longitude', 'number', '经度');
    this.addOutput('latitude', 'number', '纬度');
    this.addOutput('altitude', 'number', '海拔');
  }

  executeWithContext(context: ExecutionContext): void {
    const component = this.getInputValueFromContext('component', context) as GeospatialComponent;

    if (component) {
      const coordinate = component.getGeographicCoordinate();

      this.setOutputValue('coordinate', coordinate, context);
      this.setOutputValue('longitude', coordinate.longitude, context);
      this.setOutputValue('latitude', coordinate.latitude, context);
      this.setOutputValue('altitude', coordinate.altitude || 0, context);
    }
  }
}

/**
 * 设置地理坐标节点
 */
export class SetGeographicCoordinateNode extends SpatialVisualScriptNode {
  constructor() {
    super('SetGeographicCoordinate', '设置地理坐标', NodeCategory.SPATIAL);

    this.addInput('component', 'GeospatialComponent', '地理空间组件');
    this.addInput('coordinate', 'GeographicCoordinate', '新坐标');
    this.addInput('execute', 'exec', '执行');

    this.addOutput('onComplete', 'exec', '完成');
    this.addOutput('success', 'boolean', '成功');
  }

  executeWithContext(context: ExecutionContext): void {
    const component = this.getInputValueFromContext('component', context) as GeospatialComponent;
    const coordinate = this.getInputValueFromContext('coordinate', context) as GeographicCoordinate;

    try {
      component.setGeographicCoordinate(coordinate);
      this.setOutputValue('success', true, context);
    } catch (error) {
      console.error('设置地理坐标失败:', error);
      this.setOutputValue('success', false, context);
    }

    this.executeOutput('onComplete', context);
  }
}

/**
 * 计算距离节点
 */
export class CalculateDistanceNode extends SpatialVisualScriptNode {
  constructor() {
    super('CalculateDistance', '计算距离', NodeCategory.SPATIAL);

    this.addInput('coordinate1', 'GeographicCoordinate', '坐标1');
    this.addInput('coordinate2', 'GeographicCoordinate', '坐标2');

    this.addOutput('distance', 'number', '距离(米)');
    this.addOutput('distanceKm', 'number', '距离(公里)');
  }

  executeWithContext(context: ExecutionContext): void {
    const coord1 = this.getInputValueFromContext('coordinate1', context) as GeographicCoordinate;
    const coord2 = this.getInputValueFromContext('coordinate2', context) as GeographicCoordinate;
    
    if (coord1 && coord2) {
      const analysisEngine = SpatialAnalysisEngine.getInstance();
      const distance = analysisEngine.haversineDistance(coord1, coord2);
      
      this.setOutputValue('distance', distance, context);
      this.setOutputValue('distanceKm', distance / 1000, context);
    } else {
      this.setOutputValue('distance', 0, context);
      this.setOutputValue('distanceKm', 0, context);
    }
  }
}

/**
 * 缓冲区分析节点
 */
export class BufferAnalysisNode extends SpatialVisualScriptNode {
  constructor() {
    super('BufferAnalysis', '缓冲区分析', NodeCategory.SPATIAL);

    this.addInput('geometry', 'object', '输入几何');
    this.addInput('distance', 'number', '缓冲距离', 100);
    this.addInput('unit', 'string', '距离单位', 'meters');
    this.addInput('segments', 'number', '分段数', 32);
    this.addInput('execute', 'exec', '执行');

    this.addOutput('onComplete', 'exec', '完成');
    this.addOutput('result', 'object', '缓冲区几何');
    this.addOutput('success', 'boolean', '成功');
    this.addOutput('error', 'string', '错误信息');
  }

  executeWithContext(context: ExecutionContext): void {
    const geometry = this.getInputValueFromContext('geometry', context) as any;
    const distance = this.getInputValueFromContext('distance', context) as number;
    const unit = this.getInputValueFromContext('unit', context) as 'meters' | 'kilometers' | 'degrees';
    const segments = this.getInputValueFromContext('segments', context) as number;
    
    const params: BufferAnalysisParams = {
      distance,
      unit,
      segments
    };
    
    try {
      const analysisEngine = SpatialAnalysisEngine.getInstance();
      const result = analysisEngine.buffer(geometry, params);
      
      this.setOutputValue('result', result.result, context);
      this.setOutputValue('success', result.success, context);
      this.setOutputValue('error', result.error || '', context);
    } catch (error) {
      this.setOutputValue('result', null, context);
      this.setOutputValue('success', false, context);
      this.setOutputValue('error', error instanceof Error ? error.message : '分析失败', context);
    }
    
    this.executeOutput('onComplete', context);
  }
}

/**
 * 相交分析节点
 */
export class IntersectionAnalysisNode extends SpatialVisualScriptNode {
  constructor() {
    super('IntersectionAnalysis', '相交分析', NodeCategory.SPATIAL);

    this.addInput('geometry1', 'object', '几何1');
    this.addInput('geometry2', 'object', '几何2');
    this.addInput('execute', 'exec', '执行');

    this.addOutput('onComplete', 'exec', '完成');
    this.addOutput('result', 'object', '相交几何');
    this.addOutput('hasIntersection', 'boolean', '有相交');
    this.addOutput('success', 'boolean', '成功');
  }

  executeWithContext(context: ExecutionContext): void {
    const geometry1 = this.getInputValueFromContext('geometry1', context) as any;
    const geometry2 = this.getInputValueFromContext('geometry2', context) as any;
    
    try {
      const analysisEngine = SpatialAnalysisEngine.getInstance();
      const result = analysisEngine.intersection(geometry1, geometry2);
      
      this.setOutputValue('result', result.result, context);
      this.setOutputValue('hasIntersection', !!result.result, context);
      this.setOutputValue('success', result.success, context);
    } catch (error) {
      this.setOutputValue('result', null, context);
      this.setOutputValue('hasIntersection', false, context);
      this.setOutputValue('success', false, context);
    }
    
    this.executeOutput('onComplete', context);
  }
}

/**
 * 点在多边形内判断节点
 */
export class PointInPolygonNode extends SpatialVisualScriptNode {
  constructor() {
    super('PointInPolygon', '点在多边形内', NodeCategory.SPATIAL);

    this.addInput('point', 'GeographicCoordinate', '点坐标');
    this.addInput('polygon', 'array', '多边形坐标');

    this.addOutput('inside', 'boolean', '在内部');
  }

  executeWithContext(context: ExecutionContext): void {
    const point = this.getInputValueFromContext('point', context) as GeographicCoordinate;
    const polygon = this.getInputValueFromContext('polygon', context) as GeographicCoordinate[];
    
    if (point && polygon && polygon.length >= 3) {
      const analysisEngine = SpatialAnalysisEngine.getInstance();
      const pointArray = [point.longitude, point.latitude];
      const polygonArray = polygon.map(coord => [coord.longitude, coord.latitude]);
      
      const inside = analysisEngine.pointInPolygon(pointArray, polygonArray);
      this.setOutputValue('inside', inside, context);
    } else {
      this.setOutputValue('inside', false, context);
    }
  }
}

/**
 * 创建GeoJSON节点
 */
export class CreateGeoJSONNode extends SpatialVisualScriptNode {
  constructor() {
    super('CreateGeoJSON', '创建GeoJSON', NodeCategory.SPATIAL);

    this.addInput('component', 'GeospatialComponent', '地理空间组件');

    this.addOutput('geoJSON', 'object', 'GeoJSON对象');
    this.addOutput('geoJSONString', 'string', 'GeoJSON字符串');
  }

  executeWithContext(context: ExecutionContext): void {
    const component = this.getInputValueFromContext('component', context) as GeospatialComponent;
    
    if (component) {
      const geoJSON = component.toGeoJSON();
      
      this.setOutputValue('geoJSON', geoJSON, context);
      this.setOutputValue('geoJSONString', JSON.stringify(geoJSON, null, 2), context);
    } else {
      this.setOutputValue('geoJSON', null, context);
      this.setOutputValue('geoJSONString', '', context);
    }
  }
}

/**
 * 从GeoJSON创建组件节点
 */
export class CreateFromGeoJSONNode extends SpatialVisualScriptNode {
  constructor() {
    super('CreateFromGeoJSON', '从GeoJSON创建', NodeCategory.SPATIAL);

    this.addInput('geoJSON', 'object', 'GeoJSON对象');
    this.addInput('coordinateSystem', 'string', '坐标系', 'WGS84');

    this.addOutput('component', 'GeospatialComponent', '地理空间组件');
    this.addOutput('success', 'boolean', '成功');
  }

  executeWithContext(context: ExecutionContext): void {
    const geoJSON = this.getInputValueFromContext('geoJSON', context) as any;
    const coordinateSystem = this.getInputValueFromContext('coordinateSystem', context) as CoordinateSystemType;
    
    try {
      const component = GeospatialComponent.fromGeoJSON(geoJSON, coordinateSystem);
      
      this.setOutputValue('component', component, context);
      this.setOutputValue('success', true, context);
    } catch (error) {
      console.error('从GeoJSON创建组件失败:', error);
      this.setOutputValue('component', null, context);
      this.setOutputValue('success', false, context);
    }
  }
}

/**
 * 设置地图视图节点
 */
export class SetMapViewNode extends SpatialVisualScriptNode {
  constructor() {
    super('SetMapView', '设置地图视图', NodeCategory.SPATIAL);

    this.addInput('spatialSystem', 'SpatialInfoSystem', '空间系统');
    this.addInput('center', 'GeographicCoordinate', '中心坐标');
    this.addInput('zoom', 'number', '缩放级别', 10);
    this.addInput('execute', 'exec', '执行');

    this.addOutput('onComplete', 'exec', '完成');
    this.addOutput('success', 'boolean', '成功');
  }

  executeWithContext(context: ExecutionContext): void {
    const spatialSystem = this.getInputValueFromContext('spatialSystem', context) as any;
    const center = this.getInputValueFromContext('center', context) as GeographicCoordinate;
    const zoom = this.getInputValueFromContext('zoom', context) as number;

    try {
      spatialSystem.setMapView(center, zoom);
      this.setOutputValue('success', true, context);
    } catch (error) {
      console.error('设置地图视图失败:', error);
      this.setOutputValue('success', false, context);
    }

    this.executeOutput('onComplete', context);
  }
}

/**
 * 获取地图视图节点
 */
export class GetMapViewNode extends SpatialVisualScriptNode {
  constructor() {
    super('GetMapView', '获取地图视图', NodeCategory.SPATIAL);

    this.addInput('spatialSystem', 'SpatialInfoSystem', '空间系统');

    this.addOutput('center', 'GeographicCoordinate', '中心坐标');
    this.addOutput('zoom', 'number', '缩放级别');
    this.addOutput('hasView', 'boolean', '有视图');
  }

  executeWithContext(context: ExecutionContext): void {
    const spatialSystem = this.getInputValueFromContext('spatialSystem', context) as any;

    try {
      const view = spatialSystem.getMapView();

      if (view) {
        this.setOutputValue('center', view.center, context);
        this.setOutputValue('zoom', view.zoom, context);
        this.setOutputValue('hasView', true, context);
      } else {
        this.setOutputValue('center', null, context);
        this.setOutputValue('zoom', 0, context);
        this.setOutputValue('hasView', false, context);
      }
    } catch (error) {
      console.error('获取地图视图失败:', error);
      this.setOutputValue('center', null, context);
      this.setOutputValue('zoom', 0, context);
      this.setOutputValue('hasView', false, context);
    }
  }
}

/**
 * 设置地图提供者节点
 */
export class SetMapProviderNode extends SpatialVisualScriptNode {
  constructor() {
    super('SetMapProvider', '设置地图提供者', NodeCategory.SPATIAL);

    this.addInput('spatialSystem', 'SpatialInfoSystem', '空间系统');
    this.addInput('provider', 'string', '提供者', 'osm');
    this.addInput('execute', 'exec', '执行');

    this.addOutput('onComplete', 'exec', '完成');
    this.addOutput('success', 'boolean', '成功');
  }

  executeWithContext(context: ExecutionContext): void {
    const spatialSystem = this.getInputValueFromContext('spatialSystem', context) as any;
    const provider = this.getInputValueFromContext('provider', context) as string;

    try {
      spatialSystem.setTileProvider(provider);
      this.setOutputValue('success', true, context);
    } catch (error) {
      console.error('设置地图提供者失败:', error);
      this.setOutputValue('success', false, context);
    }

    this.executeOutput('onComplete', context);
  }
}

// 导出所有空间节点
export const SPATIAL_NODES = [
  CreateGeographicCoordinateNode,
  CoordinateTransformNode,
  CreateGeospatialComponentNode,
  AddGeospatialComponentNode,
  GetGeographicCoordinateNode,
  SetGeographicCoordinateNode,
  CalculateDistanceNode,
  BufferAnalysisNode,
  IntersectionAnalysisNode,
  PointInPolygonNode,
  CreateGeoJSONNode,
  CreateFromGeoJSONNode,
  SetMapViewNode,
  GetMapViewNode,
  SetMapProviderNode
];
