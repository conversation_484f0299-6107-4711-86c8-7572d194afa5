/**
 * 路径跟随组件测试
 */
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import * as THREE from 'three';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { PathFollowingComponent, PathFollowingState } from '../../src/navigation/components/PathFollowingComponent';
import { AvatarPath } from '../../src/navigation/path/AvatarPath';
import { PathPoint } from '../../src/navigation/path/PathPoint';
import { LoopMode, InterpolationType } from '../../src/navigation/types';

// Mock Transform组件
class MockTransform {
  private position = new THREE.Vector3();
  private rotation = new THREE.Quaternion();

  setPosition(x: number, y: number, z: number) {
    this.position.set(x, y, z);
  }

  getPosition() {
    return this.position.clone();
  }

  setRotationFromQuaternion(quaternion: THREE.Quaternion) {
    this.rotation.copy(quaternion);
  }

  getRotation() {
    return this.rotation.clone();
  }
}

describe('PathFollowingComponent', () => {
  let world: World;
  let entity: Entity;
  let component: PathFollowingComponent;
  let path: AvatarPath;
  let mockTransform: MockTransform;

  beforeEach(() => {
    // 创建测试环境
    world = new World();
    entity = new Entity('test_entity');
    
    // 添加Mock Transform组件
    mockTransform = new MockTransform();
    entity.getTransform = jest.fn().mockReturnValue(mockTransform);

    // 创建测试路径
    path = new AvatarPath({
      name: '测试路径',
      avatarId: 'test_avatar',
      loopMode: LoopMode.NONE,
      interpolation: InterpolationType.LINEAR
    });

    // 添加路径点
    path.addPoint(new PathPoint(new THREE.Vector3(0, 0, 0), {
      waitTime: 1,
      speed: 2.0,
      animation: 'idle'
    }));

    path.addPoint(new PathPoint(new THREE.Vector3(10, 0, 0), {
      waitTime: 0,
      speed: 2.0,
      animation: 'walk'
    }));

    path.addPoint(new PathPoint(new THREE.Vector3(10, 0, 10), {
      waitTime: 2,
      speed: 1.0,
      animation: 'idle'
    }));

    // 创建组件
    component = new PathFollowingComponent(entity, {
      path: path.toJSON(),
      paused: true // 初始暂停状态
    });
  });

  afterEach(() => {
    component?.destroy();
    jest.clearAllMocks();
  });

  describe('基本功能', () => {
    test('应该能够创建组件', () => {
      expect(component).toBeDefined();
      expect(component.getState()).toBe(PathFollowingState.STOPPED);
      expect(component.getProgress()).toBe(0);
    });

    test('应该能够设置路径', () => {
      const newPath = new AvatarPath({
        name: '新路径',
        avatarId: 'test_avatar',
        loopMode: LoopMode.LOOP,
        interpolation: InterpolationType.SMOOTH
      });

      newPath.addPoint(new PathPoint(new THREE.Vector3(0, 0, 0), {
        speed: 1.0,
        waitTime: 0,
        animation: 'walk'
      }));

      newPath.addPoint(new PathPoint(new THREE.Vector3(5, 0, 0), {
        speed: 1.0,
        waitTime: 0,
        animation: 'walk'
      }));

      component.setPath(newPath);
      expect(component.getPath()).toBeDefined();
    });

    test('应该能够开始跟随路径', () => {
      component.start();
      expect(component.getState()).toBe(PathFollowingState.RUNNING);
    });

    test('应该能够停止跟随路径', () => {
      component.start();
      component.stop();
      expect(component.getState()).toBe(PathFollowingState.STOPPED);
      expect(component.getProgress()).toBe(0);
    });

    test('应该能够暂停和恢复路径', () => {
      component.start();
      component.pause();
      expect(component.getState()).toBe(PathFollowingState.PAUSED);

      component.resume();
      expect(component.getState()).toBe(PathFollowingState.RUNNING);
    });
  });

  describe('路径跟随', () => {
    test('应该能够更新位置', () => {
      component.start();
      
      // 模拟时间流逝
      component.update(0.1); // 100ms
      
      // 检查位置是否更新
      const position = component.getCurrentPosition();
      expect(position).toBeDefined();
    });

    test('应该能够设置和获取进度', () => {
      component.setProgress(0.5);
      expect(component.getProgress()).toBeCloseTo(0.5, 2);
    });

    test('应该能够设置速度倍数', () => {
      component.setSpeedMultiplier(2.0);
      expect(component.getSpeedMultiplier()).toBe(2.0);
    });

    test('应该能够设置循环模式', () => {
      component.setLoop(true);
      // 验证循环设置（通过行为验证）
      component.start();
      
      // 快进到路径结束
      const totalDuration = path.totalDuration;
      component.update(totalDuration + 1);
      
      // 在循环模式下，状态应该仍然是RUNNING
      expect(component.getState()).toBe(PathFollowingState.RUNNING);
    });
  });

  describe('事件系统', () => {
    test('应该触发路径开始事件', (done) => {
      component.on('pathStarted', (data) => {
        expect(data).toBeDefined();
        expect(data.type).toBe('pathStarted');
        done();
      });

      component.start();
    });

    test('应该触发路径完成事件', (done) => {
      component.on('pathCompleted', (data) => {
        expect(data).toBeDefined();
        expect(data.type).toBe('pathCompleted');
        done();
      });

      component.start();
      
      // 快进到路径结束
      const totalDuration = path.totalDuration;
      component.update(totalDuration + 1);
    });

    test('应该触发路径点到达事件', (done) => {
      let waypointCount = 0;
      
      component.on('waypointReached', (data) => {
        waypointCount++;
        expect(data).toBeDefined();
        expect(data.currentWaypointIndex).toBeGreaterThanOrEqual(0);
        
        if (waypointCount >= 2) {
          done();
        }
      });

      component.start();
      
      // 逐步更新以触发路径点事件
      for (let i = 0; i < 100; i++) {
        component.update(0.1);
      }
    });

    test('应该能够移除事件监听', () => {
      let eventCount = 0;
      
      const handler = () => {
        eventCount++;
      };

      component.on('pathStarted', handler);
      component.start();
      expect(eventCount).toBe(1);

      component.off('pathStarted', handler);
      component.stop();
      component.start();
      expect(eventCount).toBe(1); // 应该没有增加
    });
  });

  describe('状态管理', () => {
    test('应该正确管理状态转换', () => {
      // 初始状态
      expect(component.getState()).toBe(PathFollowingState.STOPPED);

      // 开始 -> 运行
      component.start();
      expect(component.getState()).toBe(PathFollowingState.RUNNING);

      // 暂停 -> 暂停
      component.pause();
      expect(component.getState()).toBe(PathFollowingState.PAUSED);

      // 恢复 -> 运行
      component.resume();
      expect(component.getState()).toBe(PathFollowingState.RUNNING);

      // 停止 -> 停止
      component.stop();
      expect(component.getState()).toBe(PathFollowingState.STOPPED);
    });

    test('应该在无效路径时保持停止状态', () => {
      // 创建无效路径（只有一个点）
      const invalidPath = new AvatarPath({
        name: '无效路径',
        avatarId: 'test_avatar',
        loopMode: LoopMode.NONE,
        interpolation: InterpolationType.LINEAR
      });

      invalidPath.addPoint(new PathPoint(new THREE.Vector3(0, 0, 0), {
        speed: 1.0,
        waitTime: 0,
        animation: 'idle'
      }));

      component.setPath(invalidPath);
      component.start();
      
      // 应该保持停止状态
      expect(component.getState()).toBe(PathFollowingState.STOPPED);
    });
  });

  describe('位置和旋转', () => {
    test('应该正确更新实体位置', () => {
      component.start();
      component.update(0.1);

      // 验证Transform的setPosition被调用
      expect(entity.getTransform().setPosition).toHaveBeenCalled();
    });

    test('应该正确更新实体旋转', () => {
      component.start();
      component.update(0.1);

      // 验证Transform的setRotationFromQuaternion被调用
      expect(entity.getTransform().setRotationFromQuaternion).toHaveBeenCalled();
    });

    test('应该能够获取当前位置', () => {
      component.start();
      component.update(0.1);

      const position = component.getCurrentPosition();
      expect(position).toBeInstanceOf(THREE.Vector3);
    });

    test('应该能够获取当前旋转', () => {
      component.start();
      component.update(0.1);

      const rotation = component.getCurrentRotation();
      expect(rotation).toBeInstanceOf(THREE.Quaternion);
    });

    test('应该能够获取当前速度', () => {
      component.start();
      component.update(0.1);

      const speed = component.getCurrentSpeed();
      expect(typeof speed).toBe('number');
      expect(speed).toBeGreaterThanOrEqual(0);
    });

    test('应该能够获取当前动画', () => {
      component.start();
      component.update(0.1);

      const animation = component.getCurrentAnimation();
      expect(typeof animation).toBe('string');
    });
  });

  describe('性能测试', () => {
    test('更新应该高效', () => {
      component.start();
      
      const startTime = performance.now();
      
      // 执行1000次更新
      for (let i = 0; i < 1000; i++) {
        component.update(0.016); // 60 FPS
      }
      
      const updateTime = performance.now() - startTime;
      expect(updateTime).toBeLessThan(100); // 应该在100ms内完成
    });

    test('应该能够处理高频率更新', () => {
      component.start();
      
      // 模拟高频率更新（120 FPS）
      for (let i = 0; i < 120; i++) {
        component.update(1/120);
      }
      
      // 应该仍然正常工作
      expect(component.getState()).toBe(PathFollowingState.RUNNING);
      expect(component.getProgress()).toBeGreaterThan(0);
    });
  });

  describe('边界条件', () => {
    test('应该处理空路径', () => {
      const emptyPath = new AvatarPath({
        name: '空路径',
        avatarId: 'test_avatar',
        loopMode: LoopMode.NONE,
        interpolation: InterpolationType.LINEAR
      });

      component.setPath(emptyPath);
      component.start();
      
      expect(component.getState()).toBe(PathFollowingState.STOPPED);
    });

    test('应该处理负时间增量', () => {
      component.start();
      
      // 负时间增量不应该导致错误
      expect(() => {
        component.update(-0.1);
      }).not.toThrow();
    });

    test('应该处理极大的时间增量', () => {
      component.start();
      
      // 极大的时间增量不应该导致错误
      expect(() => {
        component.update(1000);
      }).not.toThrow();
    });

    test('应该处理进度边界值', () => {
      // 测试进度边界值
      component.setProgress(-1);
      expect(component.getProgress()).toBe(0);

      component.setProgress(2);
      expect(component.getProgress()).toBe(1);
    });
  });

  describe('内存管理', () => {
    test('销毁应该清理资源', () => {
      component.start();
      
      // 添加事件监听器
      const handler = jest.fn();
      component.on('pathCompleted', handler);
      
      // 销毁组件
      component.destroy();
      
      // 验证状态被重置
      expect(component.getState()).toBe(PathFollowingState.STOPPED);
      
      // 验证事件监听器被移除（通过触发事件验证）
      component.start(); // 这应该不会触发事件
      expect(handler).not.toHaveBeenCalled();
    });
  });
});
