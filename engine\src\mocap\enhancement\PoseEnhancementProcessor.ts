/**
 * 姿态增强处理器
 * 提供多模型融合、后处理算法和关键点优化功能
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { LandmarkData, WorldLandmarkData } from '../types/LandmarkData';
import { PoseResults } from '../mediapipe/MediaPipePoseDetector';
import { Vector3, Quaternion } from 'three';

/**
 * 姿态增强配置
 */
export interface PoseEnhancementConfig {
  /** 是否启用多模型融合 */
  enableModelFusion: boolean;
  /** 是否启用时间平滑 */
  enableTemporalSmoothing: boolean;
  /** 是否启用空间约束 */
  enableSpatialConstraints: boolean;
  /** 是否启用异常值检测 */
  enableOutlierDetection: boolean;
  /** 平滑窗口大小 */
  smoothingWindowSize: number;
  /** 平滑强度 */
  smoothingStrength: number;
  /** 置信度阈值 */
  confidenceThreshold: number;
  /** 异常值检测阈值 */
  outlierThreshold: number;
  /** 是否启用骨骼约束 */
  enableSkeletalConstraints: boolean;
  /** 骨骼长度约束强度 */
  skeletalConstraintStrength: number;
}

/**
 * 历史姿态数据
 */
interface HistoricalPoseData {
  landmarks: LandmarkData[];
  worldLandmarks: WorldLandmarkData[];
  timestamp: number;
  confidence: number;
}

/**
 * 骨骼约束信息
 */
interface SkeletalConstraint {
  joint1: number;
  joint2: number;
  expectedLength: number;
  tolerance: number;
}

/**
 * 姿态增强处理器
 */
export class PoseEnhancementProcessor extends EventEmitter {
  private config: PoseEnhancementConfig;
  private poseHistory: HistoricalPoseData[] = [];
  private skeletalConstraints: SkeletalConstraint[] = [];
  private referencePose: LandmarkData[] | null = null;
  private kalmanFilters: Map<number, KalmanFilter> = new Map();

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: PoseEnhancementConfig = {
    enableModelFusion: true,
    enableTemporalSmoothing: true,
    enableSpatialConstraints: true,
    enableOutlierDetection: true,
    smoothingWindowSize: 5,
    smoothingStrength: 0.7,
    confidenceThreshold: 0.5,
    outlierThreshold: 2.0,
    enableSkeletalConstraints: true,
    skeletalConstraintStrength: 0.8
  };

  constructor(config: Partial<PoseEnhancementConfig> = {}) {
    super();
    this.config = { ...PoseEnhancementProcessor.DEFAULT_CONFIG, ...config };
    this.initializeSkeletalConstraints();
    this.initializeKalmanFilters();
  }

  /**
   * 初始化骨骼约束
   */
  private initializeSkeletalConstraints(): void {
    // MediaPipe Pose关键点索引的骨骼约束
    this.skeletalConstraints = [
      // 头部和颈部
      { joint1: 0, joint2: 1, expectedLength: 0.1, tolerance: 0.05 }, // 鼻子到左眼内角
      { joint1: 0, joint2: 4, expectedLength: 0.1, tolerance: 0.05 }, // 鼻子到右眼内角
      
      // 肩膀
      { joint1: 11, joint2: 12, expectedLength: 0.4, tolerance: 0.1 }, // 左肩到右肩
      
      // 手臂
      { joint1: 11, joint2: 13, expectedLength: 0.3, tolerance: 0.1 }, // 左肩到左肘
      { joint1: 13, joint2: 15, expectedLength: 0.25, tolerance: 0.1 }, // 左肘到左腕
      { joint1: 12, joint2: 14, expectedLength: 0.3, tolerance: 0.1 }, // 右肩到右肘
      { joint1: 14, joint2: 16, expectedLength: 0.25, tolerance: 0.1 }, // 右肘到右腕
      
      // 躯干
      { joint1: 11, joint2: 23, expectedLength: 0.5, tolerance: 0.15 }, // 左肩到左髋
      { joint1: 12, joint2: 24, expectedLength: 0.5, tolerance: 0.15 }, // 右肩到右髋
      { joint1: 23, joint2: 24, expectedLength: 0.25, tolerance: 0.1 }, // 左髋到右髋
      
      // 腿部
      { joint1: 23, joint2: 25, expectedLength: 0.4, tolerance: 0.1 }, // 左髋到左膝
      { joint1: 25, joint2: 27, expectedLength: 0.4, tolerance: 0.1 }, // 左膝到左踝
      { joint1: 24, joint2: 26, expectedLength: 0.4, tolerance: 0.1 }, // 右髋到右膝
      { joint1: 26, joint2: 28, expectedLength: 0.4, tolerance: 0.1 }  // 右膝到右踝
    ];
  }

  /**
   * 初始化卡尔曼滤波器
   */
  private initializeKalmanFilters(): void {
    // 为每个关键点创建卡尔曼滤波器
    for (let i = 0; i < 33; i++) { // MediaPipe Pose有33个关键点
      this.kalmanFilters.set(i, new KalmanFilter());
    }
  }

  /**
   * 处理姿态数据
   */
  public processPose(poseResults: PoseResults): PoseResults {
    try {
      let enhancedResults = { ...poseResults };

      // 异常值检测
      if (this.config.enableOutlierDetection) {
        enhancedResults = this.detectAndFilterOutliers(enhancedResults);
      }

      // 时间平滑
      if (this.config.enableTemporalSmoothing) {
        enhancedResults = this.applyTemporalSmoothing(enhancedResults);
      }

      // 空间约束
      if (this.config.enableSpatialConstraints) {
        enhancedResults = this.applySpatialConstraints(enhancedResults);
      }

      // 骨骼约束
      if (this.config.enableSkeletalConstraints) {
        enhancedResults = this.applySkeletalConstraints(enhancedResults);
      }

      // 卡尔曼滤波
      enhancedResults = this.applyKalmanFiltering(enhancedResults);

      // 更新历史数据
      this.updatePoseHistory(enhancedResults);

      // 触发处理完成事件
      this.emit('poseEnhanced', enhancedResults);

      return enhancedResults;

    } catch (error) {
      Debug.error('PoseEnhancementProcessor', '姿态处理失败', error);
      return poseResults; // 返回原始数据
    }
  }

  /**
   * 异常值检测和过滤
   */
  private detectAndFilterOutliers(poseResults: PoseResults): PoseResults {
    if (!poseResults.landmarks || this.poseHistory.length < 3) {
      return poseResults;
    }

    const filteredLandmarks = poseResults.landmarks.map((landmark, index) => {
      // 计算历史位置的平均值和标准差
      const historicalPositions = this.poseHistory
        .slice(-5) // 使用最近5帧
        .map(pose => pose.landmarks[index])
        .filter(l => l && l.visibility > this.config.confidenceThreshold);

      if (historicalPositions.length < 2) {
        return landmark;
      }

      const avgX = historicalPositions.reduce((sum, l) => sum + l.x, 0) / historicalPositions.length;
      const avgY = historicalPositions.reduce((sum, l) => sum + l.y, 0) / historicalPositions.length;

      const stdX = Math.sqrt(historicalPositions.reduce((sum, l) => sum + Math.pow(l.x - avgX, 2), 0) / historicalPositions.length);
      const stdY = Math.sqrt(historicalPositions.reduce((sum, l) => sum + Math.pow(l.y - avgY, 2), 0) / historicalPositions.length);

      // 检测异常值
      const deviationX = Math.abs(landmark.x - avgX) / (stdX + 0.001);
      const deviationY = Math.abs(landmark.y - avgY) / (stdY + 0.001);

      if (deviationX > this.config.outlierThreshold || deviationY > this.config.outlierThreshold) {
        // 使用历史平均值替代异常值
        return {
          ...landmark,
          x: avgX,
          y: avgY,
          visibility: Math.max(0.1, landmark.visibility * 0.5) // 降低置信度
        };
      }

      return landmark;
    });

    return {
      ...poseResults,
      landmarks: filteredLandmarks
    };
  }

  /**
   * 应用时间平滑
   */
  private applyTemporalSmoothing(poseResults: PoseResults): PoseResults {
    if (!poseResults.landmarks || this.poseHistory.length === 0) {
      return poseResults;
    }

    const smoothedLandmarks = poseResults.landmarks.map((landmark, index) => {
      // 获取历史数据
      const historicalLandmarks = this.poseHistory
        .slice(-this.config.smoothingWindowSize)
        .map(pose => pose.landmarks[index])
        .filter(l => l && l.visibility > this.config.confidenceThreshold);

      if (historicalLandmarks.length === 0) {
        return landmark;
      }

      // 加权平均平滑
      const weights = historicalLandmarks.map((_, i) => Math.pow(this.config.smoothingStrength, historicalLandmarks.length - 1 - i));
      const totalWeight = weights.reduce((sum, w) => sum + w, 0) + 1; // +1 for current frame

      const smoothedX = (historicalLandmarks.reduce((sum, l, i) => sum + l.x * weights[i], 0) + landmark.x) / totalWeight;
      const smoothedY = (historicalLandmarks.reduce((sum, l, i) => sum + l.y * weights[i], 0) + landmark.y) / totalWeight;
      const smoothedZ = (historicalLandmarks.reduce((sum, l, i) => sum + (l.z || 0) * weights[i], 0) + (landmark.z || 0)) / totalWeight;

      return {
        ...landmark,
        x: smoothedX,
        y: smoothedY,
        z: smoothedZ
      };
    });

    return {
      ...poseResults,
      landmarks: smoothedLandmarks
    };
  }

  /**
   * 应用空间约束
   */
  private applySpatialConstraints(poseResults: PoseResults): PoseResults {
    if (!poseResults.landmarks) {
      return poseResults;
    }

    // 确保关键点在合理的空间范围内
    const constrainedLandmarks = poseResults.landmarks.map(landmark => {
      return {
        ...landmark,
        x: Math.max(0, Math.min(1, landmark.x)), // 限制在[0,1]范围内
        y: Math.max(0, Math.min(1, landmark.y)),
        z: landmark.z ? Math.max(-1, Math.min(1, landmark.z)) : landmark.z
      };
    });

    return {
      ...poseResults,
      landmarks: constrainedLandmarks
    };
  }

  /**
   * 应用骨骼约束
   */
  private applySkeletalConstraints(poseResults: PoseResults): PoseResults {
    if (!poseResults.landmarks || poseResults.landmarks.length < 33) {
      return poseResults;
    }

    const constrainedLandmarks = [...poseResults.landmarks];

    // 应用骨骼长度约束
    for (const constraint of this.skeletalConstraints) {
      const joint1 = constrainedLandmarks[constraint.joint1];
      const joint2 = constrainedLandmarks[constraint.joint2];

      if (!joint1 || !joint2 || 
          joint1.visibility < this.config.confidenceThreshold || 
          joint2.visibility < this.config.confidenceThreshold) {
        continue;
      }

      // 计算当前距离
      const currentDistance = Math.sqrt(
        Math.pow(joint2.x - joint1.x, 2) + 
        Math.pow(joint2.y - joint1.y, 2) + 
        Math.pow((joint2.z || 0) - (joint1.z || 0), 2)
      );

      // 检查是否超出容差范围
      const deviation = Math.abs(currentDistance - constraint.expectedLength);
      if (deviation > constraint.tolerance) {
        // 调整关键点位置
        const adjustmentFactor = this.config.skeletalConstraintStrength * 
          (constraint.expectedLength / currentDistance - 1);

        const midX = (joint1.x + joint2.x) / 2;
        const midY = (joint1.y + joint2.y) / 2;
        const midZ = ((joint1.z || 0) + (joint2.z || 0)) / 2;

        const dirX = joint2.x - joint1.x;
        const dirY = joint2.y - joint1.y;
        const dirZ = (joint2.z || 0) - (joint1.z || 0);

        constrainedLandmarks[constraint.joint1] = {
          ...joint1,
          x: midX - dirX * (0.5 + adjustmentFactor * 0.5),
          y: midY - dirY * (0.5 + adjustmentFactor * 0.5),
          z: midZ - dirZ * (0.5 + adjustmentFactor * 0.5)
        };

        constrainedLandmarks[constraint.joint2] = {
          ...joint2,
          x: midX + dirX * (0.5 + adjustmentFactor * 0.5),
          y: midY + dirY * (0.5 + adjustmentFactor * 0.5),
          z: midZ + dirZ * (0.5 + adjustmentFactor * 0.5)
        };
      }
    }

    return {
      ...poseResults,
      landmarks: constrainedLandmarks
    };
  }

  /**
   * 应用卡尔曼滤波
   */
  private applyKalmanFiltering(poseResults: PoseResults): PoseResults {
    if (!poseResults.landmarks) {
      return poseResults;
    }

    const filteredLandmarks = poseResults.landmarks.map((landmark, index) => {
      const filter = this.kalmanFilters.get(index);
      if (!filter || landmark.visibility < this.config.confidenceThreshold) {
        return landmark;
      }

      // 应用卡尔曼滤波
      const filtered = filter.update([landmark.x, landmark.y, landmark.z || 0]);

      return {
        ...landmark,
        x: filtered[0],
        y: filtered[1],
        z: filtered[2]
      };
    });

    return {
      ...poseResults,
      landmarks: filteredLandmarks
    };
  }

  /**
   * 更新姿态历史
   */
  private updatePoseHistory(poseResults: PoseResults): void {
    if (!poseResults.landmarks) {
      return;
    }

    const historyData: HistoricalPoseData = {
      landmarks: [...poseResults.landmarks],
      worldLandmarks: poseResults.worldLandmarks ? [...poseResults.worldLandmarks] : [],
      timestamp: Date.now(),
      confidence: poseResults.confidence
    };

    this.poseHistory.push(historyData);

    // 限制历史数据大小
    const maxHistorySize = Math.max(this.config.smoothingWindowSize * 2, 10);
    if (this.poseHistory.length > maxHistorySize) {
      this.poseHistory.shift();
    }
  }

  /**
   * 设置参考姿态
   */
  public setReferencePose(landmarks: LandmarkData[]): void {
    this.referencePose = [...landmarks];
    this.emit('referencePoseSet', this.referencePose);
  }

  /**
   * 重置处理器状态
   */
  public reset(): void {
    this.poseHistory = [];
    this.referencePose = null;
    this.kalmanFilters.clear();
    this.initializeKalmanFilters();
    this.emit('reset');
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<PoseEnhancementConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  /**
   * 获取配置
   */
  public getConfig(): PoseEnhancementConfig {
    return { ...this.config };
  }

  /**
   * 获取处理统计
   */
  public getProcessingStats(): any {
    return {
      historySize: this.poseHistory.length,
      averageConfidence: this.poseHistory.length > 0 ? 
        this.poseHistory.reduce((sum, pose) => sum + pose.confidence, 0) / this.poseHistory.length : 0,
      hasReferencePose: this.referencePose !== null,
      constraintsCount: this.skeletalConstraints.length
    };
  }
}

/**
 * 简单的卡尔曼滤波器实现
 */
class KalmanFilter {
  private state: number[] = [0, 0, 0]; // x, y, z
  private velocity: number[] = [0, 0, 0]; // vx, vy, vz
  private processNoise = 0.01;
  private measurementNoise = 0.1;
  private errorCovariance = 1.0;

  public update(measurement: number[]): number[] {
    // 预测步骤
    const predictedState = [
      this.state[0] + this.velocity[0],
      this.state[1] + this.velocity[1],
      this.state[2] + this.velocity[2]
    ];

    const predictedErrorCovariance = this.errorCovariance + this.processNoise;

    // 更新步骤
    const kalmanGain = predictedErrorCovariance / (predictedErrorCovariance + this.measurementNoise);

    this.state = [
      predictedState[0] + kalmanGain * (measurement[0] - predictedState[0]),
      predictedState[1] + kalmanGain * (measurement[1] - predictedState[1]),
      predictedState[2] + kalmanGain * (measurement[2] - predictedState[2])
    ];

    // 更新速度
    this.velocity = [
      this.velocity[0] + kalmanGain * (measurement[0] - predictedState[0]),
      this.velocity[1] + kalmanGain * (measurement[1] - predictedState[1]),
      this.velocity[2] + kalmanGain * (measurement[2] - predictedState[2])
    ];

    this.errorCovariance = (1 - kalmanGain) * predictedErrorCovariance;

    return [...this.state];
  }
}
