/**
 * 测试数字人路径系统修复
 */
import { AvatarPathSystem } from './engine/src/navigation/systems/AvatarPathSystem';
import { AvatarPathComponent } from './engine/src/navigation/components/AvatarPathComponent';
import { PathFollowingComponent } from './engine/src/navigation/components/PathFollowingComponent';
import { Entity } from './engine/src/core/Entity';

// 测试组件实例化
console.log('测试开始...');

try {
  // 创建实体
  const entity = new Entity('test-entity');
  
  // 测试 AvatarPathComponent
  const pathComponent = new AvatarPathComponent(entity, {
    autoStart: false,
    debug: true
  });
  
  console.log('AvatarPathComponent 创建成功');
  
  // 测试 PathFollowingComponent
  const followingComponent = new PathFollowingComponent(entity, {
    loop: false,
    speedMultiplier: 1.0
  });
  
  console.log('PathFollowingComponent 创建成功');
  
  // 测试 AvatarPathSystem
  const pathSystem = new AvatarPathSystem({
    debug: true,
    enableValidation: true
  });
  
  console.log('AvatarPathSystem 创建成功');
  
  // 测试 createInstance 方法
  const clonedPathComponent = pathComponent.createInstance();
  console.log('AvatarPathComponent.createInstance() 成功');
  
  const clonedFollowingComponent = followingComponent.createInstance();
  console.log('PathFollowingComponent.createInstance() 成功');
  
  console.log('所有测试通过！');
  
} catch (error) {
  console.error('测试失败:', error);
}
