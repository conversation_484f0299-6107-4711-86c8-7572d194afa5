#!/usr/bin/env node

/**
 * UI组件兼容性测试脚本
 * 测试不同浏览器、设备和屏幕分辨率的兼容性
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${title}`, 'bright');
  log('='.repeat(60), 'cyan');
}

// 测试配置
const testConfigurations = [
  // 桌面端配置
  {
    name: '桌面端 - 1920x1080',
    viewport: { width: 1920, height: 1080 },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    deviceType: 'desktop'
  },
  {
    name: '桌面端 - 1366x768',
    viewport: { width: 1366, height: 768 },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    deviceType: 'desktop'
  },
  {
    name: '桌面端 - 1280x720',
    viewport: { width: 1280, height: 720 },
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    deviceType: 'desktop'
  },
  // 平板端配置
  {
    name: '平板端 - iPad',
    viewport: { width: 768, height: 1024 },
    userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
    deviceType: 'tablet'
  },
  {
    name: '平板端 - iPad Pro',
    viewport: { width: 1024, height: 1366 },
    userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
    deviceType: 'tablet'
  },
  // 移动端配置
  {
    name: '移动端 - iPhone 12',
    viewport: { width: 390, height: 844 },
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
    deviceType: 'mobile'
  },
  {
    name: '移动端 - Samsung Galaxy',
    viewport: { width: 360, height: 640 },
    userAgent: 'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
    deviceType: 'mobile'
  }
];

// 兼容性测试用例
const compatibilityTests = [
  {
    name: 'CSS Grid布局支持',
    description: '测试CSS Grid布局在不同设备上的表现',
    test: async (page) => {
      await page.setContent(`
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            .grid-container {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 16px;
              padding: 16px;
            }
            .grid-item {
              background: #1890ff;
              color: white;
              padding: 20px;
              text-align: center;
              border-radius: 4px;
            }
          </style>
        </head>
        <body>
          <div class="grid-container">
            <div class="grid-item">Item 1</div>
            <div class="grid-item">Item 2</div>
            <div class="grid-item">Item 3</div>
            <div class="grid-item">Item 4</div>
          </div>
        </body>
        </html>
      `);
      
      const gridSupport = await page.evaluate(() => {
        const container = document.querySelector('.grid-container');
        const computedStyle = window.getComputedStyle(container);
        return computedStyle.display === 'grid';
      });
      
      const itemsLayout = await page.evaluate(() => {
        const items = document.querySelectorAll('.grid-item');
        const positions = Array.from(items).map(item => {
          const rect = item.getBoundingClientRect();
          return { x: rect.left, y: rect.top, width: rect.width, height: rect.height };
        });
        return positions;
      });
      
      return {
        gridSupport,
        itemsCount: itemsLayout.length,
        passed: gridSupport && itemsLayout.length === 4
      };
    }
  },
  {
    name: 'Flexbox布局支持',
    description: '测试Flexbox布局的兼容性',
    test: async (page) => {
      await page.setContent(`
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            .flex-container {
              display: flex;
              flex-direction: row;
              justify-content: space-between;
              align-items: center;
              padding: 16px;
              background: #f0f0f0;
            }
            .flex-item {
              flex: 1;
              background: #52c41a;
              color: white;
              padding: 16px;
              margin: 0 8px;
              text-align: center;
              border-radius: 4px;
            }
          </style>
        </head>
        <body>
          <div class="flex-container">
            <div class="flex-item">Flex 1</div>
            <div class="flex-item">Flex 2</div>
            <div class="flex-item">Flex 3</div>
          </div>
        </body>
        </html>
      `);
      
      const flexSupport = await page.evaluate(() => {
        const container = document.querySelector('.flex-container');
        const computedStyle = window.getComputedStyle(container);
        return computedStyle.display === 'flex';
      });
      
      const itemsAlignment = await page.evaluate(() => {
        const items = document.querySelectorAll('.flex-item');
        const container = document.querySelector('.flex-container');
        const containerRect = container.getBoundingClientRect();
        
        let alignedProperly = true;
        let previousRight = 0;
        
        items.forEach((item, index) => {
          const rect = item.getBoundingClientRect();
          
          // 检查垂直居中
          const itemCenter = rect.top + rect.height / 2;
          const containerCenter = containerRect.top + containerRect.height / 2;
          if (Math.abs(itemCenter - containerCenter) > 5) {
            alignedProperly = false;
          }
          
          // 检查水平间距
          if (index > 0 && rect.left <= previousRight) {
            alignedProperly = false;
          }
          previousRight = rect.right;
        });
        
        return alignedProperly;
      });
      
      return {
        flexSupport,
        itemsAlignment,
        passed: flexSupport && itemsAlignment
      };
    }
  },
  {
    name: 'CSS变量支持',
    description: '测试CSS自定义属性的支持情况',
    test: async (page) => {
      await page.setContent(`
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            :root {
              --primary-color: #1890ff;
              --secondary-color: #52c41a;
              --border-radius: 8px;
              --spacing: 16px;
            }
            .css-vars-test {
              background: var(--primary-color);
              color: white;
              border-radius: var(--border-radius);
              padding: var(--spacing);
              margin: var(--spacing);
            }
            .css-vars-test:hover {
              background: var(--secondary-color);
            }
          </style>
        </head>
        <body>
          <div class="css-vars-test">CSS Variables Test</div>
        </body>
        </html>
      `);
      
      const cssVarsSupport = await page.evaluate(() => {
        const element = document.querySelector('.css-vars-test');
        const computedStyle = window.getComputedStyle(element);
        
        // 检查CSS变量是否被正确解析
        const backgroundColor = computedStyle.backgroundColor;
        const borderRadius = computedStyle.borderRadius;
        const padding = computedStyle.padding;
        
        return {
          backgroundColor,
          borderRadius,
          padding,
          supported: backgroundColor.includes('rgb') && borderRadius !== '0px' && padding !== '0px'
        };
      });
      
      return {
        ...cssVarsSupport,
        passed: cssVarsSupport.supported
      };
    }
  },
  {
    name: '触摸事件支持',
    description: '测试触摸事件的支持情况',
    test: async (page) => {
      await page.setContent(`
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            .touch-test {
              width: 200px;
              height: 200px;
              background: #fa8c16;
              color: white;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 8px;
              user-select: none;
              touch-action: manipulation;
            }
          </style>
        </head>
        <body>
          <div class="touch-test" id="touchTest">Touch Test</div>
          <script>
            window.touchEvents = {
              touchstart: false,
              touchmove: false,
              touchend: false
            };
            
            const element = document.getElementById('touchTest');
            
            element.addEventListener('touchstart', () => {
              window.touchEvents.touchstart = true;
            });
            
            element.addEventListener('touchmove', () => {
              window.touchEvents.touchmove = true;
            });
            
            element.addEventListener('touchend', () => {
              window.touchEvents.touchend = true;
            });
          </script>
        </body>
        </html>
      `);
      
      // 检查触摸事件API是否存在
      const touchAPISupport = await page.evaluate(() => {
        return {
          touchSupported: 'ontouchstart' in window,
          touchEventsSupported: typeof TouchEvent !== 'undefined'
        };
      });
      
      // 模拟触摸事件（如果支持）
      if (touchAPISupport.touchSupported) {
        const element = await page.$('#touchTest');
        const box = await element.boundingBox();
        
        // 模拟触摸序列
        await page.touchscreen.tap(box.x + box.width / 2, box.y + box.height / 2);
      }
      
      const touchEventResults = await page.evaluate(() => window.touchEvents);
      
      return {
        ...touchAPISupport,
        ...touchEventResults,
        passed: touchAPISupport.touchSupported || touchAPISupport.touchEventsSupported
      };
    }
  },
  {
    name: '响应式设计测试',
    description: '测试响应式布局的适配性',
    test: async (page) => {
      await page.setContent(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            .responsive-container {
              display: flex;
              flex-wrap: wrap;
              gap: 16px;
              padding: 16px;
            }
            .responsive-item {
              flex: 1 1 300px;
              min-width: 250px;
              background: #722ed1;
              color: white;
              padding: 20px;
              border-radius: 8px;
              text-align: center;
            }
            
            @media (max-width: 768px) {
              .responsive-item {
                flex: 1 1 100%;
                min-width: auto;
              }
            }
            
            @media (max-width: 480px) {
              .responsive-container {
                padding: 8px;
                gap: 8px;
              }
              .responsive-item {
                padding: 12px;
              }
            }
          </style>
        </head>
        <body>
          <div class="responsive-container">
            <div class="responsive-item">Item 1</div>
            <div class="responsive-item">Item 2</div>
            <div class="responsive-item">Item 3</div>
          </div>
        </body>
        </html>
      `);
      
      const viewport = page.viewport();
      const responsiveLayout = await page.evaluate((viewportWidth) => {
        const container = document.querySelector('.responsive-container');
        const items = document.querySelectorAll('.responsive-item');
        
        const containerRect = container.getBoundingClientRect();
        const itemRects = Array.from(items).map(item => item.getBoundingClientRect());
        
        // 检查布局是否合理
        let layoutValid = true;
        let itemsPerRow = 0;
        let currentRowY = itemRects[0].top;
        
        itemRects.forEach(rect => {
          if (Math.abs(rect.top - currentRowY) < 10) {
            itemsPerRow++;
          } else {
            // 新行
            currentRowY = rect.top;
          }
          
          // 检查是否溢出容器
          if (rect.right > containerRect.right + 5) {
            layoutValid = false;
          }
        });
        
        return {
          viewportWidth,
          containerWidth: containerRect.width,
          itemCount: itemRects.length,
          itemsPerRow,
          layoutValid,
          expectedLayout: viewportWidth <= 480 ? 'single-column' : 
                          viewportWidth <= 768 ? 'responsive' : 'multi-column'
        };
      }, viewport.width);
      
      return {
        ...responsiveLayout,
        passed: responsiveLayout.layoutValid
      };
    }
  }
];

// 运行兼容性测试
async function runCompatibilityTests() {
  logSection('UI组件兼容性测试');
  
  const allResults = [];
  
  for (const config of testConfigurations) {
    log(`\n测试配置: ${config.name}`, 'yellow');
    log(`分辨率: ${config.viewport.width}x${config.viewport.height}`, 'blue');
    log(`设备类型: ${config.deviceType}`, 'blue');
    
    let browser;
    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      
      const page = await browser.newPage();
      await page.setViewport(config.viewport);
      await page.setUserAgent(config.userAgent);
      
      const configResults = {
        config: config.name,
        viewport: config.viewport,
        deviceType: config.deviceType,
        tests: []
      };
      
      // 运行每个测试用例
      for (const test of compatibilityTests) {
        try {
          log(`  运行: ${test.name}`, 'cyan');
          const result = await test.test(page);
          
          result.testName = test.name;
          result.description = test.description;
          configResults.tests.push(result);
          
          const status = result.passed ? '✅' : '❌';
          log(`  结果: ${status}`, result.passed ? 'green' : 'red');
          
        } catch (error) {
          log(`  测试失败: ${error.message}`, 'red');
          configResults.tests.push({
            testName: test.name,
            description: test.description,
            passed: false,
            error: error.message
          });
        }
      }
      
      allResults.push(configResults);
      
    } catch (error) {
      log(`配置测试失败: ${error.message}`, 'red');
      allResults.push({
        config: config.name,
        error: error.message,
        tests: []
      });
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }
  
  // 生成报告
  generateCompatibilityReport(allResults);
  
  return allResults;
}

// 生成兼容性测试报告
function generateCompatibilityReport(results) {
  logSection('兼容性测试报告');
  
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;
  
  results.forEach(configResult => {
    if (configResult.tests) {
      totalTests += configResult.tests.length;
      passedTests += configResult.tests.filter(t => t.passed).length;
      failedTests += configResult.tests.filter(t => !t.passed).length;
    }
  });
  
  log(`总测试数: ${totalTests}`, 'bright');
  log(`通过: ${passedTests}`, 'green');
  log(`失败: ${failedTests}`, failedTests > 0 ? 'red' : 'green');
  log(`成功率: ${totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0}%`, 
      passedTests === totalTests ? 'green' : 'yellow');
  
  // 按配置显示结果
  log('\n按配置分组的结果:', 'bright');
  results.forEach((configResult, index) => {
    log(`\n${index + 1}. ${configResult.config}`, 'yellow');
    
    if (configResult.error) {
      log(`   错误: ${configResult.error}`, 'red');
      return;
    }
    
    if (configResult.tests) {
      const configPassed = configResult.tests.filter(t => t.passed).length;
      const configTotal = configResult.tests.length;
      log(`   通过率: ${configTotal > 0 ? ((configPassed / configTotal) * 100).toFixed(1) : 0}% (${configPassed}/${configTotal})`, 
          configPassed === configTotal ? 'green' : 'yellow');
      
      configResult.tests.forEach(test => {
        const status = test.passed ? '✅' : '❌';
        const color = test.passed ? 'green' : 'red';
        log(`   ${status} ${test.testName}`, color);
        
        if (test.error) {
          log(`     错误: ${test.error}`, 'red');
        }
      });
    }
  });
  
  // 保存报告
  const reportPath = path.join(__dirname, '../reports/compatibility-test-report.json');
  const reportDir = path.dirname(reportPath);
  
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTests,
      passedTests,
      failedTests,
      successRate: totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0
    },
    results
  };
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  log(`\n报告已保存到: ${reportPath}`, 'cyan');
  
  return passedTests === totalTests;
}

// 主函数
async function main() {
  const startTime = Date.now();
  
  log('🚀 开始UI组件兼容性测试', 'bright');
  
  try {
    await runCompatibilityTests();
  } catch (error) {
    log(`测试过程中发生错误: ${error.message}`, 'red');
    process.exit(1);
  }
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(1);
  
  logSection('测试完成');
  log(`总耗时: ${duration}秒`, 'bright');
  log('🎉 兼容性测试完成！', 'green');
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  log('未处理的Promise拒绝:', 'red');
  log(reason, 'red');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  log('未捕获的异常:', 'red');
  log(error.message, 'red');
  process.exit(1);
});

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { runCompatibilityTests, generateCompatibilityReport };
