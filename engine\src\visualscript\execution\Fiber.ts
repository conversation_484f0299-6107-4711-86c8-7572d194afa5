/**
 * 视觉脚本执行纤程
 * 负责执行视觉脚本的一条执行路径
 */
import { Node, NodeConnection } from '../nodes/Node';
import { VisualScriptEngine } from '../VisualScriptEngine';

/**
 * 纤程执行步骤结果
 */
export interface FiberStepResult {
  /** 是否已完成 */
  completed: boolean;
  /** 是否需要暂停 */
  pause: boolean;
  /** 执行的节点 */
  node?: Node;
  /** 执行结果 */
  result?: any;
  /** 错误信息 */
  error?: any;
}

/**
 * 纤程选项
 */
export interface FiberOptions {
  /** 视觉脚本引擎 */
  engine: VisualScriptEngine;
  /** 源节点 */
  sourceNode: Node;
  /** 输出名称 */
  outputName: string;
  /** 完成回调 */
  callback?: () => void;
}

/**
 * 视觉脚本执行纤程
 * 负责执行视觉脚本的一条执行路径
 */
export class Fiber {
  /** 视觉脚本引擎 */
  private engine: VisualScriptEngine;
  
  /** 当前节点 */
  private currentNode: Node;
  
  /** 当前输出名称 */
  private currentOutputName: string;
  
  /** 执行步数 */
  private steps: number = 0;
  
  /** 最大执行步数 */
  private maxSteps: number = 1000;
  
  /** 开始时间 */
  private startTime: number = 0;
  
  /** 最大执行时间（毫秒） */
  private maxTime: number = 1000;
  
  /** 是否已完成 */
  private completed: boolean = false;
  
  /** 是否已暂停 */
  private paused: boolean = false;
  
  /** 完成回调 */
  private callback?: () => void;
  
  /**
   * 创建纤程
   * @param options 纤程选项
   */
  constructor(options: FiberOptions) {
    this.engine = options.engine;
    this.currentNode = options.sourceNode;
    this.currentOutputName = options.outputName;
    this.callback = options.callback;
    this.startTime = Date.now();
  }
  
  /**
   * 执行一步
   * @returns 执行结果
   */
  public executeStep(): FiberStepResult {
    // 如果已完成或已暂停，直接返回
    if (this.completed || this.paused) {
      return {
        completed: this.completed,
        pause: this.paused
      };
    }
    
    // 检查是否超过最大步数或最大时间
    if (this.steps >= this.maxSteps || Date.now() - this.startTime >= this.maxTime) {
      this.paused = true;
      
      return {
        completed: false,
        pause: true
      };
    }
    
    // 增加步数
    this.steps++;
    
    try {
      // 获取当前节点的输出连接
      const connections = this.getOutputConnections();
      
      // 如果没有连接，标记为完成
      if (connections.length === 0) {
        this.completed = true;
        
        // 调用完成回调
        if (this.callback) {
          this.callback();
        }
        
        return {
          completed: true,
          pause: false
        };
      }
      
      // 执行所有连接
      for (let i = 0; i < connections.length; i++) {
        const connection = connections[i];
        
        // 获取目标节点
        const targetNode = connection.targetNode;
        
        // 设置当前节点和输出名称
        this.currentNode = targetNode;
        this.currentOutputName = '';
        
        // 执行目标节点
        const result = targetNode.execute();
        
        // 返回执行结果
        return {
          completed: false,
          pause: false,
          node: targetNode,
          result: result
        };
      }
      
      // 如果没有执行任何节点，标记为完成
      this.completed = true;
      
      // 调用完成回调
      if (this.callback) {
        this.callback();
      }
      
      return {
        completed: true,
        pause: false
      };
    } catch (error) {
      // 发生错误，标记为完成
      this.completed = true;
      
      return {
        completed: true,
        pause: false,
        error: error
      };
    }
  }
  
  /**
   * 获取输出连接
   * @returns 输出连接列表
   */
  private getOutputConnections(): NodeConnection[] {
    // 直接从节点的输出连接映射中获取连接
    const connections = (this.currentNode as any).outputConnections?.get(this.currentOutputName) || [];
    return connections;
  }
  
  /**
   * 暂停执行
   */
  public pause(): void {
    this.paused = true;
  }
  
  /**
   * 恢复执行
   */
  public resume(): void {
    this.paused = false;
  }
  
  /**
   * 重置执行状态
   */
  public reset(): void {
    this.steps = 0;
    this.startTime = Date.now();
    this.completed = false;
    this.paused = false;
  }
  
  /**
   * 获取当前节点
   * @returns 当前节点
   */
  public getCurrentNode(): Node {
    return this.currentNode;
  }
  
  /**
   * 获取当前输出名称
   * @returns 当前输出名称
   */
  public getCurrentOutputName(): string {
    return this.currentOutputName;
  }
  
  /**
   * 获取执行步数
   * @returns 执行步数
   */
  public getSteps(): number {
    return this.steps;
  }
  
  /**
   * 获取执行时间（毫秒）
   * @returns 执行时间
   */
  public getExecutionTime(): number {
    return Date.now() - this.startTime;
  }
  
  /**
   * 是否已完成
   * @returns 是否已完成
   */
  public isCompleted(): boolean {
    return this.completed;
  }
  
  /**
   * 是否已暂停
   * @returns 是否已暂停
   */
  public isPaused(): boolean {
    return this.paused;
  }
  
  /**
   * 设置最大执行步数
   * @param steps 最大执行步数
   */
  public setMaxSteps(steps: number): void {
    this.maxSteps = steps;
  }
  
  /**
   * 设置最大执行时间（毫秒）
   * @param time 最大执行时间
   */
  public setMaxTime(time: number): void {
    this.maxTime = time;
  }
}
