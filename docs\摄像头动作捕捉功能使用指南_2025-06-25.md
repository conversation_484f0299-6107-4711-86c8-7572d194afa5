# 摄像头动作捕捉功能使用指南

**版本**: 1.0  
**日期**: 2025年6月25日  
**作者**: DL引擎开发团队  

## 概述

摄像头动作捕捉功能允许用户通过普通摄像头（笔记本电脑、平板等自带摄像头）捕捉人物动作，并将这些动作实时映射到虚拟环境中的化身，实现与虚拟物体的自然交互。

## 功能特性

### 🎯 核心功能
- **实时姿态识别**: 使用MediaPipe技术进行高精度人体姿态检测
- **手部追踪**: 精确识别手部关键点和手指动作
- **手势识别**: 支持抓取、张开、指向、竖拇指等常用手势
- **虚拟交互**: 将真实动作映射到虚拟环境中的物体交互
- **多人支持**: 支持多用户协作场景

### 📊 性能指标
- **姿态识别帧率**: 30FPS
- **交互延迟**: <100ms
- **识别精度**: >90%
- **支持分辨率**: 640x480 ~ 1920x1080

## 快速开始

### 1. 系统要求

**硬件要求:**
- 摄像头设备（内置或外接）
- CPU: Intel i5或同等性能以上
- 内存: 8GB以上
- 显卡: 支持WebGL的独立显卡（推荐）

**软件要求:**
- 现代浏览器（Chrome 90+, Firefox 88+, Safari 14+）
- 摄像头访问权限
- 稳定的网络连接（用于加载MediaPipe模型）

### 2. 启用功能

#### 在编辑器中启用

1. 打开DL引擎编辑器
2. 在右侧面板找到"摄像头动作捕捉"选项
3. 点击启用开关
4. 授权摄像头访问权限
5. 选择摄像头设备和配置参数
6. 点击"开始校准"进行初始设置

#### 通过代码启用

```typescript
import { CameraMotionCaptureSystem } from './engine/src/mocap/CameraMotionCaptureSystem';

// 创建系统实例
const motionCaptureSystem = new CameraMotionCaptureSystem(world, {
  enabled: true,
  camera: {
    resolution: { width: 1280, height: 720 },
    frameRate: 30
  },
  enableHandTracking: true,
  enableGestureRecognition: true,
  enableVirtualInteraction: true
});

// 初始化系统
await motionCaptureSystem.initialize();
```

### 3. 基础配置

#### 摄像头设置
- **分辨率**: 推荐1280x720，平衡性能和质量
- **帧率**: 30FPS，确保流畅的动作捕捉
- **设备选择**: 选择质量最好的可用摄像头

#### 检测参数
- **模型复杂度**: 1（平衡精度和性能）
- **检测置信度**: 0.5（适中的检测阈值）
- **平滑系数**: 0.5（减少抖动）

#### 交互设置
- **交互距离**: 2.0米（手部与物体的最大交互距离）
- **抓取阈值**: 0.7（抓取手势的置信度要求）
- **手势置信度**: 0.6（手势识别的最小置信度）

## 使用视觉脚本节点

### 摄像头输入节点 (CameraInput)

**功能**: 从摄像头获取视频流数据

**输入端口**:
- `start`: 启动摄像头
- `stop`: 停止摄像头
- `deviceId`: 摄像头设备ID
- `resolution`: 视频分辨率
- `frameRate`: 视频帧率

**输出端口**:
- `frame`: 当前视频帧
- `imageData`: 图像数据对象
- `isActive`: 摄像头是否正在运行
- `fps`: 实际帧率
- `onStarted`: 启动完成事件
- `onError`: 错误事件

**使用示例**:
```
[摄像头输入] -> [姿态检测]
    |
    v
[视频预览]
```

### 姿态检测节点 (PoseDetection)

**功能**: 使用MediaPipe检测人体姿态关键点

**输入端口**:
- `imageData`: 要检测的图像数据
- `detect`: 触发姿态检测
- `modelComplexity`: 模型复杂度
- `minDetectionConfidence`: 检测置信度

**输出端口**:
- `landmarks`: 2D姿态关键点数组
- `worldLandmarks`: 3D世界坐标关键点
- `confidence`: 检测置信度
- `isDetected`: 是否检测到人体
- `nose`, `leftShoulder`, `rightShoulder`等: 特定关键点

**使用示例**:
```
[摄像头输入] -> [姿态检测] -> [虚拟化身控制]
                    |
                    v
                [关键点可视化]
```

### 手部追踪节点 (HandTracking)

**功能**: 检测手部关键点和识别手势

**输入端口**:
- `imageData`: 要检测的图像数据
- `detect`: 触发手部检测
- `maxNumHands`: 最大检测手数
- `enableGestureRecognition`: 启用手势识别

**输出端口**:
- `leftHand`, `rightHand`: 左右手关键点
- `leftGesture`, `rightGesture`: 左右手手势
- `handsDetected`: 是否检测到手部
- `onGestureChanged`: 手势变化事件

**使用示例**:
```
[摄像头输入] -> [手部追踪] -> [虚拟交互]
                    |
                    v
                [手势显示]
```

### 虚拟交互节点 (VirtualInteraction)

**功能**: 将手势映射到虚拟环境交互

**输入端口**:
- `leftGesture`, `rightGesture`: 左右手手势
- `leftHandPosition`, `rightHandPosition`: 左右手位置
- `avatarEntity`: 虚拟化身实体
- `targetObjects`: 可交互物体数组

**输出端口**:
- `grabbedObject`: 被抓取的物体
- `isGrabbing`: 是否正在抓取
- `onGrab`, `onRelease`: 抓取和释放事件
- `onGestureCommand`: 手势命令事件

**使用示例**:
```
[手部追踪] -> [虚拟交互] -> [物体控制]
                |
                v
            [交互反馈]
```

## 完整工作流程示例

### 基础动作捕捉流程

```
[摄像头输入] -> [姿态检测] -> [化身动画]
    |              |
    v              v
[视频预览]    [关键点显示]
```

### 手势交互流程

```
[摄像头输入] -> [手部追踪] -> [虚拟交互] -> [物体操作]
    |              |              |
    v              v              v
[视频预览]    [手势显示]    [交互反馈]
```

### 完整集成流程

```
[摄像头输入] -> [姿态检测] -> [化身控制]
    |              |
    v              v
[手部追踪] -> [虚拟交互] -> [场景交互]
    |              |
    v              v
[手势显示]    [事件日志]
```

## 最佳实践

### 环境设置
1. **光线条件**: 确保充足且均匀的光线
2. **背景选择**: 使用简洁、对比度高的背景
3. **摄像头位置**: 摄像头应正对用户，高度适中
4. **活动空间**: 确保有足够的活动空间

### 性能优化
1. **分辨率选择**: 根据设备性能选择合适的分辨率
2. **帧率设置**: 在流畅性和性能之间找到平衡
3. **模型复杂度**: 根据精度需求调整模型复杂度
4. **处理间隔**: 适当调整处理间隔以优化性能

### 交互设计
1. **手势设计**: 使用直观、易识别的手势
2. **反馈机制**: 提供清晰的视觉和听觉反馈
3. **容错处理**: 设计容错机制处理识别错误
4. **用户引导**: 提供清晰的使用指导和提示

## 故障排除

### 常见问题

**Q: 摄像头无法启动**
A: 检查摄像头权限、设备连接和浏览器兼容性

**Q: 姿态检测精度低**
A: 改善光线条件、调整摄像头角度、增加模型复杂度

**Q: 手势识别不准确**
A: 确保手部完全在视野内、调整手势置信度阈值

**Q: 交互延迟高**
A: 降低分辨率、减少处理间隔、优化网络连接

**Q: 性能问题**
A: 关闭不必要的功能、降低质量设置、检查硬件配置

### 调试工具

1. **调试模式**: 启用调试模式查看详细日志
2. **性能监控**: 监控FPS、处理时间等性能指标
3. **可视化工具**: 使用关键点可视化检查检测效果
4. **事件日志**: 查看交互事件历史记录

## 高级功能

### 自定义手势
```typescript
// 添加自定义手势识别
const customGestureRecognizer = (handLandmarks: LandmarkData[]) => {
  // 实现自定义手势识别逻辑
  return {
    type: 'CUSTOM_GESTURE',
    confidence: 0.8
  };
};
```

### 多人协作
```typescript
// 配置多人动作捕捉
const multiUserConfig = {
  maxUsers: 4,
  userSeparation: true,
  collaborativeInteraction: true
};
```

### 数据记录
```typescript
// 记录动作数据用于回放或分析
const recorder = new MotionDataRecorder();
recorder.startRecording();
// ... 执行动作
const recordedData = recorder.stopRecording();
```

## 总结

摄像头动作捕捉功能为DL引擎提供了强大的人机交互能力，通过简单的配置和直观的视觉脚本节点，用户可以轻松实现复杂的动作捕捉和虚拟交互功能。

遵循本指南的最佳实践，您可以创建出色的沉浸式体验，让用户通过自然的身体动作与虚拟世界进行交互。

---

**技术支持**: 如有问题，请联系DL引擎开发团队  
**文档更新**: 本文档将随功能更新持续维护
