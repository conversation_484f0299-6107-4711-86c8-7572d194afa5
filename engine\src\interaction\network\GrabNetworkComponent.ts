/**
 * 抓取网络组件
 * 用于处理抓取系统的网络同步
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EventEmitter } from '../../utils/EventEmitter';
import { Hand } from '../components/GrabbableComponent';
import { Debug } from '../../utils/Debug';

/**
 * 抓取网络事件类型
 */
export enum GrabNetworkEventType {
  /** 抓取请求 */
  GRAB_REQUEST = 'grabRequest',
  /** 抓取确认 */
  GRAB_CONFIRM = 'grabConfirm',
  /** 抓取拒绝 */
  GRAB_REJECT = 'grabReject',
  /** 释放请求 */
  RELEASE_REQUEST = 'releaseRequest',
  /** 释放确认 */
  RELEASE_CONFIRM = 'releaseConfirm',
  /** 状态同步 */
  STATE_SYNC = 'stateSync'
}

/**
 * 抓取网络事件数据
 */
export interface GrabNetworkEventData {
  /** 事件类型 */
  type: GrabNetworkEventType;
  /** 抓取者ID */
  grabberEntityId: string;
  /** 被抓取实体ID */
  grabbedEntityId: string;
  /** 抓取手 */
  hand: Hand;
  /** 时间戳 */
  timestamp: number;
  /** 用户ID */
  userId: string;
  /** 会话ID */
  sessionId: string;
  /** 其他数据 */
  [key: string]: any;
}

/**
 * 抓取网络组件配置
 */
export interface GrabNetworkComponentConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 同步间隔（毫秒） */
  syncInterval?: number;
  /** 是否是权威节点 */
  isAuthority?: boolean;
  /** 用户ID */
  userId?: string;
  /** 会话ID */
  sessionId?: string;
}

/**
 * 抓取网络组件
 */
export class GrabNetworkComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE: string = 'GrabNetworkComponent';

  /** 是否启用 */
  private _enabled: boolean;

  /** 同步间隔（毫秒） */
  private _syncInterval: number;

  /** 是否是权威节点 */
  private _isAuthority: boolean;

  /** 用户ID */
  private _userId: string;

  /** 会话ID */
  private _sessionId: string;

  /** 上次同步时间 */
  private _lastSyncTime: number = 0;

  /** 待处理的事件队列 */
  private _pendingEvents: GrabNetworkEventData[] = [];

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 网络连接状态 */
  private _isConnected: boolean = false;

  /** 网络延迟（毫秒） */
  private _networkLatency: number = 0;

  /**
   * 构造函数
   * @param entity 关联的实体
   * @param config 组件配置
   */
  constructor(entity: Entity, config: GrabNetworkComponentConfig = {}) {
    // 调用基类构造函数，传入组件类型名称
    super(GrabNetworkComponent.TYPE);

    // 设置实体引用
    this.setEntity(entity);

    // 初始化属性
    this._enabled = config.enabled !== undefined ? config.enabled : true;
    this._syncInterval = config.syncInterval || 100; // 默认100毫秒同步一次
    this._isAuthority = config.isAuthority !== undefined ? config.isAuthority : false;
    this._userId = config.userId || '';
    this._sessionId = config.sessionId || '';
  }

  /**
   * 获取是否启用
   */
  get componentEnabled(): boolean {
    return this._enabled;
  }

  /**
   * 设置是否启用
   */
  set componentEnabled(value: boolean) {
    this._enabled = value;
  }

  /**
   * 获取同步间隔
   */
  get syncInterval(): number {
    return this._syncInterval;
  }

  /**
   * 设置同步间隔
   */
  set syncInterval(value: number) {
    this._syncInterval = value;
  }

  /**
   * 获取是否是权威节点
   */
  get isAuthority(): boolean {
    return this._isAuthority;
  }

  /**
   * 设置是否是权威节点
   */
  set isAuthority(value: boolean) {
    this._isAuthority = value;
  }

  /**
   * 获取用户ID
   */
  get userId(): string {
    return this._userId;
  }

  /**
   * 设置用户ID
   */
  set userId(value: string) {
    this._userId = value;
  }

  /**
   * 获取会话ID
   */
  get sessionId(): string {
    return this._sessionId;
  }

  /**
   * 设置会话ID
   */
  set sessionId(value: string) {
    this._sessionId = value;
  }

  /**
   * 获取网络连接状态
   */
  get isConnected(): boolean {
    return this._isConnected;
  }

  /**
   * 设置网络连接状态
   */
  set isConnected(value: boolean) {
    this._isConnected = value;
  }

  /**
   * 获取网络延迟
   */
  get networkLatency(): number {
    return this._networkLatency;
  }

  /**
   * 设置网络延迟
   */
  set networkLatency(value: number) {
    this._networkLatency = value;
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器函数
   */
  addEventListener(event: GrabNetworkEventType, listener: (data: GrabNetworkEventData) => void): this {
    this.eventEmitter.on(event, listener);
    return this;
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器函数
   */
  removeEventListener(event: GrabNetworkEventType, listener?: (data: GrabNetworkEventData) => void): this {
    this.eventEmitter.off(event, listener);
    return this;
  }

  /**
   * 发送抓取请求
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @param hand 抓取手
   * @returns 是否发送成功
   */
  sendGrabRequest(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean {
    if (!this._enabled || !this._isConnected) {
      return false;
    }

    const eventData: GrabNetworkEventData = {
      type: GrabNetworkEventType.GRAB_REQUEST,
      grabberEntityId,
      grabbedEntityId,
      hand,
      timestamp: Date.now(),
      userId: this._userId,
      sessionId: this._sessionId
    };

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 发送抓取确认
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @param hand 抓取手
   * @returns 是否发送成功
   */
  sendGrabConfirm(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean {
    if (!this._enabled || !this._isConnected) {
      return false;
    }

    const eventData: GrabNetworkEventData = {
      type: GrabNetworkEventType.GRAB_CONFIRM,
      grabberEntityId,
      grabbedEntityId,
      hand,
      timestamp: Date.now(),
      userId: this._userId,
      sessionId: this._sessionId
    };

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 发送抓取拒绝
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @param hand 抓取手
   * @param reason 拒绝原因
   * @returns 是否发送成功
   */
  sendGrabReject(grabberEntityId: string, grabbedEntityId: string, hand: Hand, reason: string): boolean {
    if (!this._enabled || !this._isConnected) {
      return false;
    }

    const eventData: GrabNetworkEventData = {
      type: GrabNetworkEventType.GRAB_REJECT,
      grabberEntityId,
      grabbedEntityId,
      hand,
      timestamp: Date.now(),
      userId: this._userId,
      sessionId: this._sessionId,
      reason
    };

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 发送释放请求
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @param hand 抓取手
   * @returns 是否发送成功
   */
  sendReleaseRequest(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean {
    if (!this._enabled || !this._isConnected) {
      return false;
    }

    const eventData: GrabNetworkEventData = {
      type: GrabNetworkEventType.RELEASE_REQUEST,
      grabberEntityId,
      grabbedEntityId,
      hand,
      timestamp: Date.now(),
      userId: this._userId,
      sessionId: this._sessionId
    };

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 发送释放确认
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @param hand 抓取手
   * @returns 是否发送成功
   */
  sendReleaseConfirm(grabberEntityId: string, grabbedEntityId: string, hand: Hand): boolean {
    if (!this._enabled || !this._isConnected) {
      return false;
    }

    const eventData: GrabNetworkEventData = {
      type: GrabNetworkEventType.RELEASE_CONFIRM,
      grabberEntityId,
      grabbedEntityId,
      hand,
      timestamp: Date.now(),
      userId: this._userId,
      sessionId: this._sessionId
    };

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 发送状态同步
   * @param grabberEntityId 抓取者实体ID
   * @param grabbedEntityId 被抓取实体ID
   * @param hand 抓取手
   * @param state 状态数据
   * @returns 是否发送成功
   */
  sendStateSync(grabberEntityId: string, grabbedEntityId: string, hand: Hand, state: any): boolean {
    if (!this._enabled || !this._isConnected) {
      return false;
    }

    // 检查是否需要同步
    const now = Date.now();
    if (now - this._lastSyncTime < this._syncInterval) {
      return false;
    }

    this._lastSyncTime = now;

    const eventData: GrabNetworkEventData = {
      type: GrabNetworkEventType.STATE_SYNC,
      grabberEntityId,
      grabbedEntityId,
      hand,
      timestamp: now,
      userId: this._userId,
      sessionId: this._sessionId,
      state
    };

    // 发送网络事件
    this.sendNetworkEvent(eventData);

    return true;
  }

  /**
   * 接收网络事件
   * @param eventData 事件数据
   */
  receiveNetworkEvent(eventData: GrabNetworkEventData): void {
    if (!this._enabled) {
      return;
    }

    // 添加到待处理队列
    this._pendingEvents.push(eventData);
  }

  /**
   * 处理待处理事件
   */
  processPendingEvents(): void {
    if (!this._enabled || this._pendingEvents.length === 0) {
      return;
    }

    // 处理所有待处理事件
    const events = [...this._pendingEvents];
    this._pendingEvents = [];

    for (const eventData of events) {
      // 触发事件
      this.eventEmitter.emit(eventData.type, eventData);
    }
  }

  /**
   * 发送网络事件
   * @param eventData 事件数据
   * @private
   */
  private sendNetworkEvent(eventData: GrabNetworkEventData): void {
    // 这里应该调用网络系统发送事件
    // 在实际项目中，这部分应该由网络系统处理
    Debug.log('GrabNetworkComponent', `发送网络事件: ${eventData.type}`);

    // 模拟网络延迟
    setTimeout(() => {
      // 这里应该是网络系统接收到事件后的回调
      // 在实际项目中，这部分应该由网络系统处理
      this.receiveNetworkEvent(eventData);
    }, this._networkLatency);
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new GrabNetworkComponent(this.getEntity()!, {
      enabled: this._enabled,
      syncInterval: this._syncInterval,
      isAuthority: this._isAuthority,
      userId: this._userId,
      sessionId: this._sessionId
    });
  }

  /**
   * 更新组件
   * @param deltaTime 时间增量（秒）
   */
  update(_deltaTime: number): void {
    if (!this._enabled) {
      return;
    }

    // 处理待处理事件
    this.processPendingEvents();
  }
}
