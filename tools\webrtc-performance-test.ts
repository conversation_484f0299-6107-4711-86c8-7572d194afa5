/**
 * WebRTC性能测试工具
 * 用于测试和验证WebRTC传输性能优化效果
 */
import { UltraLowLatencyWebRTC } from '../engine/src/network/UltraLowLatencyWebRTC';

export interface PerformanceTestConfig {
  testDuration: number;        // 测试持续时间(秒)
  packetSize: number;          // 数据包大小(字节)
  sendInterval: number;        // 发送间隔(毫秒)
  targetLatency: number;       // 目标延迟(毫秒)
  concurrentConnections: number; // 并发连接数
}

export interface TestResults {
  averageLatency: number;
  minLatency: number;
  maxLatency: number;
  p95Latency: number;
  p99Latency: number;
  packetLossRate: number;
  throughput: number;
  jitter: number;
  connectionSuccessRate: number;
  targetLatencyAchieved: boolean;
}

export class WebRTCPerformanceTest {
  private config: PerformanceTestConfig;
  private connections: UltraLowLatencyWebRTC[] = [];
  private testResults: TestResults[] = [];
  private isRunning = false;

  constructor(config: Partial<PerformanceTestConfig> = {}) {
    this.config = {
      testDuration: 60,
      packetSize: 1024,
      sendInterval: 16,  // ~60fps
      targetLatency: 30,
      concurrentConnections: 1,
      ...config
    };
  }

  async runPerformanceTest(): Promise<TestResults> {
    console.log('🚀 开始WebRTC性能测试...');
    console.log(`配置: ${JSON.stringify(this.config, null, 2)}`);

    this.isRunning = true;
    const startTime = Date.now();

    try {
      // 创建并发连接
      await this.createConnections();
      
      // 等待连接建立
      await this.waitForConnections();
      
      // 开始性能测试
      const testPromises = this.connections.map((conn, index) => 
        this.runSingleConnectionTest(conn, index)
      );
      
      // 等待所有测试完成
      const results = await Promise.all(testPromises);
      
      // 聚合结果
      const aggregatedResults = this.aggregateResults(results);
      
      const endTime = Date.now();
      const actualDuration = (endTime - startTime) / 1000;
      
      console.log(`✅ 测试完成，实际耗时: ${actualDuration.toFixed(2)}秒`);
      console.log(`📊 测试结果:`);
      console.log(`   平均延迟: ${aggregatedResults.averageLatency.toFixed(2)}ms`);
      console.log(`   95%分位延迟: ${aggregatedResults.p95Latency.toFixed(2)}ms`);
      console.log(`   99%分位延迟: ${aggregatedResults.p99Latency.toFixed(2)}ms`);
      console.log(`   丢包率: ${(aggregatedResults.packetLossRate * 100).toFixed(2)}%`);
      console.log(`   吞吐量: ${(aggregatedResults.throughput / 1024 / 1024).toFixed(2)} MB/s`);
      console.log(`   抖动: ${aggregatedResults.jitter.toFixed(2)}ms`);
      console.log(`   目标延迟达成: ${aggregatedResults.targetLatencyAchieved ? '✅' : '❌'}`);

      return aggregatedResults;

    } finally {
      this.isRunning = false;
      await this.cleanup();
    }
  }

  private async createConnections(): Promise<void> {
    console.log(`📡 创建 ${this.config.concurrentConnections} 个并发连接...`);
    
    for (let i = 0; i < this.config.concurrentConnections; i++) {
      const connection = new UltraLowLatencyWebRTC({
        targetLatency: this.config.targetLatency,
        maxLatency: this.config.targetLatency * 2,
        adaptiveBuffering: true,
        hardwareAcceleration: true,
        simdOptimization: true,
        zeroCopyTransfer: true
      });

      await connection.initialize();
      this.connections.push(connection);
    }
  }

  private async waitForConnections(): Promise<void> {
    console.log('⏳ 等待连接建立...');
    
    // 模拟信令过程
    for (let i = 0; i < this.connections.length; i += 2) {
      const conn1 = this.connections[i];
      const conn2 = this.connections[i + 1];
      
      if (conn1 && conn2) {
        await this.establishConnection(conn1, conn2);
      }
    }
    
    // 等待数据通道打开
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  private async establishConnection(conn1: UltraLowLatencyWebRTC, conn2: UltraLowLatencyWebRTC): Promise<void> {
    // 创建offer
    const offer = await conn1.createOffer();
    
    // 设置远程描述
    await conn2.handleAnswer(offer);
    
    // 创建answer
    const answer = await conn2.createOffer();
    
    // 完成连接
    await conn1.handleAnswer(answer);
  }

  private async runSingleConnectionTest(connection: UltraLowLatencyWebRTC, connectionIndex: number): Promise<TestResults> {
    const latencies: number[] = [];
    const timestamps: number[] = [];
    let packetsSent = 0;
    let packetsReceived = 0;
    let totalBytes = 0;

    console.log(`🔄 开始连接 ${connectionIndex} 的性能测试...`);

    // 监听数据接收
    connection.on('dataReceived', (data: ArrayBuffer, metadata: any) => {
      packetsReceived++;
      latencies.push(metadata.transmissionLatency);
      timestamps.push(metadata.receiveTimestamp);
    });

    // 生成测试数据
    const testData = new ArrayBuffer(this.config.packetSize);
    const testView = new Uint8Array(testData);
    for (let i = 0; i < testView.length; i++) {
      testView[i] = Math.floor(Math.random() * 256);
    }

    // 开始发送数据
    const sendInterval = setInterval(async () => {
      if (!this.isRunning) {
        clearInterval(sendInterval);
        return;
      }

      try {
        await connection.sendData(testData);
        packetsSent++;
        totalBytes += this.config.packetSize;
      } catch (error) {
        console.error(`连接 ${connectionIndex} 发送数据失败:`, error);
      }
    }, this.config.sendInterval);

    // 等待测试完成
    await new Promise(resolve => setTimeout(resolve, this.config.testDuration * 1000));
    clearInterval(sendInterval);

    // 等待最后的数据包
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 计算结果
    const packetLossRate = (packetsSent - packetsReceived) / packetsSent;
    const throughput = totalBytes / this.config.testDuration; // bytes/second
    
    const sortedLatencies = latencies.sort((a, b) => a - b);
    const averageLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
    const minLatency = sortedLatencies[0] || 0;
    const maxLatency = sortedLatencies[sortedLatencies.length - 1] || 0;
    const p95Latency = sortedLatencies[Math.floor(sortedLatencies.length * 0.95)] || 0;
    const p99Latency = sortedLatencies[Math.floor(sortedLatencies.length * 0.99)] || 0;
    
    // 计算抖动
    const jitter = this.calculateJitter(latencies);
    
    const results: TestResults = {
      averageLatency,
      minLatency,
      maxLatency,
      p95Latency,
      p99Latency,
      packetLossRate,
      throughput,
      jitter,
      connectionSuccessRate: packetsReceived > 0 ? 1 : 0,
      targetLatencyAchieved: p95Latency <= this.config.targetLatency
    };

    console.log(`✅ 连接 ${connectionIndex} 测试完成:`);
    console.log(`   发送: ${packetsSent}, 接收: ${packetsReceived}`);
    console.log(`   平均延迟: ${averageLatency.toFixed(2)}ms`);
    console.log(`   95%延迟: ${p95Latency.toFixed(2)}ms`);

    return results;
  }

  private calculateJitter(latencies: number[]): number {
    if (latencies.length < 2) return 0;
    
    let jitterSum = 0;
    for (let i = 1; i < latencies.length; i++) {
      jitterSum += Math.abs(latencies[i] - latencies[i - 1]);
    }
    
    return jitterSum / (latencies.length - 1);
  }

  private aggregateResults(results: TestResults[]): TestResults {
    if (results.length === 0) {
      throw new Error('没有测试结果');
    }

    const aggregated: TestResults = {
      averageLatency: 0,
      minLatency: Math.min(...results.map(r => r.minLatency)),
      maxLatency: Math.max(...results.map(r => r.maxLatency)),
      p95Latency: 0,
      p99Latency: 0,
      packetLossRate: 0,
      throughput: 0,
      jitter: 0,
      connectionSuccessRate: 0,
      targetLatencyAchieved: false
    };

    // 计算平均值
    aggregated.averageLatency = results.reduce((sum, r) => sum + r.averageLatency, 0) / results.length;
    aggregated.p95Latency = results.reduce((sum, r) => sum + r.p95Latency, 0) / results.length;
    aggregated.p99Latency = results.reduce((sum, r) => sum + r.p99Latency, 0) / results.length;
    aggregated.packetLossRate = results.reduce((sum, r) => sum + r.packetLossRate, 0) / results.length;
    aggregated.throughput = results.reduce((sum, r) => sum + r.throughput, 0);
    aggregated.jitter = results.reduce((sum, r) => sum + r.jitter, 0) / results.length;
    aggregated.connectionSuccessRate = results.reduce((sum, r) => sum + r.connectionSuccessRate, 0) / results.length;
    aggregated.targetLatencyAchieved = results.every(r => r.targetLatencyAchieved);

    return aggregated;
  }

  private async cleanup(): Promise<void> {
    console.log('🧹 清理测试资源...');
    
    for (const connection of this.connections) {
      connection.close();
    }
    
    this.connections = [];
    this.testResults = [];
  }

  async runBenchmarkSuite(): Promise<void> {
    console.log('🏁 开始WebRTC性能基准测试套件...');

    const testConfigs = [
      { name: '基础测试 (1KB, 60fps)', packetSize: 1024, sendInterval: 16 },
      { name: '高频测试 (512B, 120fps)', packetSize: 512, sendInterval: 8 },
      { name: '大包测试 (4KB, 30fps)', packetSize: 4096, sendInterval: 33 },
      { name: '并发测试 (1KB, 60fps, 4连接)', packetSize: 1024, sendInterval: 16, concurrentConnections: 4 }
    ];

    for (const testConfig of testConfigs) {
      console.log(`\n📋 运行测试: ${testConfig.name}`);
      
      this.config = { ...this.config, ...testConfig };
      const results = await this.runPerformanceTest();
      
      // 保存结果
      this.testResults.push(results);
      
      // 等待一段时间再进行下一个测试
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    console.log('\n📈 基准测试套件完成！');
    this.generateReport();
  }

  private generateReport(): void {
    console.log('\n📊 性能测试报告');
    console.log('='.repeat(50));
    
    this.testResults.forEach((result, index) => {
      console.log(`\n测试 ${index + 1}:`);
      console.log(`  平均延迟: ${result.averageLatency.toFixed(2)}ms`);
      console.log(`  95%延迟: ${result.p95Latency.toFixed(2)}ms`);
      console.log(`  丢包率: ${(result.packetLossRate * 100).toFixed(2)}%`);
      console.log(`  吞吐量: ${(result.throughput / 1024 / 1024).toFixed(2)} MB/s`);
      console.log(`  目标达成: ${result.targetLatencyAchieved ? '✅' : '❌'}`);
    });
  }
}

// 运行测试的主函数
export async function runWebRTCPerformanceTest(): Promise<void> {
  const tester = new WebRTCPerformanceTest({
    testDuration: 30,
    targetLatency: 30,
    packetSize: 1024,
    sendInterval: 16,
    concurrentConnections: 2
  });

  try {
    await tester.runBenchmarkSuite();
  } catch (error) {
    console.error('❌ 性能测试失败:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runWebRTCPerformanceTest();
}
