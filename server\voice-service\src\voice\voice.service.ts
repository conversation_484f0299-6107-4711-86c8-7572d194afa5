import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  SpeechRecognitionService,
  SpeechRecognitionConfig,
  SpeechRecognitionResult,
  SpeechProvider,
} from '../speech-recognition/speech-recognition.service';
import {
  SpeechSynthesisService,
  SpeechSynthesisConfig,
  SpeechSynthesisResult,
  TTSProvider,
} from '../speech-synthesis/speech-synthesis.service';
import {
  AudioProcessingService,
  AudioProcessingConfig,
  AudioAnalysis,
} from '../audio-processing/audio-processing.service';
import {
  LipSyncService,
  LipSyncConfig,
  LipSyncData,
} from '../lip-sync/lip-sync.service';

@Injectable()
export class VoiceService {
  constructor(
    private speechRecognitionService: SpeechRecognitionService,
    private speechSynthesisService: SpeechSynthesisService,
    private audioProcessingService: AudioProcessingService,
    private lipSyncService: LipSyncService,
    private configService: ConfigService,
    private eventEmitter: EventEmitter2,
  ) {}

  /**
   * 语音识别
   */
  async recognizeSpeech(
    audioBuffer: Buffer,
    config: SpeechRecognitionConfig,
  ): Promise<SpeechRecognitionResult> {
    const result = await this.speechRecognitionService.recognizeAudioFile(audioBuffer, config);
    
    // 发送识别完成事件
    this.eventEmitter.emit('speech.recognized', {
      id: result.id,
      text: result.text,
      confidence: result.confidence,
      language: result.language,
      provider: result.provider,
    });

    return result;
  }

  /**
   * 语音合成
   */
  async synthesizeSpeech(
    text: string,
    config: SpeechSynthesisConfig,
  ): Promise<SpeechSynthesisResult> {
    const result = await this.speechSynthesisService.synthesizeSpeech(text, config);
    
    // 发送合成完成事件
    this.eventEmitter.emit('speech.synthesized', {
      id: result.id,
      text,
      duration: result.duration,
      voice: result.voice,
      language: result.language,
      provider: result.provider,
    });

    return result;
  }

  /**
   * 音频处理
   */
  async processAudio(
    audioBuffer: Buffer,
    config: AudioProcessingConfig,
  ): Promise<Buffer> {
    return this.audioProcessingService.convertAudio(audioBuffer, config);
  }

  /**
   * 生成嘴形同步数据
   */
  async generateLipSync(
    audioBuffer: Buffer,
    text: string,
    config: LipSyncConfig,
  ): Promise<LipSyncData> {
    return this.lipSyncService.generateLipSync(audioBuffer, text, config);
  }

  /**
   * 获取可用语音列表
   */
  async getAvailableVoices(provider: TTSProvider, language?: string): Promise<any[]> {
    return this.speechSynthesisService.getAvailableVoices(provider, language);
  }

  /**
   * 获取支持的语言列表
   */
  getSupportedLanguages(provider: SpeechProvider): string[] {
    return this.speechRecognitionService.getSupportedLanguages(provider);
  }

  /**
   * 语音预览
   */
  async previewVoice(
    voice: string,
    language: string,
    provider: TTSProvider,
  ): Promise<SpeechSynthesisResult> {
    return this.speechSynthesisService.previewVoice(voice, language, provider);
  }

  /**
   * 分析音频
   */
  async analyzeAudio(audioBuffer: Buffer): Promise<AudioAnalysis> {
    return this.audioProcessingService.analyzeAudio(audioBuffer);
  }

  /**
   * 获取服务统计信息
   */
  getStatistics(): any {
    return {
      speechRecognition: this.speechRecognitionService.getServiceStatistics(),
      speechSynthesis: this.speechSynthesisService.getServiceStatistics(),
      audioProcessing: this.audioProcessingService.getSupportedFormats(),
      lipSync: {
        supportedLanguages: this.lipSyncService.getSupportedLanguages(),
        supportedVisemes: this.lipSyncService.getSupportedVisemes(),
      },
    };
  }

  /**
   * 开始实时语音识别
   */
  async startRealtimeRecognition(
    config: SpeechRecognitionConfig,
    onResult?: (result: Partial<SpeechRecognitionResult>) => void,
    onError?: (error: string) => void,
  ): Promise<string> {
    return this.speechRecognitionService.startRealtimeRecognition(config, onResult, onError);
  }

  /**
   * 停止实时语音识别
   */
  async stopRealtimeRecognition(sessionId: string): Promise<void> {
    return this.speechRecognitionService.stopRealtimeRecognition(sessionId);
  }

  /**
   * 批量语音识别
   */
  async batchRecognize(
    audioFiles: Buffer[],
    config: SpeechRecognitionConfig,
  ): Promise<SpeechRecognitionResult[]> {
    return this.speechRecognitionService.batchRecognize(audioFiles, config);
  }

  /**
   * 批量语音合成
   */
  async batchSynthesize(
    texts: string[],
    config: SpeechSynthesisConfig,
  ): Promise<SpeechSynthesisResult[]> {
    return this.speechSynthesisService.batchSynthesize(texts, config);
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.speechSynthesisService.clearCache();
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    status: string;
    services: {
      speechRecognition: boolean;
      speechSynthesis: boolean;
      audioProcessing: boolean;
      lipSync: boolean;
    };
  }> {
    // 这里可以添加各个服务的健康检查
    return {
      status: 'ok',
      services: {
        speechRecognition: true,
        speechSynthesis: true,
        audioProcessing: true,
        lipSync: true,
      },
    };
  }
}
