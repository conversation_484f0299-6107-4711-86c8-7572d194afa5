/**
 * 情感学习与适应系统
 * 基于用户交互历史实现个性化情感响应学习
 */

import { System } from '../core/System';
import { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 用户情感偏好数据
 */
export interface UserEmotionPreference {
  /** 用户ID */
  userId: string;
  /** 情感类型偏好 */
  emotionTypePreferences: Map<string, number>;
  /** 情感强度偏好 */
  intensityPreferences: Map<string, number>;
  /** 响应时间偏好 */
  responseTimePreferences: number;
  /** 交互频率偏好 */
  interactionFrequencyPreferences: number;
  /** 学习更新时间 */
  lastUpdated: number;
  /** 交互次数 */
  interactionCount: number;
}

/**
 * 情感响应效果评估数据
 */
export interface EmotionResponseEvaluation {
  /** 响应ID */
  responseId: string;
  /** 用户ID */
  userId: string;
  /** 情感类型 */
  emotionType: string;
  /** 响应强度 */
  intensity: number;
  /** 用户反馈分数 (0-1) */
  userFeedbackScore: number;
  /** 响应时间 */
  responseTime: number;
  /** 持续时间 */
  duration: number;
  /** 上下文信息 */
  context: any;
  /** 评估时间 */
  timestamp: number;
}

/**
 * 情感学习配置
 */
export interface EmotionLearningConfig {
  /** 学习率 */
  learningRate: number;
  /** 遗忘因子 */
  forgettingFactor: number;
  /** 最小学习样本数 */
  minLearningSamples: number;
  /** 最大历史记录数 */
  maxHistorySize: number;
  /** 学习更新间隔（毫秒） */
  learningUpdateInterval: number;
  /** 是否启用在线学习 */
  enableOnlineLearning: boolean;
  /** 是否启用个性化适应 */
  enablePersonalization: boolean;
  /** 调试模式 */
  debug: boolean;
}

/**
 * 情感适应策略
 */
export interface EmotionAdaptationStrategy {
  /** 策略名称 */
  name: string;
  /** 适应权重 */
  weight: number;
  /** 适应参数 */
  parameters: Map<string, number>;
  /** 适应效果评分 */
  effectivenessScore: number;
  /** 使用次数 */
  usageCount: number;
}

/**
 * 情感学习与适应系统
 */
export class EmotionLearningSystem extends System {
  /** 系统名称 */
  static readonly NAME = 'EmotionLearningSystem';

  /** 配置 */
  private config: EmotionLearningConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 用户情感偏好映射 */
  private userPreferences: Map<string, UserEmotionPreference> = new Map();

  /** 情感响应评估历史 */
  private evaluationHistory: Map<string, EmotionResponseEvaluation[]> = new Map();

  /** 情感适应策略 */
  private adaptationStrategies: Map<string, EmotionAdaptationStrategy> = new Map();

  /** 学习模型参数 */
  private learningParameters: Map<string, number> = new Map();

  /** 最后学习更新时间 */
  private lastLearningUpdate: number = 0;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: Partial<EmotionLearningConfig> = {}) {
    super();

    this.config = {
      learningRate: 0.1,
      forgettingFactor: 0.95,
      minLearningSamples: 5,
      maxHistorySize: 1000,
      learningUpdateInterval: 60000, // 1分钟
      enableOnlineLearning: true,
      enablePersonalization: true,
      debug: false,
      ...config,
    };
  }

  /**
   * 初始化系统
   */
  public async initialize(): Promise<void> {
    if (this.initialized) return;

    // 初始化学习参数
    this.initializeLearningParameters();

    // 初始化适应策略
    this.initializeAdaptationStrategies();

    // 加载历史数据
    await this.loadHistoricalData();

    this.initialized = true;

    if (this.config.debug) {
      console.log('情感学习系统初始化完成');
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized) return;

    const currentTime = Date.now();

    // 检查是否需要进行学习更新
    if (currentTime - this.lastLearningUpdate > this.config.learningUpdateInterval) {
      this.performLearningUpdate();
      this.lastLearningUpdate = currentTime;
    }

    // 更新适应策略
    this.updateAdaptationStrategies(deltaTime);

    // 清理过期数据
    this.cleanupExpiredData();
  }

  /**
   * 记录用户情感交互
   * @param userId 用户ID
   * @param emotionType 情感类型
   * @param intensity 强度
   * @param context 上下文
   */
  public recordEmotionInteraction(
    userId: string,
    emotionType: string,
    intensity: number,
    context: any = {}
  ): void {
    // 获取或创建用户偏好
    let preference = this.userPreferences.get(userId);
    if (!preference) {
      preference = this.createUserPreference(userId);
      this.userPreferences.set(userId, preference);
    }

    // 更新情感类型偏好
    const currentTypePreference = preference.emotionTypePreferences.get(emotionType) || 0;
    preference.emotionTypePreferences.set(
      emotionType,
      currentTypePreference + this.config.learningRate
    );

    // 更新强度偏好
    const intensityKey = this.getIntensityKey(intensity);
    const currentIntensityPreference = preference.intensityPreferences.get(intensityKey) || 0;
    preference.intensityPreferences.set(
      intensityKey,
      currentIntensityPreference + this.config.learningRate
    );

    // 更新交互统计
    preference.interactionCount++;
    preference.lastUpdated = Date.now();

    // 触发学习事件
    this.eventEmitter.emit('emotionInteractionRecorded', {
      userId,
      emotionType,
      intensity,
      context,
    });

    if (this.config.debug) {
      console.log('记录情感交互:', { userId, emotionType, intensity });
    }
  }

  /**
   * 评估情感响应效果
   * @param evaluation 评估数据
   */
  public evaluateEmotionResponse(evaluation: EmotionResponseEvaluation): void {
    // 获取用户评估历史
    let history = this.evaluationHistory.get(evaluation.userId);
    if (!history) {
      history = [];
      this.evaluationHistory.set(evaluation.userId, history);
    }

    // 添加评估记录
    history.push(evaluation);

    // 限制历史记录数量
    if (history.length > this.config.maxHistorySize) {
      history.shift();
    }

    // 如果启用在线学习，立即更新模型
    if (this.config.enableOnlineLearning) {
      this.updateModelWithEvaluation(evaluation);
    }

    // 触发评估事件
    this.eventEmitter.emit('emotionResponseEvaluated', evaluation);

    if (this.config.debug) {
      console.log('评估情感响应:', evaluation);
    }
  }

  /**
   * 获取用户个性化情感参数
   * @param userId 用户ID
   * @param emotionType 情感类型
   * @returns 个性化参数
   */
  public getPersonalizedEmotionParameters(
    userId: string,
    emotionType: string
  ): {
    intensity: number;
    duration: number;
    responseTime: number;
  } {
    const preference = this.userPreferences.get(userId);
    
    if (!preference || !this.config.enablePersonalization) {
      // 返回默认参数
      return {
        intensity: 0.7,
        duration: 2.0,
        responseTime: 0.5,
      };
    }

    // 计算个性化强度
    const typePreference = preference.emotionTypePreferences.get(emotionType) || 0;
    const normalizedTypePreference = Math.min(1.0, typePreference / preference.interactionCount);
    
    // 计算个性化强度偏好
    const intensityPreference = this.calculateIntensityPreference(preference);
    
    return {
      intensity: Math.max(0.1, Math.min(1.0, 0.7 + (normalizedTypePreference - 0.5) * 0.4)),
      duration: Math.max(0.5, Math.min(5.0, 2.0 + (intensityPreference - 0.5) * 2.0)),
      responseTime: Math.max(0.1, Math.min(2.0, preference.responseTimePreferences)),
    };
  }

  /**
   * 获取推荐的情感适应策略
   * @param userId 用户ID
   * @param context 上下文
   * @returns 推荐策略
   */
  public getRecommendedAdaptationStrategy(
    userId: string,
    context: any = {}
  ): EmotionAdaptationStrategy | null {
    const strategies = Array.from(this.adaptationStrategies.values());
    
    if (strategies.length === 0) return null;

    // 根据效果评分排序
    strategies.sort((a, b) => b.effectivenessScore - a.effectivenessScore);

    // 返回最佳策略
    return strategies[0];
  }

  /**
   * 初始化学习参数
   */
  private initializeLearningParameters(): void {
    this.learningParameters.set('emotionTypeWeight', 0.4);
    this.learningParameters.set('intensityWeight', 0.3);
    this.learningParameters.set('durationWeight', 0.2);
    this.learningParameters.set('responseTimeWeight', 0.1);
  }

  /**
   * 初始化适应策略
   */
  private initializeAdaptationStrategies(): void {
    // 保守策略
    this.adaptationStrategies.set('conservative', {
      name: 'conservative',
      weight: 0.8,
      parameters: new Map([
        ['intensityMultiplier', 0.8],
        ['durationMultiplier', 1.2],
        ['responseDelayMultiplier', 1.1],
      ]),
      effectivenessScore: 0.7,
      usageCount: 0,
    });

    // 积极策略
    this.adaptationStrategies.set('aggressive', {
      name: 'aggressive',
      weight: 1.2,
      parameters: new Map([
        ['intensityMultiplier', 1.3],
        ['durationMultiplier', 0.8],
        ['responseDelayMultiplier', 0.7],
      ]),
      effectivenessScore: 0.6,
      usageCount: 0,
    });

    // 平衡策略
    this.adaptationStrategies.set('balanced', {
      name: 'balanced',
      weight: 1.0,
      parameters: new Map([
        ['intensityMultiplier', 1.0],
        ['durationMultiplier', 1.0],
        ['responseDelayMultiplier', 1.0],
      ]),
      effectivenessScore: 0.8,
      usageCount: 0,
    });
  }

  /**
   * 创建用户偏好
   * @param userId 用户ID
   * @returns 用户偏好
   */
  private createUserPreference(userId: string): UserEmotionPreference {
    return {
      userId,
      emotionTypePreferences: new Map(),
      intensityPreferences: new Map(),
      responseTimePreferences: 0.5,
      interactionFrequencyPreferences: 1.0,
      lastUpdated: Date.now(),
      interactionCount: 0,
    };
  }

  /**
   * 获取强度键
   * @param intensity 强度值
   * @returns 强度键
   */
  private getIntensityKey(intensity: number): string {
    if (intensity < 0.3) return 'low';
    if (intensity < 0.7) return 'medium';
    return 'high';
  }

  /**
   * 计算强度偏好
   * @param preference 用户偏好
   * @returns 强度偏好值
   */
  private calculateIntensityPreference(preference: UserEmotionPreference): number {
    const lowCount = preference.intensityPreferences.get('low') || 0;
    const mediumCount = preference.intensityPreferences.get('medium') || 0;
    const highCount = preference.intensityPreferences.get('high') || 0;
    
    const total = lowCount + mediumCount + highCount;
    if (total === 0) return 0.5;

    // 计算加权平均
    return (lowCount * 0.2 + mediumCount * 0.5 + highCount * 0.8) / total;
  }

  /**
   * 执行学习更新
   */
  private performLearningUpdate(): void {
    // 更新所有用户的偏好
    for (const [userId, preference] of this.userPreferences) {
      this.updateUserPreference(userId, preference);
    }

    // 更新适应策略效果
    this.updateAdaptationStrategiesEffectiveness();

    if (this.config.debug) {
      console.log('执行学习更新');
    }
  }

  /**
   * 更新用户偏好
   * @param userId 用户ID
   * @param preference 用户偏好
   */
  private updateUserPreference(userId: string, preference: UserEmotionPreference): void {
    // 应用遗忘因子
    for (const [key, value] of preference.emotionTypePreferences) {
      preference.emotionTypePreferences.set(key, value * this.config.forgettingFactor);
    }

    for (const [key, value] of preference.intensityPreferences) {
      preference.intensityPreferences.set(key, value * this.config.forgettingFactor);
    }

    // 更新响应时间偏好
    preference.responseTimePreferences *= this.config.forgettingFactor;
  }

  /**
   * 更新适应策略
   * @param deltaTime 帧间隔时间
   */
  private updateAdaptationStrategies(deltaTime: number): void {
    // 这里可以添加策略动态调整逻辑
    // 目前保持简单实现
  }

  /**
   * 使用评估更新模型
   * @param evaluation 评估数据
   */
  private updateModelWithEvaluation(evaluation: EmotionResponseEvaluation): void {
    // 根据用户反馈调整学习参数
    const feedbackWeight = evaluation.userFeedbackScore * this.config.learningRate;
    
    // 更新情感类型权重
    const currentTypeWeight = this.learningParameters.get('emotionTypeWeight') || 0.4;
    this.learningParameters.set(
      'emotionTypeWeight',
      currentTypeWeight + feedbackWeight * 0.1
    );
  }

  /**
   * 更新适应策略效果
   */
  private updateAdaptationStrategiesEffectiveness(): void {
    for (const [name, strategy] of this.adaptationStrategies) {
      // 根据使用情况和反馈更新效果评分
      if (strategy.usageCount > 0) {
        // 简化的效果更新逻辑
        strategy.effectivenessScore = Math.max(0.1, 
          strategy.effectivenessScore * 0.9 + 0.1 * (strategy.usageCount / 10)
        );
      }
    }
  }

  /**
   * 清理过期数据
   */
  private cleanupExpiredData(): void {
    const currentTime = Date.now();
    const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天

    // 清理过期的用户偏好
    for (const [userId, preference] of this.userPreferences) {
      if (currentTime - preference.lastUpdated > maxAge) {
        this.userPreferences.delete(userId);
      }
    }

    // 清理过期的评估历史
    for (const [userId, history] of this.evaluationHistory) {
      const filteredHistory = history.filter(
        evaluation => currentTime - evaluation.timestamp < maxAge
      );
      
      if (filteredHistory.length === 0) {
        this.evaluationHistory.delete(userId);
      } else {
        this.evaluationHistory.set(userId, filteredHistory);
      }
    }
  }

  /**
   * 加载历史数据
   */
  private async loadHistoricalData(): Promise<void> {
    // 这里可以从数据库或文件加载历史数据
    // 目前保持空实现
    if (this.config.debug) {
      console.log('加载历史数据');
    }
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    this.userPreferences.clear();
    this.evaluationHistory.clear();
    this.adaptationStrategies.clear();
    this.learningParameters.clear();
    this.eventEmitter.removeAllListeners();
    this.initialized = false;
  }
}
