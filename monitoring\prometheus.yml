# Prometheus配置文件
# 用于监控DL引擎RAG应用系统

global:
  scrape_interval: 15s
  evaluation_interval: 15s

# 规则文件
rule_files:
  - "rules/*.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node Exporter - 系统指标
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # API网关监控
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:3000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # 知识库服务监控
  - job_name: 'knowledge-service'
    static_configs:
      - targets: ['knowledge-service:4011']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # RAG服务监控
  - job_name: 'rag-service'
    static_configs:
      - targets: ['rag-service:4012']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # 数字人服务监控
  - job_name: 'avatar-service'
    static_configs:
      - targets: ['avatar-service:4013']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # 语音服务监控
  - job_name: 'voice-service'
    static_configs:
      - targets: ['voice-service:4014']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # MySQL监控
  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql-exporter:9104']

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # Elasticsearch监控
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch-exporter:9114']

  # Nginx监控
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']

  # 自定义业务指标
  - job_name: 'rag-business-metrics'
    static_configs:
      - targets: ['api-gateway:3000']
    metrics_path: '/api/metrics/business'
    scrape_interval: 30s

# 远程写入配置（可选）
# remote_write:
#   - url: "https://your-remote-prometheus/api/v1/write"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"

# 远程读取配置（可选）
# remote_read:
#   - url: "https://your-remote-prometheus/api/v1/read"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"
