import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { 
  XAPIStatement, 
  StatementResult, 
  StatementQuery,
  Actor 
} from '../xapi/interfaces/xapi.interface';

/**
 * Learninglocker客户端配置
 */
export interface LearninglocketConfig {
  endpoint: string;              // Learninglocker LRS端点
  username: string;              // 基本认证用户名
  password: string;              // 基本认证密码
  version: string;               // xAPI版本
  organization: string;          // 组织标识
  store: string;                 // 存储标识
  timeout?: number;              // 请求超时时间
}

/**
 * Learninglocker客户端服务
 * 负责与Learninglocker 7 LRS通信
 */
@Injectable()
export class LearninglocketClientService {
  private readonly logger = new Logger(LearninglocketClientService.name);
  private readonly config: LearninglocketConfig;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.config = {
      endpoint: this.configService.get<string>('LEARNINGLOCKER_ENDPOINT'),
      username: this.configService.get<string>('LEARNINGLOCKER_USERNAME'),
      password: this.configService.get<string>('LEARNINGLOCKER_PASSWORD'),
      version: this.configService.get<string>('LEARNINGLOCKER_VERSION', '2.0.0'),
      organization: this.configService.get<string>('LEARNINGLOCKER_ORGANIZATION'),
      store: this.configService.get<string>('LEARNINGLOCKER_STORE'),
      timeout: this.configService.get<number>('LEARNINGLOCKER_TIMEOUT', 30000),
    };

    this.setupHttpClient();
  }

  /**
   * 配置HTTP客户端
   */
  private setupHttpClient(): void {
    const auth = Buffer.from(`${this.config.username}:${this.config.password}`).toString('base64');
    
    this.httpService.axiosRef.defaults.baseURL = this.config.endpoint;
    this.httpService.axiosRef.defaults.timeout = this.config.timeout;
    this.httpService.axiosRef.defaults.headers.common['Authorization'] = `Basic ${auth}`;
    this.httpService.axiosRef.defaults.headers.common['X-Experience-API-Version'] = this.config.version;
    this.httpService.axiosRef.defaults.headers.common['Content-Type'] = 'application/json';
    
    // 添加请求拦截器
    this.httpService.axiosRef.interceptors.request.use(
      (config) => {
        this.logger.debug(`发送请求到Learninglocker: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        this.logger.error('请求拦截器错误:', error);
        return Promise.reject(error);
      }
    );

    // 添加响应拦截器
    this.httpService.axiosRef.interceptors.response.use(
      (response) => {
        this.logger.debug(`收到Learninglocker响应: ${response.status} ${response.statusText}`);
        return response;
      },
      (error) => {
        this.logger.error('响应拦截器错误:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * 发送单个xAPI语句到Learninglocker
   * @param statement xAPI语句
   * @returns 语句ID
   */
  async sendStatement(statement: XAPIStatement): Promise<string> {
    try {
      // 确保语句有ID
      if (!statement.id) {
        statement.id = this.generateUUID();
      }

      // 确保有时间戳
      if (!statement.timestamp) {
        statement.timestamp = new Date().toISOString();
      }

      // 验证语句
      this.validateStatement(statement);

      const response = await firstValueFrom(
        this.httpService.post('/statements', statement)
      );

      this.logger.log(`成功发送xAPI语句到Learninglocker: ${statement.id}`);
      return statement.id;
    } catch (error) {
      this.logger.error(`发送xAPI语句到Learninglocker失败: ${error.message}`, error.stack);
      throw new Error(`发送xAPI语句到Learninglocker失败: ${error.message}`);
    }
  }

  /**
   * 批量发送xAPI语句到Learninglocker
   * @param statements xAPI语句数组
   * @returns 语句ID数组
   */
  async sendStatements(statements: XAPIStatement[]): Promise<string[]> {
    try {
      if (!statements || statements.length === 0) {
        return [];
      }

      // 为每个语句生成ID和时间戳
      const processedStatements = statements.map(statement => {
        if (!statement.id) {
          statement.id = this.generateUUID();
        }
        if (!statement.timestamp) {
          statement.timestamp = new Date().toISOString();
        }
        
        // 验证语句
        this.validateStatement(statement);
        
        return statement;
      });

      const response = await firstValueFrom(
        this.httpService.post('/statements', processedStatements)
      );

      const statementIds = processedStatements.map(s => s.id);
      this.logger.log(`成功批量发送 ${statementIds.length} 条xAPI语句到Learninglocker`);
      return statementIds;
    } catch (error) {
      this.logger.error(`批量发送xAPI语句到Learninglocker失败: ${error.message}`, error.stack);
      throw new Error(`批量发送xAPI语句到Learninglocker失败: ${error.message}`);
    }
  }

  /**
   * 从Learninglocker查询xAPI语句
   * @param query 查询参数
   * @returns 语句结果
   */
  async getStatements(query: StatementQuery = {}): Promise<StatementResult> {
    try {
      const params = this.buildQueryParams(query);
      
      const response = await firstValueFrom(
        this.httpService.get('/statements', { params })
      );

      this.logger.log(`从Learninglocker查询到 ${response.data.statements?.length || 0} 条xAPI语句`);
      return response.data;
    } catch (error) {
      this.logger.error(`从Learninglocker查询xAPI语句失败: ${error.message}`, error.stack);
      throw new Error(`从Learninglocker查询xAPI语句失败: ${error.message}`);
    }
  }

  /**
   * 从Learninglocker获取单个语句
   * @param statementId 语句ID
   * @returns xAPI语句
   */
  async getStatement(statementId: string): Promise<XAPIStatement> {
    try {
      const response = await firstValueFrom(
        this.httpService.get('/statements', { 
          params: { statementId } 
        })
      );

      if (!response.data.statements || response.data.statements.length === 0) {
        throw new Error(`在Learninglocker中未找到语句: ${statementId}`);
      }

      return response.data.statements[0];
    } catch (error) {
      this.logger.error(`从Learninglocker获取xAPI语句失败: ${error.message}`, error.stack);
      throw new Error(`从Learninglocker获取xAPI语句失败: ${error.message}`);
    }
  }

  /**
   * 获取用户的学习记录
   * @param userId 用户ID
   * @param options 查询选项
   * @returns 学习记录
   */
  async getUserLearningRecords(
    userId: string, 
    options: {
      since?: Date;
      until?: Date;
      limit?: number;
      verb?: string;
      activity?: string;
    } = {}
  ): Promise<StatementResult> {
    try {
      const agent: Actor = {
        objectType: 'Agent',
        account: {
          homePage: 'http://dl-engine.com',
          name: userId
        }
      };

      const query: StatementQuery = {
        agent,
        limit: options.limit || 100,
        ascending: false
      };

      if (options.since) {
        query.since = options.since.toISOString();
      }

      if (options.until) {
        query.until = options.until.toISOString();
      }

      if (options.verb) {
        query.verb = options.verb;
      }

      if (options.activity) {
        query.activity = options.activity;
      }

      return await this.getStatements(query);
    } catch (error) {
      this.logger.error(`获取用户学习记录失败: ${error.message}`, error.stack);
      throw new Error(`获取用户学习记录失败: ${error.message}`);
    }
  }

  /**
   * 获取学习分析数据
   * @param userId 用户ID
   * @param timeRange 时间范围
   * @returns 分析数据
   */
  async getLearningAnalytics(
    userId: string,
    timeRange: { start: Date; end: Date }
  ): Promise<any> {
    try {
      const records = await this.getUserLearningRecords(userId, {
        since: timeRange.start,
        until: timeRange.end,
        limit: 1000
      });

      return this.analyzeLearningRecords(records.statements);
    } catch (error) {
      this.logger.error(`获取学习分析数据失败: ${error.message}`, error.stack);
      throw new Error(`获取学习分析数据失败: ${error.message}`);
    }
  }

  /**
   * 分析学习记录
   * @param statements xAPI语句数组
   * @returns 分析结果
   */
  private analyzeLearningRecords(statements: XAPIStatement[]): any {
    const analytics = {
      totalActivities: statements.length,
      verbCounts: {} as { [verb: string]: number },
      emotionCounts: {} as { [emotion: string]: number },
      knowledgeAreas: {} as { [area: string]: number },
      averageScore: 0,
      completionRate: 0,
      timeSpent: 0,
      sessionCount: 0
    };

    let totalScore = 0;
    let scoreCount = 0;
    let completedCount = 0;
    const sessions = new Set<string>();

    statements.forEach(statement => {
      // 统计动词
      const verbId = statement.verb.id;
      analytics.verbCounts[verbId] = (analytics.verbCounts[verbId] || 0) + 1;

      // 统计情感
      const emotion = statement.result?.extensions?.['http://dl-engine.com/xapi/extensions/emotion'];
      if (emotion) {
        analytics.emotionCounts[emotion] = (analytics.emotionCounts[emotion] || 0) + 1;
      }

      // 统计知识领域
      const knowledgeArea = statement.context?.extensions?.['http://dl-engine.com/xapi/extensions/knowledge-area'];
      if (knowledgeArea) {
        analytics.knowledgeAreas[knowledgeArea] = (analytics.knowledgeAreas[knowledgeArea] || 0) + 1;
      }

      // 统计分数
      if (statement.result?.score?.scaled !== undefined) {
        totalScore += statement.result.score.scaled;
        scoreCount++;
      }

      // 统计完成情况
      if (statement.result?.completion) {
        completedCount++;
      }

      // 统计会话
      const sessionId = statement.context?.extensions?.['http://dl-engine.com/xapi/extensions/session-id'];
      if (sessionId) {
        sessions.add(sessionId);
      }

      // 统计时间
      if (statement.result?.duration) {
        analytics.timeSpent += this.parseDuration(statement.result.duration);
      }
    });

    analytics.averageScore = scoreCount > 0 ? totalScore / scoreCount : 0;
    analytics.completionRate = statements.length > 0 ? completedCount / statements.length : 0;
    analytics.sessionCount = sessions.size;

    return analytics;
  }

  /**
   * 解析ISO 8601持续时间
   * @param duration ISO 8601持续时间字符串
   * @returns 毫秒数
   */
  private parseDuration(duration: string): number {
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return 0;

    const hours = parseInt(match[1] || '0', 10);
    const minutes = parseInt(match[2] || '0', 10);
    const seconds = parseInt(match[3] || '0', 10);

    return (hours * 3600 + minutes * 60 + seconds) * 1000;
  }

  /**
   * 测试与Learninglocker的连接
   * @returns 连接状态
   */
  async testConnection(): Promise<boolean> {
    try {
      await firstValueFrom(
        this.httpService.get('/about')
      );
      this.logger.log('Learninglocker连接测试成功');
      return true;
    } catch (error) {
      this.logger.error(`Learninglocker连接测试失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取Learninglocker状态信息
   * @returns 状态信息
   */
  async getStatus(): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.httpService.get('/about')
      );
      return response.data;
    } catch (error) {
      this.logger.error(`获取Learninglocker状态失败: ${error.message}`);
      throw new Error(`获取Learninglocker状态失败: ${error.message}`);
    }
  }

  /**
   * 验证xAPI语句
   * @param statement xAPI语句
   */
  private validateStatement(statement: XAPIStatement): void {
    if (!statement.actor) {
      throw new Error('xAPI语句缺少actor字段');
    }

    if (!statement.verb || !statement.verb.id) {
      throw new Error('xAPI语句缺少verb字段或verb.id');
    }

    if (!statement.object) {
      throw new Error('xAPI语句缺少object字段');
    }

    // 验证时间戳格式
    if (statement.timestamp && !this.isValidISO8601(statement.timestamp)) {
      throw new Error('时间戳格式无效，必须是ISO 8601格式');
    }
  }

  /**
   * 构建查询参数
   * @param query 查询对象
   * @returns 查询参数
   */
  private buildQueryParams(query: StatementQuery): any {
    const params: any = {};

    Object.keys(query).forEach(key => {
      const value = query[key];
      if (value !== undefined && value !== null) {
        if (typeof value === 'object') {
          params[key] = JSON.stringify(value);
        } else {
          params[key] = value;
        }
      }
    });

    return params;
  }

  /**
   * 生成UUID
   * @returns UUID字符串
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * 验证ISO 8601时间戳格式
   * @param timestamp 时间戳字符串
   * @returns 是否有效
   */
  private isValidISO8601(timestamp: string): boolean {
    const iso8601Regex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;
    return iso8601Regex.test(timestamp) && !isNaN(Date.parse(timestamp));
  }

  /**
   * 获取客户端配置信息（隐藏敏感信息）
   * @returns 配置信息
   */
  getConfig(): Partial<LearninglocketConfig> {
    return {
      endpoint: this.config.endpoint,
      version: this.config.version,
      organization: this.config.organization,
      store: this.config.store,
      timeout: this.config.timeout
    };
  }
}
