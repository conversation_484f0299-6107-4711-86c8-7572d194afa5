/**
 * 撤销/重做服务
 * 提供操作历史管理功能
 */

// 操作类型枚举
export enum ActionType {
  CREATE_ELEMENT = 'createElement',
  DELETE_ELEMENT = 'deleteElement',
  UPDATE_ELEMENT = 'updateElement',
  MOVE_ELEMENT = 'moveElement',
  RESIZE_ELEMENT = 'resizeElement',
  CHANGE_PROPERTY = 'changeProperty',
  GROUP_ELEMENTS = 'groupElements',
  UNGROUP_ELEMENTS = 'ungroupElements',
  DUPLICATE_ELEMENT = 'duplicateElement',
  PASTE_ELEMENTS = 'pasteElements'
}

// 操作数据接口
export interface ActionData {
  type: ActionType;
  elementId?: string;
  elementIds?: string[];
  oldValue?: any;
  newValue?: any;
  position?: { x: number; y: number };
  size?: { width: number; height: number };
  properties?: Record<string, any>;
  parentId?: string;
  index?: number;
  timestamp: number;
  description: string;
}

// 操作接口
export interface Action {
  id: string;
  type: ActionType;
  data: ActionData;
  execute: () => void;
  undo: () => void;
  redo: () => void;
  canMerge?: (action: Action) => boolean;
  merge?: (action: Action) => Action;
}

// 操作组接口（用于批量操作）
export interface ActionGroup {
  id: string;
  name: string;
  actions: Action[];
  timestamp: number;
}

// 历史记录配置
export interface UndoRedoConfig {
  maxHistorySize: number;
  enableGrouping: boolean;
  groupTimeout: number; // 毫秒
  enableMerging: boolean;
  mergeTimeout: number; // 毫秒
}

// 默认配置
const DEFAULT_CONFIG: UndoRedoConfig = {
  maxHistorySize: 100,
  enableGrouping: true,
  groupTimeout: 1000,
  enableMerging: true,
  mergeTimeout: 500
};

/**
 * 撤销/重做服务类
 */
export class UndoRedoService {
  private config: UndoRedoConfig;
  private history: (Action | ActionGroup)[] = [];
  private currentIndex: number = -1;
  private isExecuting: boolean = false;
  private currentGroup?: ActionGroup;
  private groupTimer?: number;
  private lastActionTime: number = 0;

  // 事件监听器
  private listeners: {
    historyChange: ((canUndo: boolean, canRedo: boolean) => void)[];
    actionExecute: ((action: Action) => void)[];
    actionUndo: ((action: Action) => void)[];
    actionRedo: ((action: Action) => void)[];
  } = {
    historyChange: [],
    actionExecute: [],
    actionUndo: [],
    actionRedo: []
  };

  constructor(config: Partial<UndoRedoConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * 执行操作
   * @param action 操作
   */
  execute(action: Action): void {
    if (this.isExecuting) return;

    this.isExecuting = true;

    try {
      // 执行操作
      action.execute();

      // 触发执行事件
      this.listeners.actionExecute.forEach(listener => listener(action));

      // 处理历史记录
      this.addToHistory(action);

      // 更新时间戳
      this.lastActionTime = Date.now();
    } finally {
      this.isExecuting = false;
    }
  }

  /**
   * 批量执行操作
   * @param actions 操作列表
   * @param groupName 组名称
   */
  executeGroup(actions: Action[], groupName: string): void {
    if (actions.length === 0) return;

    const group: ActionGroup = {
      id: `group_${Date.now()}`,
      name: groupName,
      actions,
      timestamp: Date.now()
    };

    this.isExecuting = true;

    try {
      // 执行所有操作
      actions.forEach(action => {
        action.execute();
        this.listeners.actionExecute.forEach(listener => listener(action));
      });

      // 添加到历史记录
      this.addGroupToHistory(group);
    } finally {
      this.isExecuting = false;
    }
  }

  /**
   * 开始操作组
   * @param groupName 组名称
   */
  startGroup(groupName: string): void {
    if (this.currentGroup) {
      this.endGroup();
    }

    this.currentGroup = {
      id: `group_${Date.now()}`,
      name: groupName,
      actions: [],
      timestamp: Date.now()
    };

    // 设置组超时
    if (this.config.enableGrouping && this.config.groupTimeout > 0) {
      this.groupTimer = window.setTimeout(() => {
        this.endGroup();
      }, this.config.groupTimeout);
    }
  }

  /**
   * 结束操作组
   */
  endGroup(): void {
    if (!this.currentGroup) return;

    // 清除定时器
    if (this.groupTimer) {
      clearTimeout(this.groupTimer);
      this.groupTimer = undefined;
    }

    // 如果组中有操作，添加到历史记录
    if (this.currentGroup.actions.length > 0) {
      this.addGroupToHistory(this.currentGroup);
    }

    this.currentGroup = undefined;
  }

  /**
   * 撤销操作
   */
  undo(): boolean {
    if (!this.canUndo()) return false;

    const item = this.history[this.currentIndex];
    this.isExecuting = true;

    try {
      if (this.isActionGroup(item)) {
        // 撤销操作组
        for (let i = item.actions.length - 1; i >= 0; i--) {
          const action = item.actions[i];
          action.undo();
          this.listeners.actionUndo.forEach(listener => listener(action));
        }
      } else {
        // 撤销单个操作
        item.undo();
        this.listeners.actionUndo.forEach(listener => listener(item));
      }

      this.currentIndex--;
      this.notifyHistoryChange();
      return true;
    } finally {
      this.isExecuting = false;
    }
  }

  /**
   * 重做操作
   */
  redo(): boolean {
    if (!this.canRedo()) return false;

    this.currentIndex++;
    const item = this.history[this.currentIndex];
    this.isExecuting = true;

    try {
      if (this.isActionGroup(item)) {
        // 重做操作组
        item.actions.forEach(action => {
          action.redo();
          this.listeners.actionRedo.forEach(listener => listener(action));
        });
      } else {
        // 重做单个操作
        item.redo();
        this.listeners.actionRedo.forEach(listener => listener(item));
      }

      this.notifyHistoryChange();
      return true;
    } finally {
      this.isExecuting = false;
    }
  }

  /**
   * 是否可以撤销
   */
  canUndo(): boolean {
    return this.currentIndex >= 0;
  }

  /**
   * 是否可以重做
   */
  canRedo(): boolean {
    return this.currentIndex < this.history.length - 1;
  }

  /**
   * 清空历史记录
   */
  clear(): void {
    this.history = [];
    this.currentIndex = -1;
    this.currentGroup = undefined;
    
    if (this.groupTimer) {
      clearTimeout(this.groupTimer);
      this.groupTimer = undefined;
    }

    this.notifyHistoryChange();
  }

  /**
   * 获取历史记录
   */
  getHistory(): (Action | ActionGroup)[] {
    return [...this.history];
  }

  /**
   * 获取当前索引
   */
  getCurrentIndex(): number {
    return this.currentIndex;
  }

  /**
   * 添加操作到历史记录
   */
  private addToHistory(action: Action): void {
    // 如果正在组操作中，添加到当前组
    if (this.currentGroup) {
      this.currentGroup.actions.push(action);
      return;
    }

    // 尝试合并操作
    if (this.config.enableMerging && this.canMergeWithLast(action)) {
      const lastAction = this.history[this.currentIndex] as Action;
      if (lastAction.merge) {
        const mergedAction = lastAction.merge(action);
        this.history[this.currentIndex] = mergedAction;
        this.notifyHistoryChange();
        return;
      }
    }

    // 移除当前索引之后的所有历史记录
    this.history = this.history.slice(0, this.currentIndex + 1);

    // 添加新操作
    this.history.push(action);
    this.currentIndex++;

    // 限制历史记录大小
    this.limitHistorySize();

    this.notifyHistoryChange();
  }

  /**
   * 添加操作组到历史记录
   */
  private addGroupToHistory(group: ActionGroup): void {
    // 移除当前索引之后的所有历史记录
    this.history = this.history.slice(0, this.currentIndex + 1);

    // 添加新操作组
    this.history.push(group);
    this.currentIndex++;

    // 限制历史记录大小
    this.limitHistorySize();

    this.notifyHistoryChange();
  }

  /**
   * 检查是否可以与最后一个操作合并
   */
  private canMergeWithLast(action: Action): boolean {
    if (this.currentIndex < 0) return false;

    const lastItem = this.history[this.currentIndex];
    if (this.isActionGroup(lastItem)) return false;

    const lastAction = lastItem as Action;
    const timeDiff = Date.now() - this.lastActionTime;

    return (
      timeDiff <= this.config.mergeTimeout &&
      lastAction.canMerge &&
      lastAction.canMerge(action)
    );
  }

  /**
   * 限制历史记录大小
   */
  private limitHistorySize(): void {
    if (this.history.length > this.config.maxHistorySize) {
      const removeCount = this.history.length - this.config.maxHistorySize;
      this.history = this.history.slice(removeCount);
      this.currentIndex -= removeCount;
    }
  }

  /**
   * 判断是否为操作组
   */
  private isActionGroup(item: Action | ActionGroup): item is ActionGroup {
    return 'actions' in item;
  }

  /**
   * 通知历史记录变化
   */
  private notifyHistoryChange(): void {
    const canUndo = this.canUndo();
    const canRedo = this.canRedo();
    this.listeners.historyChange.forEach(listener => listener(canUndo, canRedo));
  }

  /**
   * 添加事件监听器
   */
  on(event: 'historyChange', listener: (canUndo: boolean, canRedo: boolean) => void): void;
  on(event: 'actionExecute' | 'actionUndo' | 'actionRedo', listener: (action: Action) => void): void;
  on(event: string, listener: any): void {
    if (this.listeners[event as keyof typeof this.listeners]) {
      this.listeners[event as keyof typeof this.listeners].push(listener);
    }
  }

  /**
   * 移除事件监听器
   */
  off(event: 'historyChange', listener: (canUndo: boolean, canRedo: boolean) => void): void;
  off(event: 'actionExecute' | 'actionUndo' | 'actionRedo', listener: (action: Action) => void): void;
  off(event: string, listener: any): void {
    if (this.listeners[event as keyof typeof this.listeners]) {
      const listeners = this.listeners[event as keyof typeof this.listeners];
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 销毁服务
   */
  dispose(): void {
    this.clear();
    
    // 清空所有监听器
    Object.keys(this.listeners).forEach(key => {
      this.listeners[key as keyof typeof this.listeners] = [];
    });
  }
}

// 创建全局实例
export const undoRedoService = new UndoRedoService();

// 操作工厂函数
export class ActionFactory {
  /**
   * 创建元素操作
   */
  static createElementAction(
    elementData: any,
    executeCallback: () => void,
    undoCallback: () => void
  ): Action {
    return {
      id: `create_${Date.now()}`,
      type: ActionType.CREATE_ELEMENT,
      data: {
        type: ActionType.CREATE_ELEMENT,
        elementId: elementData.id,
        newValue: elementData,
        timestamp: Date.now(),
        description: `创建元素: ${elementData.name || elementData.type}`
      },
      execute: executeCallback,
      undo: undoCallback,
      redo: executeCallback
    };
  }

  /**
   * 删除元素操作
   */
  static deleteElementAction(
    elementData: any,
    executeCallback: () => void,
    undoCallback: () => void
  ): Action {
    return {
      id: `delete_${Date.now()}`,
      type: ActionType.DELETE_ELEMENT,
      data: {
        type: ActionType.DELETE_ELEMENT,
        elementId: elementData.id,
        oldValue: elementData,
        timestamp: Date.now(),
        description: `删除元素: ${elementData.name || elementData.type}`
      },
      execute: executeCallback,
      undo: undoCallback,
      redo: executeCallback
    };
  }

  /**
   * 更新元素操作
   */
  static updateElementAction(
    elementId: string,
    oldValue: any,
    newValue: any,
    executeCallback: () => void,
    undoCallback: () => void
  ): Action {
    return {
      id: `update_${Date.now()}`,
      type: ActionType.UPDATE_ELEMENT,
      data: {
        type: ActionType.UPDATE_ELEMENT,
        elementId,
        oldValue,
        newValue,
        timestamp: Date.now(),
        description: `更新元素: ${elementId}`
      },
      execute: executeCallback,
      undo: undoCallback,
      redo: executeCallback,
      canMerge: (action: Action) => {
        return (
          action.type === ActionType.UPDATE_ELEMENT &&
          action.data.elementId === elementId
        );
      },
      merge: (action: Action) => {
        return ActionFactory.updateElementAction(
          elementId,
          oldValue,
          action.data.newValue,
          action.execute,
          undoCallback
        );
      }
    };
  }

  /**
   * 移动元素操作
   */
  static moveElementAction(
    elementId: string,
    oldPosition: { x: number; y: number },
    newPosition: { x: number; y: number },
    executeCallback: () => void,
    undoCallback: () => void
  ): Action {
    return {
      id: `move_${Date.now()}`,
      type: ActionType.MOVE_ELEMENT,
      data: {
        type: ActionType.MOVE_ELEMENT,
        elementId,
        oldValue: oldPosition,
        newValue: newPosition,
        timestamp: Date.now(),
        description: `移动元素: ${elementId}`
      },
      execute: executeCallback,
      undo: undoCallback,
      redo: executeCallback,
      canMerge: (action: Action) => {
        return (
          action.type === ActionType.MOVE_ELEMENT &&
          action.data.elementId === elementId
        );
      },
      merge: (action: Action) => {
        return ActionFactory.moveElementAction(
          elementId,
          oldPosition,
          action.data.newValue,
          action.execute,
          undoCallback
        );
      }
    };
  }

  /**
   * 调整大小操作
   */
  static resizeElementAction(
    elementId: string,
    oldSize: { width: number; height: number },
    newSize: { width: number; height: number },
    executeCallback: () => void,
    undoCallback: () => void
  ): Action {
    return {
      id: `resize_${Date.now()}`,
      type: ActionType.RESIZE_ELEMENT,
      data: {
        type: ActionType.RESIZE_ELEMENT,
        elementId,
        oldValue: oldSize,
        newValue: newSize,
        timestamp: Date.now(),
        description: `调整大小: ${elementId}`
      },
      execute: executeCallback,
      undo: undoCallback,
      redo: executeCallback,
      canMerge: (action: Action) => {
        return (
          action.type === ActionType.RESIZE_ELEMENT &&
          action.data.elementId === elementId
        );
      },
      merge: (action: Action) => {
        return ActionFactory.resizeElementAction(
          elementId,
          oldSize,
          action.data.newValue,
          action.execute,
          undoCallback
        );
      }
    };
  }
}
