import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { KnowledgeBase, KnowledgeBaseStatus } from './entities/knowledge-base.entity';
import { CreateKnowledgeBaseDto, UpdateKnowledgeBaseDto, SearchKnowledgeBaseDto } from './dto/create-knowledge-base.dto';
import { VectorStoreService } from '../vector-store/vector-store.service';
import { SearchService } from '../search/search.service';

@Injectable()
export class KnowledgeBaseService {
  constructor(
    @InjectRepository(KnowledgeBase)
    private knowledgeBaseRepository: Repository<KnowledgeBase>,
    private vectorStoreService: VectorStoreService,
    private searchService: SearchService,
    private eventEmitter: EventEmitter2,
  ) {}

  /**
   * 创建知识库
   */
  async create(ownerId: string, createDto: CreateKnowledgeBaseDto): Promise<KnowledgeBase> {
    try {
      // 创建知识库实体
      const knowledgeBase = this.knowledgeBaseRepository.create({
        ...createDto,
        ownerId,
        status: createDto.status || KnowledgeBaseStatus.ACTIVE,
        config: {
          chunkSize: 1000,
          chunkOverlap: 200,
          embeddingModel: 'text-embedding-ada-002',
          searchThreshold: 0.7,
          maxResults: 10,
          ...createDto.config,
        },
        statistics: {
          documentCount: 0,
          totalSize: 0,
          vectorCount: 0,
          lastUpdated: new Date().toISOString(),
        },
      });

      const saved = await this.knowledgeBaseRepository.save(knowledgeBase);

      // 初始化向量存储集合
      await this.vectorStoreService.createCollection(saved.id);

      // 发送创建事件
      this.eventEmitter.emit('knowledge-base.created', {
        knowledgeBaseId: saved.id,
        ownerId,
        name: saved.name,
      });

      return saved;
    } catch (error) {
      throw new BadRequestException(`创建知识库失败: ${error.message}`);
    }
  }

  /**
   * 获取用户的知识库列表
   */
  async findByOwner(ownerId: string, page: number = 1, limit: number = 10): Promise<{
    data: KnowledgeBase[];
    total: number;
    page: number;
    limit: number;
  }> {
    const [data, total] = await this.knowledgeBaseRepository.findAndCount({
      where: { ownerId },
      relations: ['documents'],
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return { data, total, page, limit };
  }

  /**
   * 根据场景ID获取知识库
   */
  async findByScene(sceneId: string): Promise<KnowledgeBase[]> {
    return this.knowledgeBaseRepository.find({
      where: { sceneId, status: KnowledgeBaseStatus.ACTIVE },
      relations: ['documents'],
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 获取知识库详情
   */
  async findOne(id: string, ownerId?: string): Promise<KnowledgeBase> {
    const knowledgeBase = await this.knowledgeBaseRepository.findOne({
      where: { id },
      relations: ['documents'],
    });

    if (!knowledgeBase) {
      throw new NotFoundException('知识库不存在');
    }

    // 检查权限
    if (ownerId && knowledgeBase.ownerId !== ownerId) {
      throw new ForbiddenException('您没有权限访问此知识库');
    }

    return knowledgeBase;
  }

  /**
   * 更新知识库
   */
  async update(id: string, ownerId: string, updateDto: UpdateKnowledgeBaseDto): Promise<KnowledgeBase> {
    const knowledgeBase = await this.findOne(id, ownerId);

    // 更新字段
    Object.assign(knowledgeBase, updateDto);

    if (updateDto.config) {
      knowledgeBase.config = {
        ...knowledgeBase.config,
        ...updateDto.config,
      };
    }

    const updated = await this.knowledgeBaseRepository.save(knowledgeBase);

    // 发送更新事件
    this.eventEmitter.emit('knowledge-base.updated', {
      knowledgeBaseId: id,
      ownerId,
      changes: updateDto,
    });

    return updated;
  }

  /**
   * 删除知识库
   */
  async remove(id: string, ownerId: string): Promise<void> {
    const knowledgeBase = await this.findOne(id, ownerId);

    // 删除向量存储集合
    await this.vectorStoreService.deleteCollection(id);

    // 删除知识库
    await this.knowledgeBaseRepository.remove(knowledgeBase);

    // 发送删除事件
    this.eventEmitter.emit('knowledge-base.deleted', {
      knowledgeBaseId: id,
      ownerId,
      name: knowledgeBase.name,
    });
  }

  /**
   * 搜索知识库
   */
  async search(id: string, searchDto: SearchKnowledgeBaseDto, ownerId?: string): Promise<any> {
    const knowledgeBase = await this.findOne(id, ownerId);

    if (knowledgeBase.status !== KnowledgeBaseStatus.ACTIVE) {
      throw new BadRequestException('知识库当前不可用');
    }

    // 执行语义搜索
    const results = await this.searchService.search(id, searchDto.query, {
      topK: searchDto.topK || knowledgeBase.config.maxResults || 5,
      threshold: searchDto.threshold || knowledgeBase.config.searchThreshold || 0.7,
      filter: searchDto.filter,
    });

    // 记录搜索事件
    this.eventEmitter.emit('knowledge-base.searched', {
      knowledgeBaseId: id,
      query: searchDto.query,
      resultCount: results.length,
      ownerId,
    });

    return {
      query: searchDto.query,
      results,
      knowledgeBase: {
        id: knowledgeBase.id,
        name: knowledgeBase.name,
        description: knowledgeBase.description,
      },
    };
  }

  /**
   * 更新统计信息
   */
  async updateStatistics(id: string): Promise<void> {
    const knowledgeBase = await this.knowledgeBaseRepository.findOne({
      where: { id },
      relations: ['documents'],
    });

    if (!knowledgeBase) {
      return;
    }

    const documentCount = knowledgeBase.documents.length;
    const totalSize = knowledgeBase.documents.reduce((sum, doc) => sum + (doc.fileSize || 0), 0);
    const vectorCount = await this.vectorStoreService.getVectorCount(id);

    knowledgeBase.statistics = {
      documentCount,
      totalSize,
      vectorCount,
      lastUpdated: new Date().toISOString(),
    };

    await this.knowledgeBaseRepository.save(knowledgeBase);
  }

  /**
   * 获取知识库统计信息
   */
  async getStatistics(id: string, ownerId?: string): Promise<any> {
    const knowledgeBase = await this.findOne(id, ownerId);

    // 更新统计信息
    await this.updateStatistics(id);

    return {
      id: knowledgeBase.id,
      name: knowledgeBase.name,
      statistics: knowledgeBase.statistics,
      status: knowledgeBase.status,
      createdAt: knowledgeBase.createdAt,
      updatedAt: knowledgeBase.updatedAt,
    };
  }
}
