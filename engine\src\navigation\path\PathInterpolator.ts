/**
 * 路径插值器
 */
import * as THREE from 'three';
import { PathPoint } from './PathPoint';
import { InterpolationType } from '../types';

/**
 * 路径插值器类
 */
export class PathInterpolator {
  /**
   * 线性插值
   * @param point1 起始点
   * @param point2 结束点
   * @param t 插值参数 (0-1)
   * @returns 插值位置
   */
  public static linear(point1: PathPoint, point2: PathPoint, t: number): THREE.Vector3 {
    return point1.position.clone().lerp(point2.position, t);
  }

  /**
   * 平滑插值（使用平滑步函数）
   * @param point1 起始点
   * @param point2 结束点
   * @param t 插值参数 (0-1)
   * @returns 插值位置
   */
  public static smooth(point1: PathPoint, point2: PathPoint, t: number): THREE.Vector3 {
    // 平滑步函数: 3t² - 2t³
    const smoothT = t * t * (3 - 2 * t);
    return point1.position.clone().lerp(point2.position, smoothT);
  }

  /**
   * 贝塞尔曲线插值
   * @param point1 起始点
   * @param point2 结束点
   * @param t 插值参数 (0-1)
   * @param controlPoint1 控制点1（可选）
   * @param controlPoint2 控制点2（可选）
   * @returns 插值位置
   */
  public static bezier(
    point1: PathPoint,
    point2: PathPoint,
    t: number,
    controlPoint1?: THREE.Vector3,
    controlPoint2?: THREE.Vector3
  ): THREE.Vector3 {
    // 如果没有提供控制点，自动生成
    if (!controlPoint1 || !controlPoint2) {
      const direction = point2.position.clone().sub(point1.position);
      const distance = direction.length();
      direction.normalize();

      controlPoint1 = point1.position.clone().add(direction.clone().multiplyScalar(distance * 0.3));
      controlPoint2 = point2.position.clone().sub(direction.clone().multiplyScalar(distance * 0.3));
    }

    // 三次贝塞尔曲线
    const oneMinusT = 1 - t;
    const oneMinusT2 = oneMinusT * oneMinusT;
    const oneMinusT3 = oneMinusT2 * oneMinusT;
    const t2 = t * t;
    const t3 = t2 * t;

    const result = new THREE.Vector3();
    result.addScaledVector(point1.position, oneMinusT3);
    result.addScaledVector(controlPoint1, 3 * oneMinusT2 * t);
    result.addScaledVector(controlPoint2, 3 * oneMinusT * t2);
    result.addScaledVector(point2.position, t3);

    return result;
  }

  /**
   * 样条曲线插值（Catmull-Rom样条）
   * @param points 路径点数组
   * @param t 全局插值参数 (0-1)
   * @returns 插值位置
   */
  public static spline(points: PathPoint[], t: number): THREE.Vector3 {
    if (points.length < 2) {
      return points[0]?.position.clone() || new THREE.Vector3();
    }

    if (points.length === 2) {
      return this.linear(points[0], points[1], t);
    }

    // 将全局t转换为段索引和局部t
    const segmentCount = points.length - 1;
    const globalT = Math.max(0, Math.min(1, t));
    const segmentIndex = Math.floor(globalT * segmentCount);
    const localT = (globalT * segmentCount) - segmentIndex;

    // 确保索引在有效范围内
    const i = Math.min(segmentIndex, segmentCount - 1);

    // 获取四个控制点
    const p0 = points[Math.max(0, i - 1)].position;
    const p1 = points[i].position;
    const p2 = points[Math.min(points.length - 1, i + 1)].position;
    const p3 = points[Math.min(points.length - 1, i + 2)].position;

    // Catmull-Rom样条插值
    return this.catmullRom(p0, p1, p2, p3, localT);
  }

  /**
   * Catmull-Rom样条插值
   * @param p0 控制点0
   * @param p1 控制点1
   * @param p2 控制点2
   * @param p3 控制点3
   * @param t 插值参数 (0-1)
   * @returns 插值位置
   */
  private static catmullRom(
    p0: THREE.Vector3,
    p1: THREE.Vector3,
    p2: THREE.Vector3,
    p3: THREE.Vector3,
    t: number
  ): THREE.Vector3 {
    const t2 = t * t;
    const t3 = t2 * t;

    const v0 = p2.clone().sub(p0).multiplyScalar(0.5);
    const v1 = p3.clone().sub(p1).multiplyScalar(0.5);

    const result = new THREE.Vector3();
    result.addScaledVector(p1, 2 * t3 - 3 * t2 + 1);
    result.addScaledVector(p2, -2 * t3 + 3 * t2);
    result.addScaledVector(v0, t3 - 2 * t2 + t);
    result.addScaledVector(v1, t3 - t2);

    return result;
  }

  /**
   * 根据插值类型进行插值
   * @param type 插值类型
   * @param point1 起始点
   * @param point2 结束点
   * @param t 插值参数 (0-1)
   * @param points 所有路径点（用于样条插值）
   * @param globalT 全局插值参数（用于样条插值）
   * @returns 插值位置
   */
  public static interpolate(
    type: InterpolationType,
    point1: PathPoint,
    point2: PathPoint,
    t: number,
    points?: PathPoint[],
    globalT?: number
  ): THREE.Vector3 {
    switch (type) {
      case InterpolationType.LINEAR:
        return this.linear(point1, point2, t);
      
      case InterpolationType.SMOOTH:
        return this.smooth(point1, point2, t);
      
      case InterpolationType.BEZIER:
        return this.bezier(point1, point2, t);
      
      case InterpolationType.SPLINE:
        if (points && globalT !== undefined) {
          return this.spline(points, globalT);
        }
        return this.smooth(point1, point2, t);
      
      default:
        return this.linear(point1, point2, t);
    }
  }

  /**
   * 计算路径上指定点的切线方向
   * @param type 插值类型
   * @param point1 起始点
   * @param point2 结束点
   * @param t 插值参数 (0-1)
   * @param points 所有路径点（用于样条插值）
   * @param globalT 全局插值参数（用于样条插值）
   * @returns 切线方向（已归一化）
   */
  public static getTangent(
    type: InterpolationType,
    point1: PathPoint,
    point2: PathPoint,
    t: number,
    points?: PathPoint[],
    globalT?: number
  ): THREE.Vector3 {
    const epsilon = 0.001;
    const t1 = Math.max(0, t - epsilon);
    const t2 = Math.min(1, t + epsilon);

    const pos1 = this.interpolate(type, point1, point2, t1, points, globalT ? globalT - epsilon : undefined);
    const pos2 = this.interpolate(type, point1, point2, t2, points, globalT ? globalT + epsilon : undefined);

    return pos2.sub(pos1).normalize();
  }

  /**
   * 计算路径长度
   * @param points 路径点数组
   * @param type 插值类型
   * @param segments 分段数量（用于曲线长度计算）
   * @returns 路径长度
   */
  public static calculatePathLength(
    points: PathPoint[],
    type: InterpolationType,
    segments: number = 100
  ): number {
    if (points.length < 2) return 0;

    let totalLength = 0;

    for (let i = 0; i < points.length - 1; i++) {
      const point1 = points[i];
      const point2 = points[i + 1];

      if (type === InterpolationType.LINEAR) {
        // 线性插值直接计算距离
        totalLength += point1.distanceTo(point2);
      } else {
        // 曲线插值需要分段计算
        let segmentLength = 0;
        let prevPos = point1.position.clone();

        for (let j = 1; j <= segments; j++) {
          const t = j / segments;
          const currentPos = this.interpolate(type, point1, point2, t, points, (i + t) / (points.length - 1));
          segmentLength += prevPos.distanceTo(currentPos);
          prevPos = currentPos;
        }

        totalLength += segmentLength;
      }
    }

    return totalLength;
  }

  /**
   * 重新参数化路径（按弧长参数化）
   * @param points 路径点数组
   * @param type 插值类型
   * @param samples 采样点数量
   * @returns 弧长参数化的查找表
   */
  public static reparameterizeByArcLength(
    points: PathPoint[],
    type: InterpolationType,
    samples: number = 1000
  ): { t: number; s: number; position: THREE.Vector3 }[] {
    const table: { t: number; s: number; position: THREE.Vector3 }[] = [];
    
    if (points.length < 2) return table;

    let totalLength = 0;
    let prevPos = points[0].position.clone();

    table.push({ t: 0, s: 0, position: prevPos.clone() });

    for (let i = 1; i <= samples; i++) {
      const t = i / samples;
      const globalT = t * (points.length - 1);
      const segmentIndex = Math.floor(globalT);
      const localT = globalT - segmentIndex;

      const point1 = points[Math.min(segmentIndex, points.length - 2)];
      const point2 = points[Math.min(segmentIndex + 1, points.length - 1)];

      const currentPos = this.interpolate(type, point1, point2, localT, points, t);
      totalLength += prevPos.distanceTo(currentPos);

      table.push({ t, s: totalLength, position: currentPos.clone() });
      prevPos = currentPos;
    }

    return table;
  }
}
