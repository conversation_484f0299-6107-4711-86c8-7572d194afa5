/**
 * NFT管理器 - 处理NFT的创建、展示、交易等功能
 */

import { EventEmitter } from '../../utils/EventEmitter';
import { NFTRenderer } from './NFTRenderer';
import { NFTMinter } from './NFTMinter';
import { IPFSManager } from '../storage/IPFSManager';
import {
  NFTToken,
  NFTMetadata,
  NFTOperationResult,
  NFTSearchFilter,
  NFTRenderOptions
} from '../types/NFTTypes';
import { BlockchainManager } from '../core/BlockchainManager';

export class NFTManager extends EventEmitter {
  private blockchainManager: BlockchainManager;
  private nftRenderer: NFTRenderer;
  private nftMinter: NFTMinter;
  private ipfsManager: IPFSManager;
  private ownedNFTs: Map<string, NFTToken> = new Map();
  private displayedNFTs: Map<string, any> = new Map(); // 当前显示的NFT对象

  constructor(blockchainManager: BlockchainManager) {
    super();
    this.blockchainManager = blockchainManager;
    this.nftRenderer = new NFTRenderer(this);
    this.nftMinter = new NFTMinter(this);
    this.ipfsManager = new IPFSManager();
  }

  /**
   * 初始化NFT管理器
   */
  async initialize(): Promise<void> {
    try {
      console.log('初始化NFT管理器...');
      
      // 初始化IPFS管理器
      await this.ipfsManager.initialize();
      
      // 初始化NFT渲染器
      await this.nftRenderer.initialize();
      
      // 初始化NFT铸造器
      await this.nftMinter.initialize();
      
      // 加载用户拥有的NFT
      await this.loadUserNFTs();
      
      console.log('NFT管理器初始化完成');
    } catch (error) {
      console.error('NFT管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 铸造NFT
   */
  async mintNFT(
    assetId: string,
    metadata: NFTMetadata,
    royaltyRecipient?: string,
    royaltyPercentage?: number
  ): Promise<NFTOperationResult<NFTToken>> {
    try {
      console.log('开始铸造NFT:', assetId);
      
      // 检查钱包连接
      if (!this.blockchainManager.getState().isConnected) {
        throw new Error('钱包未连接');
      }

      // 使用NFT铸造器进行铸造
      const result = await this.nftMinter.mintNFT(
        assetId,
        metadata,
        royaltyRecipient,
        royaltyPercentage
      );

      if (result.success && result.data) {
        // 添加到拥有的NFT列表
        const nftKey = `${result.data.contractAddress}-${result.data.tokenId}`;
        this.ownedNFTs.set(nftKey, result.data);
        
        this.emit('nftMinted', result.data);
      }

      return result;
    } catch (error) {
      console.error('铸造NFT失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '铸造NFT失败'
      };
    }
  }

  /**
   * 展示NFT
   */
  async displayNFT(
    tokenId: string,
    contractAddress: string,
    renderOptions?: NFTRenderOptions
  ): Promise<NFTOperationResult<any>> {
    try {
      console.log('展示NFT:', tokenId, contractAddress);
      
      // 获取NFT信息
      const nft = await this.getNFTInfo(tokenId, contractAddress);
      if (!nft) {
        throw new Error('NFT不存在');
      }

      // 使用NFT渲染器进行渲染
      const renderResult = await this.nftRenderer.renderNFT(nft, renderOptions);
      
      if (renderResult.success && renderResult.data) {
        const nftKey = `${contractAddress}-${tokenId}`;
        this.displayedNFTs.set(nftKey, renderResult.data);
        
        this.emit('nftDisplayed', {
          nft,
          renderObject: renderResult.data
        });
      }

      return renderResult;
    } catch (error) {
      console.error('展示NFT失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '展示NFT失败'
      };
    }
  }

  /**
   * 隐藏NFT
   */
  async hideNFT(tokenId: string, contractAddress: string): Promise<void> {
    try {
      const nftKey = `${contractAddress}-${tokenId}`;
      const renderObject = this.displayedNFTs.get(nftKey);
      
      if (renderObject) {
        await this.nftRenderer.removeNFT(renderObject);
        this.displayedNFTs.delete(nftKey);
        
        this.emit('nftHidden', { tokenId, contractAddress });
      }
    } catch (error) {
      console.error('隐藏NFT失败:', error);
    }
  }

  /**
   * 获取NFT信息
   */
  async getNFTInfo(tokenId: string, contractAddress: string): Promise<NFTToken | null> {
    try {
      const nftKey = `${contractAddress}-${tokenId}`;
      
      // 先从本地缓存查找
      if (this.ownedNFTs.has(nftKey)) {
        return this.ownedNFTs.get(nftKey)!;
      }

      // 从区块链获取NFT信息
      const contractManager = this.blockchainManager.getContractManager();
      const nftContract = await contractManager.getNFTContract(contractAddress);
      
      if (!nftContract) {
        throw new Error('NFT合约不存在');
      }

      // 获取NFT元数据
      const tokenURI = await nftContract.tokenURI(tokenId);
      const owner = await nftContract.ownerOf(tokenId);
      
      // 从IPFS获取元数据
      const metadata = await this.ipfsManager.getMetadata(tokenURI);
      
      const nft: NFTToken = {
        tokenId,
        contractAddress,
        chainId: this.blockchainManager.getState().currentNetwork?.chainId || 0,
        owner,
        creator: metadata.dl_engine_data?.creator_address || '',
        metadata,
        tokenURI,
        mintedAt: new Date(metadata.dl_engine_data?.creation_timestamp || Date.now()),
        transferCount: 0,
        isForSale: false
      };

      return nft;
    } catch (error) {
      console.error('获取NFT信息失败:', error);
      return null;
    }
  }

  /**
   * 搜索NFT
   */
  async searchNFTs(filter: NFTSearchFilter): Promise<NFTToken[]> {
    try {
      // 这里应该实现NFT搜索逻辑
      // 可以从多个来源搜索：本地缓存、区块链、市场API等
      
      const results: NFTToken[] = [];
      
      // 从本地拥有的NFT中搜索
      for (const nft of this.ownedNFTs.values()) {
        if (this.matchesFilter(nft, filter)) {
          results.push(nft);
        }
      }

      // 应用排序
      if (filter.sortBy) {
        results.sort((a, b) => {
          let comparison = 0;
          
          switch (filter.sortBy) {
            case 'name':
              comparison = a.metadata.name.localeCompare(b.metadata.name);
              break;
            case 'created':
              comparison = a.mintedAt.getTime() - b.mintedAt.getTime();
              break;
            case 'price':
              const priceA = parseFloat(a.price || '0');
              const priceB = parseFloat(b.price || '0');
              comparison = priceA - priceB;
              break;
          }
          
          return filter.sortOrder === 'desc' ? -comparison : comparison;
        });
      }

      // 应用分页
      const start = filter.offset || 0;
      const end = filter.limit ? start + filter.limit : results.length;
      
      return results.slice(start, end);
    } catch (error) {
      console.error('搜索NFT失败:', error);
      return [];
    }
  }

  /**
   * 获取用户拥有的NFT
   */
  getUserNFTs(): NFTToken[] {
    return Array.from(this.ownedNFTs.values());
  }

  /**
   * 获取当前显示的NFT
   */
  getDisplayedNFTs(): Map<string, any> {
    return new Map(this.displayedNFTs);
  }

  /**
   * 转移NFT
   */
  async transferNFT(
    tokenId: string,
    contractAddress: string,
    toAddress: string
  ): Promise<NFTOperationResult<string>> {
    try {
      console.log('转移NFT:', tokenId, 'to', toAddress);
      
      const contractManager = this.blockchainManager.getContractManager();
      const nftContract = await contractManager.getNFTContract(contractAddress);
      
      if (!nftContract) {
        throw new Error('NFT合约不存在');
      }

      const fromAddress = this.blockchainManager.getState().currentAccount;
      if (!fromAddress) {
        throw new Error('钱包未连接');
      }

      // 发送转移交易
      const txHash = await nftContract.transferFrom(fromAddress, toAddress, tokenId);
      
      // 更新本地状态
      const nftKey = `${contractAddress}-${tokenId}`;
      const nft = this.ownedNFTs.get(nftKey);
      if (nft) {
        nft.owner = toAddress;
        nft.lastTransferAt = new Date();
        nft.transferCount += 1;
      }

      this.emit('nftTransferred', {
        tokenId,
        contractAddress,
        from: fromAddress,
        to: toAddress,
        transactionHash: txHash
      });

      return {
        success: true,
        data: txHash,
        transactionHash: txHash
      };
    } catch (error) {
      console.error('转移NFT失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '转移NFT失败'
      };
    }
  }

  /**
   * 加载用户NFT
   */
  private async loadUserNFTs(): Promise<void> {
    try {
      const currentAccount = this.blockchainManager.getState().currentAccount;
      if (!currentAccount) {
        return;
      }

      console.log('加载用户NFT:', currentAccount);
      
      // 这里应该实现从区块链加载用户NFT的逻辑
      // 可以通过事件日志、图数据库或NFT索引服务来获取
      
    } catch (error) {
      console.error('加载用户NFT失败:', error);
    }
  }

  /**
   * 检查NFT是否匹配过滤条件
   */
  private matchesFilter(nft: NFTToken, filter: NFTSearchFilter): boolean {
    // 检查分类
    if (filter.category && nft.metadata.dl_engine_data) {
      // 这里需要根据NFT元数据判断分类
    }

    // 检查资产类型
    if (filter.assetType && nft.metadata.dl_engine_data?.asset_type !== filter.assetType) {
      return false;
    }

    // 检查价格范围
    if (filter.priceRange && nft.price) {
      const price = parseFloat(nft.price);
      const min = parseFloat(filter.priceRange.min);
      const max = parseFloat(filter.priceRange.max);
      
      if (price < min || price > max) {
        return false;
      }
    }

    // 检查创建者
    if (filter.creator && nft.creator.toLowerCase() !== filter.creator.toLowerCase()) {
      return false;
    }

    // 检查拥有者
    if (filter.owner && nft.owner.toLowerCase() !== filter.owner.toLowerCase()) {
      return false;
    }

    return true;
  }

  /**
   * 销毁NFT管理器
   */
  async destroy(): Promise<void> {
    try {
      // 清理显示的NFT
      for (const [, renderObject] of this.displayedNFTs) {
        await this.nftRenderer.removeNFT(renderObject);
      }
      this.displayedNFTs.clear();
      
      // 清理数据
      this.ownedNFTs.clear();
      
      // 移除事件监听器
      this.removeAllListeners();
      
      console.log('NFT管理器已销毁');
    } catch (error) {
      console.error('销毁NFT管理器失败:', error);
    }
  }
}
