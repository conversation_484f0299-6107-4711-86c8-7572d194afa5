/**
 * 骨骼动画服务
 * 处理与后端API的通信
 */

export interface AvatarUploadResponse {
  success: boolean;
  avatarId: string;
  message?: string;
}

export interface SkeletonGenerationResponse {
  success: boolean;
  skeletonData: {
    boneCount: number;
    boneNames: string[];
    qualityScore: number;
    skeletonId: string;
  };
  message?: string;
}

export interface ActionUploadResponse {
  success: boolean;
  actionId: string;
  metadata: {
    duration: number;
    format: string;
    frameCount: number;
  };
  message?: string;
}

export interface ActionCompositionResponse {
  success: boolean;
  composedActionSet: {
    totalActions: number;
    totalDuration: number;
    qualityScore: number;
    actionSetId: string;
  };
  message?: string;
}

export interface AnimationSynchronizationResponse {
  success: boolean;
  synchronizedAnimations: {
    synchronizationAccuracy: number;
    emotionalCoherence: number;
    transitionSmoothness: number;
    animationSetId: string;
  };
  message?: string;
}

export class SkeletonAnimationService {
  private baseUrl: string;

  constructor(baseUrl: string = '/api/skeleton-animation') {
    this.baseUrl = baseUrl;
  }

  /**
   * 上传虚拟化身文件
   */
  async uploadAvatar(file: File): Promise<AvatarUploadResponse> {
    const formData = new FormData();
    formData.append('avatar', file);

    try {
      const response = await fetch(`${this.baseUrl}/upload-avatar`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Avatar upload failed:', error);
      throw new Error('虚拟化身上传失败');
    }
  }

  /**
   * 生成骨骼结构
   */
  async generateSkeleton(avatarId: string): Promise<SkeletonGenerationResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/generate-skeleton`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ avatarId }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Skeleton generation failed:', error);
      throw new Error('骨骼生成失败');
    }
  }

  /**
   * 上传动作文件
   */
  async uploadActionFiles(files: File[]): Promise<ActionUploadResponse[]> {
    const uploadPromises = files.map(async (file) => {
      const formData = new FormData();
      formData.append('action', file);

      try {
        const response = await fetch(`${this.baseUrl}/upload-action`, {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
      } catch (error) {
        console.error(`Action upload failed for ${file.name}:`, error);
        throw new Error(`动作文件 ${file.name} 上传失败`);
      }
    });

    return Promise.all(uploadPromises);
  }

  /**
   * 合成多个动作
   */
  async composeActions(
    avatarId: string,
    actionIds: string[],
    config: {
      defaultDuration: number;
      smoothingFactor: number;
      minQualityThreshold: number;
      blendMode: string;
    }
  ): Promise<ActionCompositionResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/compose-actions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          avatarId,
          actionIds,
          config,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Action composition failed:', error);
      throw new Error('动作合成失败');
    }
  }

  /**
   * 同步面部动画
   */
  async synchronizeAnimations(
    actionSetId: string,
    syncConfig: {
      timeAlignment: {
        tolerance: number;
        method: string;
      };
      emotionMapping: Record<string, string>;
      transitionSmoothing: {
        enabled: boolean;
        duration: number;
        curve: string;
      };
    }
  ): Promise<AnimationSynchronizationResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/synchronize-animations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          actionSetId,
          syncConfig,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Animation synchronization failed:', error);
      throw new Error('动画同步失败');
    }
  }

  /**
   * 获取处理状态
   */
  async getProcessingStatus(taskId: string): Promise<{
    status: 'pending' | 'processing' | 'completed' | 'error';
    progress: number;
    message?: string;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/status/${taskId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Status check failed:', error);
      throw new Error('状态检查失败');
    }
  }

  /**
   * 下载生成的动画文件
   */
  async downloadAnimation(animationSetId: string, format: string = 'fbx'): Promise<Blob> {
    try {
      const response = await fetch(
        `${this.baseUrl}/download/${animationSetId}?format=${format}`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.blob();
    } catch (error) {
      console.error('Animation download failed:', error);
      throw new Error('动画下载失败');
    }
  }

  /**
   * 获取支持的文件格式
   */
  async getSupportedFormats(): Promise<{
    avatar: string[];
    action: string[];
    export: string[];
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/supported-formats`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to get supported formats:', error);
      return {
        avatar: ['fbx', 'gltf', 'glb', 'obj'],
        action: ['fbx', 'gltf', 'glb', 'bvh'],
        export: ['fbx', 'gltf', 'glb']
      };
    }
  }

  /**
   * 删除资源
   */
  async deleteResource(resourceId: string, type: 'avatar' | 'action' | 'skeleton' | 'animation'): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/delete/${type}/${resourceId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.success;
    } catch (error) {
      console.error('Resource deletion failed:', error);
      throw new Error('资源删除失败');
    }
  }

  /**
   * 获取用户的资源列表
   */
  async getUserResources(): Promise<{
    avatars: Array<{ id: string; name: string; createdAt: string }>;
    skeletons: Array<{ id: string; avatarId: string; boneCount: number; qualityScore: number; createdAt: string }>;
    actionSets: Array<{ id: string; name: string; actionCount: number; totalDuration: number; createdAt: string }>;
    animations: Array<{ id: string; name: string; qualityMetrics: any; createdAt: string }>;
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/user-resources`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to get user resources:', error);
      throw new Error('获取用户资源失败');
    }
  }
}

// 创建默认实例
export const skeletonAnimationService = new SkeletonAnimationService();
