/**
 * 导航系统类型定义
 */
import * as THREE from 'three';

/**
 * 路径点接口
 */
export interface PathPoint {
  /** 路径点ID */
  id: string;
  /** 位置 */
  position: THREE.Vector3;
  /** 停留时间（秒） */
  waitTime: number;
  /** 行走速度（米/秒） */
  speed: number;
  /** 动画名称 */
  animation: string;
  /** 朝向目标 */
  lookAt?: THREE.Vector3;
  /** 触发器 */
  triggers?: PathTrigger[];
  /** 自定义数据 */
  userData?: any;
}

/**
 * 路径触发器
 */
export interface PathTrigger {
  /** 触发器类型 */
  type: 'dialogue' | 'animation' | 'sound' | 'event' | 'custom';
  /** 触发器数据 */
  data: any;
  /** 触发条件 */
  condition?: string;
  /** 延迟时间（秒） */
  delay?: number;
  /** 是否只触发一次 */
  once?: boolean;
}

/**
 * 路径元数据
 */
export interface PathMetadata {
  /** 创建时间 */
  createdAt: Date;
  /** 修改时间 */
  updatedAt: Date;
  /** 创建者 */
  creator: string;
  /** 版本号 */
  version: number;
  /** 描述 */
  description?: string;
  /** 标签 */
  tags?: string[];
  /** 分类 */
  category?: string;
}

/**
 * 循环模式
 */
export enum LoopMode {
  /** 不循环 */
  NONE = 'none',
  /** 循环 */
  LOOP = 'loop',
  /** 来回循环 */
  PINGPONG = 'pingpong'
}

/**
 * 插值类型
 */
export enum InterpolationType {
  /** 线性插值 */
  LINEAR = 'linear',
  /** 平滑插值 */
  SMOOTH = 'smooth',
  /** 贝塞尔曲线 */
  BEZIER = 'bezier',
  /** 样条曲线 */
  SPLINE = 'spline'
}

/**
 * 数字人路径接口
 */
export interface AvatarPathData {
  /** 路径ID */
  id: string;
  /** 路径名称 */
  name: string;
  /** 关联的数字人ID */
  avatarId: string;
  /** 路径点列表 */
  points: PathPoint[];
  /** 循环模式 */
  loopMode: LoopMode;
  /** 插值类型 */
  interpolation: InterpolationType;
  /** 总持续时间（秒） */
  totalDuration: number;
  /** 是否启用 */
  enabled: boolean;
  /** 元数据 */
  metadata: PathMetadata;
}

/**
 * 数字人路径选项
 */
export interface AvatarPathOptions {
  /** 路径数据 */
  pathData: AvatarPathData;
  /** 是否自动开始 */
  autoStart?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
  /** 路径平滑度 */
  smoothness?: number;
  /** 最大速度 */
  maxSpeed?: number;
  /** 最小速度 */
  minSpeed?: number;
}

/**
 * 路径跟随选项
 */
export interface PathFollowingOptions {
  /** 路径 */
  path?: AvatarPathData | any;
  /** 是否循环 */
  loop?: boolean;
  /** 速度倍数 */
  speedMultiplier?: number;
  /** 是否暂停 */
  paused?: boolean;
  /** 当前进度 (0-1) */
  progress?: number;
  /** 是否启用物理 */
  usePhysics?: boolean;
  /** 碰撞检测 */
  enableCollision?: boolean;
}

/**
 * 导航系统选项
 */
export interface NavigationSystemOptions {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否启用动态障碍物 */
  enableDynamicObstacles?: boolean;
  /** 路径平滑迭代次数 */
  pathSmoothingIterations?: number;
  /** 更新频率 */
  updateFrequency?: number;
}

/**
 * 导航网格选项
 */
export interface NavigationMeshOptions {
  /** 单元格大小 */
  cellSize?: number;
  /** 单元格高度 */
  cellHeight?: number;
  /** 代理高度 */
  agentHeight?: number;
  /** 代理半径 */
  agentRadius?: number;
  /** 最大爬坡角度 */
  agentMaxClimb?: number;
  /** 最大斜坡角度 */
  agentMaxSlope?: number;
}

/**
 * 路径查找选项
 */
export interface PathFinderOptions {
  /** 启发式权重 */
  heuristicWeight?: number;
  /** 最大搜索节点数 */
  maxSearchNodes?: number;
  /** 路径平滑 */
  smoothPath?: boolean;
  /** 优化路径 */
  optimizePath?: boolean;
}

/**
 * 导航代理选项
 */
export interface NavigationAgentOptions {
  /** 半径 */
  radius?: number;
  /** 高度 */
  height?: number;
  /** 最大速度 */
  maxSpeed?: number;
  /** 最大加速度 */
  maxAcceleration?: number;
  /** 避障权重 */
  avoidanceWeight?: number;
}

/**
 * 导航障碍物选项
 */
export interface NavigationObstacleOptions {
  /** 形状类型 */
  shape: 'box' | 'sphere' | 'cylinder' | 'mesh';
  /** 尺寸 */
  size: THREE.Vector3;
  /** 是否动态 */
  dynamic?: boolean;
  /** 影响半径 */
  influenceRadius?: number;
}

/**
 * 导航路径
 */
export interface NavigationPath {
  /** 路径点 */
  points: THREE.Vector3[];
  /** 路径长度 */
  length: number;
  /** 是否有效 */
  valid: boolean;
  /** 计算时间 */
  computeTime: number;
}

/**
 * 导航节点
 */
export interface NavigationNode {
  /** 节点ID */
  id: string;
  /** 位置 */
  position: THREE.Vector3;
  /** 邻居节点 */
  neighbors: NavigationNode[];
  /** 代价 */
  cost: number;
  /** 启发式代价 */
  heuristic: number;
  /** 父节点 */
  parent?: NavigationNode;
}

/**
 * 导航三角形
 */
export interface NavigationTriangle {
  /** 三角形ID */
  id: string;
  /** 顶点 */
  vertices: THREE.Vector3[];
  /** 中心点 */
  center: THREE.Vector3;
  /** 法向量 */
  normal: THREE.Vector3;
  /** 邻接三角形 */
  neighbors: NavigationTriangle[];
}

/**
 * 导航边
 */
export interface NavigationEdge {
  /** 边ID */
  id: string;
  /** 起点 */
  start: THREE.Vector3;
  /** 终点 */
  end: THREE.Vector3;
  /** 长度 */
  length: number;
  /** 连接的三角形 */
  triangles: NavigationTriangle[];
}

/**
 * 路径事件类型
 */
export enum PathEventType {
  /** 路径开始 */
  PATH_STARTED = 'pathStarted',
  /** 路径结束 */
  PATH_COMPLETED = 'pathCompleted',
  /** 到达路径点 */
  WAYPOINT_REACHED = 'waypointReached',
  /** 路径暂停 */
  PATH_PAUSED = 'pathPaused',
  /** 路径恢复 */
  PATH_RESUMED = 'pathResumed',
  /** 触发器激活 */
  TRIGGER_ACTIVATED = 'triggerActivated'
}

/**
 * 路径事件数据
 */
export interface PathEventData {
  /** 事件类型 */
  type: PathEventType;
  /** 路径ID */
  pathId: string;
  /** 实体ID */
  entityId: string;
  /** 当前路径点索引 */
  currentWaypointIndex?: number;
  /** 进度 (0-1) */
  progress?: number;
  /** 触发器数据 */
  triggerData?: any;
  /** 时间戳 */
  timestamp: number;
}
