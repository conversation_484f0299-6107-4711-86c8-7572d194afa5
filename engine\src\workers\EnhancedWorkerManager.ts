/**
 * 增强的WebWorker管理器
 * 专为支持100+并发用户优化的多线程处理系统
 */

// Worker任务接口
interface WorkerTask {
  id: string;
  type: string;
  data: any;
  priority: number;
  timeout?: number;
  transferable?: Transferable[];
}

// Worker任务结果
interface WorkerTaskResult {
  id: string;
  success: boolean;
  data?: any;
  error?: string;
  executionTime: number;
}

// Worker状态
enum WorkerState {
  IDLE = 'idle',
  BUSY = 'busy',
  ERROR = 'error',
  TERMINATED = 'terminated'
}

// Worker信息
interface WorkerInfo {
  id: string;
  worker: Worker;
  state: WorkerState;
  currentTask?: string;
  tasksCompleted: number;
  totalExecutionTime: number;
  lastActivity: number;
  errorCount: number;
}

// Worker管理器配置
interface EnhancedWorkerManagerConfig {
  maxWorkers: number;
  taskTimeout: number;
  workerIdleTimeout: number;
  maxErrorsPerWorker: number;
  enableLoadBalancing: boolean;
  enableTaskPriority: boolean;
  enableWorkerRecycling: boolean;
  workerRecycleThreshold: number;
}

/**
 * 增强的WebWorker管理器类
 */
export class EnhancedWorkerManager {
  private workers: Map<string, WorkerInfo> = new Map();
  private taskQueue: WorkerTask[] = [];
  private pendingTasks: Map<string, {
    resolve: (result: WorkerTaskResult) => void;
    reject: (error: Error) => void;
    timeout?: NodeJS.Timeout;
  }> = new Map();
  
  private config: EnhancedWorkerManagerConfig = {
    maxWorkers: Math.max(navigator.hardwareConcurrency || 4, 8), // 至少8个Worker
    taskTimeout: 30000, // 30秒超时
    workerIdleTimeout: 300000, // 5分钟空闲超时
    maxErrorsPerWorker: 5,
    enableLoadBalancing: true,
    enableTaskPriority: true,
    enableWorkerRecycling: true,
    workerRecycleThreshold: 1000 // 1000个任务后回收Worker
  };
  
  private workerScriptUrl: string;
  private taskIdCounter = 0;
  private workerIdCounter = 0;
  
  constructor(workerScriptUrl: string, config?: Partial<EnhancedWorkerManagerConfig>) {
    this.workerScriptUrl = workerScriptUrl;
    
    if (config) {
      this.config = { ...this.config, ...config };
    }
    
    this.initialize();
  }
  
  /**
   * 初始化Worker管理器
   */
  private initialize(): void {
    // 创建初始Worker池
    for (let i = 0; i < Math.min(this.config.maxWorkers, 4); i++) {
      this.createWorker();
    }
    
    // 启动定期清理
    setInterval(() => {
      this.cleanupIdleWorkers();
      this.recycleOldWorkers();
    }, 60000); // 每分钟清理一次
    
    console.log(`[EnhancedWorkerManager] 初始化完成，创建了 ${this.workers.size} 个Worker`);
  }
  
  /**
   * 创建新的Worker
   */
  private createWorker(): WorkerInfo {
    const workerId = `worker-${this.workerIdCounter++}`;
    
    try {
      const worker = new Worker(this.workerScriptUrl);
      
      const workerInfo: WorkerInfo = {
        id: workerId,
        worker,
        state: WorkerState.IDLE,
        tasksCompleted: 0,
        totalExecutionTime: 0,
        lastActivity: Date.now(),
        errorCount: 0
      };
      
      // 设置Worker事件监听器
      worker.onmessage = (event) => {
        this.handleWorkerMessage(workerId, event.data);
      };
      
      worker.onerror = (error) => {
        this.handleWorkerError(workerId, error);
      };
      
      this.workers.set(workerId, workerInfo);
      
      console.log(`[EnhancedWorkerManager] 创建Worker: ${workerId}`);
      return workerInfo;
      
    } catch (error) {
      console.error(`[EnhancedWorkerManager] 创建Worker失败:`, error);
      throw error;
    }
  }
  
  /**
   * 执行任务
   */
  public async executeTask(
    type: string,
    data: any,
    priority: number = 0,
    timeout?: number,
    transferable?: Transferable[]
  ): Promise<WorkerTaskResult> {
    const taskId = `task-${this.taskIdCounter++}`;
    
    const task: WorkerTask = {
      id: taskId,
      type,
      data,
      priority,
      timeout: timeout || this.config.taskTimeout,
      transferable
    };
    
    return new Promise((resolve, reject) => {
      // 存储Promise回调
      this.pendingTasks.set(taskId, { resolve, reject });
      
      // 设置超时
      const timeoutHandle = setTimeout(() => {
        this.handleTaskTimeout(taskId);
      }, task.timeout!);
      
      this.pendingTasks.get(taskId)!.timeout = timeoutHandle;
      
      // 尝试立即分配任务
      const assignedWorker = this.assignTaskToWorker(task);
      
      if (!assignedWorker) {
        // 如果没有可用Worker，加入队列
        if (this.config.enableTaskPriority) {
          // 按优先级插入队列
          const insertIndex = this.taskQueue.findIndex(t => t.priority < priority);
          if (insertIndex === -1) {
            this.taskQueue.push(task);
          } else {
            this.taskQueue.splice(insertIndex, 0, task);
          }
        } else {
          this.taskQueue.push(task);
        }
        
        // 如果Worker数量未达到最大值，创建新Worker
        if (this.workers.size < this.config.maxWorkers) {
          this.createWorker();
        }
      }
    });
  }
  
  /**
   * 分配任务给Worker
   */
  private assignTaskToWorker(task: WorkerTask): WorkerInfo | null {
    let bestWorker: WorkerInfo | null = null;
    
    if (this.config.enableLoadBalancing) {
      // 负载均衡：选择任务数最少的空闲Worker
      let minTasks = Infinity;
      
      for (const worker of this.workers.values()) {
        if (worker.state === WorkerState.IDLE && worker.tasksCompleted < minTasks) {
          minTasks = worker.tasksCompleted;
          bestWorker = worker;
        }
      }
    } else {
      // 简单策略：选择第一个空闲Worker
      for (const worker of this.workers.values()) {
        if (worker.state === WorkerState.IDLE) {
          bestWorker = worker;
          break;
        }
      }
    }
    
    if (bestWorker) {
      this.sendTaskToWorker(bestWorker, task);
      return bestWorker;
    }
    
    return null;
  }
  
  /**
   * 发送任务给Worker
   */
  private sendTaskToWorker(workerInfo: WorkerInfo, task: WorkerTask): void {
    workerInfo.state = WorkerState.BUSY;
    workerInfo.currentTask = task.id;
    workerInfo.lastActivity = Date.now();
    
    const message = {
      id: task.id,
      type: task.type,
      data: task.data
    };
    
    try {
      if (task.transferable && task.transferable.length > 0) {
        workerInfo.worker.postMessage(message, task.transferable);
      } else {
        workerInfo.worker.postMessage(message);
      }
    } catch (error) {
      console.error(`[EnhancedWorkerManager] 发送任务失败:`, error);
      this.handleWorkerError(workerInfo.id, error);
    }
  }
  
  /**
   * 处理Worker消息
   */
  private handleWorkerMessage(workerId: string, message: any): void {
    const workerInfo = this.workers.get(workerId);
    if (!workerInfo) return;
    
    const { id: taskId, success, data, error, executionTime } = message;
    
    // 更新Worker状态
    workerInfo.state = WorkerState.IDLE;
    workerInfo.currentTask = undefined;
    workerInfo.tasksCompleted++;
    workerInfo.totalExecutionTime += executionTime || 0;
    workerInfo.lastActivity = Date.now();
    
    // 处理任务结果
    const pendingTask = this.pendingTasks.get(taskId);
    if (pendingTask) {
      // 清除超时
      if (pendingTask.timeout) {
        clearTimeout(pendingTask.timeout);
      }
      
      const result: WorkerTaskResult = {
        id: taskId,
        success,
        data,
        error,
        executionTime: executionTime || 0
      };
      
      if (success) {
        pendingTask.resolve(result);
      } else {
        pendingTask.reject(new Error(error || 'Worker任务执行失败'));
      }
      
      this.pendingTasks.delete(taskId);
    }
    
    // 处理队列中的下一个任务
    this.processNextTask();
  }
  
  /**
   * 处理Worker错误
   */
  private handleWorkerError(workerId: string, error: any): void {
    const workerInfo = this.workers.get(workerId);
    if (!workerInfo) return;
    
    console.error(`[EnhancedWorkerManager] Worker ${workerId} 错误:`, error);
    
    workerInfo.errorCount++;
    workerInfo.state = WorkerState.ERROR;
    
    // 如果错误次数过多，终止Worker
    if (workerInfo.errorCount >= this.config.maxErrorsPerWorker) {
      this.terminateWorker(workerId);
      
      // 创建新Worker替换
      if (this.workers.size < this.config.maxWorkers) {
        this.createWorker();
      }
    }
    
    // 处理当前任务失败
    if (workerInfo.currentTask) {
      const pendingTask = this.pendingTasks.get(workerInfo.currentTask);
      if (pendingTask) {
        pendingTask.reject(new Error(`Worker错误: ${error.message || error}`));
        this.pendingTasks.delete(workerInfo.currentTask);
      }
      workerInfo.currentTask = undefined;
    }
  }
  
  /**
   * 处理任务超时
   */
  private handleTaskTimeout(taskId: string): void {
    const pendingTask = this.pendingTasks.get(taskId);
    if (pendingTask) {
      pendingTask.reject(new Error('任务执行超时'));
      this.pendingTasks.delete(taskId);
    }
    
    // 找到执行该任务的Worker并重置
    for (const workerInfo of this.workers.values()) {
      if (workerInfo.currentTask === taskId) {
        console.warn(`[EnhancedWorkerManager] 任务 ${taskId} 超时，重置Worker ${workerInfo.id}`);
        this.resetWorker(workerInfo.id);
        break;
      }
    }
  }
  
  /**
   * 处理队列中的下一个任务
   */
  private processNextTask(): void {
    if (this.taskQueue.length === 0) return;
    
    const nextTask = this.taskQueue.shift()!;
    const assignedWorker = this.assignTaskToWorker(nextTask);
    
    if (!assignedWorker) {
      // 如果仍然没有可用Worker，重新加入队列
      this.taskQueue.unshift(nextTask);
    }
  }
  
  /**
   * 重置Worker
   */
  private resetWorker(workerId: string): void {
    this.terminateWorker(workerId);
    this.createWorker();
  }
  
  /**
   * 终止Worker
   */
  private terminateWorker(workerId: string): void {
    const workerInfo = this.workers.get(workerId);
    if (!workerInfo) return;
    
    try {
      workerInfo.worker.terminate();
    } catch (error) {
      console.error(`[EnhancedWorkerManager] 终止Worker失败:`, error);
    }
    
    workerInfo.state = WorkerState.TERMINATED;
    this.workers.delete(workerId);
    
    console.log(`[EnhancedWorkerManager] Worker ${workerId} 已终止`);
  }
  
  /**
   * 清理空闲Worker
   */
  private cleanupIdleWorkers(): void {
    const now = Date.now();
    const workersToTerminate: string[] = [];
    
    for (const [workerId, workerInfo] of this.workers) {
      if (
        workerInfo.state === WorkerState.IDLE &&
        now - workerInfo.lastActivity > this.config.workerIdleTimeout &&
        this.workers.size > 2 // 保持至少2个Worker
      ) {
        workersToTerminate.push(workerId);
      }
    }
    
    for (const workerId of workersToTerminate) {
      this.terminateWorker(workerId);
    }
  }
  
  /**
   * 回收老旧Worker
   */
  private recycleOldWorkers(): void {
    if (!this.config.enableWorkerRecycling) return;
    
    for (const [workerId, workerInfo] of this.workers) {
      if (
        workerInfo.state === WorkerState.IDLE &&
        workerInfo.tasksCompleted >= this.config.workerRecycleThreshold
      ) {
        console.log(`[EnhancedWorkerManager] 回收Worker ${workerId}，已完成 ${workerInfo.tasksCompleted} 个任务`);
        this.resetWorker(workerId);
      }
    }
  }
  
  /**
   * 获取Worker统计信息
   */
  public getStats(): {
    totalWorkers: number;
    idleWorkers: number;
    busyWorkers: number;
    queuedTasks: number;
    pendingTasks: number;
    totalTasksCompleted: number;
    averageExecutionTime: number;
  } {
    let idleWorkers = 0;
    let busyWorkers = 0;
    let totalTasksCompleted = 0;
    let totalExecutionTime = 0;
    
    for (const workerInfo of this.workers.values()) {
      if (workerInfo.state === WorkerState.IDLE) idleWorkers++;
      if (workerInfo.state === WorkerState.BUSY) busyWorkers++;
      totalTasksCompleted += workerInfo.tasksCompleted;
      totalExecutionTime += workerInfo.totalExecutionTime;
    }
    
    return {
      totalWorkers: this.workers.size,
      idleWorkers,
      busyWorkers,
      queuedTasks: this.taskQueue.length,
      pendingTasks: this.pendingTasks.size,
      totalTasksCompleted,
      averageExecutionTime: totalTasksCompleted > 0 ? totalExecutionTime / totalTasksCompleted : 0
    };
  }
  
  /**
   * 销毁Worker管理器
   */
  public destroy(): void {
    // 终止所有Worker
    for (const workerId of this.workers.keys()) {
      this.terminateWorker(workerId);
    }
    
    // 清理待处理任务
    for (const [taskId, pendingTask] of this.pendingTasks) {
      if (pendingTask.timeout) {
        clearTimeout(pendingTask.timeout);
      }
      pendingTask.reject(new Error('Worker管理器已销毁'));
    }
    
    this.pendingTasks.clear();
    this.taskQueue.length = 0;
    
    console.log('[EnhancedWorkerManager] Worker管理器已销毁');
  }
}
