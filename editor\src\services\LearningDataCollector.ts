import { EventEmitter } from 'events';
import { XAPIStatement, LEARNING_VERBS, DL_ENGINE_EXTENSIONS } from '../types/xapi.types';

/**
 * 学习数据采集器配置
 */
export interface CollectorConfig {
  apiEndpoint: string;           // API端点
  batchSize: number;             // 批量大小
  flushInterval: number;         // 刷新间隔(毫秒)
  maxRetries: number;            // 最大重试次数
  retryDelay: number;            // 重试延迟(毫秒)
  enableLocalStorage: boolean;   // 启用本地存储
  debug: boolean;                // 调试模式
}

/**
 * 学习者信息
 */
export interface LearnerInfo {
  userId: string;
  userName?: string;
  email?: string;
  sessionId: string;
}

/**
 * 路径点信息
 */
export interface PathPoint {
  position: { x: number; y: number; z: number };
  timestamp: number;
  duration: number;
  action?: string;
}

/**
 * 学习数据采集器
 * 负责收集前端学习行为数据并发送到服务器
 */
export class LearningDataCollector extends EventEmitter {
  private config: CollectorConfig;
  private learnerInfo: LearnerInfo;
  private eventQueue: XAPIStatement[] = [];
  private isOnline: boolean = navigator.onLine;
  private flushTimer: NodeJS.Timeout | null = null;
  private retryQueue: XAPIStatement[] = [];

  constructor(config: Partial<CollectorConfig>, learnerInfo: LearnerInfo) {
    super();

    this.config = {
      apiEndpoint: '/api/learning-tracking/statements',
      batchSize: 10,
      flushInterval: 5000,
      maxRetries: 3,
      retryDelay: 1000,
      enableLocalStorage: true,
      debug: false,
      ...config
    };

    this.learnerInfo = learnerInfo;

    this.initializeCollector();
  }

  /**
   * 初始化采集器
   */
  private initializeCollector(): void {
    // 监听网络状态
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.processRetryQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });

    // 启动定时刷新
    this.startFlushTimer();

    // 页面卸载时保存数据
    window.addEventListener('beforeunload', () => {
      this.flush();
      this.saveToLocalStorage();
    });

    // 从本地存储恢复数据
    this.loadFromLocalStorage();

    if (this.config.debug) {
      console.log('学习数据采集器已初始化', this.config);
    }
  }

  /**
   * 记录数字人对话交互
   */
  async recordAvatarInteraction(data: {
    avatarId: string;
    avatarName?: string;
    question: string;
    answer: string;
    emotion?: string;
    satisfaction?: number; // 1-5
    duration?: number;
    knowledgeArea?: string;
    sceneId?: string;
  }): Promise<void> {
    const extensions: any = {
      [DL_ENGINE_EXTENSIONS.QUESTION]: data.question
    };

    if (data.emotion) {
      extensions[DL_ENGINE_EXTENSIONS.EMOTION] = data.emotion;
    }

    if (data.knowledgeArea) {
      extensions[DL_ENGINE_EXTENSIONS.KNOWLEDGE_AREA] = data.knowledgeArea;
    }

    const statement: XAPIStatement = {
      id: this.generateUUID(),
      actor: this.createActor(),
      verb: this.createVerb(LEARNING_VERBS.TALKED_WITH_AVATAR, '与数字人对话'),
      object: this.createAvatarActivity(data.avatarId, data.avatarName),
      result: {
        response: data.answer,
        score: data.satisfaction ? { scaled: data.satisfaction / 5 } : undefined,
        success: data.satisfaction ? data.satisfaction >= 3 : undefined,
        duration: data.duration ? this.formatDuration(data.duration) : undefined,
        extensions
      },
      context: this.createContext({
        sceneId: data.sceneId,
        avatarId: data.avatarId,
        knowledgeArea: data.knowledgeArea
      }),
      timestamp: new Date().toISOString()
    };

    this.addToQueue(statement);
    this.emit('avatarInteraction', data);
  }

  /**
   * 记录学习路径跟踪
   */
  async recordPathFollowing(data: {
    pathId: string;
    pathName?: string;
    startTime: Date;
    endTime: Date;
    completionRate: number; // 0-1
    stoppingPoints: PathPoint[];
    difficulty?: 'easy' | 'medium' | 'hard';
    sceneId?: string;
  }): Promise<void> {
    const extensions: any = {
      [DL_ENGINE_EXTENSIONS.STOPPING_POINTS]: data.stoppingPoints
    };

    if (data.difficulty) {
      extensions[DL_ENGINE_EXTENSIONS.DIFFICULTY] = data.difficulty;
    }

    const duration = data.endTime.getTime() - data.startTime.getTime();

    const statement: XAPIStatement = {
      id: this.generateUUID(),
      actor: this.createActor(),
      verb: this.createVerb(LEARNING_VERBS.FOLLOWED_PATH, '跟随路径'),
      object: this.createPathActivity(data.pathId, data.pathName),
      result: {
        completion: data.completionRate >= 1.0,
        score: { scaled: data.completionRate },
        duration: this.formatDuration(duration),
        extensions
      },
      context: this.createContext({
        sceneId: data.sceneId
      }),
      timestamp: data.endTime.toISOString()
    };

    this.addToQueue(statement);
    this.emit('pathFollowing', data);
  }

  /**
   * 记录知识推荐接收
   */
  async recordRecommendationReceived(data: {
    recommendationId: string;
    contentTitle?: string;
    contentType: string;
    relevanceScore: number; // 0-1
    userAction: 'accepted' | 'rejected' | 'ignored';
    timeToDecision?: number;
    knowledgeArea?: string;
  }): Promise<void> {
    const extensions: any = {
      [DL_ENGINE_EXTENSIONS.CONTENT_TYPE]: data.contentType
    };

    if (data.timeToDecision) {
      extensions[DL_ENGINE_EXTENSIONS.DECISION_TIME] = data.timeToDecision;
    }

    if (data.knowledgeArea) {
      extensions[DL_ENGINE_EXTENSIONS.KNOWLEDGE_AREA] = data.knowledgeArea;
    }

    const statement: XAPIStatement = {
      id: this.generateUUID(),
      actor: this.createActor(),
      verb: this.createVerb(LEARNING_VERBS.RECEIVED_RECOMMENDATION, '接收推荐'),
      object: this.createRecommendationActivity(data.recommendationId, data.contentTitle),
      result: {
        response: data.userAction,
        score: { scaled: data.relevanceScore },
        success: data.userAction === 'accepted',
        extensions
      },
      context: this.createContext({
        knowledgeArea: data.knowledgeArea
      }),
      timestamp: new Date().toISOString()
    };

    this.addToQueue(statement);
    this.emit('recommendationReceived', data);
  }

  /**
   * 记录情感表达
   */
  async recordEmotionExpression(data: {
    emotion: string;
    intensity: number; // 0-1
    trigger: string;
    context: string;
    sceneId?: string;
    avatarId?: string;
  }): Promise<void> {
    const extensions: any = {
      [DL_ENGINE_EXTENSIONS.TRIGGER]: data.trigger,
      [DL_ENGINE_EXTENSIONS.CONTEXT]: data.context
    };

    const statement: XAPIStatement = {
      id: this.generateUUID(),
      actor: this.createActor(),
      verb: this.createVerb(LEARNING_VERBS.EXPRESSED_EMOTION, '表达情感'),
      object: this.createEmotionActivity(data.emotion),
      result: {
        score: { scaled: data.intensity },
        extensions
      },
      context: this.createContext({
        sceneId: data.sceneId,
        avatarId: data.avatarId
      }),
      timestamp: new Date().toISOString()
    };

    this.addToQueue(statement);
    this.emit('emotionExpression', data);
  }

  /**
   * 记录场景探索
   */
  async recordSceneExploration(data: {
    sceneId: string;
    sceneName?: string;
    startTime: Date;
    endTime: Date;
    areasVisited: string[];
    interactionsCount: number;
    completionRate?: number;
  }): Promise<void> {
    const extensions: any = {
      'http://dl-engine.com/xapi/extensions/areas-visited': data.areasVisited,
      'http://dl-engine.com/xapi/extensions/interactions-count': data.interactionsCount
    };

    const duration = data.endTime.getTime() - data.startTime.getTime();

    const statement: XAPIStatement = {
      id: this.generateUUID(),
      actor: this.createActor(),
      verb: this.createVerb(LEARNING_VERBS.EXPLORED_SCENE, '探索场景'),
      object: this.createSceneActivity(data.sceneId, data.sceneName),
      result: {
        completion: data.completionRate ? data.completionRate >= 1.0 : undefined,
        score: data.completionRate ? { scaled: data.completionRate } : undefined,
        duration: this.formatDuration(duration),
        extensions
      },
      context: this.createContext({
        sceneId: data.sceneId
      }),
      timestamp: data.endTime.toISOString()
    };

    this.addToQueue(statement);
    this.emit('sceneExploration', data);
  }

  /**
   * 添加语句到队列
   */
  private addToQueue(statement: XAPIStatement): void {
    this.eventQueue.push(statement);

    if (this.config.debug) {
      console.log('添加xAPI语句到队列:', statement);
    }

    // 如果队列达到批量大小，立即刷新
    if (this.eventQueue.length >= this.config.batchSize) {
      this.flush();
    }
  }

  /**
   * 刷新队列，发送数据
   */
  async flush(): Promise<void> {
    if (this.eventQueue.length === 0) {
      return;
    }

    const statements = [...this.eventQueue];
    this.eventQueue = [];

    if (!this.isOnline) {
      // 离线时保存到重试队列
      this.retryQueue.push(...statements);
      this.saveToLocalStorage();
      return;
    }

    try {
      await this.sendStatements(statements);
      this.emit('flush', { count: statements.length, success: true });
    } catch (error) {
      console.error('发送学习数据失败:', error);
      
      // 添加到重试队列
      this.retryQueue.push(...statements);
      this.emit('flush', { count: statements.length, success: false, error });
    }
  }

  /**
   * 发送语句到服务器
   */
  private async sendStatements(statements: XAPIStatement[]): Promise<void> {
    const response = await fetch(this.config.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.getAuthToken()}`
      },
      body: JSON.stringify({ statements })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    if (this.config.debug) {
      console.log(`成功发送 ${statements.length} 条学习记录`);
    }
  }

  /**
   * 处理重试队列
   */
  private async processRetryQueue(): Promise<void> {
    if (this.retryQueue.length === 0) {
      return;
    }

    const statements = [...this.retryQueue];
    this.retryQueue = [];

    try {
      await this.sendStatements(statements);
      this.emit('retrySuccess', { count: statements.length });
    } catch (error) {
      console.error('重试发送失败:', error);
      // 重新加入重试队列，但限制重试次数
      this.retryQueue.push(...statements);
    }
  }

  /**
   * 启动定时刷新
   */
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);
  }

  /**
   * 停止定时刷新
   */
  private stopFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
  }

  /**
   * 保存到本地存储
   */
  private saveToLocalStorage(): void {
    if (!this.config.enableLocalStorage) {
      return;
    }

    try {
      const data = {
        eventQueue: this.eventQueue,
        retryQueue: this.retryQueue,
        timestamp: Date.now()
      };
      
      localStorage.setItem('learningDataCollector', JSON.stringify(data));
    } catch (error) {
      console.error('保存到本地存储失败:', error);
    }
  }

  /**
   * 从本地存储加载
   */
  private loadFromLocalStorage(): void {
    if (!this.config.enableLocalStorage) {
      return;
    }

    try {
      const dataStr = localStorage.getItem('learningDataCollector');
      if (dataStr) {
        const data = JSON.parse(dataStr);
        
        // 检查数据是否过期（24小时）
        if (Date.now() - data.timestamp < 24 * 60 * 60 * 1000) {
          this.eventQueue = data.eventQueue || [];
          this.retryQueue = data.retryQueue || [];
        }
        
        // 清除本地存储
        localStorage.removeItem('learningDataCollector');
      }
    } catch (error) {
      console.error('从本地存储加载失败:', error);
    }
  }

  /**
   * 创建Actor对象
   */
  private createActor(): any {
    const actor: any = {
      objectType: 'Agent'
    };

    if (this.learnerInfo.email) {
      actor.mbox = `mailto:${this.learnerInfo.email}`;
    } else {
      actor.account = {
        homePage: 'http://dl-engine.com',
        name: this.learnerInfo.userId
      };
    }

    if (this.learnerInfo.userName) {
      actor.name = this.learnerInfo.userName;
    }

    return actor;
  }

  /**
   * 创建Verb对象
   */
  private createVerb(verbId: string, displayName: string): any {
    return {
      id: verbId,
      display: {
        'zh-CN': displayName,
        'en-US': displayName
      }
    };
  }

  /**
   * 创建数字人活动
   */
  private createAvatarActivity(avatarId: string, avatarName?: string): any {
    return {
      objectType: 'Activity',
      id: `http://dl-engine.com/avatars/${avatarId}`,
      definition: {
        name: avatarName ? { 'zh-CN': avatarName, 'en-US': avatarName } : undefined,
        description: { 'zh-CN': '数字人交互', 'en-US': 'Avatar Interaction' },
        type: 'http://dl-engine.com/activity-types/avatar'
      }
    };
  }

  /**
   * 创建路径活动
   */
  private createPathActivity(pathId: string, pathName?: string): any {
    return {
      objectType: 'Activity',
      id: `http://dl-engine.com/paths/${pathId}`,
      definition: {
        name: pathName ? { 'zh-CN': pathName, 'en-US': pathName } : undefined,
        description: { 'zh-CN': '学习路径', 'en-US': 'Learning Path' },
        type: 'http://dl-engine.com/activity-types/learning-path'
      }
    };
  }

  /**
   * 创建推荐活动
   */
  private createRecommendationActivity(recommendationId: string, contentTitle?: string): any {
    return {
      objectType: 'Activity',
      id: `http://dl-engine.com/recommendations/${recommendationId}`,
      definition: {
        name: contentTitle ? { 'zh-CN': contentTitle, 'en-US': contentTitle } : undefined,
        description: { 'zh-CN': '知识推荐', 'en-US': 'Knowledge Recommendation' },
        type: 'http://dl-engine.com/activity-types/recommendation'
      }
    };
  }

  /**
   * 创建情感活动
   */
  private createEmotionActivity(emotion: string): any {
    return {
      objectType: 'Activity',
      id: `http://dl-engine.com/emotions/${emotion}`,
      definition: {
        name: { 'zh-CN': emotion, 'en-US': emotion },
        description: { 'zh-CN': '情感表达', 'en-US': 'Emotion Expression' },
        type: 'http://dl-engine.com/activity-types/emotion'
      }
    };
  }

  /**
   * 创建场景活动
   */
  private createSceneActivity(sceneId: string, sceneName?: string): any {
    return {
      objectType: 'Activity',
      id: `http://dl-engine.com/scenes/${sceneId}`,
      definition: {
        name: sceneName ? { 'zh-CN': sceneName, 'en-US': sceneName } : undefined,
        description: { 'zh-CN': '3D场景', 'en-US': '3D Scene' },
        type: 'http://dl-engine.com/activity-types/scene'
      }
    };
  }

  /**
   * 创建上下文对象
   */
  private createContext(options: {
    sceneId?: string;
    avatarId?: string;
    knowledgeArea?: string;
  } = {}): any {
    const context: any = {
      platform: 'DL-Engine',
      language: 'zh-CN',
      extensions: {
        [DL_ENGINE_EXTENSIONS.SESSION_ID]: this.learnerInfo.sessionId
      }
    };

    if (options.sceneId) {
      context.extensions[DL_ENGINE_EXTENSIONS.SCENE_ID] = options.sceneId;
    }

    if (options.avatarId) {
      context.extensions[DL_ENGINE_EXTENSIONS.AVATAR_ID] = options.avatarId;
    }

    if (options.knowledgeArea) {
      context.extensions[DL_ENGINE_EXTENSIONS.KNOWLEDGE_AREA] = options.knowledgeArea;
    }

    return context;
  }

  /**
   * 格式化持续时间
   */
  private formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    const remainingMinutes = minutes % 60;
    const remainingSeconds = seconds % 60;

    let duration = 'PT';
    
    if (hours > 0) {
      duration += `${hours}H`;
    }
    
    if (remainingMinutes > 0) {
      duration += `${remainingMinutes}M`;
    }
    
    if (remainingSeconds > 0 || duration === 'PT') {
      duration += `${remainingSeconds}S`;
    }

    return duration;
  }

  /**
   * 生成UUID
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * 获取认证令牌
   */
  private getAuthToken(): string {
    // 从localStorage或其他地方获取认证令牌
    return localStorage.getItem('authToken') || '';
  }

  /**
   * 销毁采集器
   */
  destroy(): void {
    this.stopFlushTimer();
    this.flush();
    this.saveToLocalStorage();
    this.removeAllListeners();
  }

  /**
   * 获取统计信息
   */
  getStats(): {
    queueSize: number;
    retryQueueSize: number;
    isOnline: boolean;
    config: CollectorConfig;
  } {
    return {
      queueSize: this.eventQueue.length,
      retryQueueSize: this.retryQueue.length,
      isOnline: this.isOnline,
      config: this.config
    };
  }
}
