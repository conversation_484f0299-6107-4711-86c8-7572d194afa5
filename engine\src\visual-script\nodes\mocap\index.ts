/**
 * 动作捕捉相关视觉脚本节点导出
 */

// 导出节点类
export { CameraInputNode } from './CameraInputNode';
export { PoseDetectionNode } from './PoseDetectionNode';
export { HandTrackingNode } from './HandTrackingNode';
export { VirtualInteractionNode } from './VirtualInteractionNode';

// 导出节点类型和配置接口
export type { CameraInputNodeConfig } from './CameraInputNode';
export type { PoseDetectionNodeConfig } from './PoseDetectionNode';
export type { HandTrackingNodeConfig } from './HandTrackingNode';
export type { VirtualInteractionNodeConfig, InteractionEventData } from './VirtualInteractionNode';

// 节点注册信息
export const MOCAP_NODES = {
  CameraInput: {
    type: 'CameraInput',
    name: '摄像头输入',
    description: '从摄像头获取视频流数据',
    category: 'Input',
    class: 'CameraInputNode'
  },
  PoseDetection: {
    type: 'PoseDetection',
    name: '姿态检测',
    description: '使用MediaPipe检测人体姿态关键点',
    category: 'AI',
    class: 'PoseDetectionNode'
  },
  HandTracking: {
    type: 'HandTracking',
    name: '手部追踪',
    description: '使用MediaPipe检测手部关键点和识别手势',
    category: 'AI',
    class: 'HandTrackingNode'
  },
  VirtualInteraction: {
    type: 'VirtualInteraction',
    name: '虚拟交互',
    description: '将手势和动作映射到虚拟环境交互',
    category: 'Interaction',
    class: 'VirtualInteractionNode'
  }
};

/**
 * 注册所有动作捕捉节点到视觉脚本系统
 */
export function registerMocapNodes(nodeRegistry: any): void {
  // 这里应该调用实际的节点注册系统
  // nodeRegistry.register('CameraInput', CameraInputNode);
  // nodeRegistry.register('PoseDetection', PoseDetectionNode);
  // nodeRegistry.register('HandTracking', HandTrackingNode);
  // nodeRegistry.register('VirtualInteraction', VirtualInteractionNode);
  
  console.log('动作捕捉节点已注册:', Object.keys(MOCAP_NODES));
}
