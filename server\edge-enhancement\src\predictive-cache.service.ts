import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConfigService } from '@nestjs/config';

/**
 * 用户行为模式接口
 */
export interface BehaviorPattern {
  userId: string;
  actionSequences: ActionSequence[];
  accessPatterns: AccessPattern[];
  temporalPatterns: TemporalPattern[];
  preferences: UserPreferences;
  lastUpdated: Date;
}

/**
 * 动作序列接口
 */
export interface ActionSequence {
  actions: UserAction[];
  frequency: number;
  confidence: number;
  lastOccurrence: Date;
}

/**
 * 用户动作接口
 */
export interface UserAction {
  type: string;
  resourceId: string;
  timestamp: Date;
  duration: number;
  context: Record<string, any>;
}

/**
 * 访问模式接口
 */
export interface AccessPattern {
  resourceType: string;
  accessFrequency: number;
  averageAccessTime: number;
  peakHours: number[];
  seasonality: number[];
}

/**
 * 时间模式接口
 */
export interface TemporalPattern {
  timeOfDay: number;
  dayOfWeek: number;
  accessProbability: number;
  resourceTypes: string[];
}

/**
 * 用户偏好接口
 */
export interface UserPreferences {
  preferredResourceTypes: string[];
  qualityPreference: 'low' | 'medium' | 'high';
  latencyTolerance: number;
  bandwidthPreference: number;
}

/**
 * 预测结果接口
 */
export interface Prediction {
  action: UserAction;
  confidence: number;
  resourceId: string;
  priority: number;
  estimatedAccessTime: Date;
  probability: number;
}

/**
 * 缓存层级枚举
 */
export enum CacheLevel {
  L1 = 'L1', // 内存缓存
  L2 = 'L2', // SSD缓存
  L3 = 'L3'  // 网络缓存
}

/**
 * 缓存项接口
 */
export interface CacheItem {
  key: string;
  value: any;
  size: number;
  level: CacheLevel;
  accessCount: number;
  lastAccessed: Date;
  createdAt: Date;
  expiresAt?: Date;
  priority: number;
  metadata: Record<string, any>;
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  l1MaxSize: number;
  l2MaxSize: number;
  l3MaxSize: number;
  l1MaxMemory: number;
  l2MaxMemory: number;
  l3MaxMemory: number;
  defaultTTL: number;
  cleanupInterval: number;
  preloadThreshold: number;
}

/**
 * 预测性缓存管理器
 * 基于用户行为分析的智能缓存预加载和多层级缓存协调
 */
@Injectable()
export class PredictiveCacheService {
  private readonly logger = new Logger(PredictiveCacheService.name);

  // 用户行为数据存储
  private userBehaviors: Map<string, BehaviorPattern> = new Map();

  // 多层级缓存存储
  private l1Cache: Map<string, CacheItem> = new Map(); // 内存缓存
  private l2Cache: Map<string, CacheItem> = new Map(); // SSD缓存
  private l3Cache: Map<string, CacheItem> = new Map(); // 网络缓存

  // 缓存配置
  private config: CacheConfig;

  // 预测模型参数
  private sequenceWeights: Map<string, number> = new Map();
  private temporalWeights: Map<string, number> = new Map();

  // 性能统计
  private stats = {
    totalRequests: 0,
    l1Hits: 0,
    l2Hits: 0,
    l3Hits: 0,
    misses: 0,
    preloadHits: 0,
    totalPreloads: 0,
    averageResponseTime: 0
  };

  // 清理定时器
  private cleanupTimer: NodeJS.Timeout | null = null;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initializeConfig();
    this.startCleanupTimer();
  }

  /**
   * 初始化配置
   */
  private initializeConfig(): void {
    this.config = {
      l1MaxSize: this.configService.get<number>('CACHE_L1_MAX_SIZE', 1000),
      l2MaxSize: this.configService.get<number>('CACHE_L2_MAX_SIZE', 10000),
      l3MaxSize: this.configService.get<number>('CACHE_L3_MAX_SIZE', 100000),
      l1MaxMemory: this.configService.get<number>('CACHE_L1_MAX_MEMORY', 512 * 1024 * 1024), // 512MB
      l2MaxMemory: this.configService.get<number>('CACHE_L2_MAX_MEMORY', 2 * 1024 * 1024 * 1024), // 2GB
      l3MaxMemory: this.configService.get<number>('CACHE_L3_MAX_MEMORY', 10 * 1024 * 1024 * 1024), // 10GB
      defaultTTL: this.configService.get<number>('CACHE_DEFAULT_TTL', 3600), // 1小时
      cleanupInterval: this.configService.get<number>('CACHE_CLEANUP_INTERVAL', 300000), // 5分钟
      preloadThreshold: this.configService.get<number>('CACHE_PRELOAD_THRESHOLD', 0.7)
    };

    this.logger.log('预测性缓存服务配置初始化完成');
  }

  /**
   * 分析用户行为并预加载资源
   * @param userId 用户ID
   */
  async analyzeBehaviorAndPreload(userId: string): Promise<void> {
    const behaviorPattern = await this.analyzeBehavior(userId);
    const predictions = this.predictNextActions(behaviorPattern);

    for (const prediction of predictions) {
      if (prediction.confidence > this.config.preloadThreshold) {
        await this.preloadResource(
          prediction.resourceId,
          prediction.priority,
          userId
        );
      }
    }

    this.logger.debug(`用户 ${userId} 行为分析完成，生成 ${predictions.length} 个预测`);
  }

  /**
   * 分析用户行为
   * @param userId 用户ID
   * @returns 行为模式
   */
  private async analyzeBehavior(userId: string): Promise<BehaviorPattern> {
    let pattern = this.userBehaviors.get(userId);

    if (!pattern) {
      pattern = {
        userId,
        actionSequences: [],
        accessPatterns: [],
        temporalPatterns: [],
        preferences: {
          preferredResourceTypes: [],
          qualityPreference: 'medium',
          latencyTolerance: 100,
          bandwidthPreference: 1000000
        },
        lastUpdated: new Date()
      };
      this.userBehaviors.set(userId, pattern);
    }

    // 更新行为模式
    await this.updateBehaviorPattern(pattern);

    return pattern;
  }

  /**
   * 更新行为模式
   * @param pattern 行为模式
   */
  private async updateBehaviorPattern(pattern: BehaviorPattern): Promise<void> {
    // 这里应该从数据库或日志中获取用户的最新行为数据
    // 简化实现，使用模拟数据

    const now = new Date();
    const hourOfDay = now.getHours();
    const dayOfWeek = now.getDay();

    // 更新时间模式
    const existingTemporal = pattern.temporalPatterns.find(
      tp => tp.timeOfDay === hourOfDay && tp.dayOfWeek === dayOfWeek
    );

    if (existingTemporal) {
      existingTemporal.accessProbability = Math.min(1, existingTemporal.accessProbability + 0.1);
    } else {
      pattern.temporalPatterns.push({
        timeOfDay: hourOfDay,
        dayOfWeek: dayOfWeek,
        accessProbability: 0.5,
        resourceTypes: ['scene', 'texture', 'model']
      });
    }

    pattern.lastUpdated = now;
  }

  /**
   * 预测下一步动作
   * @param pattern 行为模式
   * @returns 预测列表
   */
  private predictNextActions(pattern: BehaviorPattern): Prediction[] {
    const predictions: Prediction[] = [];
    const now = new Date();

    // 基于动作序列预测
    for (const sequence of pattern.actionSequences) {
      if (sequence.actions.length > 1) {
        const lastAction = sequence.actions[sequence.actions.length - 1];
        const nextAction = this.predictNextInSequence(sequence);

        if (nextAction) {
          predictions.push({
            action: nextAction,
            confidence: sequence.confidence * sequence.frequency,
            resourceId: nextAction.resourceId,
            priority: this.calculatePriority(nextAction, pattern),
            estimatedAccessTime: new Date(now.getTime() + 60000), // 1分钟后
            probability: sequence.confidence
          });
        }
      }
    }

    // 基于时间模式预测
    const currentHour = now.getHours();
    const currentDay = now.getDay();

    for (const temporal of pattern.temporalPatterns) {
      if (Math.abs(temporal.timeOfDay - currentHour) <= 1 &&
          temporal.dayOfWeek === currentDay) {

        for (const resourceType of temporal.resourceTypes) {
          predictions.push({
            action: {
              type: 'access',
              resourceId: `${resourceType}_${Math.random().toString(36).substr(2, 9)}`,
              timestamp: now,
              duration: 300000, // 5分钟
              context: { type: resourceType }
            },
            confidence: temporal.accessProbability,
            resourceId: `${resourceType}_predicted`,
            priority: this.calculateTemporalPriority(temporal),
            estimatedAccessTime: new Date(now.getTime() + 300000), // 5分钟后
            probability: temporal.accessProbability
          });
        }
      }
    }

    // 按置信度排序
    return predictions.sort((a, b) => b.confidence - a.confidence).slice(0, 10);
  }

  /**
   * 预测序列中的下一个动作
   * @param sequence 动作序列
   * @returns 预测的下一个动作
   */
  private predictNextInSequence(sequence: ActionSequence): UserAction | null {
    if (sequence.actions.length < 2) return null;

    const lastAction = sequence.actions[sequence.actions.length - 1];
    const patterns = this.findSequencePatterns(sequence);

    if (patterns.length > 0) {
      const mostLikelyPattern = patterns[0];
      return {
        type: mostLikelyPattern.nextType,
        resourceId: `${mostLikelyPattern.nextType}_${Date.now()}`,
        timestamp: new Date(),
        duration: lastAction.duration,
        context: { predicted: true, pattern: mostLikelyPattern }
      };
    }

    return null;
  }

  /**
   * 查找序列模式
   * @param sequence 动作序列
   * @returns 模式列表
   */
  private findSequencePatterns(sequence: ActionSequence): any[] {
    // 简化的模式识别
    const actionTypes = sequence.actions.map(action => action.type);
    const patterns = [];

    // 查找重复模式
    for (let i = 0; i < actionTypes.length - 1; i++) {
      const current = actionTypes[i];
      const next = actionTypes[i + 1];

      patterns.push({
        currentType: current,
        nextType: next,
        confidence: 0.8,
        frequency: 1
      });
    }

    return patterns.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * 计算动作优先级
   * @param action 用户动作
   * @param pattern 行为模式
   * @returns 优先级
   */
  private calculatePriority(action: UserAction, pattern: BehaviorPattern): number {
    let priority = 50; // 基础优先级

    // 根据用户偏好调整
    if (pattern.preferences.preferredResourceTypes.includes(action.type)) {
      priority += 30;
    }

    // 根据访问频率调整
    const accessPattern = pattern.accessPatterns.find(ap => ap.resourceType === action.type);
    if (accessPattern) {
      priority += accessPattern.accessFrequency * 20;
    }

    return Math.min(100, Math.max(0, priority));
  }

  /**
   * 计算时间模式优先级
   * @param temporal 时间模式
   * @returns 优先级
   */
  private calculateTemporalPriority(temporal: TemporalPattern): number {
    return Math.floor(temporal.accessProbability * 100);
  }

  /**
   * 预加载资源
   * @param resourceId 资源ID
   * @param priority 优先级
   * @param userId 用户ID
   */
  private async preloadResource(resourceId: string, priority: number, userId?: string): Promise<void> {
    try {
      // 检查是否已经缓存
      if (await this.has(resourceId)) {
        this.logger.debug(`资源 ${resourceId} 已存在于缓存中`);
        return;
      }

      // 模拟资源加载
      const resourceData = await this.loadResource(resourceId);

      if (resourceData) {
        await this.set(resourceId, resourceData, {
          priority,
          preloaded: true,
          userId,
          ttl: this.config.defaultTTL
        });

        this.stats.totalPreloads++;

        this.eventEmitter.emit('cache.preloaded', {
          resourceId,
          priority,
          userId,
          size: this.calculateSize(resourceData)
        });

        this.logger.debug(`预加载资源成功: ${resourceId} (优先级: ${priority})`);
      }
    } catch (error) {
      this.logger.error(`预加载资源失败: ${resourceId}`, error);
    }
  }

  /**
   * 加载资源（模拟）
   * @param resourceId 资源ID
   * @returns 资源数据
   */
  private async loadResource(resourceId: string): Promise<any> {
    // 模拟异步资源加载
    await new Promise(resolve => setTimeout(resolve, Math.random() * 100));

    return {
      id: resourceId,
      data: `Resource data for ${resourceId}`,
      metadata: {
        type: resourceId.split('_')[0],
        size: Math.floor(Math.random() * 1000000),
        version: '1.0.0'
      },
      loadedAt: new Date()
    };
  }

  /**
   * 获取缓存项
   * @param key 缓存键
   * @returns 缓存值
   */
  async get(key: string): Promise<any> {
    this.stats.totalRequests++;
    const startTime = Date.now();

    try {
      // L1缓存查找
      let item = this.l1Cache.get(key);
      if (item && !this.isExpired(item)) {
        this.updateAccessStats(item);
        this.stats.l1Hits++;
        this.updateResponseTime(startTime);
        return item.value;
      }

      // L2缓存查找
      item = this.l2Cache.get(key);
      if (item && !this.isExpired(item)) {
        // 提升到L1缓存
        await this.promoteToL1(item);
        this.updateAccessStats(item);
        this.stats.l2Hits++;
        this.updateResponseTime(startTime);
        return item.value;
      }

      // L3缓存查找
      item = this.l3Cache.get(key);
      if (item && !this.isExpired(item)) {
        // 提升到L2和L1缓存
        await this.promoteToL2(item);
        await this.promoteToL1(item);
        this.updateAccessStats(item);
        this.stats.l3Hits++;
        this.updateResponseTime(startTime);
        return item.value;
      }

      this.stats.misses++;
      this.updateResponseTime(startTime);
      return null;
    } catch (error) {
      this.logger.error(`缓存获取失败: ${key}`, error);
      this.stats.misses++;
      this.updateResponseTime(startTime);
      return null;
    }
  }

  /**
   * 设置缓存项
   * @param key 缓存键
   * @param value 缓存值
   * @param options 选项
   */
  async set(key: string, value: any, options: any = {}): Promise<void> {
    const size = this.calculateSize(value);
    const priority = options.priority || 50;
    const ttl = options.ttl || this.config.defaultTTL;

    const item: CacheItem = {
      key,
      value,
      size,
      level: CacheLevel.L3, // 默认存储到L3
      accessCount: 0,
      lastAccessed: new Date(),
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + ttl * 1000),
      priority,
      metadata: {
        preloaded: options.preloaded || false,
        userId: options.userId,
        ...options.metadata
      }
    };

    // 根据大小和优先级决定缓存层级
    if (size < this.config.l1MaxMemory / this.config.l1MaxSize && priority >= 80) {
      await this.setToL1(item);
    } else if (size < this.config.l2MaxMemory / this.config.l2MaxSize && priority >= 60) {
      await this.setToL2(item);
    } else {
      await this.setToL3(item);
    }

    this.eventEmitter.emit('cache.set', {
      key,
      level: item.level,
      size,
      priority
    });
  }

  /**
   * 检查缓存是否存在
   * @param key 缓存键
   * @returns 是否存在
   */
  async has(key: string): Promise<boolean> {
    return this.l1Cache.has(key) || this.l2Cache.has(key) || this.l3Cache.has(key);
  }

  /**
   * 删除缓存项
   * @param key 缓存键
   */
  async delete(key: string): Promise<boolean> {
    let deleted = false;

    if (this.l1Cache.has(key)) {
      this.l1Cache.delete(key);
      deleted = true;
    }

    if (this.l2Cache.has(key)) {
      this.l2Cache.delete(key);
      deleted = true;
    }

    if (this.l3Cache.has(key)) {
      this.l3Cache.delete(key);
      deleted = true;
    }

    if (deleted) {
      this.eventEmitter.emit('cache.deleted', { key });
    }

    return deleted;
  }

  /**
   * 清空所有缓存
   */
  async clear(): Promise<void> {
    this.l1Cache.clear();
    this.l2Cache.clear();
    this.l3Cache.clear();

    this.eventEmitter.emit('cache.cleared');
    this.logger.log('所有缓存已清空');
  }

  /**
   * 获取缓存统计信息
   * @returns 统计信息
   */
  getStatistics(): any {
    const totalHits = this.stats.l1Hits + this.stats.l2Hits + this.stats.l3Hits;
    const hitRate = this.stats.totalRequests > 0 ? totalHits / this.stats.totalRequests : 0;
    const preloadHitRate = this.stats.totalPreloads > 0 ? this.stats.preloadHits / this.stats.totalPreloads : 0;

    return {
      ...this.stats,
      hitRate: hitRate.toFixed(3),
      preloadHitRate: preloadHitRate.toFixed(3),
      l1Size: this.l1Cache.size,
      l2Size: this.l2Cache.size,
      l3Size: this.l3Cache.size,
      totalCacheSize: this.l1Cache.size + this.l2Cache.size + this.l3Cache.size,
      userBehaviorCount: this.userBehaviors.size
    };
  }

  /**
   * 检查缓存项是否过期
   * @param item 缓存项
   * @returns 是否过期
   */
  private isExpired(item: CacheItem): boolean {
    return item.expiresAt ? item.expiresAt < new Date() : false;
  }

  /**
   * 更新访问统计
   * @param item 缓存项
   */
  private updateAccessStats(item: CacheItem): void {
    item.accessCount++;
    item.lastAccessed = new Date();

    if (item.metadata.preloaded) {
      this.stats.preloadHits++;
    }
  }

  /**
   * 更新响应时间
   * @param startTime 开始时间
   */
  private updateResponseTime(startTime: number): void {
    const responseTime = Date.now() - startTime;
    this.stats.averageResponseTime =
      (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime) / this.stats.totalRequests;
  }

  /**
   * 计算数据大小
   * @param data 数据
   * @returns 大小（字节）
   */
  private calculateSize(data: any): number {
    return JSON.stringify(data).length * 2; // 简化计算，假设每个字符2字节
  }

  /**
   * 提升到L1缓存
   * @param item 缓存项
   */
  private async promoteToL1(item: CacheItem): Promise<void> {
    if (this.l1Cache.size >= this.config.l1MaxSize) {
      await this.evictFromL1();
    }

    item.level = CacheLevel.L1;
    this.l1Cache.set(item.key, item);
  }

  /**
   * 提升到L2缓存
   * @param item 缓存项
   */
  private async promoteToL2(item: CacheItem): Promise<void> {
    if (this.l2Cache.size >= this.config.l2MaxSize) {
      await this.evictFromL2();
    }

    item.level = CacheLevel.L2;
    this.l2Cache.set(item.key, item);
  }

  /**
   * 设置到L1缓存
   * @param item 缓存项
   */
  private async setToL1(item: CacheItem): Promise<void> {
    if (this.l1Cache.size >= this.config.l1MaxSize) {
      await this.evictFromL1();
    }

    item.level = CacheLevel.L1;
    this.l1Cache.set(item.key, item);
  }

  /**
   * 设置到L2缓存
   * @param item 缓存项
   */
  private async setToL2(item: CacheItem): Promise<void> {
    if (this.l2Cache.size >= this.config.l2MaxSize) {
      await this.evictFromL2();
    }

    item.level = CacheLevel.L2;
    this.l2Cache.set(item.key, item);
  }

  /**
   * 设置到L3缓存
   * @param item 缓存项
   */
  private async setToL3(item: CacheItem): Promise<void> {
    if (this.l3Cache.size >= this.config.l3MaxSize) {
      await this.evictFromL3();
    }

    item.level = CacheLevel.L3;
    this.l3Cache.set(item.key, item);
  }

  /**
   * 从L1缓存淘汰项目
   */
  private async evictFromL1(): Promise<void> {
    const items = Array.from(this.l1Cache.values());
    const lruItem = items.reduce((oldest, current) =>
      current.lastAccessed < oldest.lastAccessed ? current : oldest
    );

    this.l1Cache.delete(lruItem.key);

    // 降级到L2缓存
    if (this.l2Cache.size < this.config.l2MaxSize) {
      lruItem.level = CacheLevel.L2;
      this.l2Cache.set(lruItem.key, lruItem);
    }
  }

  /**
   * 从L2缓存淘汰项目
   */
  private async evictFromL2(): Promise<void> {
    const items = Array.from(this.l2Cache.values());
    const lruItem = items.reduce((oldest, current) =>
      current.lastAccessed < oldest.lastAccessed ? current : oldest
    );

    this.l2Cache.delete(lruItem.key);

    // 降级到L3缓存
    if (this.l3Cache.size < this.config.l3MaxSize) {
      lruItem.level = CacheLevel.L3;
      this.l3Cache.set(lruItem.key, lruItem);
    }
  }

  /**
   * 从L3缓存淘汰项目
   */
  private async evictFromL3(): Promise<void> {
    const items = Array.from(this.l3Cache.values());
    const lruItem = items.reduce((oldest, current) =>
      current.lastAccessed < oldest.lastAccessed ? current : oldest
    );

    this.l3Cache.delete(lruItem.key);
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * 清理过期项目
   */
  private cleanup(): void {
    const now = new Date();
    let cleanedCount = 0;

    // 清理L1缓存
    for (const [key, item] of this.l1Cache.entries()) {
      if (this.isExpired(item)) {
        this.l1Cache.delete(key);
        cleanedCount++;
      }
    }

    // 清理L2缓存
    for (const [key, item] of this.l2Cache.entries()) {
      if (this.isExpired(item)) {
        this.l2Cache.delete(key);
        cleanedCount++;
      }
    }

    // 清理L3缓存
    for (const [key, item] of this.l3Cache.entries()) {
      if (this.isExpired(item)) {
        this.l3Cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug(`清理了 ${cleanedCount} 个过期缓存项`);
      this.eventEmitter.emit('cache.cleaned', { cleanedCount });
    }
  }

  /**
   * 停止服务
   */
  async onModuleDestroy(): Promise<void> {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    this.logger.log('预测性缓存服务已停止');
  }
}