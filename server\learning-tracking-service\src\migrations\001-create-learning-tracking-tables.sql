-- 学习记录跟踪系统数据库迁移脚本
-- 创建学习记录、用户画像、推荐记录和内容表

-- 学习记录表
CREATE TABLE learning_records (
  id VARCHAR(36) PRIMARY KEY,
  statement_id VARCHAR(255) UNIQUE NOT NULL,
  user_id VARCHAR(255) NOT NULL,
  verb VARCHAR(255) NOT NULL,
  object_id VARCHAR(255) NOT NULL,
  result JSON,
  context JSON,
  timestamp DATETIME NOT NULL,
  raw_statement JSON NOT NULL,
  sync_status ENUM('pending', 'syncing', 'synced', 'failed', 'retry') DEFAULT 'pending',
  retry_count INT DEFAULT 0,
  last_error TEXT,
  last_sync_attempt DATETIME,
  synced_at DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_user_id (user_id),
  INDEX idx_timestamp (timestamp),
  INDEX idx_verb (verb),
  INDEX idx_sync_status (sync_status),
  INDEX idx_user_timestamp (user_id, timestamp)
);

-- 学习者画像表
CREATE TABLE learner_profiles (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(255) UNIQUE NOT NULL,
  demographics JSON,
  learning_preferences JSON,
  knowledge_areas JSON,
  behavior_patterns JSON,
  learning_goals JSON,
  social_learning JSON,
  cognitive_traits JSON,
  emotional_traits JSON,
  learning_outcomes JSON,
  metadata JSON,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_user_id (user_id)
);

-- 内容表
CREATE TABLE contents (
  id VARCHAR(36) PRIMARY KEY,
  title VARCHAR(500) NOT NULL,
  description TEXT,
  type ENUM('concept', 'exercise', 'video', 'article', 'interactive', 'path', 'resource') NOT NULL,
  difficulty ENUM('easy', 'medium', 'hard') NOT NULL,
  duration INT NOT NULL,
  tags JSON,
  knowledge_area VARCHAR(255),
  prerequisites JSON,
  learning_objectives JSON,
  content_url VARCHAR(1000),
  thumbnail_url VARCHAR(1000),
  author_id VARCHAR(255),
  views INT DEFAULT 0,
  completions INT DEFAULT 0,
  average_rating DECIMAL(3,2) DEFAULT 0,
  rating_count INT DEFAULT 0,
  success_rate DECIMAL(3,2) DEFAULT 0,
  enabled BOOLEAN DEFAULT TRUE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_type (type),
  INDEX idx_difficulty (difficulty),
  INDEX idx_knowledge_area (knowledge_area),
  INDEX idx_enabled (enabled),
  INDEX idx_type_area (type, knowledge_area)
);

-- 推荐记录表
CREATE TABLE recommendations (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(255) NOT NULL,
  content_id VARCHAR(255) NOT NULL,
  type ENUM('concept', 'exercise', 'video', 'article', 'interactive', 'path', 'resource') NOT NULL,
  title VARCHAR(500) NOT NULL,
  description TEXT,
  relevance_score DECIMAL(3,2) NOT NULL,
  difficulty ENUM('easy', 'medium', 'hard') NOT NULL,
  estimated_duration INT NOT NULL,
  reason TEXT,
  tags JSON,
  knowledge_area VARCHAR(255),
  prerequisites JSON,
  learning_objectives JSON,
  status ENUM('pending', 'accepted', 'rejected', 'ignored', 'completed') DEFAULT 'pending',
  feedback_rating INT,
  feedback_comment TEXT,
  expires_at DATETIME,
  viewed_at DATETIME,
  accepted_at DATETIME,
  completed_at DATETIME,
  metadata JSON,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_user_id (user_id),
  INDEX idx_content_id (content_id),
  INDEX idx_status (status),
  INDEX idx_knowledge_area (knowledge_area),
  INDEX idx_created_at (created_at),
  INDEX idx_user_status (user_id, status),
  
  FOREIGN KEY (content_id) REFERENCES contents(id) ON DELETE CASCADE
);

-- 学习会话表
CREATE TABLE learning_sessions (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(255) NOT NULL,
  session_id VARCHAR(255) NOT NULL,
  start_time DATETIME NOT NULL,
  end_time DATETIME,
  duration INT,
  activities_count INT DEFAULT 0,
  knowledge_areas JSON,
  emotions JSON,
  average_satisfaction DECIMAL(3,2),
  device_type VARCHAR(50),
  platform VARCHAR(50),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_user_id (user_id),
  INDEX idx_session_id (session_id),
  INDEX idx_start_time (start_time),
  INDEX idx_user_start (user_id, start_time)
);

-- 用户偏好表
CREATE TABLE user_preferences (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(255) UNIQUE NOT NULL,
  content_types JSON,
  knowledge_areas JSON,
  difficulties JSON,
  authors JSON,
  tags JSON,
  time_preferences JSON,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_user_id (user_id)
);

-- 内容相似度表
CREATE TABLE content_similarities (
  id VARCHAR(36) PRIMARY KEY,
  content_id1 VARCHAR(36) NOT NULL,
  content_id2 VARCHAR(36) NOT NULL,
  similarity DECIMAL(3,2) NOT NULL,
  factors JSON,
  computed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_content1 (content_id1),
  INDEX idx_content2 (content_id2),
  INDEX idx_similarity (similarity),
  UNIQUE KEY unique_pair (content_id1, content_id2),
  
  FOREIGN KEY (content_id1) REFERENCES contents(id) ON DELETE CASCADE,
  FOREIGN KEY (content_id2) REFERENCES contents(id) ON DELETE CASCADE
);

-- 推荐反馈表
CREATE TABLE recommendation_feedbacks (
  id VARCHAR(36) PRIMARY KEY,
  recommendation_id VARCHAR(36) NOT NULL,
  user_id VARCHAR(255) NOT NULL,
  action ENUM('accepted', 'rejected', 'ignored', 'completed', 'bookmarked') NOT NULL,
  rating INT,
  comment TEXT,
  time_to_decision INT,
  context JSON,
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_recommendation_id (recommendation_id),
  INDEX idx_user_id (user_id),
  INDEX idx_action (action),
  INDEX idx_timestamp (timestamp),
  
  FOREIGN KEY (recommendation_id) REFERENCES recommendations(id) ON DELETE CASCADE
);

-- 学习路径表
CREATE TABLE learning_paths (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(500) NOT NULL,
  description TEXT,
  knowledge_area VARCHAR(255),
  difficulty ENUM('easy', 'medium', 'hard') NOT NULL,
  estimated_duration INT,
  prerequisites JSON,
  learning_objectives JSON,
  content_sequence JSON,
  enabled BOOLEAN DEFAULT TRUE,
  created_by VARCHAR(255),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_knowledge_area (knowledge_area),
  INDEX idx_difficulty (difficulty),
  INDEX idx_enabled (enabled)
);

-- 学习进度表
CREATE TABLE learning_progress (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(255) NOT NULL,
  content_id VARCHAR(36),
  path_id VARCHAR(36),
  progress_type ENUM('content', 'path') NOT NULL,
  completion_rate DECIMAL(3,2) DEFAULT 0,
  time_spent INT DEFAULT 0,
  last_accessed DATETIME,
  completed_at DATETIME,
  score DECIMAL(3,2),
  attempts INT DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_user_id (user_id),
  INDEX idx_content_id (content_id),
  INDEX idx_path_id (path_id),
  INDEX idx_progress_type (progress_type),
  INDEX idx_completion_rate (completion_rate),
  INDEX idx_user_content (user_id, content_id),
  INDEX idx_user_path (user_id, path_id),
  
  FOREIGN KEY (content_id) REFERENCES contents(id) ON DELETE CASCADE,
  FOREIGN KEY (path_id) REFERENCES learning_paths(id) ON DELETE CASCADE
);

-- 知识图谱表
CREATE TABLE knowledge_graph (
  id VARCHAR(36) PRIMARY KEY,
  concept_id VARCHAR(255) NOT NULL,
  concept_name VARCHAR(500) NOT NULL,
  parent_concept_id VARCHAR(255),
  knowledge_area VARCHAR(255),
  difficulty_level INT DEFAULT 1,
  prerequisites JSON,
  related_concepts JSON,
  content_ids JSON,
  description TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_concept_id (concept_id),
  INDEX idx_parent_concept (parent_concept_id),
  INDEX idx_knowledge_area (knowledge_area),
  INDEX idx_difficulty_level (difficulty_level)
);

-- 系统配置表
CREATE TABLE system_configs (
  id VARCHAR(36) PRIMARY KEY,
  config_key VARCHAR(255) UNIQUE NOT NULL,
  config_value JSON,
  description TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_config_key (config_key)
);

-- 插入默认系统配置
INSERT INTO system_configs (id, config_key, config_value, description) VALUES
(UUID(), 'recommendation_algorithm', '{"name": "personalized_hybrid", "version": "1.0", "enabled": true}', '推荐算法配置'),
(UUID(), 'sync_settings', '{"batch_size": 50, "max_retries": 3, "retry_delay": 5000}', '数据同步设置'),
(UUID(), 'profile_analysis', '{"min_data_points": 10, "analysis_depth": "detailed", "update_frequency": "daily"}', '用户画像分析配置'),
(UUID(), 'xapi_settings', '{"version": "2.0.0", "timeout": 30000, "enable_validation": true}', 'xAPI协议设置');

-- 插入示例内容数据
INSERT INTO contents (id, title, description, type, difficulty, duration, tags, knowledge_area, prerequisites, learning_objectives, enabled) VALUES
(UUID(), 'JavaScript基础语法', 'JavaScript编程语言的基础语法介绍', 'article', 'easy', 30, '["javascript", "programming", "syntax"]', 'programming', '[]', '["掌握JavaScript基本语法", "理解变量和数据类型"]', true),
(UUID(), 'React组件开发', 'React框架中组件的开发方法', 'video', 'medium', 45, '["react", "component", "frontend"]', 'frontend', '["JavaScript基础"]', '["创建React组件", "理解组件生命周期"]', true),
(UUID(), '数据结构与算法', '常用数据结构和算法的实现', 'interactive', 'hard', 60, '["algorithm", "data-structure", "computer-science"]', 'computer-science', '["编程基础"]', '["掌握常用数据结构", "实现基本算法"]', true),
(UUID(), 'Python入门练习', 'Python编程语言的入门练习题', 'exercise', 'easy', 20, '["python", "programming", "practice"]', 'programming', '[]', '["熟悉Python语法", "完成基础编程练习"]', true),
(UUID(), '机器学习概念', '机器学习的基本概念和应用', 'concept', 'medium', 40, '["machine-learning", "ai", "concepts"]', 'artificial-intelligence', '["数学基础", "编程基础"]', '["理解机器学习概念", "了解常用算法"]', true);

-- 创建触发器：自动更新时间戳
DELIMITER $$

CREATE TRIGGER update_learning_records_timestamp
BEFORE UPDATE ON learning_records
FOR EACH ROW
BEGIN
  SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$

CREATE TRIGGER update_learner_profiles_timestamp
BEFORE UPDATE ON learner_profiles
FOR EACH ROW
BEGIN
  SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$

CREATE TRIGGER update_recommendations_timestamp
BEFORE UPDATE ON recommendations
FOR EACH ROW
BEGIN
  SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$

CREATE TRIGGER update_contents_timestamp
BEFORE UPDATE ON contents
FOR EACH ROW
BEGIN
  SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$

DELIMITER ;

-- 创建视图：学习统计视图
CREATE VIEW learning_stats_view AS
SELECT 
  lr.user_id,
  COUNT(*) as total_activities,
  COUNT(DISTINCT DATE(lr.timestamp)) as active_days,
  COUNT(DISTINCT JSON_UNQUOTE(JSON_EXTRACT(lr.context, '$.extensions."http://dl-engine.com/xapi/extensions/knowledge-area"'))) as knowledge_areas_count,
  AVG(CASE WHEN JSON_UNQUOTE(JSON_EXTRACT(lr.result, '$.score.scaled')) IS NOT NULL 
           THEN CAST(JSON_UNQUOTE(JSON_EXTRACT(lr.result, '$.score.scaled')) AS DECIMAL(3,2)) 
           ELSE NULL END) as average_score,
  SUM(CASE WHEN JSON_UNQUOTE(JSON_EXTRACT(lr.result, '$.completion')) = 'true' THEN 1 ELSE 0 END) as completed_activities,
  MAX(lr.timestamp) as last_activity_time
FROM learning_records lr
WHERE lr.sync_status = 'synced'
GROUP BY lr.user_id;

-- 创建视图：推荐效果统计视图
CREATE VIEW recommendation_stats_view AS
SELECT 
  r.user_id,
  r.knowledge_area,
  COUNT(*) as total_recommendations,
  SUM(CASE WHEN r.status IN ('accepted', 'completed') THEN 1 ELSE 0 END) as accepted_recommendations,
  SUM(CASE WHEN r.status = 'completed' THEN 1 ELSE 0 END) as completed_recommendations,
  AVG(r.relevance_score) as average_relevance_score,
  AVG(r.feedback_rating) as average_rating
FROM recommendations r
GROUP BY r.user_id, r.knowledge_area;

-- 添加数据完整性约束
ALTER TABLE learning_records 
ADD CONSTRAINT chk_retry_count CHECK (retry_count >= 0),
ADD CONSTRAINT chk_timestamp CHECK (timestamp <= CURRENT_TIMESTAMP);

ALTER TABLE recommendations 
ADD CONSTRAINT chk_relevance_score CHECK (relevance_score >= 0 AND relevance_score <= 1),
ADD CONSTRAINT chk_feedback_rating CHECK (feedback_rating IS NULL OR (feedback_rating >= 1 AND feedback_rating <= 5)),
ADD CONSTRAINT chk_estimated_duration CHECK (estimated_duration > 0);

ALTER TABLE contents 
ADD CONSTRAINT chk_duration CHECK (duration > 0),
ADD CONSTRAINT chk_views CHECK (views >= 0),
ADD CONSTRAINT chk_completions CHECK (completions >= 0),
ADD CONSTRAINT chk_average_rating CHECK (average_rating >= 0 AND average_rating <= 5),
ADD CONSTRAINT chk_rating_count CHECK (rating_count >= 0),
ADD CONSTRAINT chk_success_rate CHECK (success_rate >= 0 AND success_rate <= 1);

-- 创建存储过程：清理过期推荐
DELIMITER $$

CREATE PROCEDURE CleanExpiredRecommendations()
BEGIN
  DECLARE done INT DEFAULT FALSE;
  DECLARE expired_count INT DEFAULT 0;
  
  -- 删除过期且未被接受的推荐
  DELETE FROM recommendations 
  WHERE expires_at < CURRENT_TIMESTAMP 
    AND status = 'pending';
  
  SET expired_count = ROW_COUNT();
  
  SELECT CONCAT('清理了 ', expired_count, ' 条过期推荐记录') as result;
END$$

DELIMITER ;

-- 创建事件调度器：定期清理过期推荐
CREATE EVENT IF NOT EXISTS cleanup_expired_recommendations
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
  CALL CleanExpiredRecommendations();
