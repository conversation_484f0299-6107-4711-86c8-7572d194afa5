/**
 * 数字人路径服务
 */
import { AvatarPath, IAvatarPath, IPathPoint } from '../models/AvatarPath';
import { Logger } from '../utils/Logger';
import { ValidationError, NotFoundError, ConflictError } from '../utils/errors';
import { EventEmitter } from 'events';
import { Types } from 'mongoose';

/**
 * 路径创建数据接口
 */
export interface CreatePathData {
  name: string;
  avatarId: string;
  projectId: string;
  sceneId?: string;
  points: IPathPoint[];
  loopMode?: 'none' | 'loop' | 'pingpong';
  interpolation?: 'linear' | 'smooth' | 'bezier' | 'spline';
  enabled?: boolean;
  metadata?: {
    description?: string;
    tags?: string[];
    category?: string;
  };
}

/**
 * 路径更新数据接口
 */
export interface UpdatePathData {
  name?: string;
  points?: IPathPoint[];
  loopMode?: 'none' | 'loop' | 'pingpong';
  interpolation?: 'linear' | 'smooth' | 'bezier' | 'spline';
  enabled?: boolean;
  metadata?: {
    description?: string;
    tags?: string[];
    category?: string;
  };
}

/**
 * 路径查询选项接口
 */
export interface PathQueryOptions {
  projectId: string;
  keyword?: string;
  avatarId?: string;
  sceneId?: string;
  tags?: string[];
  category?: string;
  enabled?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * 路径统计信息接口
 */
export interface PathStatistics {
  totalPaths: number;
  enabledPaths: number;
  disabledPaths: number;
  averageDuration: number;
  averageLength: number;
  totalPoints: number;
  pathsByLoopMode: Record<string, number>;
  pathsByInterpolation: Record<string, number>;
  pathsByCategory: Record<string, number>;
}

/**
 * 数字人路径服务类
 */
export class AvatarPathService extends EventEmitter {
  private logger = new Logger('AvatarPathService');

  /**
   * 创建路径
   * @param userId 用户ID
   * @param data 路径数据
   * @returns 创建的路径
   */
  public async createPath(userId: string, data: CreatePathData): Promise<IAvatarPath> {
    try {
      this.logger.info('创建路径', { userId, pathName: data.name });

      // 验证输入数据
      this.validateCreateData(data);

      // 检查路径名称是否重复
      const existingPath = await AvatarPath.findOne({
        projectId: data.projectId,
        name: data.name
      });

      if (existingPath) {
        throw new ConflictError(`路径名称 "${data.name}" 已存在`);
      }

      // 创建路径
      const path = new AvatarPath({
        ...data,
        metadata: {
          creator: userId,
          createdAt: new Date(),
          updatedAt: new Date(),
          version: 1,
          description: data.metadata?.description || '',
          tags: data.metadata?.tags || [],
          category: data.metadata?.category
        }
      });

      // 验证路径
      const validation = path.validate();
      if (!validation.valid) {
        throw new ValidationError(`路径验证失败: ${validation.errors.join(', ')}`);
      }

      // 保存路径
      const savedPath = await path.save();

      // 发射事件
      this.emit('pathCreated', {
        pathId: savedPath.id,
        userId,
        projectId: data.projectId,
        avatarId: data.avatarId
      });

      this.logger.info('路径创建成功', { pathId: savedPath.id });
      return savedPath;

    } catch (error) {
      this.logger.error('创建路径失败', error);
      throw error;
    }
  }

  /**
   * 更新路径
   * @param userId 用户ID
   * @param pathId 路径ID
   * @param data 更新数据
   * @returns 更新后的路径
   */
  public async updatePath(userId: string, pathId: string, data: UpdatePathData): Promise<IAvatarPath> {
    try {
      this.logger.info('更新路径', { userId, pathId });

      // 查找路径
      const path = await AvatarPath.findById(pathId);
      if (!path) {
        throw new NotFoundError(`路径 ${pathId} 不存在`);
      }

      // 检查权限（简化实现，实际应该检查用户权限）
      // if (path.metadata.creator !== userId) {
      //   throw new ForbiddenError('没有权限修改此路径');
      // }

      // 更新字段
      if (data.name !== undefined) {
        // 检查名称是否重复
        const existingPath = await AvatarPath.findOne({
          _id: { $ne: pathId },
          projectId: path.projectId,
          name: data.name
        });

        if (existingPath) {
          throw new ConflictError(`路径名称 "${data.name}" 已存在`);
        }

        path.name = data.name;
      }

      if (data.points !== undefined) {
        path.points = data.points;
      }

      if (data.loopMode !== undefined) {
        path.loopMode = data.loopMode;
      }

      if (data.interpolation !== undefined) {
        path.interpolation = data.interpolation;
      }

      if (data.enabled !== undefined) {
        path.enabled = data.enabled;
      }

      if (data.metadata) {
        if (data.metadata.description !== undefined) {
          path.metadata.description = data.metadata.description;
        }
        if (data.metadata.tags !== undefined) {
          path.metadata.tags = data.metadata.tags;
        }
        if (data.metadata.category !== undefined) {
          path.metadata.category = data.metadata.category;
        }
      }

      // 验证更新后的路径
      const validation = path.validate();
      if (!validation.valid) {
        throw new ValidationError(`路径验证失败: ${validation.errors.join(', ')}`);
      }

      // 保存更新
      const updatedPath = await path.save();

      // 发射事件
      this.emit('pathUpdated', {
        pathId: updatedPath.id,
        userId,
        projectId: path.projectId,
        changes: Object.keys(data)
      });

      this.logger.info('路径更新成功', { pathId: updatedPath.id });
      return updatedPath;

    } catch (error) {
      this.logger.error('更新路径失败', error);
      throw error;
    }
  }

  /**
   * 删除路径
   * @param userId 用户ID
   * @param pathId 路径ID
   */
  public async deletePath(userId: string, pathId: string): Promise<void> {
    try {
      this.logger.info('删除路径', { userId, pathId });

      // 查找路径
      const path = await AvatarPath.findById(pathId);
      if (!path) {
        throw new NotFoundError(`路径 ${pathId} 不存在`);
      }

      // 检查权限
      // if (path.metadata.creator !== userId) {
      //   throw new ForbiddenError('没有权限删除此路径');
      // }

      // 删除路径
      await AvatarPath.findByIdAndDelete(pathId);

      // 发射事件
      this.emit('pathDeleted', {
        pathId,
        userId,
        projectId: path.projectId,
        avatarId: path.avatarId
      });

      this.logger.info('路径删除成功', { pathId });

    } catch (error) {
      this.logger.error('删除路径失败', error);
      throw error;
    }
  }

  /**
   * 获取路径详情
   * @param pathId 路径ID
   * @returns 路径详情
   */
  public async getPath(pathId: string): Promise<IAvatarPath> {
    try {
      const path = await AvatarPath.findById(pathId);
      if (!path) {
        throw new NotFoundError(`路径 ${pathId} 不存在`);
      }

      return path;

    } catch (error) {
      this.logger.error('获取路径失败', error);
      throw error;
    }
  }

  /**
   * 查询路径列表
   * @param options 查询选项
   * @returns 路径列表和分页信息
   */
  public async queryPaths(options: PathQueryOptions): Promise<{
    paths: IAvatarPath[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'metadata.updatedAt',
        sortOrder = 'desc'
      } = options;

      // 构建查询条件
      const filter: any = { projectId: options.projectId };

      if (options.keyword) {
        filter.$or = [
          { name: { $regex: options.keyword, $options: 'i' } },
          { 'metadata.description': { $regex: options.keyword, $options: 'i' } }
        ];
      }

      if (options.avatarId) {
        filter.avatarId = options.avatarId;
      }

      if (options.sceneId) {
        filter.sceneId = options.sceneId;
      }

      if (options.tags && options.tags.length > 0) {
        filter['metadata.tags'] = { $in: options.tags };
      }

      if (options.category) {
        filter['metadata.category'] = options.category;
      }

      if (typeof options.enabled === 'boolean') {
        filter.enabled = options.enabled;
      }

      // 构建排序
      const sort: any = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // 执行查询
      const [paths, total] = await Promise.all([
        AvatarPath.find(filter)
          .sort(sort)
          .skip((page - 1) * limit)
          .limit(limit),
        AvatarPath.countDocuments(filter)
      ]);

      return {
        paths,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };

    } catch (error) {
      this.logger.error('查询路径失败', error);
      throw error;
    }
  }

  /**
   * 克隆路径
   * @param userId 用户ID
   * @param pathId 原路径ID
   * @param newName 新路径名称
   * @returns 克隆的路径
   */
  public async clonePath(userId: string, pathId: string, newName?: string): Promise<IAvatarPath> {
    try {
      this.logger.info('克隆路径', { userId, pathId, newName });

      // 获取原路径
      const originalPath = await this.getPath(pathId);

      // 克隆路径
      const clonedPath = originalPath.clone(newName);
      clonedPath.metadata.creator = userId;

      // 检查名称是否重复
      const existingPath = await AvatarPath.findOne({
        projectId: originalPath.projectId,
        name: clonedPath.name
      });

      if (existingPath) {
        // 自动生成唯一名称
        let counter = 1;
        let uniqueName = `${clonedPath.name} (${counter})`;
        
        while (await AvatarPath.findOne({
          projectId: originalPath.projectId,
          name: uniqueName
        })) {
          counter++;
          uniqueName = `${clonedPath.name} (${counter})`;
        }
        
        clonedPath.name = uniqueName;
      }

      // 保存克隆的路径
      const savedPath = await clonedPath.save();

      // 发射事件
      this.emit('pathCloned', {
        originalPathId: pathId,
        clonedPathId: savedPath.id,
        userId,
        projectId: originalPath.projectId
      });

      this.logger.info('路径克隆成功', { 
        originalPathId: pathId,
        clonedPathId: savedPath.id 
      });

      return savedPath;

    } catch (error) {
      this.logger.error('克隆路径失败', error);
      throw error;
    }
  }

  /**
   * 获取路径统计信息
   * @param projectId 项目ID
   * @returns 统计信息
   */
  public async getPathStatistics(projectId: string): Promise<PathStatistics> {
    try {
      const paths = await AvatarPath.find({ projectId });

      const stats: PathStatistics = {
        totalPaths: paths.length,
        enabledPaths: paths.filter(p => p.enabled).length,
        disabledPaths: paths.filter(p => !p.enabled).length,
        averageDuration: 0,
        averageLength: 0,
        totalPoints: 0,
        pathsByLoopMode: {},
        pathsByInterpolation: {},
        pathsByCategory: {}
      };

      if (paths.length > 0) {
        // 计算平均值
        const totalDuration = paths.reduce((sum, p) => sum + p.totalDuration, 0);
        const totalLength = paths.reduce((sum, p) => sum + p.totalLength, 0);
        stats.averageDuration = totalDuration / paths.length;
        stats.averageLength = totalLength / paths.length;

        // 计算总点数
        stats.totalPoints = paths.reduce((sum, p) => sum + p.points.length, 0);

        // 按循环模式分组
        paths.forEach(path => {
          stats.pathsByLoopMode[path.loopMode] = 
            (stats.pathsByLoopMode[path.loopMode] || 0) + 1;
        });

        // 按插值类型分组
        paths.forEach(path => {
          stats.pathsByInterpolation[path.interpolation] = 
            (stats.pathsByInterpolation[path.interpolation] || 0) + 1;
        });

        // 按分类分组
        paths.forEach(path => {
          const category = path.metadata.category || 'uncategorized';
          stats.pathsByCategory[category] = 
            (stats.pathsByCategory[category] || 0) + 1;
        });
      }

      return stats;

    } catch (error) {
      this.logger.error('获取路径统计失败', error);
      throw error;
    }
  }

  /**
   * 批量操作路径
   * @param userId 用户ID
   * @param pathIds 路径ID列表
   * @param operation 操作类型
   * @param data 操作数据
   */
  public async batchOperation(
    userId: string,
    pathIds: string[],
    operation: 'enable' | 'disable' | 'delete' | 'updateCategory',
    data?: any
  ): Promise<void> {
    try {
      this.logger.info('批量操作路径', { userId, pathIds, operation });

      switch (operation) {
        case 'enable':
          await AvatarPath.updateMany(
            { _id: { $in: pathIds } },
            { enabled: true }
          );
          break;

        case 'disable':
          await AvatarPath.updateMany(
            { _id: { $in: pathIds } },
            { enabled: false }
          );
          break;

        case 'delete':
          await AvatarPath.deleteMany({ _id: { $in: pathIds } });
          break;

        case 'updateCategory':
          if (data && data.category) {
            await AvatarPath.updateMany(
              { _id: { $in: pathIds } },
              { 'metadata.category': data.category }
            );
          }
          break;

        default:
          throw new ValidationError(`不支持的操作: ${operation}`);
      }

      // 发射事件
      this.emit('pathBatchOperation', {
        pathIds,
        operation,
        userId,
        data
      });

      this.logger.info('批量操作完成', { pathIds, operation });

    } catch (error) {
      this.logger.error('批量操作失败', error);
      throw error;
    }
  }

  /**
   * 验证创建数据
   * @param data 创建数据
   */
  private validateCreateData(data: CreatePathData): void {
    if (!data.name || data.name.trim() === '') {
      throw new ValidationError('路径名称不能为空');
    }

    if (!data.avatarId || data.avatarId.trim() === '') {
      throw new ValidationError('数字人ID不能为空');
    }

    if (!data.projectId || data.projectId.trim() === '') {
      throw new ValidationError('项目ID不能为空');
    }

    if (!data.points || data.points.length < 2) {
      throw new ValidationError('路径至少需要2个点');
    }

    // 验证路径点
    data.points.forEach((point, index) => {
      if (!point.id) {
        throw new ValidationError(`路径点${index + 1}缺少ID`);
      }

      if (!point.position || 
          typeof point.position.x !== 'number' ||
          typeof point.position.y !== 'number' ||
          typeof point.position.z !== 'number') {
        throw new ValidationError(`路径点${index + 1}位置数据无效`);
      }

      if (typeof point.speed !== 'number' || point.speed <= 0) {
        throw new ValidationError(`路径点${index + 1}速度必须大于0`);
      }

      if (typeof point.waitTime !== 'number' || point.waitTime < 0) {
        throw new ValidationError(`路径点${index + 1}等待时间不能为负数`);
      }
    });
  }
}

// 导出单例实例
export const avatarPathService = new AvatarPathService();
