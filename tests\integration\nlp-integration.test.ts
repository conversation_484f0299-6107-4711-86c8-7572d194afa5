/**
 * NLP场景生成器集成测试
 */
import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';

describe('NLP场景生成器集成测试', () => {
  let engineNLP: any;
  let editorNLP: any;
  let serverAPI: any;

  beforeAll(async () => {
    // 初始化各层次的NLP组件
    console.log('初始化集成测试环境...');
  });

  afterAll(async () => {
    // 清理测试环境
    console.log('清理集成测试环境...');
  });

  describe('接口一致性测试', () => {
    test('所有层次都有generateSceneFromNaturalLanguage方法', async () => {
      expect(typeof engineNLP?.generateSceneFromNaturalLanguage).toBe('function');
      expect(typeof editorNLP?.generateSceneFromNaturalLanguage).toBe('function');
      expect(typeof serverAPI?.generateScene).toBe('function');
    });

    test('所有层次都有registerCustomStyle方法', async () => {
      expect(typeof engineNLP?.registerCustomStyle).toBe('function');
      expect(typeof editorNLP?.registerCustomStyle).toBe('function');
      expect(typeof serverAPI?.registerCustomStyle).toBe('function');
    });

    test('所有层次都有registerCustomObject方法', async () => {
      expect(typeof engineNLP?.registerCustomObject).toBe('function');
      expect(typeof editorNLP?.registerCustomObject).toBe('function');
      expect(typeof serverAPI?.registerCustomObject).toBe('function');
    });

    test('所有层次都有registerAIService方法', async () => {
      expect(typeof engineNLP?.registerAIService).toBe('function');
      expect(typeof editorNLP?.registerAIService).toBe('function');
      expect(typeof serverAPI?.registerAIService).toBe('function');
    });

    test('所有层次都有getPerformanceMetrics方法', async () => {
      expect(typeof engineNLP?.getPerformanceMetrics).toBe('function');
      expect(typeof editorNLP?.getPerformanceMetrics).toBe('function');
      expect(typeof serverAPI?.getPerformanceMetrics).toBe('function');
    });
  });

  describe('功能对等性测试', () => {
    test('基础场景生成功能一致', async () => {
      const testInput = '创建一个现代办公室';
      const options = {
        style: 'realistic',
        quality: 80,
        maxObjects: 30
      };

      // 测试引擎层
      const engineResult = await engineNLP?.generateSceneFromNaturalLanguage(testInput, options);
      expect(engineResult).toBeDefined();
      expect(engineResult.entities).toBeDefined();

      // 测试编辑器层
      const editorResult = await editorNLP?.generateSceneFromNaturalLanguage(testInput, options);
      expect(editorResult).toBeDefined();
      expect(editorResult.entities).toBeDefined();

      // 验证结果结构一致
      expect(typeof engineResult).toBe(typeof editorResult);
    });

    test('自定义风格功能一致', async () => {
      const customStyle = {
        name: 'test_style',
        description: '测试风格',
        materialPresets: [],
        lightingPresets: [],
        objectModifiers: [],
        atmosphereSettings: {
          fogDensity: 0.1,
          fogColor: '#ffffff',
          skyboxType: 'default',
          postProcessingEffects: []
        },
        colorPalette: ['#ffffff', '#000000']
      };

      // 在所有层次注册自定义风格
      engineNLP?.registerCustomStyle(customStyle);
      editorNLP?.registerCustomStyle(customStyle);

      // 验证注册成功
      const engineStyles = engineNLP?.getCustomStyles();
      const editorStyles = editorNLP?.getCustomStyles();

      expect(engineStyles).toContain('test_style');
      expect(editorStyles).toContain('test_style');
    });

    test('自定义对象功能一致', async () => {
      const customObject = {
        name: 'test_object',
        category: 'test',
        description: '测试对象',
        geometryFactory: () => ({ type: 'box', width: 1, height: 1, depth: 1 }),
        defaultMaterial: 'standard',
        boundingBox: { min: { x: -0.5, y: 0, z: -0.5 }, max: { x: 0.5, y: 1, z: 0.5 } },
        tags: ['test'],
        complexity: 1
      };

      // 在所有层次注册自定义对象
      engineNLP?.registerCustomObject(customObject);
      editorNLP?.registerCustomObject(customObject);

      // 验证注册成功
      const engineObjects = engineNLP?.getCustomObjects();
      const editorObjects = editorNLP?.getCustomObjects();

      expect(engineObjects).toContain('test_object');
      expect(editorObjects).toContain('test_object');
    });

    test('AI服务功能一致', async () => {
      const aiService = {
        name: 'test_ai',
        endpoint: 'http://localhost:8080/test',
        model: 'test-model',
        capabilities: ['text_understanding'],
        rateLimits: {
          requestsPerMinute: 60,
          requestsPerHour: 1000,
          maxConcurrent: 5
        }
      };

      // 在所有层次注册AI服务
      engineNLP?.registerAIService(aiService);
      editorNLP?.registerAIService(aiService);

      // 验证注册成功
      const engineServices = engineNLP?.getAIServices();
      const editorServices = editorNLP?.getAIServices();

      expect(engineServices).toContain('test_ai');
      expect(editorServices).toContain('test_ai');
    });
  });

  describe('跨层次数据同步测试', () => {
    test('自定义风格跨层次同步', async () => {
      const syncStyle = {
        name: 'sync_test_style',
        description: '同步测试风格',
        materialPresets: [],
        lightingPresets: [],
        objectModifiers: [],
        atmosphereSettings: {
          fogDensity: 0.2,
          fogColor: '#cccccc',
          skyboxType: 'gradient',
          postProcessingEffects: ['bloom']
        },
        colorPalette: ['#ff0000', '#00ff00', '#0000ff']
      };

      // 在引擎层注册
      engineNLP?.registerCustomStyle(syncStyle);

      // 等待同步
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 验证其他层次是否同步
      const editorStyles = editorNLP?.getCustomStyles();
      expect(editorStyles).toContain('sync_test_style');
    });

    test('性能指标跨层次同步', async () => {
      // 在引擎层生成场景
      await engineNLP?.generateSceneFromNaturalLanguage('测试场景', {
        style: 'realistic',
        quality: 50,
        maxObjects: 10
      });

      // 获取性能指标
      const engineMetrics = engineNLP?.getPerformanceMetrics();
      expect(engineMetrics.totalGenerations).toBeGreaterThan(0);

      // 等待同步
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 验证其他层次的性能指标是否同步
      const editorMetrics = editorNLP?.getPerformanceMetrics();
      expect(editorMetrics.totalGenerations).toBeGreaterThan(0);
    });
  });

  describe('视觉脚本节点集成测试', () => {
    test('NLP场景生成节点功能完整', async () => {
      // 测试场景生成节点
      const sceneNode = {
        type: 'nlp/scene/generate',
        inputs: {
          text: '创建一个测试场景',
          style: 'realistic',
          quality: 70,
          maxObjects: 20
        }
      };

      // 模拟节点执行
      const result = await executeVisualScriptNode(sceneNode);
      expect(result.success).toBe(true);
      expect(result.scene).toBeDefined();
    });

    test('自定义风格注册节点功能完整', async () => {
      // 测试自定义风格注册节点
      const styleNode = {
        type: 'nlp/style/register',
        inputs: {
          style_name: 'node_test_style',
          description: '节点测试风格',
          color_palette: ['#ffffff', '#000000']
        }
      };

      // 模拟节点执行
      const result = await executeVisualScriptNode(styleNode);
      expect(result.success).toBe(true);
      expect(result.styleConfig).toBeDefined();
    });

    test('性能监控节点功能完整', async () => {
      // 测试性能监控节点
      const perfNode = {
        type: 'nlp/performance/monitor',
        inputs: {
          action: 'get_metrics'
        }
      };

      // 模拟节点执行
      const result = await executeVisualScriptNode(perfNode);
      expect(result.success).toBe(true);
      expect(result.metrics).toBeDefined();
    });
  });

  describe('服务器端API集成测试', () => {
    test('场景生成API支持扩展功能', async () => {
      const requestData = {
        text: '创建一个API测试场景',
        style: 'realistic',
        quality: 80,
        maxObjects: 25,
        userId: 'test-user',
        enableAdvancedFeatures: true,
        customStyle: {
          name: 'api_test_style',
          description: 'API测试风格'
        }
      };

      // 模拟API调用
      const response = await callServerAPI('/api/v1/nlp-scene/generate', requestData);
      expect(response.success).toBe(true);
      expect(response.data.sceneData).toBeDefined();
      expect(response.data.metadata.customFeaturesUsed).toBeDefined();
    });
  });
});

// 辅助函数
async function executeVisualScriptNode(nodeConfig: any): Promise<any> {
  // 模拟视觉脚本节点执行
  return { success: true, ...nodeConfig.inputs };
}

async function callServerAPI(endpoint: string, data: any): Promise<any> {
  // 模拟服务器API调用
  return { success: true, data: { sceneData: {}, metadata: {} } };
}
