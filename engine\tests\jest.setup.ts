/**
 * Jest测试环境设置
 */

// 模拟Three.js
jest.mock('three', () => ({
  Vector2: jest.fn().mockImplementation((x = 0, y = 0) => ({
    x,
    y,
    set: jest.fn(),
    clone: jest.fn().mockReturnThis(),
    copy: jest.fn().mockReturnThis(),
    add: jest.fn().mockReturnThis(),
    sub: jest.fn().mockReturnThis(),
    multiply: jest.fn().mockReturnThis(),
    divide: jest.fn().mockReturnThis(),
    length: jest.fn().mockReturnValue(Math.sqrt(x * x + y * y)),
    normalize: jest.fn().mockReturnThis()
  })),
  Vector3: jest.fn().mockImplementation((x = 0, y = 0, z = 0) => ({
    x,
    y,
    z,
    set: jest.fn(),
    clone: jest.fn().mockReturnThis(),
    copy: jest.fn().mockReturnThis(),
    add: jest.fn().mockReturnThis(),
    sub: jest.fn().mockReturnThis(),
    multiply: jest.fn().mockReturnThis(),
    divide: jest.fn().mockReturnThis(),
    length: jest.fn().mockReturnValue(Math.sqrt(x * x + y * y + z * z)),
    normalize: jest.fn().mockReturnThis()
  })),
  Color: jest.fn().mockImplementation((r = 1, g = 1, b = 1) => ({
    r,
    g,
    b,
    set: jest.fn(),
    setHex: jest.fn(),
    setRGB: jest.fn(),
    clone: jest.fn().mockReturnThis(),
    copy: jest.fn().mockReturnThis()
  }))
}));

// 模拟DOM环境
global.document = {
  createElement: jest.fn(() => ({
    style: {},
    innerHTML: '',
    textContent: '',
    appendChild: jest.fn(),
    removeChild: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    getBoundingClientRect: jest.fn(() => ({
      left: 0,
      top: 0,
      width: 100,
      height: 50,
      right: 100,
      bottom: 50
    })),
    contains: jest.fn(() => false),
    querySelector: jest.fn(),
    classList: {
      add: jest.fn(),
      remove: jest.fn(),
      contains: jest.fn()
    },
    offsetWidth: 100,
    offsetHeight: 50,
    parentElement: null,
    firstChild: null
  })),
  body: {
    appendChild: jest.fn(),
    removeChild: jest.fn()
  },
  head: {
    appendChild: jest.fn(),
    removeChild: jest.fn()
  },
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  elementFromPoint: jest.fn()
} as any;

global.window = {
  innerWidth: 1920,
  innerHeight: 1080,
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  setTimeout: jest.fn((fn, delay) => setTimeout(fn, delay)),
  clearTimeout: jest.fn(clearTimeout),
  getComputedStyle: () => ({
    getPropertyValue: () => ''
  }),
  matchMedia: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn()
  })),
  performance: {
    now: jest.fn(() => Date.now())
  }
} as any;

// 模拟ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}));

// 模拟IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}));

// 模拟requestAnimationFrame
global.requestAnimationFrame = jest.fn((cb) => setTimeout(cb, 16));
global.cancelAnimationFrame = jest.fn(clearTimeout);

// 设置测试超时
jest.setTimeout(10000);
