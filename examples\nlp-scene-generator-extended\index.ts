/**
 * NLPSceneGenerator 扩展功能集成测试示例
 * 
 * 本示例展示了如何使用NLPSceneGenerator的扩展功能，包括：
 * - 自定义风格系统
 * - 自定义对象类型
 * - 外部AI服务集成
 * - 高级生成选项
 */

import * as THREE from 'three';
import {
  Engine,
  World,
  Scene,
  NLPSceneGenerator,
  CustomStyleConfig,
  CustomObjectType,
  ExternalAIServiceConfig,
  AICapability,
  MaterialType,
  GenerationOptions
} from '../../engine/src';

class ExtendedNLPSceneGeneratorExample {
  private engine: Engine;
  private nlpGenerator: NLPSceneGenerator;

  constructor() {
    this.engine = new Engine();
  }

  /**
   * 初始化示例
   */
  public async initialize(): Promise<void> {
    console.log('初始化扩展NLP场景生成器示例...');

    // 初始化引擎
    await this.engine.initialize();

    // 获取NLP场景生成器
    this.nlpGenerator = this.engine.getWorld().getSystem('NLPSceneGenerator') as NLPSceneGenerator;

    // 设置自定义风格
    this.setupCustomStyles();

    // 设置自定义对象类型
    this.setupCustomObjects();

    // 设置AI服务
    this.setupAIServices();

    // 设置事件监听
    this.setupEventListeners();

    console.log('扩展NLP场景生成器示例初始化完成');
  }

  /**
   * 设置自定义风格
   */
  private setupCustomStyles(): void {
    console.log('设置自定义风格...');

    // 工业风格
    const industrialStyle: CustomStyleConfig = {
      name: 'industrial_modern',
      description: '现代工业风格',
      materialPresets: [
        {
          name: 'industrial_steel',
          type: MaterialType.PHYSICAL,
          properties: {
            color: 0x444444,
            roughness: 0.7,
            metalness: 0.9
          },
          applicableObjects: ['桌子', '椅子', '架子', '管道']
        },
        {
          name: 'concrete_floor',
          type: MaterialType.STANDARD,
          properties: {
            color: 0x666666,
            roughness: 0.9,
            metalness: 0.1
          },
          applicableObjects: ['地面', '墙壁']
        }
      ],
      lightingPresets: [{
        name: 'industrial_lighting',
        ambientIntensity: 0.2,
        directionalIntensity: 1.0,
        colorTemperature: 4000,
        shadowSettings: {
          enabled: true,
          quality: 'medium',
          softness: 0.3,
          bias: -0.0005
        }
      }],
      objectModifiers: [],
      atmosphereSettings: {
        fogDensity: 0.05,
        fogColor: '#555555',
        skyboxType: 'urban',
        postProcessingEffects: ['vignette', 'grain']
      },
      colorPalette: ['#444444', '#666666', '#888888', '#aa6c39', '#cc9966']
    };

    // 未来科技风格
    const futuristicStyle: CustomStyleConfig = {
      name: 'futuristic_tech',
      description: '未来科技风格',
      materialPresets: [
        {
          name: 'holographic_metal',
          type: MaterialType.PHYSICAL,
          properties: {
            color: 0x00ffff,
            roughness: 0.1,
            metalness: 1.0,
            emissive: 0x001122
          },
          applicableObjects: ['设备', '面板', '控制台']
        },
        {
          name: 'energy_glass',
          type: MaterialType.PHYSICAL,
          properties: {
            color: 0xffffff,
            roughness: 0.0,
            metalness: 0.0,
            transmission: 0.9,
            emissive: 0x0066ff
          },
          applicableObjects: ['屏幕', '窗户', '隔板']
        }
      ],
      lightingPresets: [{
        name: 'neon_lighting',
        ambientIntensity: 0.1,
        directionalIntensity: 0.5,
        colorTemperature: 8000,
        shadowSettings: {
          enabled: true,
          quality: 'high',
          softness: 0.8,
          bias: -0.0001
        }
      }],
      objectModifiers: [],
      atmosphereSettings: {
        fogDensity: 0.02,
        fogColor: '#001122',
        skyboxType: 'space',
        postProcessingEffects: ['bloom', 'chromatic_aberration']
      },
      colorPalette: ['#00ffff', '#0066ff', '#ffffff', '#001122', '#003344']
    };

    // 注册自定义风格
    this.nlpGenerator.registerCustomStyle(industrialStyle);
    this.nlpGenerator.registerCustomStyle(futuristicStyle);

    console.log('自定义风格设置完成');
  }

  /**
   * 设置自定义对象类型
   */
  private setupCustomObjects(): void {
    console.log('设置自定义对象类型...');

    // 智能工作站
    const smartWorkstation: CustomObjectType = {
      name: 'smart_workstation',
      category: 'furniture',
      description: '智能工作站，集成多屏显示和AI助手',
      geometryFactory: (params) => {
        // 创建工作站的复合几何体
        const deskGeometry = new THREE.BoxGeometry(2.0, 0.08, 1.2);
        return deskGeometry;
      },
      defaultMaterial: 'industrial_steel',
      boundingBox: new THREE.Box3(
        new THREE.Vector3(-1.0, 0, -0.6),
        new THREE.Vector3(1.0, 1.5, 0.6)
      ),
      tags: ['furniture', 'workstation', 'smart', 'technology'],
      complexity: 7,
      additionalComponents: ['InteractiveComponent', 'AIAssistantComponent', 'MultiDisplayComponent']
    };

    // 全息投影设备
    const hologramProjector: CustomObjectType = {
      name: 'hologram_projector',
      category: 'technology',
      description: '全息投影设备，可显示3D全息图像',
      geometryFactory: (params) => {
        const projectorGeometry = new THREE.CylinderGeometry(0.3, 0.4, 0.8, 16);
        return projectorGeometry;
      },
      defaultMaterial: 'holographic_metal',
      boundingBox: new THREE.Box3(
        new THREE.Vector3(-0.4, 0, -0.4),
        new THREE.Vector3(0.4, 0.8, 0.4)
      ),
      tags: ['technology', 'hologram', 'projector', 'display'],
      complexity: 8,
      additionalComponents: ['HologramComponent', 'ProjectionComponent']
    };

    // 智能机器人助手
    const robotAssistant: CustomObjectType = {
      name: 'robot_assistant',
      category: 'robot',
      description: '智能机器人助手，可执行各种任务',
      geometryFactory: (params) => {
        const bodyGeometry = new THREE.CapsuleGeometry(0.3, 1.2, 4, 8);
        return bodyGeometry;
      },
      defaultMaterial: 'holographic_metal',
      boundingBox: new THREE.Box3(
        new THREE.Vector3(-0.3, 0, -0.3),
        new THREE.Vector3(0.3, 1.8, 0.3)
      ),
      tags: ['robot', 'assistant', 'ai', 'automation'],
      complexity: 9,
      additionalComponents: ['AIComponent', 'MovementComponent', 'SpeechComponent']
    };

    // 注册自定义对象类型
    this.nlpGenerator.registerCustomObject(smartWorkstation);
    this.nlpGenerator.registerCustomObject(hologramProjector);
    this.nlpGenerator.registerCustomObject(robotAssistant);

    console.log('自定义对象类型设置完成');
  }

  /**
   * 设置AI服务
   */
  private setupAIServices(): void {
    console.log('设置AI服务...');

    // 模拟的本地AI服务
    const localAIService: ExternalAIServiceConfig = {
      name: 'local_ai_service',
      endpoint: 'http://localhost:8080/api',
      model: 'local-nlp-model',
      capabilities: [
        AICapability.TEXT_UNDERSTANDING,
        AICapability.SCENE_PLANNING
      ],
      rateLimits: {
        requestsPerMinute: 100,
        requestsPerHour: 5000,
        maxConcurrent: 10
      }
    };

    // 模拟的云端AI服务
    const cloudAIService: ExternalAIServiceConfig = {
      name: 'cloud_ai_service',
      endpoint: 'https://api.example.com/v1',
      model: 'advanced-scene-generator',
      capabilities: [
        AICapability.TEXT_UNDERSTANDING,
        AICapability.SCENE_PLANNING,
        AICapability.OBJECT_GENERATION,
        AICapability.OPTIMIZATION
      ],
      rateLimits: {
        requestsPerMinute: 60,
        requestsPerHour: 1000,
        maxConcurrent: 5
      },
      fallbackService: 'local_ai_service'
    };

    // 注册AI服务
    this.nlpGenerator.registerAIService(localAIService);
    this.nlpGenerator.registerAIService(cloudAIService);

    console.log('AI服务设置完成');
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    console.log('设置事件监听...');

    // 监听场景生成完成事件
    this.nlpGenerator.addEventListener('sceneGenerated', (event: any) => {
      console.log('✅ 场景生成完成:', {
        输入文本: event.userInput,
        处理时间: `${event.metrics.processingTime}ms`,
        实体数量: event.scene.getEntities().length,
        复杂度评分: event.understanding.complexity,
        情感色调: event.understanding.emotionalTone.primary
      });
    });

    // 监听生成错误事件
    this.nlpGenerator.addEventListener('generationError', (event: any) => {
      console.error('❌ 场景生成失败:', {
        输入文本: event.userInput,
        错误信息: event.error.message
      });
    });

    console.log('事件监听设置完成');
  }

  /**
   * 运行测试场景
   */
  public async runTests(): Promise<void> {
    console.log('\n开始运行扩展功能测试...\n');

    // 测试1: 工业风格场景
    await this.testIndustrialScene();

    // 测试2: 未来科技场景
    await this.testFuturisticScene();

    // 测试3: 自定义对象场景
    await this.testCustomObjectScene();

    // 测试4: AI增强场景
    await this.testAIEnhancedScene();

    // 测试5: 性能监控
    this.testPerformanceMonitoring();

    console.log('\n所有测试完成！');
  }

  /**
   * 测试工业风格场景
   */
  private async testIndustrialScene(): Promise<void> {
    console.log('🏭 测试工业风格场景生成...');

    try {
      const scene = await this.nlpGenerator.generateSceneFromNaturalLanguage(
        '创建一个现代化的工业车间，有钢铁结构、工作台、机械设备和管道系统',
        {
          style: 'industrial_modern',
          quality: 85,
          maxObjects: 25,
          constraints: {
            maxPolygons: 150000,
            targetFrameRate: 60
          },
          enableAdvancedFeatures: true,
          seedValue: 12345
        }
      );

      console.log('工业风格场景生成成功');
    } catch (error) {
      console.error('工业风格场景生成失败:', error);
    }
  }

  /**
   * 测试未来科技场景
   */
  private async testFuturisticScene(): Promise<void> {
    console.log('🚀 测试未来科技场景生成...');

    try {
      const scene = await this.nlpGenerator.generateSceneFromNaturalLanguage(
        '创建一个未来科技实验室，有全息显示器、能量面板、智能控制台和发光的装置',
        {
          style: 'futuristic_tech',
          quality: 90,
          maxObjects: 20,
          constraints: {
            maxPolygons: 180000,
            targetFrameRate: 60
          },
          enableAdvancedFeatures: true,
          aiServices: ['cloud_ai_service']
        }
      );

      console.log('未来科技场景生成成功');
    } catch (error) {
      console.error('未来科技场景生成失败:', error);
    }
  }

  /**
   * 测试自定义对象场景
   */
  private async testCustomObjectScene(): Promise<void> {
    console.log('🤖 测试自定义对象场景生成...');

    try {
      const scene = await this.nlpGenerator.generateSceneFromNaturalLanguage(
        '创建一个智能办公室，包含智能工作站、全息投影设备和机器人助手',
        {
          style: 'futuristic_tech',
          quality: 88,
          maxObjects: 15,
          constraints: {
            maxPolygons: 200000,
            targetFrameRate: 60
          },
          customObjects: [
            // 自定义对象会在setupCustomObjects中注册
          ],
          enableAdvancedFeatures: true
        }
      );

      console.log('自定义对象场景生成成功');
    } catch (error) {
      console.error('自定义对象场景生成失败:', error);
    }
  }

  /**
   * 测试AI增强场景
   */
  private async testAIEnhancedScene(): Promise<void> {
    console.log('🧠 测试AI增强场景生成...');

    try {
      const scene = await this.nlpGenerator.generateSceneFromNaturalLanguage(
        '创建一个温馨舒适的家庭客厅，要体现家的温暖和亲情，有柔和的灯光和舒适的家具',
        {
          style: 'realistic',
          quality: 92,
          maxObjects: 30,
          constraints: {
            maxPolygons: 160000,
            targetFrameRate: 60
          },
          aiServices: ['cloud_ai_service', 'local_ai_service'],
          enableAdvancedFeatures: true
        }
      );

      console.log('AI增强场景生成成功');
    } catch (error) {
      console.error('AI增强场景生成失败:', error);
    }
  }

  /**
   * 测试性能监控
   */
  private testPerformanceMonitoring(): void {
    console.log('📊 测试性能监控...');

    const metrics = this.nlpGenerator.getPerformanceMetrics();
    console.log('性能指标:', {
      总生成次数: metrics.totalGenerations,
      平均生成时间: `${metrics.averageGenerationTime.toFixed(2)}ms`,
      缓存命中率: `${(metrics.cacheHitRate * 100).toFixed(2)}%`,
      错误率: `${(metrics.errorRate * 100).toFixed(2)}%`
    });

    // 获取已注册的功能
    console.log('已注册的自定义风格:', this.nlpGenerator.getCustomStyles());
    console.log('已注册的自定义对象:', this.nlpGenerator.getCustomObjects());
    console.log('已注册的AI服务:', this.nlpGenerator.getAIServices());
  }
}

// 运行示例
async function runExample() {
  const example = new ExtendedNLPSceneGeneratorExample();
  
  try {
    await example.initialize();
    await example.runTests();
  } catch (error) {
    console.error('示例运行失败:', error);
  }
}

// 如果直接运行此文件，则执行示例
if (require.main === module) {
  runExample();
}

export { ExtendedNLPSceneGeneratorExample };
