#!/usr/bin/env node

/**
 * DL引擎RAG应用系统测试脚本
 * 用于测试RAG应用系统的各个功能模块
 */

const axios = require('axios');
const WebSocket = require('ws');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

// 配置
const config = {
  apiGateway: 'http://localhost:3000',
  knowledgeService: 'http://localhost:4011',
  ragService: 'http://localhost:4012',
  avatarService: 'http://localhost:4013',
  voiceService: 'ws://localhost:4014',
  testTimeout: 30000,
};

// 测试结果
const testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: [],
};

// 日志函数
const log = {
  info: (msg) => console.log(`\x1b[34m[INFO]\x1b[0m ${msg}`),
  success: (msg) => console.log(`\x1b[32m[SUCCESS]\x1b[0m ${msg}`),
  warning: (msg) => console.log(`\x1b[33m[WARNING]\x1b[0m ${msg}`),
  error: (msg) => console.log(`\x1b[31m[ERROR]\x1b[0m ${msg}`),
};

// 测试工具函数
async function runTest(testName, testFn) {
  testResults.total++;
  log.info(`运行测试: ${testName}`);
  
  try {
    await testFn();
    testResults.passed++;
    testResults.details.push({ name: testName, status: 'PASSED' });
    log.success(`测试通过: ${testName}`);
  } catch (error) {
    testResults.failed++;
    testResults.details.push({ name: testName, status: 'FAILED', error: error.message });
    log.error(`测试失败: ${testName} - ${error.message}`);
  }
}

// 等待函数
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 1. 测试服务健康状态
async function testServiceHealth() {
  const services = [
    { name: '知识库服务', url: `${config.knowledgeService}/health` },
    { name: 'RAG服务', url: `${config.ragService}/health` },
    { name: '数字人服务', url: `${config.avatarService}/health` },
    { name: 'API网关', url: `${config.apiGateway}/health` },
  ];

  for (const service of services) {
    await runTest(`${service.name}健康检查`, async () => {
      const response = await axios.get(service.url, { timeout: 5000 });
      if (response.status !== 200) {
        throw new Error(`服务返回状态码: ${response.status}`);
      }
    });
  }
}

// 2. 测试知识库功能
async function testKnowledgeBase() {
  let knowledgeBaseId;

  // 创建知识库
  await runTest('创建知识库', async () => {
    const response = await axios.post(`${config.knowledgeService}/api/knowledge-bases`, {
      name: '测试知识库',
      description: '用于测试的知识库',
      language: 'zh-CN',
    });
    
    if (response.status !== 201) {
      throw new Error(`创建失败，状态码: ${response.status}`);
    }
    
    knowledgeBaseId = response.data.id;
    log.info(`知识库ID: ${knowledgeBaseId}`);
  });

  // 上传文档
  await runTest('上传文档', async () => {
    // 创建测试文档
    const testContent = `
# 测试文档

这是一个用于测试的文档。

## 问题1
什么是人工智能？

人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。

## 问题2
什么是机器学习？

机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。
    `;
    
    const testFilePath = path.join(__dirname, 'test-document.txt');
    fs.writeFileSync(testFilePath, testContent);
    
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testFilePath));
    formData.append('knowledgeBaseId', knowledgeBaseId);
    
    const response = await axios.post(
      `${config.knowledgeService}/api/documents/upload`,
      formData,
      {
        headers: formData.getHeaders(),
        timeout: 30000,
      }
    );
    
    if (response.status !== 201) {
      throw new Error(`上传失败，状态码: ${response.status}`);
    }
    
    // 清理测试文件
    fs.unlinkSync(testFilePath);
    
    log.info(`文档ID: ${response.data.id}`);
  });

  // 等待文档处理
  await delay(5000);

  // 测试知识库搜索
  await runTest('知识库搜索', async () => {
    const response = await axios.post(`${config.knowledgeService}/api/search`, {
      knowledgeBaseId,
      query: '什么是人工智能',
      topK: 3,
    });
    
    if (response.status !== 200) {
      throw new Error(`搜索失败，状态码: ${response.status}`);
    }
    
    if (!response.data.results || response.data.results.length === 0) {
      throw new Error('搜索结果为空');
    }
    
    log.info(`搜索到 ${response.data.results.length} 个结果`);
  });

  return knowledgeBaseId;
}

// 3. 测试RAG对话功能
async function testRAGDialogue(knowledgeBaseId) {
  let sessionId;

  // 创建对话会话
  await runTest('创建RAG会话', async () => {
    const response = await axios.post(`${config.ragService}/api/sessions`, {
      knowledgeBaseId,
      userId: 'test-user',
      config: {
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 1000,
      },
    });
    
    if (response.status !== 201) {
      throw new Error(`创建会话失败，状态码: ${response.status}`);
    }
    
    sessionId = response.data.sessionId;
    log.info(`会话ID: ${sessionId}`);
  });

  // 发送消息
  await runTest('RAG对话测试', async () => {
    const response = await axios.post(`${config.ragService}/api/sessions/${sessionId}/messages`, {
      message: '请介绍一下人工智能',
      includeContext: true,
    });
    
    if (response.status !== 200) {
      throw new Error(`对话失败，状态码: ${response.status}`);
    }
    
    if (!response.data.response) {
      throw new Error('对话响应为空');
    }
    
    log.info(`RAG回答: ${response.data.response.substring(0, 100)}...`);
  });

  // 获取会话历史
  await runTest('获取会话历史', async () => {
    const response = await axios.get(`${config.ragService}/api/sessions/${sessionId}/messages`);
    
    if (response.status !== 200) {
      throw new Error(`获取历史失败，状态码: ${response.status}`);
    }
    
    if (!response.data.messages || response.data.messages.length === 0) {
      throw new Error('会话历史为空');
    }
    
    log.info(`会话历史包含 ${response.data.messages.length} 条消息`);
  });

  return sessionId;
}

// 4. 测试数字人功能
async function testAvatarService() {
  let avatarId;

  // 创建数字人配置
  await runTest('创建数字人配置', async () => {
    const response = await axios.post(`${config.avatarService}/api/avatars`, {
      name: '测试数字人',
      description: '用于测试的数字人',
      appearance: {
        model: '/models/default_avatar.glb',
        texture: '/textures/default_skin.jpg',
      },
      voice: {
        provider: 'azure',
        voice: 'zh-CN-XiaoxiaoNeural',
        language: 'zh-CN',
      },
      personality: {
        type: 'friendly',
        traits: ['helpful', 'patient'],
      },
    });
    
    if (response.status !== 201) {
      throw new Error(`创建数字人失败，状态码: ${response.status}`);
    }
    
    avatarId = response.data.id;
    log.info(`数字人ID: ${avatarId}`);
  });

  // 获取数字人配置
  await runTest('获取数字人配置', async () => {
    const response = await axios.get(`${config.avatarService}/api/avatars/${avatarId}`);
    
    if (response.status !== 200) {
      throw new Error(`获取配置失败，状态码: ${response.status}`);
    }
    
    if (response.data.name !== '测试数字人') {
      throw new Error('数字人配置不匹配');
    }
  });

  return avatarId;
}

// 5. 测试语音服务
async function testVoiceService() {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error('语音服务连接超时'));
    }, 10000);

    const ws = new WebSocket(config.voiceService.replace('ws://', 'ws://') + '/voice');
    
    ws.on('open', () => {
      clearTimeout(timeout);
      log.info('语音服务WebSocket连接成功');
      
      // 发送测试消息
      ws.send(JSON.stringify({
        type: 'synthesize',
        text: '这是一个语音合成测试',
        voice: 'zh-CN-XiaoxiaoNeural',
        format: 'wav',
      }));
    });
    
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        if (message.type === 'synthesis_result') {
          log.info('语音合成测试成功');
          ws.close();
          resolve();
        }
      } catch (error) {
        log.warning('收到非JSON消息');
      }
    });
    
    ws.on('error', (error) => {
      clearTimeout(timeout);
      reject(new Error(`WebSocket错误: ${error.message}`));
    });
    
    ws.on('close', () => {
      clearTimeout(timeout);
    });
  });
}

// 6. 测试完整RAG应用流程
async function testCompleteRAGFlow(knowledgeBaseId, avatarId) {
  // 创建RAG应用
  await runTest('创建RAG应用', async () => {
    const response = await axios.post(`${config.apiGateway}/api/rag-applications`, {
      name: '测试RAG应用',
      description: '完整的RAG应用测试',
      knowledgeBaseId,
      avatarId,
      config: {
        maxConcurrentSessions: 5,
        sessionTimeout: 1800,
        welcomeMessage: '您好，我是测试助手',
      },
    });
    
    if (response.status !== 201) {
      throw new Error(`创建RAG应用失败，状态码: ${response.status}`);
    }
    
    log.info(`RAG应用ID: ${response.data.id}`);
  });

  // 测试应用启动
  await runTest('启动RAG应用', async () => {
    const response = await axios.post(`${config.apiGateway}/api/rag-applications/${response.data.id}/start`);
    
    if (response.status !== 200) {
      throw new Error(`启动应用失败，状态码: ${response.status}`);
    }
  });
}

// 7. 性能测试
async function testPerformance() {
  await runTest('并发对话测试', async () => {
    const concurrentRequests = 5;
    const promises = [];
    
    for (let i = 0; i < concurrentRequests; i++) {
      const promise = axios.post(`${config.ragService}/api/sessions`, {
        knowledgeBaseId: 'test-kb',
        userId: `test-user-${i}`,
      }).then(sessionResponse => {
        return axios.post(`${config.ragService}/api/sessions/${sessionResponse.data.sessionId}/messages`, {
          message: `并发测试消息 ${i}`,
        });
      });
      
      promises.push(promise);
    }
    
    const results = await Promise.allSettled(promises);
    const successful = results.filter(r => r.status === 'fulfilled').length;
    
    if (successful < concurrentRequests * 0.8) {
      throw new Error(`并发测试失败率过高: ${concurrentRequests - successful}/${concurrentRequests}`);
    }
    
    log.info(`并发测试成功: ${successful}/${concurrentRequests}`);
  });
}

// 主测试函数
async function runAllTests() {
  log.info('开始RAG应用系统测试...');
  log.info('='.repeat(50));
  
  try {
    // 1. 服务健康检查
    log.info('1. 服务健康检查');
    await testServiceHealth();
    
    // 2. 知识库功能测试
    log.info('2. 知识库功能测试');
    const knowledgeBaseId = await testKnowledgeBase();
    
    // 3. RAG对话功能测试
    log.info('3. RAG对话功能测试');
    const sessionId = await testRAGDialogue(knowledgeBaseId);
    
    // 4. 数字人功能测试
    log.info('4. 数字人功能测试');
    const avatarId = await testAvatarService();
    
    // 5. 语音服务测试
    log.info('5. 语音服务测试');
    await runTest('语音服务连接测试', testVoiceService);
    
    // 6. 完整RAG应用流程测试
    log.info('6. 完整RAG应用流程测试');
    await testCompleteRAGFlow(knowledgeBaseId, avatarId);
    
    // 7. 性能测试
    log.info('7. 性能测试');
    await testPerformance();
    
  } catch (error) {
    log.error(`测试过程中发生错误: ${error.message}`);
  }
  
  // 输出测试结果
  log.info('='.repeat(50));
  log.info('测试结果汇总:');
  log.info(`总测试数: ${testResults.total}`);
  log.success(`通过: ${testResults.passed}`);
  log.error(`失败: ${testResults.failed}`);
  log.info(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(2)}%`);
  
  // 详细结果
  console.log('\n详细测试结果:');
  testResults.details.forEach(test => {
    const status = test.status === 'PASSED' ? '✅' : '❌';
    console.log(`${status} ${test.name}`);
    if (test.error) {
      console.log(`   错误: ${test.error}`);
    }
  });
  
  // 退出码
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// 处理命令行参数
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
DL引擎RAG应用系统测试脚本

用法: node test-rag-system.js [选项]

选项:
  --help, -h     显示帮助信息
  --timeout <ms> 设置测试超时时间 (默认: 30000ms)

示例:
  node test-rag-system.js
  node test-rag-system.js --timeout 60000
  `);
  process.exit(0);
}

// 设置超时
const timeoutIndex = args.indexOf('--timeout');
if (timeoutIndex !== -1 && args[timeoutIndex + 1]) {
  config.testTimeout = parseInt(args[timeoutIndex + 1]);
}

// 运行测试
runAllTests().catch(error => {
  log.error(`测试运行失败: ${error.message}`);
  process.exit(1);
});
