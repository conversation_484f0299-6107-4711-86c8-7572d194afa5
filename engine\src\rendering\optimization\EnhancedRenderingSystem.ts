/**
 * 增强的渲染系统
 * 专为支持100+并发用户优化的高性能渲染系统
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import { Camera } from '../Camera';
import { Scene } from '../../scene/Scene';
import { Renderer } from '../Renderer';
import { PerformanceMonitor, PerformanceMetricType } from '../../utils/PerformanceMonitor';
import { Debug } from '../../utils/Debug';

// 渲染批次接口
interface RenderBatch {
  id: string;
  geometry: THREE.BufferGeometry;
  material: THREE.Material;
  instances: THREE.Matrix4[];
  count: number;
  visible: boolean;
  lastUpdate: number;
}

// 渲染统计信息
interface RenderStats {
  drawCalls: number;
  triangles: number;
  vertices: number;
  textures: number;
  geometries: number;
  materials: number;
  frameTime: number;
  fps: number;
}

// 渲染配置
interface EnhancedRenderingConfig {
  // 批处理设置
  enableBatching: boolean;
  maxBatchSize: number;
  batchUpdateInterval: number;
  
  // 实例化渲染
  enableInstancing: boolean;
  maxInstancesPerBatch: number;
  
  // 视锥体剔除
  enableFrustumCulling: boolean;
  cullingUpdateInterval: number;
  
  // 遮挡剔除
  enableOcclusionCulling: boolean;
  occlusionQueryEnabled: boolean;
  
  // LOD系统
  enableLOD: boolean;
  lodDistances: number[];
  
  // 性能监控
  enablePerformanceMonitoring: boolean;
  performanceUpdateInterval: number;
  
  // 自适应质量
  enableAdaptiveQuality: boolean;
  targetFPS: number;
  qualityLevels: string[];
  
  // 多线程渲染
  enableWorkerRendering: boolean;
  workerCount: number;
}

/**
 * 增强的渲染系统类
 */
export class EnhancedRenderingSystem extends System {
  private renderer: Renderer;
  private activeCamera: Camera | null = null;
  private activeScene: Scene | null = null;
  
  // 渲染批次管理
  private renderBatches: Map<string, RenderBatch> = new Map();
  private batchUpdateCounter = 0;
  
  // 实例化渲染
  private instancedMeshes: Map<string, THREE.InstancedMesh> = new Map();
  private instanceMatrices: Map<string, THREE.Matrix4[]> = new Map();
  
  // 视锥体剔除
  private frustum = new THREE.Frustum();
  private cameraMatrix = new THREE.Matrix4();
  private cullingUpdateCounter = 0;
  
  // 性能监控
  private performanceMonitor: PerformanceMonitor;
  private renderStats: RenderStats = {
    drawCalls: 0,
    triangles: 0,
    vertices: 0,
    textures: 0,
    geometries: 0,
    materials: 0,
    frameTime: 0,
    fps: 0
  };
  
  // 配置
  private config: EnhancedRenderingConfig = {
    enableBatching: true,
    maxBatchSize: 1000,
    batchUpdateInterval: 5,
    
    enableInstancing: true,
    maxInstancesPerBatch: 1000,
    
    enableFrustumCulling: true,
    cullingUpdateInterval: 3,
    
    enableOcclusionCulling: false,
    occlusionQueryEnabled: false,
    
    enableLOD: true,
    lodDistances: [50, 100, 200, 500],
    
    enablePerformanceMonitoring: true,
    performanceUpdateInterval: 60,
    
    enableAdaptiveQuality: true,
    targetFPS: 60,
    qualityLevels: ['low', 'medium', 'high', 'ultra'],
    
    enableWorkerRendering: false,
    workerCount: 2
  };
  
  // 自适应质量
  private currentQualityLevel = 2; // medium
  private frameTimeHistory: number[] = [];
  private qualityAdjustmentCooldown = 0;
  
  constructor(renderer: Renderer, config?: Partial<EnhancedRenderingConfig>) {
    super();
    this.renderer = renderer;
    
    if (config) {
      this.config = { ...this.config, ...config };
    }
    
    // 初始化性能监控
    this.performanceMonitor = PerformanceMonitor.getInstance();
    
    this.initialize();
  }
  
  /**
   * 初始化系统
   */
  public initialize(): void {
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.start();
    }
    
    Debug.log('EnhancedRenderingSystem', '增强渲染系统已初始化');
  }
  
  /**
   * 设置活跃相机
   */
  public setActiveCamera(camera: Camera): void {
    this.activeCamera = camera;
  }
  
  /**
   * 设置活跃场景
   */
  public setActiveScene(scene: Scene): void {
    this.activeScene = scene;
  }
  
  /**
   * 更新系统
   */
  public update(_deltaTime: number): void {
    if (!this.activeCamera || !this.activeScene) {
      return;
    }
    
    const startTime = performance.now();
    
    // 更新视锥体剔除
    if (this.config.enableFrustumCulling) {
      this.updateFrustumCulling();
    }
    
    // 更新渲染批次
    if (this.config.enableBatching) {
      this.updateRenderBatches();
    }
    
    // 更新实例化渲染
    if (this.config.enableInstancing) {
      this.updateInstancedRendering();
    }
    
    // 执行渲染
    this.performRender();
    
    // 更新性能统计
    const frameTime = performance.now() - startTime;
    this.updateRenderStats(frameTime);
    
    // 自适应质量调整
    if (this.config.enableAdaptiveQuality) {
      this.updateAdaptiveQuality(frameTime);
    }
    
    // 性能监控
    if (this.config.enablePerformanceMonitoring) {
      this.updatePerformanceMonitoring();
    }
  }
  
  /**
   * 更新视锥体剔除
   */
  private updateFrustumCulling(): void {
    this.cullingUpdateCounter++;
    
    if (this.cullingUpdateCounter % this.config.cullingUpdateInterval !== 0) {
      return;
    }
    
    if (!this.activeCamera) return;
    
    // 更新视锥体
    this.cameraMatrix.multiplyMatrices(
      this.activeCamera.getThreeCamera().projectionMatrix,
      this.activeCamera.getThreeCamera().matrixWorldInverse
    );
    this.frustum.setFromProjectionMatrix(this.cameraMatrix);
    
    // 对所有渲染批次进行视锥体剔除
    for (const batch of this.renderBatches.values()) {
      batch.visible = this.isBatchVisible(batch);
    }
  }
  
  /**
   * 检查批次是否可见
   */
  private isBatchVisible(batch: RenderBatch): boolean {
    // 简化的包围盒检测
    const boundingBox = batch.geometry.boundingBox;
    if (!boundingBox) {
      batch.geometry.computeBoundingBox();
      return true; // 如果无法计算包围盒，则认为可见
    }
    
    return this.frustum.intersectsBox(boundingBox);
  }
  
  /**
   * 更新渲染批次
   */
  private updateRenderBatches(): void {
    this.batchUpdateCounter++;
    
    if (this.batchUpdateCounter % this.config.batchUpdateInterval !== 0) {
      return;
    }
    
    // 这里可以添加批次合并和优化逻辑
    // 例如：合并相同材质和几何体的对象
  }
  
  /**
   * 更新实例化渲染
   */
  private updateInstancedRendering(): void {
    for (const [key, matrices] of this.instanceMatrices) {
      const instancedMesh = this.instancedMeshes.get(key);
      if (instancedMesh && matrices.length > 0) {
        // 更新实例矩阵
        for (let i = 0; i < matrices.length; i++) {
          instancedMesh.setMatrixAt(i, matrices[i]);
        }
        instancedMesh.instanceMatrix.needsUpdate = true;
        instancedMesh.count = matrices.length;
      }
    }
  }
  
  /**
   * 执行渲染
   */
  private performRender(): void {
    if (!this.activeCamera || !this.activeScene) return;
    
    // 重置渲染统计
    this.renderStats.drawCalls = 0;
    this.renderStats.triangles = 0;
    this.renderStats.vertices = 0;
    
    // 渲染场景
    this.renderer.render(this.activeScene, this.activeCamera);
    
    // 渲染实例化对象
    this.renderInstancedObjects();
  }
  
  /**
   * 渲染实例化对象
   */
  private renderInstancedObjects(): void {
    const threeRenderer = this.renderer.getThreeRenderer();
    const threeScene = this.activeScene?.getThreeScene();
    const threeCamera = this.activeCamera?.getThreeCamera();
    
    if (!threeRenderer || !threeScene || !threeCamera) return;
    
    for (const instancedMesh of this.instancedMeshes.values()) {
      if (instancedMesh.count > 0) {
        threeRenderer.render(instancedMesh as any, threeCamera);
        this.renderStats.drawCalls++;
      }
    }
  }
  
  /**
   * 更新渲染统计
   */
  private updateRenderStats(frameTime: number): void {
    this.renderStats.frameTime = frameTime;
    this.renderStats.fps = 1000 / frameTime;
    
    // 更新帧时间历史
    this.frameTimeHistory.push(frameTime);
    if (this.frameTimeHistory.length > 60) {
      this.frameTimeHistory.shift();
    }
  }
  
  /**
   * 更新自适应质量
   */
  private updateAdaptiveQuality(_frameTime: number): void {
    if (this.qualityAdjustmentCooldown > 0) {
      this.qualityAdjustmentCooldown--;
      return;
    }
    
    const targetFrameTime = 1000 / this.config.targetFPS;
    const avgFrameTime = this.frameTimeHistory.reduce((a, b) => a + b, 0) / this.frameTimeHistory.length;
    
    if (avgFrameTime > targetFrameTime * 1.2 && this.currentQualityLevel > 0) {
      // 降低质量
      this.currentQualityLevel--;
      this.applyQualityLevel(this.currentQualityLevel);
      this.qualityAdjustmentCooldown = 60; // 1秒冷却
    } else if (avgFrameTime < targetFrameTime * 0.8 && this.currentQualityLevel < this.config.qualityLevels.length - 1) {
      // 提高质量
      this.currentQualityLevel++;
      this.applyQualityLevel(this.currentQualityLevel);
      this.qualityAdjustmentCooldown = 60; // 1秒冷却
    }
  }
  
  /**
   * 应用质量级别
   */
  private applyQualityLevel(level: number): void {
    const qualityName = this.config.qualityLevels[level];
    
    switch (qualityName) {
      case 'low':
        this.config.maxBatchSize = 500;
        this.config.maxInstancesPerBatch = 500;
        break;
      case 'medium':
        this.config.maxBatchSize = 1000;
        this.config.maxInstancesPerBatch = 1000;
        break;
      case 'high':
        this.config.maxBatchSize = 2000;
        this.config.maxInstancesPerBatch = 2000;
        break;
      case 'ultra':
        this.config.maxBatchSize = 5000;
        this.config.maxInstancesPerBatch = 5000;
        break;
    }
    
    Debug.log('EnhancedRenderingSystem', `质量级别调整为: ${qualityName}`);
  }
  
  /**
   * 更新性能监控
   */
  private updatePerformanceMonitoring(): void {
    this.performanceMonitor.updateMetric(PerformanceMetricType.FPS, this.renderStats.fps);
    this.performanceMonitor.updateMetric(PerformanceMetricType.FRAME_TIME, this.renderStats.frameTime);
    this.performanceMonitor.updateMetric(PerformanceMetricType.DRAW_CALLS, this.renderStats.drawCalls);
  }
  
  /**
   * 获取渲染统计
   */
  public getRenderStats(): RenderStats {
    return { ...this.renderStats };
  }
  
  /**
   * 获取当前配置
   */
  public getConfig(): EnhancedRenderingConfig {
    return { ...this.config };
  }
  
  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<EnhancedRenderingConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}
