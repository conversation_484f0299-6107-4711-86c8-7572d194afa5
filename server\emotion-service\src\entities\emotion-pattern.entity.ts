/**
 * 情感模式实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('emotion_patterns')
@Index(['userId', 'confidence'])
export class EmotionPattern {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  userId: string;

  @Column({ type: 'varchar', length: 255 })
  patternName: string;

  @Column({ type: 'json' })
  emotionSequence: string[];

  @Column({ type: 'json' })
  timeIntervals: number[];

  @Column({ type: 'json' })
  intensityPattern: number[];

  @Column({ type: 'int' })
  frequency: number;

  @Column({ type: 'decimal', precision: 5, scale: 3 })
  confidence: number;

  @Column({ type: 'timestamp', nullable: true })
  lastOccurrence: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
