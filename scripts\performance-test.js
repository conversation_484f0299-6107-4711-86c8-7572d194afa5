#!/usr/bin/env node

/**
 * UI组件性能测试脚本
 * 测试大量UI元素渲染性能、拖拽操作流畅性等
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${title}`, 'bright');
  log('='.repeat(60), 'cyan');
}

// 性能测试配置
const performanceTests = [
  {
    name: '大量UI元素渲染性能测试',
    description: '测试渲染100个UI元素的性能',
    test: async (page) => {
      // 创建100个UI元素
      const startTime = Date.now();
      
      await page.evaluate(() => {
        const container = document.createElement('div');
        container.id = 'performance-test-container';
        document.body.appendChild(container);
        
        for (let i = 0; i < 100; i++) {
          const element = document.createElement('div');
          element.className = 'ui-element';
          element.style.position = 'absolute';
          element.style.left = `${Math.random() * 800}px`;
          element.style.top = `${Math.random() * 600}px`;
          element.style.width = '50px';
          element.style.height = '30px';
          element.style.backgroundColor = '#1890ff';
          element.style.border = '1px solid #d9d9d9';
          element.textContent = `Element ${i}`;
          container.appendChild(element);
        }
      });
      
      const endTime = Date.now();
      const renderTime = endTime - startTime;
      
      // 测试重绘性能
      const repaintStartTime = Date.now();
      await page.evaluate(() => {
        const elements = document.querySelectorAll('.ui-element');
        elements.forEach((element, index) => {
          element.style.backgroundColor = index % 2 === 0 ? '#52c41a' : '#ff4d4f';
        });
      });
      const repaintEndTime = Date.now();
      const repaintTime = repaintEndTime - repaintStartTime;
      
      return {
        renderTime,
        repaintTime,
        elementCount: 100,
        passed: renderTime < 1000 && repaintTime < 500
      };
    }
  },
  {
    name: '拖拽操作流畅性测试',
    description: '测试拖拽操作的响应时间和流畅性',
    test: async (page) => {
      // 创建可拖拽元素
      await page.evaluate(() => {
        const element = document.createElement('div');
        element.id = 'draggable-element';
        element.style.position = 'absolute';
        element.style.left = '100px';
        element.style.top = '100px';
        element.style.width = '100px';
        element.style.height = '100px';
        element.style.backgroundColor = '#1890ff';
        element.style.cursor = 'move';
        element.textContent = 'Drag me';
        document.body.appendChild(element);
        
        let isDragging = false;
        let startTime = 0;
        let frameCount = 0;
        
        element.addEventListener('mousedown', (e) => {
          isDragging = true;
          startTime = performance.now();
          frameCount = 0;
        });
        
        document.addEventListener('mousemove', (e) => {
          if (isDragging) {
            frameCount++;
            element.style.left = e.clientX - 50 + 'px';
            element.style.top = e.clientY - 50 + 'px';
          }
        });
        
        document.addEventListener('mouseup', () => {
          if (isDragging) {
            const endTime = performance.now();
            const duration = endTime - startTime;
            const fps = frameCount / (duration / 1000);
            
            window.dragTestResult = {
              duration,
              frameCount,
              fps
            };
          }
          isDragging = false;
        });
      });
      
      // 模拟拖拽操作
      const element = await page.$('#draggable-element');
      const box = await element.boundingBox();
      
      await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2);
      await page.mouse.down();
      
      // 执行拖拽路径
      const steps = 50;
      for (let i = 0; i <= steps; i++) {
        const x = box.x + box.width / 2 + (i * 5);
        const y = box.y + box.height / 2 + Math.sin(i * 0.2) * 50;
        await page.mouse.move(x, y);
        await page.waitForTimeout(16); // 60fps
      }
      
      await page.mouse.up();
      
      // 获取测试结果
      const result = await page.evaluate(() => window.dragTestResult);
      
      return {
        ...result,
        passed: result.fps > 30 // 期望FPS大于30
      };
    }
  },
  {
    name: '主题切换响应速度测试',
    description: '测试主题切换的响应时间',
    test: async (page) => {
      // 创建测试元素
      await page.evaluate(() => {
        const container = document.createElement('div');
        container.id = 'theme-test-container';
        document.body.appendChild(container);
        
        // 创建50个元素用于主题测试
        for (let i = 0; i < 50; i++) {
          const element = document.createElement('div');
          element.className = 'theme-element';
          element.style.padding = '10px';
          element.style.margin = '5px';
          element.style.backgroundColor = 'var(--ui-color-background, #ffffff)';
          element.style.color = 'var(--ui-color-text, #000000)';
          element.style.border = '1px solid var(--ui-color-border, #d9d9d9)';
          element.textContent = `Theme Element ${i}`;
          container.appendChild(element);
        }
        
        // 定义主题切换函数
        window.switchTheme = (theme) => {
          const startTime = performance.now();
          
          if (theme === 'dark') {
            document.documentElement.style.setProperty('--ui-color-background', '#141414');
            document.documentElement.style.setProperty('--ui-color-text', '#ffffff');
            document.documentElement.style.setProperty('--ui-color-border', '#434343');
          } else {
            document.documentElement.style.setProperty('--ui-color-background', '#ffffff');
            document.documentElement.style.setProperty('--ui-color-text', '#000000');
            document.documentElement.style.setProperty('--ui-color-border', '#d9d9d9');
          }
          
          // 强制重绘
          document.body.offsetHeight;
          
          const endTime = performance.now();
          return endTime - startTime;
        };
      });
      
      // 测试主题切换时间
      const lightToDarkTime = await page.evaluate(() => window.switchTheme('dark'));
      await page.waitForTimeout(100);
      const darkToLightTime = await page.evaluate(() => window.switchTheme('light'));
      
      const averageTime = (lightToDarkTime + darkToLightTime) / 2;
      
      return {
        lightToDarkTime,
        darkToLightTime,
        averageTime,
        passed: averageTime < 100 // 期望切换时间小于100ms
      };
    }
  },
  {
    name: '内存使用测试',
    description: '测试UI组件的内存使用情况',
    test: async (page) => {
      // 获取初始内存使用
      const initialMemory = await page.evaluate(() => {
        if (performance.memory) {
          return {
            usedJSHeapSize: performance.memory.usedJSHeapSize,
            totalJSHeapSize: performance.memory.totalJSHeapSize,
            jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
          };
        }
        return null;
      });
      
      // 创建大量UI元素
      await page.evaluate(() => {
        const container = document.createElement('div');
        container.id = 'memory-test-container';
        document.body.appendChild(container);
        
        window.testElements = [];
        
        for (let i = 0; i < 1000; i++) {
          const element = document.createElement('div');
          element.className = 'memory-test-element';
          element.style.position = 'absolute';
          element.style.left = `${Math.random() * 1000}px`;
          element.style.top = `${Math.random() * 1000}px`;
          element.style.width = '20px';
          element.style.height = '20px';
          element.style.backgroundColor = `hsl(${Math.random() * 360}, 50%, 50%)`;
          element.textContent = i.toString();
          
          // 添加事件监听器
          element.addEventListener('click', () => {
            element.style.backgroundColor = 'red';
          });
          
          container.appendChild(element);
          window.testElements.push(element);
        }
      });
      
      // 等待一段时间让内存稳定
      await page.waitForTimeout(1000);
      
      // 获取创建元素后的内存使用
      const afterCreateMemory = await page.evaluate(() => {
        if (performance.memory) {
          return {
            usedJSHeapSize: performance.memory.usedJSHeapSize,
            totalJSHeapSize: performance.memory.totalJSHeapSize,
            jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
          };
        }
        return null;
      });
      
      // 清理元素
      await page.evaluate(() => {
        const container = document.getElementById('memory-test-container');
        if (container) {
          container.remove();
        }
        window.testElements = null;
        
        // 强制垃圾回收（如果可用）
        if (window.gc) {
          window.gc();
        }
      });
      
      // 等待垃圾回收
      await page.waitForTimeout(2000);
      
      // 获取清理后的内存使用
      const afterCleanupMemory = await page.evaluate(() => {
        if (performance.memory) {
          return {
            usedJSHeapSize: performance.memory.usedJSHeapSize,
            totalJSHeapSize: performance.memory.totalJSHeapSize,
            jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
          };
        }
        return null;
      });
      
      if (!initialMemory || !afterCreateMemory || !afterCleanupMemory) {
        return {
          error: '浏览器不支持内存监控',
          passed: true
        };
      }
      
      const memoryIncrease = afterCreateMemory.usedJSHeapSize - initialMemory.usedJSHeapSize;
      const memoryLeakage = afterCleanupMemory.usedJSHeapSize - initialMemory.usedJSHeapSize;
      const leakagePercentage = (memoryLeakage / memoryIncrease) * 100;
      
      return {
        initialMemory: Math.round(initialMemory.usedJSHeapSize / 1024 / 1024 * 100) / 100,
        afterCreateMemory: Math.round(afterCreateMemory.usedJSHeapSize / 1024 / 1024 * 100) / 100,
        afterCleanupMemory: Math.round(afterCleanupMemory.usedJSHeapSize / 1024 / 1024 * 100) / 100,
        memoryIncrease: Math.round(memoryIncrease / 1024 / 1024 * 100) / 100,
        memoryLeakage: Math.round(memoryLeakage / 1024 / 1024 * 100) / 100,
        leakagePercentage: Math.round(leakagePercentage * 100) / 100,
        passed: leakagePercentage < 10 // 期望内存泄漏小于10%
      };
    }
  }
];

// 运行性能测试
async function runPerformanceTests() {
  logSection('UI组件性能测试');
  
  let browser;
  try {
    // 启动浏览器
    log('启动浏览器...', 'blue');
    browser = await puppeteer.launch({
      headless: false, // 设置为false以便观察测试过程
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--allow-running-insecure-content',
        '--js-flags=--expose-gc' // 启用垃圾回收
      ]
    });
    
    const page = await browser.newPage();
    
    // 设置视口
    await page.setViewport({ width: 1200, height: 800 });
    
    // 创建基础HTML页面
    await page.setContent(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>UI Performance Test</title>
        <style>
          body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
          }
          .ui-element {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            border-radius: 4px;
          }
        </style>
      </head>
      <body>
        <h1>UI Performance Test</h1>
      </body>
      </html>
    `);
    
    const results = [];
    
    // 运行每个性能测试
    for (const test of performanceTests) {
      log(`\n运行测试: ${test.name}`, 'yellow');
      log(`描述: ${test.description}`, 'blue');
      
      try {
        const startTime = Date.now();
        const result = await test.test(page);
        const endTime = Date.now();
        
        result.testName = test.name;
        result.testDuration = endTime - startTime;
        
        results.push(result);
        
        const status = result.passed ? '✅ 通过' : '❌ 失败';
        const color = result.passed ? 'green' : 'red';
        log(`结果: ${status}`, color);
        
        // 显示详细结果
        Object.keys(result).forEach(key => {
          if (key !== 'testName' && key !== 'passed') {
            log(`  ${key}: ${result[key]}`, 'cyan');
          }
        });
        
        // 清理页面
        await page.evaluate(() => {
          document.body.innerHTML = '<h1>UI Performance Test</h1>';
        });
        
      } catch (error) {
        log(`测试失败: ${error.message}`, 'red');
        results.push({
          testName: test.name,
          passed: false,
          error: error.message
        });
      }
    }
    
    // 生成报告
    generatePerformanceReport(results);
    
  } catch (error) {
    log(`性能测试失败: ${error.message}`, 'red');
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// 生成性能测试报告
function generatePerformanceReport(results) {
  logSection('性能测试报告');
  
  const totalTests = results.length;
  const passedTests = results.filter(r => r.passed).length;
  const failedTests = totalTests - passedTests;
  
  log(`总测试数: ${totalTests}`, 'bright');
  log(`通过: ${passedTests}`, 'green');
  log(`失败: ${failedTests}`, failedTests > 0 ? 'red' : 'green');
  log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`, 
      passedTests === totalTests ? 'green' : 'yellow');
  
  // 详细结果
  log('\n详细结果:', 'bright');
  results.forEach((result, index) => {
    const status = result.passed ? '✅' : '❌';
    const color = result.passed ? 'green' : 'red';
    log(`${index + 1}. ${status} ${result.testName}`, color);
    
    if (result.error) {
      log(`   错误: ${result.error}`, 'red');
    }
  });
  
  // 保存报告到文件
  const reportPath = path.join(__dirname, '../reports/performance-test-report.json');
  const reportDir = path.dirname(reportPath);
  
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTests,
      passedTests,
      failedTests,
      successRate: ((passedTests / totalTests) * 100).toFixed(1)
    },
    results
  };
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  log(`\n报告已保存到: ${reportPath}`, 'cyan');
  
  return passedTests === totalTests;
}

// 主函数
async function main() {
  const startTime = Date.now();
  
  log('🚀 开始UI组件性能测试', 'bright');
  
  try {
    await runPerformanceTests();
  } catch (error) {
    log(`测试过程中发生错误: ${error.message}`, 'red');
    process.exit(1);
  }
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(1);
  
  logSection('测试完成');
  log(`总耗时: ${duration}秒`, 'bright');
  log('🎉 性能测试完成！', 'green');
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  log('未处理的Promise拒绝:', 'red');
  log(reason, 'red');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  log('未捕获的异常:', 'red');
  log(error.message, 'red');
  process.exit(1);
});

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { runPerformanceTests, generatePerformanceReport };
