/**
 * 日志记录器
 * 提供统一的日志记录功能
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

export interface LoggerOptions {
  /** 日志级别 */
  level?: LogLevel;
  /** 是否启用时间戳 */
  enableTimestamp?: boolean;
  /** 是否启用颜色 */
  enableColors?: boolean;
  /** 日志前缀 */
  prefix?: string;
}

/**
 * 日志记录器类
 */
export class Logger {
  private name: string;
  private level: LogLevel;
  private enableTimestamp: boolean;
  private enableColors: boolean;
  private prefix: string;

  // 颜色常量
  private static readonly COLORS = {
    DEBUG: '\x1b[36m', // 青色
    INFO: '\x1b[32m',  // 绿色
    WARN: '\x1b[33m',  // 黄色
    ERROR: '\x1b[31m', // 红色
    RESET: '\x1b[0m'   // 重置
  };

  /**
   * 构造函数
   * @param name 日志记录器名称
   * @param options 选项
   */
  constructor(name: string, options: LoggerOptions = {}) {
    this.name = name;
    this.level = options.level ?? LogLevel.INFO;
    this.enableTimestamp = options.enableTimestamp ?? true;
    this.enableColors = options.enableColors ?? true;
    this.prefix = options.prefix ?? '';
  }

  /**
   * 设置日志级别
   * @param level 日志级别
   */
  public setLevel(level: LogLevel): void {
    this.level = level;
  }

  /**
   * 获取日志级别
   * @returns 日志级别
   */
  public getLevel(): LogLevel {
    return this.level;
  }

  /**
   * 调试日志
   * @param message 消息
   * @param data 数据
   */
  public debug(message: string, data?: any): void {
    if (this.level <= LogLevel.DEBUG) {
      this.log(LogLevel.DEBUG, message, data);
    }
  }

  /**
   * 信息日志
   * @param message 消息
   * @param data 数据
   */
  public info(message: string, data?: any): void {
    if (this.level <= LogLevel.INFO) {
      this.log(LogLevel.INFO, message, data);
    }
  }

  /**
   * 警告日志
   * @param message 消息
   * @param data 数据
   */
  public warn(message: string, data?: any): void {
    if (this.level <= LogLevel.WARN) {
      this.log(LogLevel.WARN, message, data);
    }
  }

  /**
   * 错误日志
   * @param message 消息
   * @param data 数据
   */
  public error(message: string, data?: any): void {
    if (this.level <= LogLevel.ERROR) {
      this.log(LogLevel.ERROR, message, data);
    }
  }

  /**
   * 记录日志
   * @param level 日志级别
   * @param message 消息
   * @param data 数据
   */
  private log(level: LogLevel, message: string, data?: any): void {
    const timestamp = this.enableTimestamp ? this.getTimestamp() : '';
    const levelStr = LogLevel[level];
    const nameStr = `[${this.name}]`;
    const prefixStr = this.prefix ? `[${this.prefix}]` : '';
    
    let logMessage = `${timestamp}${prefixStr}${nameStr}[${levelStr}] ${message}`;
    
    // 添加颜色
    if (this.enableColors && typeof window === 'undefined') { // 仅在 Node.js 环境中使用颜色
      const color = Logger.COLORS[levelStr as keyof typeof Logger.COLORS] || '';
      logMessage = `${color}${logMessage}${Logger.COLORS.RESET}`;
    }

    // 选择合适的控制台方法
    const consoleMethod = this.getConsoleMethod(level);
    
    if (data !== undefined) {
      consoleMethod(logMessage, data);
    } else {
      consoleMethod(logMessage);
    }
  }

  /**
   * 获取时间戳
   * @returns 时间戳字符串
   */
  private getTimestamp(): string {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    const milliseconds = now.getMilliseconds().toString().padStart(3, '0');
    return `[${hours}:${minutes}:${seconds}.${milliseconds}] `;
  }

  /**
   * 获取控制台方法
   * @param level 日志级别
   * @returns 控制台方法
   */
  private getConsoleMethod(level: LogLevel): (...args: any[]) => void {
    switch (level) {
      case LogLevel.DEBUG:
        return console.debug || console.log;
      case LogLevel.INFO:
        return console.info || console.log;
      case LogLevel.WARN:
        return console.warn || console.log;
      case LogLevel.ERROR:
        return console.error || console.log;
      default:
        return console.log;
    }
  }

  /**
   * 创建子日志记录器
   * @param name 子记录器名称
   * @param options 选项
   * @returns 子日志记录器
   */
  public createChild(name: string, options: Partial<LoggerOptions> = {}): Logger {
    return new Logger(`${this.name}.${name}`, {
      level: this.level,
      enableTimestamp: this.enableTimestamp,
      enableColors: this.enableColors,
      prefix: this.prefix,
      ...options
    });
  }
}

/**
 * 默认日志记录器
 */
export const defaultLogger = new Logger('DL-Engine');

/**
 * 创建日志记录器
 * @param name 名称
 * @param options 选项
 * @returns 日志记录器实例
 */
export function createLogger(name: string, options?: LoggerOptions): Logger {
  return new Logger(name, options);
}
