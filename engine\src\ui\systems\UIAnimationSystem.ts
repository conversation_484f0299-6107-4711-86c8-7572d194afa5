/**
 * UIAnimationSystem.ts
 *
 * UI动画系统，管理UI元素的动画效果
 */

import { System } from '../../core/System';
import type { World } from '../../core/World';
import type { Entity } from '../../core/Entity';
import { Vector2, Vector3, Color } from 'three';
import { UIComponent } from '../components/UIComponent';
import { UIAnimationComponent, UIAnimation, UIEasing, AnimationTargetProperty } from '../components/UIAnimationComponent';
import { UISystem } from './UISystem';
import { UIAnimationType } from '../interfaces/IUIElement';

/**
 * UI动画系统配置
 */
export interface UIAnimationSystemConfig {
  // 是否启用调试模式
  debug?: boolean;

  // 默认动画持续时间（毫秒）
  defaultDuration?: number;

  // 默认动画延迟（毫秒）
  defaultDelay?: number;

  // 默认动画缓动函数
  defaultEasing?: (t: number) => number;
}

/**
 * UI动画系统
 * 管理UI元素的动画效果
 */
export class UIAnimationSystem extends System {
  // UI系统引用
  private uiSystem: UISystem;

  // 配置
  private config: UIAnimationSystemConfig;

  // 动画组件列表
  private animationComponents: Map<Entity, UIAnimationComponent> = new Map();

  /**
   * 构造函数
   * @param world 世界实例
   * @param uiSystem UI系统实例
   * @param config UI动画系统配置
   */
  constructor(world: World, uiSystem: UISystem, config: UIAnimationSystemConfig = {}) {
    // 调用基类构造函数，传入优先级（默认为0）
    super(0);

    // 设置世界引用（使用基类方法）
    this.setWorld(world);

    this.uiSystem = uiSystem;

    this.config = {
      debug: config.debug || false,
      defaultDuration: config.defaultDuration || 500,
      defaultDelay: config.defaultDelay || 0,
      defaultEasing: config.defaultEasing || UIEasing.cubicInOut
    };
  }

  /**
   * 注册动画组件
   * @param entity 实体
   * @param component 动画组件
   */
  registerAnimationComponent(entity: Entity, component: UIAnimationComponent): void {
    this.animationComponents.set(entity, component);
    this.uiSystem.registerUIAnimationComponent(entity, component);
  }

  /**
   * 注销动画组件
   * @param entity 实体
   */
  unregisterAnimationComponent(entity: Entity): void {
    this.animationComponents.delete(entity);
    this.uiSystem.unregisterUIAnimationComponent(entity);
  }

  /**
   * 获取或创建动画组件
   * @param entity 实体
   * @returns 动画组件
   */
  getOrCreateAnimationComponent(entity: Entity): UIAnimationComponent {
    let component = this.animationComponents.get(entity);

    if (!component) {
      component = new UIAnimationComponent();
      this.registerAnimationComponent(entity, component);
    }

    return component;
  }

  /**
   * 创建淡入动画
   * @param entity 实体
   * @param uiComponent UI组件
   * @param duration 持续时间（毫秒）
   * @param delay 延迟（毫秒）
   * @param easing 缓动函数
   * @returns 创建的动画
   */
  createFadeInAnimation(
    entity: Entity,
    uiComponent: UIComponent,
    duration: number = this.config.defaultDuration!,
    delay: number = this.config.defaultDelay!,
    easing: (t: number) => number = this.config.defaultEasing!
  ): UIAnimation {
    const animationComponent = this.getOrCreateAnimationComponent(entity);

    const animation = animationComponent.createAnimation(
      UIAnimationType.FADE,
      uiComponent,
      'opacity',
      0,
      1,
      duration,
      {
        delay,
        easing,
        onComplete: () => {
          if (this.config.debug) {
            console.log(`Fade in animation completed for entity ${entity.id}`);
          }
        }
      }
    );

    animation.start();

    return animation;
  }

  /**
   * 创建淡出动画
   * @param entity 实体
   * @param uiComponent UI组件
   * @param duration 持续时间（毫秒）
   * @param delay 延迟（毫秒）
   * @param easing 缓动函数
   * @returns 创建的动画
   */
  createFadeOutAnimation(
    entity: Entity,
    uiComponent: UIComponent,
    duration: number = this.config.defaultDuration!,
    delay: number = this.config.defaultDelay!,
    easing: (t: number) => number = this.config.defaultEasing!
  ): UIAnimation {
    const animationComponent = this.getOrCreateAnimationComponent(entity);

    const animation = animationComponent.createAnimation(
      UIAnimationType.FADE,
      uiComponent,
      'opacity',
      uiComponent.opacity,
      0,
      duration,
      {
        delay,
        easing,
        onComplete: () => {
          if (this.config.debug) {
            console.log(`Fade out animation completed for entity ${entity.id}`);
          }
        }
      }
    );

    animation.start();

    return animation;
  }

  /**
   * 创建移动动画
   * @param entity 实体
   * @param uiComponent UI组件
   * @param to 目标位置
   * @param duration 持续时间（毫秒）
   * @param delay 延迟（毫秒）
   * @param easing 缓动函数
   * @returns 创建的动画
   */
  createMoveAnimation(
    entity: Entity,
    uiComponent: UIComponent,
    to: Vector2 | Vector3,
    duration: number = this.config.defaultDuration!,
    delay: number = this.config.defaultDelay!,
    easing: (t: number) => number = this.config.defaultEasing!
  ): UIAnimation {
    const animationComponent = this.getOrCreateAnimationComponent(entity);

    const animation = animationComponent.createAnimation(
      UIAnimationType.MOVE,
      uiComponent,
      'position',
      uiComponent.position.clone(),
      to,
      duration,
      {
        delay,
        easing,
        onComplete: () => {
          if (this.config.debug) {
            console.log(`Move animation completed for entity ${entity.id}`);
          }
        }
      }
    );

    animation.start();

    return animation;
  }

  /**
   * 创建缩放动画
   * @param entity 实体
   * @param uiComponent UI组件
   * @param to 目标尺寸
   * @param duration 持续时间（毫秒）
   * @param delay 延迟（毫秒）
   * @param easing 缓动函数
   * @returns 创建的动画
   */
  createScaleAnimation(
    entity: Entity,
    uiComponent: UIComponent,
    to: Vector2,
    duration: number = this.config.defaultDuration!,
    delay: number = this.config.defaultDelay!,
    easing: (t: number) => number = this.config.defaultEasing!
  ): UIAnimation {
    const animationComponent = this.getOrCreateAnimationComponent(entity);

    const animation = animationComponent.createAnimation(
      UIAnimationType.SCALE,
      uiComponent,
      'size',
      uiComponent.size.clone(),
      to,
      duration,
      {
        delay,
        easing,
        onComplete: () => {
          if (this.config.debug) {
            console.log(`Scale animation completed for entity ${entity.id}`);
          }
        }
      }
    );

    animation.start();

    return animation;
  }

  /**
   * 创建颜色动画
   * @param entity 实体
   * @param uiComponent UI组件
   * @param property 目标属性
   * @param to 目标颜色
   * @param duration 持续时间（毫秒）
   * @param delay 延迟（毫秒）
   * @param easing 缓动函数
   * @returns 创建的动画
   */
  createColorAnimation(
    entity: Entity,
    uiComponent: UIComponent,
    property: 'backgroundColor' | 'borderColor' | 'fontColor',
    to: string | Color,
    duration: number = this.config.defaultDuration!,
    delay: number = this.config.defaultDelay!,
    easing: (t: number) => number = this.config.defaultEasing!
  ): UIAnimation {
    const animationComponent = this.getOrCreateAnimationComponent(entity);

    // 获取当前颜色
    let from: string | Color;
    switch (property) {
      case 'backgroundColor':
        from = uiComponent.backgroundColor || '#000000';
        break;
      case 'borderColor':
        from = uiComponent.borderColor || '#000000';
        break;
      case 'fontColor':
        from = (uiComponent as any).textColor || (uiComponent as any).fontColor || '#000000';
        break;
    }

    const animation = animationComponent.createAnimation(
      UIAnimationType.COLOR,
      uiComponent,
      property as AnimationTargetProperty,
      from,
      to,
      duration,
      {
        delay,
        easing,
        onComplete: () => {
          if (this.config.debug) {
            console.log(`Color animation completed for entity ${entity.id}`);
          }
        }
      }
    );

    animation.start();

    return animation;
  }

  /**
   * 创建序列动画
   * @param _entity 实体 - 未使用，但保留以保持API一致性
   * @param animations 动画列表
   * @returns 动画列表
   */
  createSequenceAnimation(_entity: Entity, animations: UIAnimation[]): UIAnimation[] {
    if (animations.length === 0) return [];

    // 设置动画序列
    for (let i = 0; i < animations.length - 1; i++) {
      const currentAnimation = animations[i];
      const nextAnimation = animations[i + 1];

      currentAnimation.onComplete = () => {
        nextAnimation.start();
      };
    }

    // 启动第一个动画
    animations[0].start();

    return animations;
  }

  /**
   * 创建并行动画
   * @param _entity 实体 - 未使用，但保留以保持API一致性
   * @param animations 动画列表
   * @returns 动画列表
   */
  createParallelAnimation(_entity: Entity, animations: UIAnimation[]): UIAnimation[] {
    // 启动所有动画
    for (const animation of animations) {
      animation.start();
    }

    return animations;
  }

  /**
   * 停止所有动画
   * @param entity 实体
   */
  stopAllAnimations(entity: Entity): void {
    const animationComponent = this.animationComponents.get(entity);
    if (animationComponent) {
      animationComponent.clearAnimations();
    }
  }

  /**
   * 更新系统
   * @param deltaTime 时间增量
   */
  update(deltaTime: number): void {
    // 更新所有动画组件
    for (const [_entity, component] of this.animationComponents) {
      component.update(deltaTime);
    }
  }

  /**
   * 销毁系统
   */
  dispose(): void {
    // 清空动画组件列表
    this.animationComponents.clear();
  }
}
