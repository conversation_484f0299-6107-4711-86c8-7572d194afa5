import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { IoAdapter } from '@nestjs/platform-socket.io';
import * as compression from 'compression';
import * as helmet from 'helmet';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // 配置微服务
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.TCP,
    options: {
      host: configService.get<string>('RAG_DIALOGUE_SERVICE_HOST', 'localhost'),
      port: configService.get<number>('RAG_DIALOGUE_SERVICE_PORT', 3009),
    },
  });

  // 配置WebSocket适配器
  app.useWebSocketAdapter(new IoAdapter(app));

  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  // 启用CORS
  app.enableCors({
    origin: configService.get<string>('CORS_ORIGIN', '*'),
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  // 启用压缩
  app.use(compression());

  // 启用安全头
  app.use(helmet());

  // API前缀
  app.setGlobalPrefix('api/v1');

  // Swagger文档配置
  const config = new DocumentBuilder()
    .setTitle('DL引擎RAG对话服务API')
    .setDescription('提供智能对话、意图理解、知识检索和回答生成功能')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('dialogue', '对话管理')
    .addTag('sessions', '会话管理')
    .addTag('intent', '意图理解')
    .addTag('generation', '回答生成')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  // 健康检查端点
  app.getHttpAdapter().get('/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'rag-dialogue-service',
      version: '1.0.0',
    });
  });

  // 启动微服务
  await app.startAllMicroservices();

  // 启动HTTP服务
  const httpPort = configService.get<number>('RAG_DIALOGUE_HTTP_PORT', 4009);
  await app.listen(httpPort);

  console.log(`RAG对话服务已启动，微服务端口: ${configService.get<number>('RAG_DIALOGUE_SERVICE_PORT', 3009)}, HTTP端口: ${httpPort}`);
}

bootstrap();
