/**
 * 数字人路径控制器
 */
import { Request, Response, NextFunction } from 'express';
import { avatarPathService, CreatePathData, UpdatePathData, PathQueryOptions } from '../services/AvatarPathService';
import { Logger } from '../utils/Logger';
import { ApiResponse } from '../utils/ApiResponse';
import { ValidationError, NotFoundError } from '../utils/errors';
import { body, param, query, validationResult } from 'express-validator';

/**
 * 数字人路径控制器类
 */
export class AvatarPathController {
  private logger = new Logger('AvatarPathController');

  /**
   * 创建路径
   */
  public async createPath(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // 验证请求
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new ValidationError('请求参数验证失败', errors.array());
      }

      const userId = req.user?.id || 'anonymous';
      const data: CreatePathData = req.body;

      const path = await avatarPathService.createPath(userId, data);

      res.json(ApiResponse.success(path, '路径创建成功'));

    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新路径
   */
  public async updatePath(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new ValidationError('请求参数验证失败', errors.array());
      }

      const userId = req.user?.id || 'anonymous';
      const pathId = req.params.pathId;
      const data: UpdatePathData = req.body;

      const path = await avatarPathService.updatePath(userId, pathId, data);

      res.json(ApiResponse.success(path, '路径更新成功'));

    } catch (error) {
      next(error);
    }
  }

  /**
   * 删除路径
   */
  public async deletePath(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new ValidationError('请求参数验证失败', errors.array());
      }

      const userId = req.user?.id || 'anonymous';
      const pathId = req.params.pathId;

      await avatarPathService.deletePath(userId, pathId);

      res.json(ApiResponse.success(null, '路径删除成功'));

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取路径详情
   */
  public async getPath(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new ValidationError('请求参数验证失败', errors.array());
      }

      const pathId = req.params.pathId;
      const path = await avatarPathService.getPath(pathId);

      res.json(ApiResponse.success(path, '获取路径成功'));

    } catch (error) {
      next(error);
    }
  }

  /**
   * 查询路径列表
   */
  public async queryPaths(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new ValidationError('请求参数验证失败', errors.array());
      }

      const options: PathQueryOptions = {
        projectId: req.query.projectId as string,
        keyword: req.query.keyword as string,
        avatarId: req.query.avatarId as string,
        sceneId: req.query.sceneId as string,
        tags: req.query.tags ? (req.query.tags as string).split(',') : undefined,
        category: req.query.category as string,
        enabled: req.query.enabled ? req.query.enabled === 'true' : undefined,
        page: req.query.page ? parseInt(req.query.page as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        sortBy: req.query.sortBy as string,
        sortOrder: req.query.sortOrder as 'asc' | 'desc'
      };

      const result = await avatarPathService.queryPaths(options);

      res.json(ApiResponse.success(result, '查询路径成功'));

    } catch (error) {
      next(error);
    }
  }

  /**
   * 克隆路径
   */
  public async clonePath(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new ValidationError('请求参数验证失败', errors.array());
      }

      const userId = req.user?.id || 'anonymous';
      const pathId = req.params.pathId;
      const newName = req.body.name;

      const clonedPath = await avatarPathService.clonePath(userId, pathId, newName);

      res.json(ApiResponse.success(clonedPath, '路径克隆成功'));

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取路径统计信息
   */
  public async getPathStatistics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new ValidationError('请求参数验证失败', errors.array());
      }

      const projectId = req.params.projectId;
      const statistics = await avatarPathService.getPathStatistics(projectId);

      res.json(ApiResponse.success(statistics, '获取统计信息成功'));

    } catch (error) {
      next(error);
    }
  }

  /**
   * 批量操作路径
   */
  public async batchOperation(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new ValidationError('请求参数验证失败', errors.array());
      }

      const userId = req.user?.id || 'anonymous';
      const { pathIds, operation, data } = req.body;

      await avatarPathService.batchOperation(userId, pathIds, operation, data);

      res.json(ApiResponse.success(null, '批量操作成功'));

    } catch (error) {
      next(error);
    }
  }

  /**
   * 导出路径
   */
  public async exportPath(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const pathId = req.params.pathId;
      const path = await avatarPathService.getPath(pathId);

      // 设置响应头
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="${path.name}.json"`);

      // 导出路径数据
      const exportData = {
        id: path.id,
        name: path.name,
        avatarId: path.avatarId,
        points: path.points,
        loopMode: path.loopMode,
        interpolation: path.interpolation,
        enabled: path.enabled,
        metadata: path.metadata,
        exportedAt: new Date().toISOString(),
        version: '1.0'
      };

      res.json(exportData);

    } catch (error) {
      next(error);
    }
  }

  /**
   * 导入路径
   */
  public async importPath(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id || 'anonymous';
      const importData = req.body;

      // 验证导入数据
      if (!importData.name || !importData.avatarId || !importData.points) {
        throw new ValidationError('导入数据格式无效');
      }

      // 构建创建数据
      const createData: CreatePathData = {
        name: importData.name,
        avatarId: importData.avatarId,
        projectId: req.body.projectId,
        sceneId: importData.sceneId,
        points: importData.points,
        loopMode: importData.loopMode || 'none',
        interpolation: importData.interpolation || 'linear',
        enabled: importData.enabled !== false,
        metadata: {
          description: importData.metadata?.description || '',
          tags: importData.metadata?.tags || [],
          category: importData.metadata?.category
        }
      };

      const path = await avatarPathService.createPath(userId, createData);

      res.json(ApiResponse.success(path, '路径导入成功'));

    } catch (error) {
      next(error);
    }
  }

  /**
   * 验证路径
   */
  public async validatePath(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const pathId = req.params.pathId;
      const path = await avatarPathService.getPath(pathId);

      const validation = path.validate();

      // 计算质量评分
      let score = 100;
      score -= validation.errors.length * 20;

      // 简单的性能评估
      const complexity = this.calculatePathComplexity(path);
      const performance = {
        complexity,
        memoryUsage: path.points.length * 1024, // 简化计算
        renderCost: Math.min(complexity * 2, 100),
        pathLength: path.totalLength,
        estimatedFPS: Math.max(30, 120 - complexity)
      };

      const result = {
        valid: validation.valid,
        score: Math.max(0, score),
        errors: validation.errors.map(error => ({
          type: 'error',
          code: 'VALIDATION_ERROR',
          message: error,
          severity: 'high'
        })),
        warnings: [],
        suggestions: [],
        performance
      };

      res.json(ApiResponse.success(result, '路径验证完成'));

    } catch (error) {
      next(error);
    }
  }

  /**
   * 计算路径复杂度
   */
  private calculatePathComplexity(path: any): number {
    if (!path.points || path.points.length < 3) return 0;

    let totalAngleChange = 0;
    let totalSpeedChange = 0;

    for (let i = 1; i < path.points.length - 1; i++) {
      const prev = path.points[i - 1];
      const curr = path.points[i];
      const next = path.points[i + 1];

      // 计算方向变化
      const v1 = {
        x: curr.position.x - prev.position.x,
        z: curr.position.z - prev.position.z
      };
      const v2 = {
        x: next.position.x - curr.position.x,
        z: next.position.z - curr.position.z
      };

      const len1 = Math.sqrt(v1.x * v1.x + v1.z * v1.z);
      const len2 = Math.sqrt(v2.x * v2.x + v2.z * v2.z);

      if (len1 > 0 && len2 > 0) {
        const dot = (v1.x * v2.x + v1.z * v2.z) / (len1 * len2);
        const angle = Math.acos(Math.max(-1, Math.min(1, dot)));
        totalAngleChange += angle;
      }

      // 计算速度变化
      totalSpeedChange += Math.abs(curr.speed - prev.speed);
    }

    // 归一化复杂度 (0-100)
    const angleComplexity = Math.min(totalAngleChange / (Math.PI * path.points.length), 1) * 50;
    const speedComplexity = Math.min(totalSpeedChange / (10 * path.points.length), 1) * 50;

    return Math.round(angleComplexity + speedComplexity);
  }
}

/**
 * 验证规则
 */
export const pathValidationRules = {
  createPath: [
    body('name').notEmpty().withMessage('路径名称不能为空').isLength({ max: 100 }).withMessage('路径名称不能超过100个字符'),
    body('avatarId').notEmpty().withMessage('数字人ID不能为空'),
    body('projectId').notEmpty().withMessage('项目ID不能为空'),
    body('points').isArray({ min: 2 }).withMessage('路径至少需要2个点'),
    body('points.*.position.x').isNumeric().withMessage('X坐标必须是数字'),
    body('points.*.position.y').isNumeric().withMessage('Y坐标必须是数字'),
    body('points.*.position.z').isNumeric().withMessage('Z坐标必须是数字'),
    body('points.*.speed').isFloat({ min: 0.1, max: 20 }).withMessage('速度必须在0.1-20之间'),
    body('points.*.waitTime').isFloat({ min: 0 }).withMessage('等待时间不能为负数'),
    body('loopMode').optional().isIn(['none', 'loop', 'pingpong']).withMessage('循环模式无效'),
    body('interpolation').optional().isIn(['linear', 'smooth', 'bezier', 'spline']).withMessage('插值类型无效')
  ],

  updatePath: [
    param('pathId').isMongoId().withMessage('路径ID格式无效'),
    body('name').optional().notEmpty().withMessage('路径名称不能为空').isLength({ max: 100 }).withMessage('路径名称不能超过100个字符'),
    body('points').optional().isArray({ min: 2 }).withMessage('路径至少需要2个点'),
    body('loopMode').optional().isIn(['none', 'loop', 'pingpong']).withMessage('循环模式无效'),
    body('interpolation').optional().isIn(['linear', 'smooth', 'bezier', 'spline']).withMessage('插值类型无效')
  ],

  getPath: [
    param('pathId').isMongoId().withMessage('路径ID格式无效')
  ],

  deletePath: [
    param('pathId').isMongoId().withMessage('路径ID格式无效')
  ],

  queryPaths: [
    query('projectId').notEmpty().withMessage('项目ID不能为空'),
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
    query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('排序方向无效')
  ],

  clonePath: [
    param('pathId').isMongoId().withMessage('路径ID格式无效'),
    body('name').optional().notEmpty().withMessage('路径名称不能为空')
  ],

  getStatistics: [
    param('projectId').notEmpty().withMessage('项目ID不能为空')
  ],

  batchOperation: [
    body('pathIds').isArray({ min: 1 }).withMessage('路径ID列表不能为空'),
    body('pathIds.*').isMongoId().withMessage('路径ID格式无效'),
    body('operation').isIn(['enable', 'disable', 'delete', 'updateCategory']).withMessage('操作类型无效')
  ]
};

// 导出控制器实例
export const avatarPathController = new AvatarPathController();
