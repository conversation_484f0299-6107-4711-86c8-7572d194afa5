/**
 * ComponentMarketService.ts
 * 
 * 组件市场服务，提供组件的上传、下载、评分和分享功能
 */

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, In } from 'typeorm';
import { Component } from '../entities/Component';
import { ComponentRating } from '../entities/ComponentRating';
import { ComponentDownload } from '../entities/ComponentDownload';
import { User } from '../entities/User';
import { Logger } from '@nestjs/common';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as archiver from 'archiver';
import * as unzipper from 'unzipper';

/**
 * 组件搜索参数
 */
export interface ComponentSearchParams {
  keyword?: string;
  category?: string;
  tags?: string[];
  author?: string;
  minRating?: number;
  sortBy?: 'downloads' | 'rating' | 'created' | 'updated';
  sortOrder?: 'ASC' | 'DESC';
  page?: number;
  limit?: number;
}

/**
 * 组件统计信息
 */
export interface ComponentStats {
  totalComponents: number;
  totalDownloads: number;
  averageRating: number;
  categoryCounts: Record<string, number>;
  popularTags: Array<{ tag: string; count: number }>;
}

/**
 * 组件发布参数
 */
export interface PublishComponentParams {
  name: string;
  description: string;
  category: string;
  tags: string[];
  version: string;
  componentData: any;
  previewImage?: string;
  documentation?: string;
  license?: string;
  dependencies?: string[];
}

@Injectable()
export class ComponentMarketService {
  private readonly logger = new Logger(ComponentMarketService.name);
  private readonly uploadPath = path.join(process.cwd(), 'uploads', 'components');

  constructor(
    @InjectRepository(Component)
    private componentRepository: Repository<Component>,
    @InjectRepository(ComponentRating)
    private ratingRepository: Repository<ComponentRating>,
    @InjectRepository(ComponentDownload)
    private downloadRepository: Repository<ComponentDownload>,
    @InjectRepository(User)
    private userRepository: Repository<User>
  ) {
    // 确保上传目录存在
    fs.ensureDirSync(this.uploadPath);
  }

  /**
   * 发布组件
   */
  async publishComponent(
    userId: string,
    params: PublishComponentParams,
    files?: { componentFile?: Express.Multer.File; previewImage?: Express.Multer.File }
  ): Promise<Component> {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new Error('用户不存在');
      }

      // 检查组件名称是否已存在
      const existingComponent = await this.componentRepository.findOne({
        where: { name: params.name, author: user }
      });

      if (existingComponent) {
        throw new Error('组件名称已存在');
      }

      // 处理组件文件
      let componentFilePath: string | undefined;
      if (files?.componentFile) {
        componentFilePath = await this.saveComponentFile(files.componentFile, params.name);
      }

      // 处理预览图片
      let previewImagePath: string | undefined;
      if (files?.previewImage) {
        previewImagePath = await this.savePreviewImage(files.previewImage, params.name);
      }

      // 创建组件记录
      const component = this.componentRepository.create({
        name: params.name,
        description: params.description,
        category: params.category,
        tags: params.tags,
        version: params.version,
        componentData: params.componentData,
        filePath: componentFilePath,
        previewImage: previewImagePath || params.previewImage,
        documentation: params.documentation,
        license: params.license || 'MIT',
        dependencies: params.dependencies || [],
        author: user,
        downloads: 0,
        rating: 0,
        ratingCount: 0,
        isPublic: true,
        isApproved: false, // 需要审核
        createdAt: new Date(),
        updatedAt: new Date()
      });

      const savedComponent = await this.componentRepository.save(component);
      
      this.logger.log(`组件发布成功: ${params.name} by ${user.username}`);
      
      return savedComponent;
    } catch (error) {
      this.logger.error(`发布组件失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 搜索组件
   */
  async searchComponents(params: ComponentSearchParams): Promise<{
    components: Component[];
    total: number;
    page: number;
    limit: number;
  }> {
    const {
      keyword,
      category,
      tags,
      author,
      minRating = 0,
      sortBy = 'downloads',
      sortOrder = 'DESC',
      page = 1,
      limit = 20
    } = params;

    const queryBuilder = this.componentRepository
      .createQueryBuilder('component')
      .leftJoinAndSelect('component.author', 'author')
      .where('component.isPublic = :isPublic', { isPublic: true })
      .andWhere('component.isApproved = :isApproved', { isApproved: true })
      .andWhere('component.rating >= :minRating', { minRating });

    // 关键词搜索
    if (keyword) {
      queryBuilder.andWhere(
        '(component.name ILIKE :keyword OR component.description ILIKE :keyword OR component.tags::text ILIKE :keyword)',
        { keyword: `%${keyword}%` }
      );
    }

    // 分类过滤
    if (category) {
      queryBuilder.andWhere('component.category = :category', { category });
    }

    // 标签过滤
    if (tags && tags.length > 0) {
      queryBuilder.andWhere('component.tags && :tags', { tags });
    }

    // 作者过滤
    if (author) {
      queryBuilder.andWhere('author.username = :author', { author });
    }

    // 排序
    const orderField = sortBy === 'created' ? 'component.createdAt' :
                      sortBy === 'updated' ? 'component.updatedAt' :
                      sortBy === 'rating' ? 'component.rating' :
                      'component.downloads';
    
    queryBuilder.orderBy(orderField, sortOrder);

    // 分页
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [components, total] = await queryBuilder.getManyAndCount();

    return {
      components,
      total,
      page,
      limit
    };
  }

  /**
   * 获取组件详情
   */
  async getComponentById(componentId: string): Promise<Component> {
    const component = await this.componentRepository.findOne({
      where: { id: componentId },
      relations: ['author', 'ratings', 'ratings.user']
    });

    if (!component) {
      throw new Error('组件不存在');
    }

    return component;
  }

  /**
   * 下载组件
   */
  async downloadComponent(componentId: string, userId?: string): Promise<{
    filePath: string;
    fileName: string;
  }> {
    const component = await this.getComponentById(componentId);

    if (!component.isPublic || !component.isApproved) {
      throw new Error('组件不可下载');
    }

    // 记录下载
    if (userId) {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (user) {
        const download = this.downloadRepository.create({
          component,
          user,
          downloadedAt: new Date()
        });
        await this.downloadRepository.save(download);
      }
    }

    // 增加下载计数
    await this.componentRepository.update(componentId, {
      downloads: component.downloads + 1
    });

    // 如果有文件路径，返回文件
    if (component.filePath && fs.existsSync(component.filePath)) {
      return {
        filePath: component.filePath,
        fileName: `${component.name}-${component.version}.zip`
      };
    }

    // 否则动态生成组件包
    const packagePath = await this.generateComponentPackage(component);
    return {
      filePath: packagePath,
      fileName: `${component.name}-${component.version}.zip`
    };
  }

  /**
   * 评分组件
   */
  async rateComponent(
    componentId: string,
    userId: string,
    rating: number,
    comment?: string
  ): Promise<ComponentRating> {
    if (rating < 1 || rating > 5) {
      throw new Error('评分必须在1-5之间');
    }

    const component = await this.getComponentById(componentId);
    const user = await this.userRepository.findOne({ where: { id: userId } });

    if (!user) {
      throw new Error('用户不存在');
    }

    // 检查是否已经评分过
    let existingRating = await this.ratingRepository.findOne({
      where: { component: { id: componentId }, user: { id: userId } }
    });

    if (existingRating) {
      // 更新现有评分
      existingRating.rating = rating;
      existingRating.comment = comment;
      existingRating.updatedAt = new Date();
      await this.ratingRepository.save(existingRating);
    } else {
      // 创建新评分
      existingRating = this.ratingRepository.create({
        component,
        user,
        rating,
        comment,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      await this.ratingRepository.save(existingRating);
    }

    // 重新计算组件平均评分
    await this.updateComponentRating(componentId);

    return existingRating;
  }

  /**
   * 获取组件统计信息
   */
  async getComponentStats(): Promise<ComponentStats> {
    const totalComponents = await this.componentRepository.count({
      where: { isPublic: true, isApproved: true }
    });

    const downloadStats = await this.componentRepository
      .createQueryBuilder('component')
      .select('SUM(component.downloads)', 'totalDownloads')
      .addSelect('AVG(component.rating)', 'averageRating')
      .where('component.isPublic = :isPublic', { isPublic: true })
      .andWhere('component.isApproved = :isApproved', { isApproved: true })
      .getRawOne();

    const categoryStats = await this.componentRepository
      .createQueryBuilder('component')
      .select('component.category', 'category')
      .addSelect('COUNT(*)', 'count')
      .where('component.isPublic = :isPublic', { isPublic: true })
      .andWhere('component.isApproved = :isApproved', { isApproved: true })
      .groupBy('component.category')
      .getRawMany();

    const categoryCounts = categoryStats.reduce((acc, stat) => {
      acc[stat.category] = parseInt(stat.count);
      return acc;
    }, {});

    // 获取热门标签
    const tagStats = await this.componentRepository
      .createQueryBuilder('component')
      .select('unnest(component.tags)', 'tag')
      .addSelect('COUNT(*)', 'count')
      .where('component.isPublic = :isPublic', { isPublic: true })
      .andWhere('component.isApproved = :isApproved', { isApproved: true })
      .groupBy('tag')
      .orderBy('count', 'DESC')
      .limit(20)
      .getRawMany();

    const popularTags = tagStats.map(stat => ({
      tag: stat.tag,
      count: parseInt(stat.count)
    }));

    return {
      totalComponents,
      totalDownloads: parseInt(downloadStats.totalDownloads) || 0,
      averageRating: parseFloat(downloadStats.averageRating) || 0,
      categoryCounts,
      popularTags
    };
  }

  /**
   * 获取用户的组件
   */
  async getUserComponents(userId: string): Promise<Component[]> {
    return this.componentRepository.find({
      where: { author: { id: userId } },
      relations: ['author'],
      order: { createdAt: 'DESC' }
    });
  }

  /**
   * 更新组件
   */
  async updateComponent(
    componentId: string,
    userId: string,
    updates: Partial<PublishComponentParams>
  ): Promise<Component> {
    const component = await this.componentRepository.findOne({
      where: { id: componentId, author: { id: userId } }
    });

    if (!component) {
      throw new Error('组件不存在或无权限');
    }

    Object.assign(component, updates, { updatedAt: new Date() });
    
    return this.componentRepository.save(component);
  }

  /**
   * 删除组件
   */
  async deleteComponent(componentId: string, userId: string): Promise<void> {
    const component = await this.componentRepository.findOne({
      where: { id: componentId, author: { id: userId } }
    });

    if (!component) {
      throw new Error('组件不存在或无权限');
    }

    // 删除相关文件
    if (component.filePath && fs.existsSync(component.filePath)) {
      await fs.remove(component.filePath);
    }

    if (component.previewImage && fs.existsSync(component.previewImage)) {
      await fs.remove(component.previewImage);
    }

    // 删除数据库记录
    await this.componentRepository.remove(component);
  }

  /**
   * 保存组件文件
   */
  private async saveComponentFile(file: Express.Multer.File, componentName: string): Promise<string> {
    const fileName = `${componentName}-${Date.now()}.zip`;
    const filePath = path.join(this.uploadPath, fileName);
    
    await fs.writeFile(filePath, file.buffer);
    
    return filePath;
  }

  /**
   * 保存预览图片
   */
  private async savePreviewImage(file: Express.Multer.File, componentName: string): Promise<string> {
    const ext = path.extname(file.originalname);
    const fileName = `${componentName}-preview-${Date.now()}${ext}`;
    const filePath = path.join(this.uploadPath, 'previews', fileName);
    
    await fs.ensureDir(path.dirname(filePath));
    await fs.writeFile(filePath, file.buffer);
    
    return filePath;
  }

  /**
   * 生成组件包
   */
  private async generateComponentPackage(component: Component): Promise<string> {
    const packagePath = path.join(this.uploadPath, 'temp', `${component.id}.zip`);
    await fs.ensureDir(path.dirname(packagePath));

    const output = fs.createWriteStream(packagePath);
    const archive = archiver('zip', { zlib: { level: 9 } });

    archive.pipe(output);

    // 添加组件数据
    archive.append(JSON.stringify(component.componentData, null, 2), { name: 'component.json' });

    // 添加元数据
    const metadata = {
      name: component.name,
      version: component.version,
      description: component.description,
      author: component.author.username,
      license: component.license,
      dependencies: component.dependencies,
      tags: component.tags,
      category: component.category
    };
    archive.append(JSON.stringify(metadata, null, 2), { name: 'package.json' });

    // 添加文档
    if (component.documentation) {
      archive.append(component.documentation, { name: 'README.md' });
    }

    await archive.finalize();

    return new Promise((resolve, reject) => {
      output.on('close', () => resolve(packagePath));
      output.on('error', reject);
    });
  }

  /**
   * 更新组件评分
   */
  private async updateComponentRating(componentId: string): Promise<void> {
    const ratingStats = await this.ratingRepository
      .createQueryBuilder('rating')
      .select('AVG(rating.rating)', 'averageRating')
      .addSelect('COUNT(*)', 'ratingCount')
      .where('rating.componentId = :componentId', { componentId })
      .getRawOne();

    await this.componentRepository.update(componentId, {
      rating: parseFloat(ratingStats.averageRating) || 0,
      ratingCount: parseInt(ratingStats.ratingCount) || 0
    });
  }
}
