/**
 * 移动设备优化器
 * 专门针对移动设备的性能优化和适配
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';

/**
 * 移动设备类型
 */
export enum MobileDeviceType {
  PHONE = 'phone',
  TABLET = 'tablet',
  FOLDABLE = 'foldable'
}

/**
 * 电池状态
 */
export interface BatteryStatus {
  level: number; // 0-1
  charging: boolean;
  chargingTime: number;
  dischargingTime: number;
}

/**
 * 网络状态
 */
export interface NetworkStatus {
  type: string; // wifi, cellular, etc.
  effectiveType: string; // slow-2g, 2g, 3g, 4g
  downlink: number; // Mbps
  rtt: number; // ms
  saveData: boolean;
}

/**
 * 移动优化配置
 */
export interface MobileOptimizationConfig {
  /** 是否启用省电模式 */
  enablePowerSaving: boolean;
  /** 是否启用网络优化 */
  enableNetworkOptimization: boolean;
  /** 是否启用触摸优化 */
  enableTouchOptimization: boolean;
  /** 是否启用方向感应 */
  enableOrientationSensing: boolean;
  /** 是否启用后台暂停 */
  enableBackgroundPause: boolean;
  /** 低电量阈值 */
  lowBatteryThreshold: number;
  /** 慢网络阈值 */
  slowNetworkThreshold: number;
  /** 触摸延迟阈值 */
  touchDelayThreshold: number;
}

/**
 * 移动设备优化器
 */
export class MobileOptimizer extends EventEmitter {
  private config: MobileOptimizationConfig;
  private deviceType: MobileDeviceType;
  private batteryStatus: BatteryStatus | null = null;
  private networkStatus: NetworkStatus | null = null;
  private orientationLocked = false;
  private isInBackground = false;
  private touchOptimizations: TouchOptimizations;
  private performanceMonitor: MobilePerformanceMonitor;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: MobileOptimizationConfig = {
    enablePowerSaving: true,
    enableNetworkOptimization: true,
    enableTouchOptimization: true,
    enableOrientationSensing: true,
    enableBackgroundPause: true,
    lowBatteryThreshold: 0.2,
    slowNetworkThreshold: 1.0, // 1 Mbps
    touchDelayThreshold: 100 // 100ms
  };

  constructor(config: Partial<MobileOptimizationConfig> = {}) {
    super();
    this.config = { ...MobileOptimizer.DEFAULT_CONFIG, ...config };
    this.deviceType = this.detectDeviceType();
    
    this.touchOptimizations = new TouchOptimizations();
    this.performanceMonitor = new MobilePerformanceMonitor();
    
    this.initializeOptimizations();
  }

  /**
   * 检测设备类型
   */
  private detectDeviceType(): MobileDeviceType {
    if (typeof window === 'undefined') return MobileDeviceType.PHONE;

    const userAgent = navigator.userAgent;
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;
    const maxDimension = Math.max(screenWidth, screenHeight);

    // 检测折叠屏
    if (userAgent.includes('Fold') || userAgent.includes('Flip')) {
      return MobileDeviceType.FOLDABLE;
    }

    // 检测平板
    if (maxDimension >= 1024 || userAgent.includes('iPad')) {
      return MobileDeviceType.TABLET;
    }

    return MobileDeviceType.PHONE;
  }

  /**
   * 初始化优化功能
   */
  private initializeOptimizations(): void {
    // 初始化电池监控
    if (this.config.enablePowerSaving) {
      this.initializeBatteryMonitoring();
    }

    // 初始化网络监控
    if (this.config.enableNetworkOptimization) {
      this.initializeNetworkMonitoring();
    }

    // 初始化触摸优化
    if (this.config.enableTouchOptimization) {
      this.initializeTouchOptimizations();
    }

    // 初始化方向感应
    if (this.config.enableOrientationSensing) {
      this.initializeOrientationSensing();
    }

    // 初始化后台检测
    if (this.config.enableBackgroundPause) {
      this.initializeBackgroundDetection();
    }

    Debug.log('MobileOptimizer', `移动优化器初始化完成，设备类型: ${this.deviceType}`);
  }

  /**
   * 初始化电池监控
   */
  private initializeBatteryMonitoring(): void {
    if ('getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        this.updateBatteryStatus(battery);
        
        battery.addEventListener('chargingchange', () => this.updateBatteryStatus(battery));
        battery.addEventListener('levelchange', () => this.updateBatteryStatus(battery));
      });
    }
  }

  /**
   * 更新电池状态
   */
  private updateBatteryStatus(battery: any): void {
    this.batteryStatus = {
      level: battery.level,
      charging: battery.charging,
      chargingTime: battery.chargingTime,
      dischargingTime: battery.dischargingTime
    };

    // 检查低电量
    if (this.batteryStatus.level <= this.config.lowBatteryThreshold && !this.batteryStatus.charging) {
      this.enablePowerSavingMode();
    } else if (this.batteryStatus.charging || this.batteryStatus.level > this.config.lowBatteryThreshold + 0.1) {
      this.disablePowerSavingMode();
    }

    this.emit('batteryStatusChanged', this.batteryStatus);
  }

  /**
   * 初始化网络监控
   */
  private initializeNetworkMonitoring(): void {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      this.updateNetworkStatus(connection);
      
      connection.addEventListener('change', () => this.updateNetworkStatus(connection));
    }
  }

  /**
   * 更新网络状态
   */
  private updateNetworkStatus(connection: any): void {
    this.networkStatus = {
      type: connection.type || 'unknown',
      effectiveType: connection.effectiveType || 'unknown',
      downlink: connection.downlink || 0,
      rtt: connection.rtt || 0,
      saveData: connection.saveData || false
    };

    // 检查慢网络
    if (this.networkStatus.downlink < this.config.slowNetworkThreshold) {
      this.enableNetworkOptimization();
    }

    this.emit('networkStatusChanged', this.networkStatus);
  }

  /**
   * 初始化触摸优化
   */
  private initializeTouchOptimizations(): void {
    this.touchOptimizations.initialize();
    
    this.touchOptimizations.on('touchPerformance', (data) => {
      if (data.averageDelay > this.config.touchDelayThreshold) {
        this.optimizeTouchPerformance();
      }
    });
  }

  /**
   * 初始化方向感应
   */
  private initializeOrientationSensing(): void {
    if (typeof window !== 'undefined') {
      window.addEventListener('orientationchange', () => {
        this.handleOrientationChange();
      });

      // 监听设备方向
      if ('DeviceOrientationEvent' in window) {
        window.addEventListener('deviceorientation', (event) => {
          this.handleDeviceOrientation(event);
        });
      }
    }
  }

  /**
   * 处理方向变化
   */
  private handleOrientationChange(): void {
    setTimeout(() => {
      const orientation = window.orientation || 0;
      
      // 根据方向调整界面
      this.emit('orientationChanged', {
        orientation,
        isLandscape: Math.abs(orientation) === 90
      });
      
      // 重新计算布局
      this.recalculateLayout();
    }, 100); // 延迟确保方向变化完成
  }

  /**
   * 处理设备方向
   */
  private handleDeviceOrientation(event: DeviceOrientationEvent): void {
    const orientation = {
      alpha: event.alpha, // Z轴旋转
      beta: event.beta,   // X轴旋转
      gamma: event.gamma  // Y轴旋转
    };

    this.emit('deviceOrientationChanged', orientation);
  }

  /**
   * 初始化后台检测
   */
  private initializeBackgroundDetection(): void {
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        this.isInBackground = document.hidden;
        
        if (this.isInBackground) {
          this.handleAppGoesToBackground();
        } else {
          this.handleAppComesToForeground();
        }
      });
    }

    // 监听页面焦点
    if (typeof window !== 'undefined') {
      window.addEventListener('blur', () => this.handleAppGoesToBackground());
      window.addEventListener('focus', () => this.handleAppComesToForeground());
    }
  }

  /**
   * 处理应用进入后台
   */
  private handleAppGoesToBackground(): void {
    Debug.log('MobileOptimizer', '应用进入后台，启用后台优化');
    
    // 暂停非必要处理
    this.emit('pauseProcessing');
    
    // 降低处理频率
    this.emit('reduceProcessingFrequency', { factor: 0.1 });
    
    this.emit('appBackgrounded');
  }

  /**
   * 处理应用回到前台
   */
  private handleAppComesToForeground(): void {
    Debug.log('MobileOptimizer', '应用回到前台，恢复正常处理');
    
    // 恢复处理
    this.emit('resumeProcessing');
    
    // 恢复处理频率
    this.emit('restoreProcessingFrequency');
    
    this.emit('appForegrounded');
  }

  /**
   * 启用省电模式
   */
  private enablePowerSavingMode(): void {
    Debug.log('MobileOptimizer', '启用省电模式');
    
    const optimizations = {
      reduceFrameRate: true,
      lowerResolution: true,
      disableNonEssentialFeatures: true,
      reduceProcessingQuality: true
    };

    this.emit('powerSavingEnabled', optimizations);
  }

  /**
   * 禁用省电模式
   */
  private disablePowerSavingMode(): void {
    Debug.log('MobileOptimizer', '禁用省电模式');
    
    this.emit('powerSavingDisabled');
  }

  /**
   * 启用网络优化
   */
  private enableNetworkOptimization(): void {
    Debug.log('MobileOptimizer', '启用网络优化');
    
    const optimizations = {
      reduceDataUsage: true,
      compressData: true,
      cacheAggressively: true,
      prioritizeLocalProcessing: true
    };

    this.emit('networkOptimizationEnabled', optimizations);
  }

  /**
   * 优化触摸性能
   */
  private optimizeTouchPerformance(): void {
    Debug.log('MobileOptimizer', '优化触摸性能');
    
    const optimizations = {
      reduceTouchSampling: true,
      enableTouchPrediction: true,
      optimizeEventHandling: true
    };

    this.emit('touchOptimizationEnabled', optimizations);
  }

  /**
   * 重新计算布局
   */
  private recalculateLayout(): void {
    // 触发布局重新计算
    this.emit('recalculateLayout');
  }

  /**
   * 获取设备信息
   */
  public getDeviceInfo(): any {
    return {
      type: this.deviceType,
      battery: this.batteryStatus,
      network: this.networkStatus,
      isInBackground: this.isInBackground,
      orientationLocked: this.orientationLocked
    };
  }

  /**
   * 获取优化建议
   */
  public getOptimizationRecommendations(): string[] {
    const recommendations: string[] = [];

    if (this.batteryStatus && this.batteryStatus.level < 0.3) {
      recommendations.push('电池电量较低，建议启用省电模式');
    }

    if (this.networkStatus && this.networkStatus.downlink < 2) {
      recommendations.push('网络速度较慢，建议降低视频质量');
    }

    if (this.deviceType === MobileDeviceType.PHONE) {
      recommendations.push('手机设备建议使用竖屏模式以获得最佳体验');
    }

    if (this.networkStatus && this.networkStatus.saveData) {
      recommendations.push('检测到数据节省模式，将自动优化数据使用');
    }

    return recommendations;
  }

  /**
   * 锁定屏幕方向
   */
  public async lockOrientation(orientation: 'portrait' | 'landscape'): Promise<boolean> {
    try {
      if ('screen' in window && 'orientation' in window.screen) {
        await (window.screen.orientation as any).lock(orientation);
        this.orientationLocked = true;
        return true;
      }
    } catch (error) {
      Debug.warn('MobileOptimizer', '无法锁定屏幕方向', error);
    }
    return false;
  }

  /**
   * 解锁屏幕方向
   */
  public unlockOrientation(): void {
    try {
      if ('screen' in window && 'orientation' in window.screen) {
        (window.screen.orientation as any).unlock();
        this.orientationLocked = false;
      }
    } catch (error) {
      Debug.warn('MobileOptimizer', '无法解锁屏幕方向', error);
    }
  }

  /**
   * 请求唤醒锁定
   */
  public async requestWakeLock(): Promise<boolean> {
    try {
      if ('wakeLock' in navigator) {
        await (navigator as any).wakeLock.request('screen');
        return true;
      }
    } catch (error) {
      Debug.warn('MobileOptimizer', '无法请求唤醒锁定', error);
    }
    return false;
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<MobileOptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  /**
   * 获取配置
   */
  public getConfig(): MobileOptimizationConfig {
    return { ...this.config };
  }

  /**
   * 销毁优化器
   */
  public destroy(): void {
    this.touchOptimizations.destroy();
    this.performanceMonitor.destroy();
    this.removeAllListeners();
  }
}

/**
 * 触摸优化器
 */
class TouchOptimizations extends EventEmitter {
  private touchStartTimes: Map<number, number> = new Map();
  private touchDelays: number[] = [];

  public initialize(): void {
    if (typeof window !== 'undefined') {
      window.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
      window.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
    }
  }

  private handleTouchStart(event: TouchEvent): void {
    const now = performance.now();
    for (let i = 0; i < event.touches.length; i++) {
      const touch = event.touches[i];
      this.touchStartTimes.set(touch.identifier, now);
    }
  }

  private handleTouchEnd(event: TouchEvent): void {
    const now = performance.now();
    for (let i = 0; i < event.changedTouches.length; i++) {
      const touch = event.changedTouches[i];
      const startTime = this.touchStartTimes.get(touch.identifier);
      
      if (startTime) {
        const delay = now - startTime;
        this.touchDelays.push(delay);
        this.touchStartTimes.delete(touch.identifier);
        
        // 保持最近100次触摸的记录
        if (this.touchDelays.length > 100) {
          this.touchDelays.shift();
        }
        
        // 每10次触摸计算一次平均延迟
        if (this.touchDelays.length % 10 === 0) {
          const averageDelay = this.touchDelays.reduce((sum, d) => sum + d, 0) / this.touchDelays.length;
          this.emit('touchPerformance', { averageDelay });
        }
      }
    }
  }

  public destroy(): void {
    this.touchStartTimes.clear();
    this.touchDelays = [];
    this.removeAllListeners();
  }
}

/**
 * 移动性能监控器
 */
class MobilePerformanceMonitor {
  private frameTimeHistory: number[] = [];
  private lastFrameTime = 0;

  public initialize(): void {
    this.startFrameMonitoring();
  }

  private startFrameMonitoring(): void {
    const measureFrame = () => {
      const now = performance.now();
      
      if (this.lastFrameTime > 0) {
        const frameTime = now - this.lastFrameTime;
        this.frameTimeHistory.push(frameTime);
        
        if (this.frameTimeHistory.length > 60) {
          this.frameTimeHistory.shift();
        }
      }
      
      this.lastFrameTime = now;
      requestAnimationFrame(measureFrame);
    };
    
    requestAnimationFrame(measureFrame);
  }

  public getAverageFrameTime(): number {
    if (this.frameTimeHistory.length === 0) return 0;
    return this.frameTimeHistory.reduce((sum, time) => sum + time, 0) / this.frameTimeHistory.length;
  }

  public getFPS(): number {
    const avgFrameTime = this.getAverageFrameTime();
    return avgFrameTime > 0 ? 1000 / avgFrameTime : 0;
  }

  public destroy(): void {
    this.frameTimeHistory = [];
  }
}
