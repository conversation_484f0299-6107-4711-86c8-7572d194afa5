/**
 * 增强的地形生成系统
 * 提供更高级的地形生成算法和优化
 */
import * as THREE from 'three';
import { SimplexNoise } from '../../utils/SimplexNoise';
import { TerrainComponent } from '../components/TerrainComponent';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';

/**
 * 增强地形生成配置
 */
export interface EnhancedTerrainConfig {
  /** 地形尺寸 */
  size: number;
  /** 地形分辨率 */
  resolution: number;
  /** 高度范围 */
  heightRange: { min: number; max: number };
  /** 噪声层配置 */
  noiseLayers: NoiseLayerConfig[];
  /** 侵蚀配置 */
  erosion?: ErosionConfig;
  /** 地形特征配置 */
  features?: TerrainFeatureConfig[];
  /** 生物群系配置 */
  biomes?: BiomeConfig[];
  /** 性能配置 */
  performance?: PerformanceConfig;
}

/**
 * 噪声层配置
 */
export interface NoiseLayerConfig {
  /** 噪声类型 */
  type: 'simplex' | 'perlin' | 'ridged' | 'billow' | 'voronoi';
  /** 频率 */
  frequency: number;
  /** 振幅 */
  amplitude: number;
  /** 八度数 */
  octaves: number;
  /** 持续性 */
  persistence: number;
  /** 间隙度 */
  lacunarity: number;
  /** 种子 */
  seed?: number;
  /** 权重 */
  weight: number;
  /** 是否启用 */
  enabled: boolean;
}

/**
 * 侵蚀配置
 */
export interface ErosionConfig {
  /** 是否启用热侵蚀 */
  enableThermal: boolean;
  /** 是否启用水力侵蚀 */
  enableHydraulic: boolean;
  /** 侵蚀强度 */
  strength: number;
  /** 侵蚀迭代次数 */
  iterations: number;
  /** 热侵蚀参数 */
  thermal?: {
    talusAngle: number;
    strength: number;
  };
  /** 水力侵蚀参数 */
  hydraulic?: {
    rainAmount: number;
    evaporation: number;
    capacity: number;
    deposition: number;
    erosion: number;
  };
}

/**
 * 地形特征配置
 */
export interface TerrainFeatureConfig {
  /** 特征类型 */
  type: 'mountain' | 'valley' | 'plateau' | 'crater' | 'ridge';
  /** 位置 */
  position: THREE.Vector2;
  /** 半径 */
  radius: number;
  /** 强度 */
  intensity: number;
  /** 衰减 */
  falloff: number;
}

/**
 * 生物群系配置
 */
export interface BiomeConfig {
  /** 生物群系名称 */
  name: string;
  /** 高度范围 */
  heightRange: { min: number; max: number };
  /** 湿度范围 */
  moistureRange: { min: number; max: number };
  /** 温度范围 */
  temperatureRange: { min: number; max: number };
  /** 颜色 */
  color: THREE.Color;
  /** 纹理权重 */
  textureWeights: number[];
}

/**
 * 性能配置
 */
export interface PerformanceConfig {
  /** 是否使用多线程 */
  useWorkers: boolean;
  /** 工作线程数量 */
  workerCount: number;
  /** 是否启用缓存 */
  enableCache: boolean;
  /** 是否启用渐进式生成 */
  enableProgressive: boolean;
  /** 块大小 */
  chunkSize: number;
}

/**
 * 增强地形生成器
 */
export class EnhancedTerrainGenerator {
  private noise: SimplexNoise;
  private performanceMonitor: PerformanceMonitor;
  private cache: Map<string, Float32Array>;

  constructor() {
    this.noise = new SimplexNoise();
    this.performanceMonitor = PerformanceMonitor.getInstance();
    this.cache = new Map();
  }

  /**
   * 生成地形高度图
   */
  public async generateHeightMap(config: EnhancedTerrainConfig): Promise<Float32Array> {
    const startTime = performance.now();
    
    Debug.log('开始生成增强地形高度图', `尺寸: ${config.size}, 分辨率: ${config.resolution}, 层数: ${config.noiseLayers.length}`);

    const { size, resolution } = config;
    const heightMap = new Float32Array(resolution * resolution);

    // 检查缓存
    const cacheKey = this.generateCacheKey(config);
    if (config.performance?.enableCache && this.cache.has(cacheKey)) {
      Debug.log('使用缓存的地形高度图');
      return this.cache.get(cacheKey)!;
    }

    // 生成基础噪声
    await this.generateBaseNoise(heightMap, config);

    // 应用地形特征
    if (config.features) {
      this.applyTerrainFeatures(heightMap, config);
    }

    // 应用侵蚀
    if (config.erosion) {
      await this.applyErosion(heightMap, config);
    }

    // 应用生物群系
    if (config.biomes) {
      this.applyBiomes(heightMap, config);
    }

    // 缓存结果
    if (config.performance?.enableCache) {
      this.cache.set(cacheKey, heightMap);
    }

    const endTime = performance.now();
    Debug.log(`地形生成完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);

    return heightMap;
  }

  /**
   * 生成基础噪声
   */
  private async generateBaseNoise(heightMap: Float32Array, config: EnhancedTerrainConfig): Promise<void> {
    const { resolution, heightRange, noiseLayers } = config;
    
    for (let i = 0; i < resolution; i++) {
      for (let j = 0; j < resolution; j++) {
        const index = i * resolution + j;
        let height = 0;
        let totalWeight = 0;

        // 计算所有噪声层的贡献
        for (const layer of noiseLayers) {
          if (!layer.enabled) continue;

          const noiseValue = this.generateLayerNoise(
            i / resolution,
            j / resolution,
            layer
          );

          height += noiseValue * layer.weight;
          totalWeight += layer.weight;
        }

        // 归一化并映射到高度范围
        if (totalWeight > 0) {
          height /= totalWeight;
        }

        height = THREE.MathUtils.lerp(heightRange.min, heightRange.max, (height + 1) * 0.5);
        heightMap[index] = height;
      }
    }
  }

  /**
   * 生成单层噪声
   */
  private generateLayerNoise(x: number, y: number, layer: NoiseLayerConfig): number {
    let value = 0;
    let amplitude = layer.amplitude;
    let frequency = layer.frequency;

    for (let octave = 0; octave < layer.octaves; octave++) {
      switch (layer.type) {
        case 'simplex':
          value += this.noise.noise2D(x * frequency, y * frequency) * amplitude;
          break;
        case 'ridged':
          value += (1 - Math.abs(this.noise.noise2D(x * frequency, y * frequency))) * amplitude;
          break;
        case 'billow':
          value += Math.abs(this.noise.noise2D(x * frequency, y * frequency)) * amplitude;
          break;
        default:
          value += this.noise.noise2D(x * frequency, y * frequency) * amplitude;
      }

      amplitude *= layer.persistence;
      frequency *= layer.lacunarity;
    }

    return value;
  }

  /**
   * 应用地形特征
   */
  private applyTerrainFeatures(heightMap: Float32Array, config: EnhancedTerrainConfig): void {
    const { resolution, features } = config;

    for (const feature of features!) {
      for (let i = 0; i < resolution; i++) {
        for (let j = 0; j < resolution; j++) {
          const index = i * resolution + j;
          const x = (i / resolution) * config.size;
          const y = (j / resolution) * config.size;

          const distance = Math.sqrt(
            Math.pow(x - feature.position.x, 2) + Math.pow(y - feature.position.y, 2)
          );

          if (distance < feature.radius) {
            const influence = this.calculateFeatureInfluence(distance, feature);
            heightMap[index] += influence;
          }
        }
      }
    }
  }

  /**
   * 计算地形特征影响
   */
  private calculateFeatureInfluence(distance: number, feature: TerrainFeatureConfig): number {
    const normalizedDistance = distance / feature.radius;
    const falloffFactor = Math.pow(1 - normalizedDistance, feature.falloff);

    switch (feature.type) {
      case 'mountain':
        return feature.intensity * falloffFactor;
      case 'valley':
        return -feature.intensity * falloffFactor;
      case 'plateau':
        return feature.intensity * (falloffFactor > 0.5 ? 1 : 0);
      case 'crater':
        return -feature.intensity * (1 - falloffFactor);
      case 'ridge':
        return feature.intensity * Math.sin(normalizedDistance * Math.PI) * falloffFactor;
      default:
        return 0;
    }
  }

  /**
   * 应用侵蚀效果
   */
  private async applyErosion(heightMap: Float32Array, config: EnhancedTerrainConfig): Promise<void> {
    const { erosion, resolution } = config;

    if (erosion!.enableThermal) {
      await this.applyThermalErosion(heightMap, resolution, erosion!);
    }

    if (erosion!.enableHydraulic) {
      await this.applyHydraulicErosion(heightMap, resolution, erosion!);
    }
  }

  /**
   * 应用热侵蚀
   */
  private async applyThermalErosion(
    heightMap: Float32Array,
    resolution: number,
    erosion: ErosionConfig
  ): Promise<void> {
    const { thermal } = erosion;
    if (!thermal) return;

    for (let iteration = 0; iteration < erosion.iterations; iteration++) {
      const newHeightMap = new Float32Array(heightMap);

      for (let i = 1; i < resolution - 1; i++) {
        for (let j = 1; j < resolution - 1; j++) {
          const index = i * resolution + j;
          const currentHeight = heightMap[index];

          // 检查相邻像素
          const neighbors = [
            heightMap[(i - 1) * resolution + j],     // 上
            heightMap[(i + 1) * resolution + j],     // 下
            heightMap[i * resolution + (j - 1)],     // 左
            heightMap[i * resolution + (j + 1)]      // 右
          ];

          let totalDiff = 0;
          let validNeighbors = 0;

          for (const neighborHeight of neighbors) {
            const diff = currentHeight - neighborHeight;
            if (diff > thermal.talusAngle) {
              totalDiff += diff;
              validNeighbors++;
            }
          }

          if (validNeighbors > 0) {
            const erosionAmount = (totalDiff / validNeighbors) * thermal.strength;
            newHeightMap[index] -= erosionAmount;
          }
        }
      }

      heightMap.set(newHeightMap);
    }
  }

  /**
   * 应用水力侵蚀
   */
  private async applyHydraulicErosion(
    heightMap: Float32Array,
    resolution: number,
    erosion: ErosionConfig
  ): Promise<void> {
    const { hydraulic } = erosion;
    if (!hydraulic) return;

    const waterMap = new Float32Array(resolution * resolution);
    const sedimentMap = new Float32Array(resolution * resolution);

    for (let iteration = 0; iteration < erosion.iterations; iteration++) {
      // 添加雨水
      for (let i = 0; i < waterMap.length; i++) {
        waterMap[i] += hydraulic.rainAmount;
      }

      // 模拟水流和侵蚀
      for (let i = 1; i < resolution - 1; i++) {
        for (let j = 1; j < resolution - 1; j++) {
          const index = i * resolution + j;
          
          // 计算水流方向和速度
          const gradient = this.calculateGradient(heightMap, i, j, resolution);
          const velocity = Math.sqrt(gradient.x * gradient.x + gradient.y * gradient.y);
          
          // 计算侵蚀和沉积
          const capacity = velocity * waterMap[index] * hydraulic.capacity;
          const erosionAmount = Math.min(
            hydraulic.erosion * velocity,
            heightMap[index] - 0.01
          );
          
          if (sedimentMap[index] > capacity) {
            // 沉积
            const deposition = (sedimentMap[index] - capacity) * hydraulic.deposition;
            heightMap[index] += deposition;
            sedimentMap[index] -= deposition;
          } else {
            // 侵蚀
            heightMap[index] -= erosionAmount;
            sedimentMap[index] += erosionAmount;
          }
        }
      }

      // 蒸发
      for (let i = 0; i < waterMap.length; i++) {
        waterMap[i] *= (1 - hydraulic.evaporation);
      }
    }
  }

  /**
   * 计算梯度
   */
  private calculateGradient(
    heightMap: Float32Array,
    i: number,
    j: number,
    resolution: number
  ): THREE.Vector2 {
    const left = heightMap[i * resolution + (j - 1)];
    const right = heightMap[i * resolution + (j + 1)];
    const up = heightMap[(i - 1) * resolution + j];
    const down = heightMap[(i + 1) * resolution + j];

    return new THREE.Vector2(
      (right - left) * 0.5,
      (down - up) * 0.5
    );
  }

  /**
   * 应用生物群系
   */
  private applyBiomes(heightMap: Float32Array, config: EnhancedTerrainConfig): void {
    // 生物群系应用逻辑
    // 这里可以根据高度、湿度、温度等因素来确定生物群系
    Debug.log('应用生物群系效果');
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(config: EnhancedTerrainConfig): string {
    return JSON.stringify({
      size: config.size,
      resolution: config.resolution,
      heightRange: config.heightRange,
      noiseLayers: config.noiseLayers,
      features: config.features
    });
  }

  /**
   * 清理缓存
   */
  public clearCache(): void {
    this.cache.clear();
    Debug.log('地形生成缓存已清理');
  }
}
