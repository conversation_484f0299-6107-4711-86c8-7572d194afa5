import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ScheduleModule } from '@nestjs/schedule';

// 边缘节点核心模块
import { EdgeInstanceModule } from './modules/edge-instance.module';
import { EdgeCacheModule } from './modules/edge-cache.module';
import { EdgeSyncModule } from './modules/edge-sync.module';
import { EdgeWebRTCModule } from './modules/edge-webrtc.module';
import { EdgeMonitoringModule } from './modules/edge-monitoring.module';

// 服务
import { EdgeNodeRegistrationService } from './services/edge-node-registration.service';
import { EdgeLoadBalancerService } from './services/edge-load-balancer.service';
import { EdgeHealthCheckService } from './services/edge-health-check.service';

// 控制器
import { EdgeGameServerController } from './controllers/edge-game-server.controller';
import { EdgeHealthController } from './controllers/edge-health.controller';

/**
 * 边缘游戏服务器主模块
 * 轻量级设计，仅包含边缘节点必需的功能
 */
@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.edge', '.env.local', '.env'],
    }),
    
    // 事件发射器模块（轻量级配置）
    EventEmitterModule.forRoot({
      wildcard: true,
      delimiter: '.',
      maxListeners: 10, // 边缘节点减少监听器数量
      verboseMemoryLeak: false, // 关闭详细内存泄漏检查以节省资源
    }),
    
    // 定时任务模块（用于健康检查和数据同步）
    ScheduleModule.forRoot(),
    
    // 微服务客户端模块（仅连接必要的中心服务）
    ClientsModule.registerAsync([
      {
        name: 'CENTRAL_HUB',
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('CENTRAL_HUB_HOST', 'central-hub'),
            port: configService.get<number>('CENTRAL_HUB_PORT', 3000),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: 'EDGE_REGISTRY',
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('EDGE_REGISTRY_HOST', 'edge-registry'),
            port: configService.get<number>('EDGE_REGISTRY_PORT', 3010),
          },
        }),
        inject: [ConfigService],
      },
    ]),
    
    // 边缘节点功能模块
    EdgeInstanceModule,      // 轻量级实例管理
    EdgeCacheModule,         // 边缘缓存
    EdgeSyncModule,          // 数据同步
    EdgeWebRTCModule,        // WebRTC通信
    EdgeMonitoringModule,    // 监控和指标
  ],
  
  controllers: [
    EdgeGameServerController,
    EdgeHealthController,
  ],
  
  providers: [
    EdgeNodeRegistrationService,
    EdgeLoadBalancerService,
    EdgeHealthCheckService,
  ],
  
  exports: [
    EdgeNodeRegistrationService,
    EdgeLoadBalancerService,
    EdgeHealthCheckService,
  ],
})
export class EdgeGameServerModule {
  constructor(
    private readonly configService: ConfigService,
    private readonly healthCheckService: EdgeHealthCheckService,
  ) {
    // 启动时初始化边缘节点
    this.initializeEdgeNode();
  }
  
  private async initializeEdgeNode(): Promise<void> {
    const logger = new (await import('@nestjs/common')).Logger('EdgeGameServerModule');
    
    try {
      // 启动健康检查
      await this.healthCheckService.startHealthChecks();
      
      logger.log('边缘游戏服务器模块初始化完成');
    } catch (error) {
      logger.error('边缘游戏服务器模块初始化失败:', error);
      throw error;
    }
  }
}
