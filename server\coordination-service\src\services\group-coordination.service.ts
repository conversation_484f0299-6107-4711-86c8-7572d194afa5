/**
 * 群体协调服务
 * 
 * 提供大规模群体行为的协调和管理功能。
 * 支持群体形成、角色分配、冲突解决、资源调度等功能。
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import Redis from 'ioredis';
import {
  GroupInfo,
  SocialEntityDetail,
  InteractionRecord,
  SocialRole
} from '../../../engine/src/ai/perception/SocialPerceptionSystem';

/**
 * 协调任务类型
 */
export enum CoordinationTaskType {
  GROUP_FORMATION = 'group_formation',
  ROLE_ASSIGNMENT = 'role_assignment',
  CONFLICT_RESOLUTION = 'conflict_resolution',
  RESOURCE_ALLOCATION = 'resource_allocation',
  TASK_DISTRIBUTION = 'task_distribution',
  COMMUNICATION_RELAY = 'communication_relay'
}

/**
 * 协调任务
 */
export interface CoordinationTask {
  id: string;
  type: CoordinationTaskType;
  priority: number;
  entityIds: string[];
  groupId?: string;
  parameters: { [key: string]: any };
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: number;
  updatedAt: number;
  deadline?: number;
  result?: any;
}

/**
 * 群体协调策略
 */
export interface CoordinationStrategy {
  name: string;
  description: string;
  applicableTaskTypes: CoordinationTaskType[];
  execute(task: CoordinationTask, context: CoordinationContext): Promise<any>;
}

/**
 * 协调上下文
 */
export interface CoordinationContext {
  entities: Map<string, SocialEntityDetail>;
  groups: Map<string, GroupInfo>;
  activeConflicts: ConflictInfo[];
  availableResources: ResourceInfo[];
  environmentState: any;
  timestamp: number;
}

/**
 * 冲突信息
 */
export interface ConflictInfo {
  id: string;
  type: string;
  participants: string[];
  severity: number;
  description: string;
  startTime: number;
  location?: { x: number; y: number; z: number };
  resolutionAttempts: number;
}

/**
 * 资源信息
 */
export interface ResourceInfo {
  id: string;
  type: string;
  capacity: number;
  currentUsage: number;
  location: { x: number; y: number; z: number };
  accessRequirements: string[];
  priority: number;
}

/**
 * 协调结果
 */
export interface CoordinationResult {
  taskId: string;
  success: boolean;
  result: any;
  affectedEntities: string[];
  executionTime: number;
  recommendations: string[];
  followUpTasks: CoordinationTask[];
}

/**
 * 群体协调服务
 */
@Injectable()
export class GroupCoordinationService {
  private readonly logger = new Logger(GroupCoordinationService.name);
  private readonly redis: Redis;
  
  private coordinationTasks = new Map<string, CoordinationTask>();
  private strategies = new Map<string, CoordinationStrategy>();
  private activeConflicts = new Map<string, ConflictInfo>();
  private resources = new Map<string, ResourceInfo>();
  
  private isProcessing = false;
  private processingQueue: CoordinationTask[] = [];
  private maxConcurrentTasks = 10;
  private currentActiveTasks = 0;

  constructor(
    private readonly eventEmitter: EventEmitter2,
    redisConfig: any
  ) {
    this.redis = new Redis(redisConfig);
    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private async initializeService(): Promise<void> {
    try {
      // 初始化协调策略
      this.initializeStrategies();
      
      // 设置消息监听
      await this.setupMessageHandlers();
      
      // 启动处理循环
      this.startProcessingLoop();
      
      this.logger.log('群体协调服务已启动');
      
    } catch (error) {
      this.logger.error('服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 初始化协调策略
   */
  private initializeStrategies(): void {
    // 群体形成策略
    this.strategies.set('proximity_grouping', {
      name: '邻近分组',
      description: '基于空间邻近性形成群体',
      applicableTaskTypes: [CoordinationTaskType.GROUP_FORMATION],
      execute: this.executeProximityGrouping.bind(this)
    });

    // 角色分配策略
    this.strategies.set('capability_based_assignment', {
      name: '能力导向分配',
      description: '基于实体能力分配角色',
      applicableTaskTypes: [CoordinationTaskType.ROLE_ASSIGNMENT],
      execute: this.executeCapabilityBasedAssignment.bind(this)
    });

    // 冲突解决策略
    this.strategies.set('mediation_resolution', {
      name: '调解解决',
      description: '通过第三方调解解决冲突',
      applicableTaskTypes: [CoordinationTaskType.CONFLICT_RESOLUTION],
      execute: this.executeMediationResolution.bind(this)
    });

    // 资源分配策略
    this.strategies.set('fair_allocation', {
      name: '公平分配',
      description: '基于需求和优先级公平分配资源',
      applicableTaskTypes: [CoordinationTaskType.RESOURCE_ALLOCATION],
      execute: this.executeFairAllocation.bind(this)
    });

    this.logger.log(`已初始化 ${this.strategies.size} 个协调策略`);
  }

  /**
   * 设置消息处理器
   */
  private async setupMessageHandlers(): Promise<void> {
    // 监听协调请求
    this.redis.subscribe('coordination:requests');
    this.redis.subscribe('coordination:conflicts');
    this.redis.subscribe('coordination:resources');
    
    this.redis.on('message', async (channel, message) => {
      try {
        const data = JSON.parse(message);
        
        switch (channel) {
          case 'coordination:requests':
            await this.handleCoordinationRequest(data);
            break;
          case 'coordination:conflicts':
            await this.handleConflictReport(data);
            break;
          case 'coordination:resources':
            await this.handleResourceUpdate(data);
            break;
        }
      } catch (error) {
        this.logger.error('消息处理失败:', error);
      }
    });
  }

  /**
   * 启动处理循环
   */
  private startProcessingLoop(): void {
    setInterval(async () => {
      if (!this.isProcessing && this.processingQueue.length > 0 && 
          this.currentActiveTasks < this.maxConcurrentTasks) {
        await this.processNextTask();
      }
    }, 100); // 100ms间隔
  }

  /**
   * 创建协调任务
   */
  public async createCoordinationTask(
    type: CoordinationTaskType,
    entityIds: string[],
    parameters: any,
    priority: number = 1,
    deadline?: number
  ): Promise<string> {
    const task: CoordinationTask = {
      id: `coord_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      priority,
      entityIds,
      parameters,
      status: 'pending',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      deadline
    };

    this.coordinationTasks.set(task.id, task);
    this.processingQueue.push(task);
    
    // 按优先级排序
    this.processingQueue.sort((a, b) => b.priority - a.priority);
    
    this.eventEmitter.emit('coordination.task.created', {
      taskId: task.id,
      type: task.type,
      entityCount: entityIds.length
    });
    
    this.logger.log(`创建协调任务: ${task.id} (${type})`);
    return task.id;
  }

  /**
   * 处理下一个任务
   */
  private async processNextTask(): Promise<void> {
    if (this.processingQueue.length === 0) return;
    
    this.isProcessing = true;
    this.currentActiveTasks++;
    
    const task = this.processingQueue.shift()!;
    
    try {
      task.status = 'processing';
      task.updatedAt = Date.now();
      
      const result = await this.executeCoordinationTask(task);
      
      task.status = 'completed';
      task.result = result;
      task.updatedAt = Date.now();
      
      this.eventEmitter.emit('coordination.task.completed', {
        taskId: task.id,
        result,
        executionTime: task.updatedAt - task.createdAt
      });
      
      this.logger.log(`协调任务完成: ${task.id}`);
      
    } catch (error) {
      task.status = 'failed';
      task.result = { error: error.message };
      task.updatedAt = Date.now();
      
      this.eventEmitter.emit('coordination.task.failed', {
        taskId: task.id,
        error: error.message
      });
      
      this.logger.error(`协调任务失败 [${task.id}]:`, error);
      
    } finally {
      this.currentActiveTasks--;
      this.isProcessing = false;
    }
  }

  /**
   * 执行协调任务
   */
  private async executeCoordinationTask(task: CoordinationTask): Promise<CoordinationResult> {
    const startTime = Date.now();
    
    // 构建协调上下文
    const context = await this.buildCoordinationContext(task);
    
    // 选择合适的策略
    const strategy = this.selectStrategy(task);
    if (!strategy) {
      throw new Error(`未找到适用于任务类型 ${task.type} 的策略`);
    }
    
    // 执行策略
    const result = await strategy.execute(task, context);
    
    // 构建协调结果
    const coordinationResult: CoordinationResult = {
      taskId: task.id,
      success: true,
      result,
      affectedEntities: task.entityIds,
      executionTime: Date.now() - startTime,
      recommendations: [],
      followUpTasks: []
    };
    
    // 发布结果
    await this.publishCoordinationResult(coordinationResult);
    
    return coordinationResult;
  }

  /**
   * 构建协调上下文
   */
  private async buildCoordinationContext(task: CoordinationTask): Promise<CoordinationContext> {
    // 获取相关实体信息
    const entities = new Map<string, SocialEntityDetail>();
    for (const entityId of task.entityIds) {
      const entityData = await this.getEntityData(entityId);
      if (entityData) {
        entities.set(entityId, entityData);
      }
    }
    
    // 获取群体信息
    const groups = await this.getRelevantGroups(task.entityIds);
    
    return {
      entities,
      groups,
      activeConflicts: Array.from(this.activeConflicts.values()),
      availableResources: Array.from(this.resources.values()),
      environmentState: await this.getEnvironmentState(),
      timestamp: Date.now()
    };
  }

  /**
   * 选择协调策略
   */
  private selectStrategy(task: CoordinationTask): CoordinationStrategy | null {
    for (const strategy of this.strategies.values()) {
      if (strategy.applicableTaskTypes.includes(task.type)) {
        return strategy;
      }
    }
    return null;
  }

  /**
   * 执行邻近分组策略
   */
  private async executeProximityGrouping(
    task: CoordinationTask, 
    context: CoordinationContext
  ): Promise<any> {
    const entities = Array.from(context.entities.values());
    const groups: string[][] = [];
    const processed = new Set<string>();
    const maxDistance = task.parameters.maxDistance || 10;
    
    for (const entity of entities) {
      if (processed.has(entity.id)) continue;
      
      const group = [entity.id];
      processed.add(entity.id);
      
      // 查找邻近实体
      for (const other of entities) {
        if (processed.has(other.id)) continue;
        
        const distance = this.calculateDistance(entity.position, other.position);
        if (distance <= maxDistance) {
          group.push(other.id);
          processed.add(other.id);
        }
      }
      
      if (group.length >= (task.parameters.minGroupSize || 2)) {
        groups.push(group);
      }
    }
    
    // 创建群体
    const createdGroups = [];
    for (const group of groups) {
      const groupId = await this.createGroup(group, 'proximity_based');
      createdGroups.push(groupId);
    }
    
    return {
      groupsCreated: createdGroups.length,
      groups: createdGroups,
      totalEntitiesGrouped: groups.reduce((sum, group) => sum + group.length, 0)
    };
  }

  /**
   * 执行基于能力的角色分配策略
   */
  private async executeCapabilityBasedAssignment(
    task: CoordinationTask,
    context: CoordinationContext
  ): Promise<any> {
    const assignments: { [entityId: string]: SocialRole } = {};
    const requiredRoles = task.parameters.requiredRoles || [
      SocialRole.LEADER,
      SocialRole.PARTICIPANT
    ];
    
    const entities = Array.from(context.entities.values());
    
    // 分配领导者
    if (requiredRoles.includes(SocialRole.LEADER)) {
      const leader = entities.reduce((best, current) => {
        const currentLeadership = current.socialSkills?.leadership || 0;
        const bestLeadership = best.socialSkills?.leadership || 0;
        return currentLeadership > bestLeadership ? current : best;
      });
      
      assignments[leader.id] = SocialRole.LEADER;
    }
    
    // 分配其他角色
    for (const entity of entities) {
      if (!assignments[entity.id]) {
        assignments[entity.id] = SocialRole.PARTICIPANT;
      }
    }
    
    // 应用角色分配
    for (const [entityId, role] of Object.entries(assignments)) {
      await this.assignRole(entityId, role);
    }
    
    return {
      assignments,
      rolesAssigned: Object.keys(assignments).length
    };
  }

  /**
   * 执行调解解决策略
   */
  private async executeMediationResolution(
    task: CoordinationTask,
    context: CoordinationContext
  ): Promise<any> {
    const conflictId = task.parameters.conflictId;
    const conflict = this.activeConflicts.get(conflictId);
    
    if (!conflict) {
      throw new Error(`冲突 ${conflictId} 不存在`);
    }
    
    // 选择调解者
    const mediator = await this.selectMediator(conflict, context);
    if (!mediator) {
      throw new Error('无法找到合适的调解者');
    }
    
    // 执行调解过程
    const resolution = await this.performMediation(conflict, mediator, context);
    
    // 更新冲突状态
    if (resolution.success) {
      this.activeConflicts.delete(conflictId);
    } else {
      conflict.resolutionAttempts++;
    }
    
    return resolution;
  }

  /**
   * 执行公平分配策略
   */
  private async executeFairAllocation(
    task: CoordinationTask,
    context: CoordinationContext
  ): Promise<any> {
    const resourceType = task.parameters.resourceType;
    const requestedAmount = task.parameters.amount;
    
    const availableResources = context.availableResources.filter(r => r.type === resourceType);
    const totalAvailable = availableResources.reduce((sum, r) => sum + (r.capacity - r.currentUsage), 0);
    
    if (totalAvailable < requestedAmount) {
      throw new Error(`资源不足: 需要 ${requestedAmount}, 可用 ${totalAvailable}`);
    }
    
    // 计算每个实体的分配量
    const entityCount = task.entityIds.length;
    const allocationPerEntity = Math.floor(requestedAmount / entityCount);
    const remainder = requestedAmount % entityCount;
    
    const allocations: { [entityId: string]: number } = {};
    
    // 基础分配
    for (const entityId of task.entityIds) {
      allocations[entityId] = allocationPerEntity;
    }
    
    // 分配余量（基于优先级）
    const entitiesByPriority = task.entityIds
      .map(id => ({ id, priority: context.entities.get(id)?.trustLevel || 0 }))
      .sort((a, b) => b.priority - a.priority);
    
    for (let i = 0; i < remainder; i++) {
      allocations[entitiesByPriority[i].id]++;
    }
    
    // 执行分配
    for (const [entityId, amount] of Object.entries(allocations)) {
      await this.allocateResource(entityId, resourceType, amount);
    }
    
    return {
      allocations,
      totalAllocated: requestedAmount,
      resourceType
    };
  }

  /**
   * 计算距离
   */
  private calculateDistance(pos1: any, pos2: any): number {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    const dz = pos1.z - pos2.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }

  /**
   * 创建群体
   */
  private async createGroup(memberIds: string[], type: string): Promise<string> {
    const groupId = `group_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const groupInfo: GroupInfo = {
      id: groupId,
      name: `${type}_group`,
      members: memberIds,
      leader: memberIds[0], // 简化：第一个成员为领导者
      type,
      cohesion: 0.5,
      productivity: 0.5,
      conflictLevel: 0,
      communicationPattern: 'open',
      decisionMakingStyle: 'consensus',
      norms: []
    };
    
    // 存储群体信息
    await this.redis.setex(
      `coordination:group:${groupId}`,
      3600, // 1小时过期
      JSON.stringify(groupInfo)
    );
    
    return groupId;
  }

  /**
   * 分配角色
   */
  private async assignRole(entityId: string, role: SocialRole): Promise<void> {
    await this.redis.setex(
      `coordination:role:${entityId}`,
      3600,
      role
    );
    
    this.eventEmitter.emit('coordination.role.assigned', {
      entityId,
      role
    });
  }

  /**
   * 选择调解者
   */
  private async selectMediator(
    conflict: ConflictInfo,
    context: CoordinationContext
  ): Promise<SocialEntityDetail | null> {
    const candidates = Array.from(context.entities.values())
      .filter(entity => !conflict.participants.includes(entity.id))
      .filter(entity => entity.socialSkills?.conflictResolution > 0.6);
    
    if (candidates.length === 0) return null;
    
    return candidates.reduce((best, current) => {
      const currentScore = current.socialSkills.conflictResolution + current.trustLevel;
      const bestScore = best.socialSkills.conflictResolution + best.trustLevel;
      return currentScore > bestScore ? current : best;
    });
  }

  /**
   * 执行调解
   */
  private async performMediation(
    conflict: ConflictInfo,
    mediator: SocialEntityDetail,
    context: CoordinationContext
  ): Promise<any> {
    // 简化的调解过程
    const mediationSuccess = mediator.socialSkills.conflictResolution > 0.7;
    
    return {
      success: mediationSuccess,
      mediatorId: mediator.id,
      resolution: mediationSuccess ? '冲突已通过调解解决' : '调解失败，需要其他解决方案',
      participants: conflict.participants
    };
  }

  /**
   * 分配资源
   */
  private async allocateResource(entityId: string, resourceType: string, amount: number): Promise<void> {
    const allocation = {
      entityId,
      resourceType,
      amount,
      timestamp: Date.now()
    };
    
    await this.redis.setex(
      `coordination:allocation:${entityId}:${resourceType}`,
      3600,
      JSON.stringify(allocation)
    );
    
    this.eventEmitter.emit('coordination.resource.allocated', allocation);
  }

  /**
   * 获取实体数据
   */
  private async getEntityData(entityId: string): Promise<SocialEntityDetail | null> {
    const dataStr = await this.redis.get(`social:entity:${entityId}`);
    return dataStr ? JSON.parse(dataStr) : null;
  }

  /**
   * 获取相关群体
   */
  private async getRelevantGroups(entityIds: string[]): Promise<Map<string, GroupInfo>> {
    const groups = new Map<string, GroupInfo>();
    
    // 简化实现：返回空Map
    // 实际应该查询包含这些实体的群体
    
    return groups;
  }

  /**
   * 获取环境状态
   */
  private async getEnvironmentState(): Promise<any> {
    // 简化实现：返回基础环境状态
    return {
      timestamp: Date.now(),
      activeEntities: await this.redis.scard('active:entities'),
      systemLoad: Math.random()
    };
  }

  /**
   * 发布协调结果
   */
  private async publishCoordinationResult(result: CoordinationResult): Promise<void> {
    await this.redis.publish('coordination:results', JSON.stringify(result));
  }

  /**
   * 处理协调请求
   */
  private async handleCoordinationRequest(data: any): Promise<void> {
    const { type, entityIds, parameters, priority, deadline } = data;
    
    try {
      const taskId = await this.createCoordinationTask(
        type,
        entityIds,
        parameters,
        priority,
        deadline
      );
      
      this.logger.log(`处理协调请求: ${taskId}`);
      
    } catch (error) {
      this.logger.error('处理协调请求失败:', error);
    }
  }

  /**
   * 处理冲突报告
   */
  private async handleConflictReport(data: any): Promise<void> {
    const conflict = data as ConflictInfo;
    this.activeConflicts.set(conflict.id, conflict);
    
    // 自动创建冲突解决任务
    await this.createCoordinationTask(
      CoordinationTaskType.CONFLICT_RESOLUTION,
      conflict.participants,
      { conflictId: conflict.id },
      3 // 高优先级
    );
    
    this.logger.log(`冲突报告: ${conflict.id}`);
  }

  /**
   * 处理资源更新
   */
  private async handleResourceUpdate(data: any): Promise<void> {
    const resource = data as ResourceInfo;
    this.resources.set(resource.id, resource);
    
    this.logger.debug(`资源更新: ${resource.id}`);
  }

  /**
   * 获取任务状态
   */
  public getTaskStatus(taskId: string): CoordinationTask | null {
    return this.coordinationTasks.get(taskId) || null;
  }

  /**
   * 获取活跃冲突
   */
  public getActiveConflicts(): ConflictInfo[] {
    return Array.from(this.activeConflicts.values());
  }

  /**
   * 获取可用资源
   */
  public getAvailableResources(): ResourceInfo[] {
    return Array.from(this.resources.values());
  }

  /**
   * 定期清理过期任务
   */
  @Cron(CronExpression.EVERY_HOUR)
  private async cleanupExpiredTasks(): Promise<void> {
    try {
      const now = Date.now();
      const expiredTasks: string[] = [];
      
      for (const [taskId, task] of this.coordinationTasks) {
        // 清理24小时前的已完成任务
        if (task.status === 'completed' && now - task.updatedAt > 24 * 60 * 60 * 1000) {
          expiredTasks.push(taskId);
        }
        
        // 清理超过截止时间的任务
        if (task.deadline && now > task.deadline && task.status === 'pending') {
          task.status = 'failed';
          task.result = { error: '任务超时' };
          task.updatedAt = now;
        }
      }
      
      // 删除过期任务
      expiredTasks.forEach(taskId => this.coordinationTasks.delete(taskId));
      
      this.logger.log(`清理了 ${expiredTasks.length} 个过期任务`);
      
    } catch (error) {
      this.logger.error('任务清理失败:', error);
    }
  }

  /**
   * 关闭服务
   */
  public async shutdown(): Promise<void> {
    this.logger.log('正在关闭群体协调服务...');
    
    // 等待当前任务完成
    while (this.currentActiveTasks > 0) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // 关闭Redis连接
    this.redis.disconnect();
    
    this.logger.log('群体协调服务已关闭');
  }
}
