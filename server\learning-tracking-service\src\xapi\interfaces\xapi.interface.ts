/**
 * xAPI 2.0 核心接口定义
 * 学习记录跟踪系统的数据模型
 */

// 语言映射接口
export interface LanguageMap {
  [languageCode: string]: string;
}

// 扩展数据接口
export interface Extensions {
  [iri: string]: any;
}

// 账户信息接口
export interface Account {
  homePage: string;
  name: string;
}

// 代理人接口（学习者或组织）
export interface Agent {
  objectType?: 'Agent';
  name?: string;
  mbox?: string;                 // 邮箱标识
  mbox_sha1sum?: string;         // 邮箱SHA1哈希
  openid?: string;               // OpenID标识
  account?: Account;             // 账户信息
}

// 组接口
export interface Group extends Agent {
  objectType: 'Group';
  member?: Agent[];              // 组成员
}

// 行为者接口（代理人或组）
export type Actor = Agent | Group;

// 动词接口
export interface Verb {
  id: string;                    // 动词IRI
  display?: LanguageMap;         // 多语言显示名称
}

// 分数接口
export interface Score {
  scaled?: number;               // 标准化分数 (-1 到 1)
  raw?: number;                  // 原始分数
  min?: number;                  // 最小分数
  max?: number;                  // 最大分数
}

// 活动定义接口
export interface ActivityDefinition {
  name?: LanguageMap;            // 活动名称
  description?: LanguageMap;     // 活动描述
  type?: string;                 // 活动类型IRI
  moreInfo?: string;             // 更多信息URL
  extensions?: Extensions;       // 扩展数据
  interactionType?: string;      // 交互类型
  correctResponsesPattern?: string[]; // 正确答案模式
  choices?: InteractionComponent[]; // 选择项
  scale?: InteractionComponent[];   // 量表
  source?: InteractionComponent[];  // 源项
  target?: InteractionComponent[];  // 目标项
  steps?: InteractionComponent[];   // 步骤
}

// 交互组件接口
export interface InteractionComponent {
  id: string;
  description?: LanguageMap;
}

// 活动对象接口
export interface Activity {
  objectType?: 'Activity';
  id: string;                    // 活动IRI
  definition?: ActivityDefinition;
}

// 语句引用接口
export interface StatementRef {
  objectType: 'StatementRef';
  id: string;                    // 被引用语句的UUID
}

// 子语句接口
export interface SubStatement {
  objectType: 'SubStatement';
  actor: Actor;
  verb: Verb;
  object: Activity | Agent | Group | StatementRef;
  result?: Result;
  context?: Context;
  timestamp?: string;
}

// 语句对象类型
export type StatementObject = Activity | Agent | Group | StatementRef | SubStatement;

// 结果接口
export interface Result {
  score?: Score;                 // 分数
  success?: boolean;             // 是否成功
  completion?: boolean;          // 是否完成
  response?: string;             // 响应内容
  duration?: string;             // 持续时间 (ISO 8601)
  extensions?: Extensions;       // 扩展数据
}

// 上下文活动接口
export interface ContextActivities {
  parent?: Activity[];           // 父活动
  grouping?: Activity[];         // 分组活动
  category?: Activity[];         // 分类活动
  other?: Activity[];            // 其他活动
}

// 上下文接口
export interface Context {
  registration?: string;         // 注册UUID
  instructor?: Actor;            // 指导者
  team?: Group;                  // 团队
  contextActivities?: ContextActivities; // 上下文活动
  revision?: string;             // 修订版本
  platform?: string;            // 平台信息
  language?: string;             // 语言代码
  statement?: StatementRef;      // 相关语句引用
  extensions?: Extensions;       // 扩展数据
}

// xAPI语句接口
export interface XAPIStatement {
  id?: string;                   // 语句UUID
  actor: Actor;                  // 行为者
  verb: Verb;                    // 动词
  object: StatementObject;       // 对象
  result?: Result;               // 结果
  context?: Context;             // 上下文
  timestamp?: string;            // 时间戳 (ISO 8601)
  stored?: string;               // 存储时间戳
  authority?: Actor;             // 权威机构
  version?: string;              // xAPI版本
  attachments?: Attachment[];    // 附件
}

// 附件接口
export interface Attachment {
  usageType: string;             // 使用类型IRI
  display: LanguageMap;          // 显示名称
  description?: LanguageMap;     // 描述
  contentType: string;           // MIME类型
  length: number;                // 字节长度
  sha2: string;                  // SHA-2哈希
  fileUrl?: string;              // 文件URL
}

// 语句结果接口
export interface StatementResult {
  statements: XAPIStatement[];   // 语句数组
  more?: string;                 // 更多结果的URL
}

// 查询参数接口
export interface StatementQuery {
  statementId?: string;          // 语句ID
  voidedStatementId?: string;    // 被撤销的语句ID
  agent?: Actor;                 // 代理人
  verb?: string;                 // 动词IRI
  activity?: string;             // 活动IRI
  registration?: string;         // 注册ID
  related_activities?: boolean;  // 包含相关活动
  related_agents?: boolean;      // 包含相关代理人
  since?: string;                // 起始时间
  until?: string;                // 结束时间
  limit?: number;                // 限制数量
  format?: 'ids' | 'exact' | 'canonical'; // 格式
  attachments?: boolean;         // 包含附件
  ascending?: boolean;           // 升序排列
}

// 学习动作词汇常量
export const LEARNING_VERBS = {
  // 基础学习动作
  EXPERIENCED: 'http://adlnet.gov/expapi/verbs/experienced',
  ATTENDED: 'http://adlnet.gov/expapi/verbs/attended',
  ATTEMPTED: 'http://adlnet.gov/expapi/verbs/attempted',
  COMPLETED: 'http://adlnet.gov/expapi/verbs/completed',
  PASSED: 'http://adlnet.gov/expapi/verbs/passed',
  FAILED: 'http://adlnet.gov/expapi/verbs/failed',
  ANSWERED: 'http://adlnet.gov/expapi/verbs/answered',
  ASKED: 'http://adlnet.gov/expapi/verbs/asked',
  
  // 交互动作
  INTERACTED: 'http://adlnet.gov/expapi/verbs/interacted',
  RESPONDED: 'http://adlnet.gov/expapi/verbs/responded',
  COMMENTED: 'http://adlnet.gov/expapi/verbs/commented',
  SHARED: 'http://adlnet.gov/expapi/verbs/shared',
  
  // 数字人交互专用动作
  TALKED_WITH_AVATAR: 'http://dl-engine.com/xapi/verbs/talked-with-avatar',
  ASKED_AVATAR: 'http://dl-engine.com/xapi/verbs/asked-avatar',
  RECEIVED_RECOMMENDATION: 'http://dl-engine.com/xapi/verbs/received-recommendation',
  FOLLOWED_PATH: 'http://dl-engine.com/xapi/verbs/followed-path',
  EXPLORED_SCENE: 'http://dl-engine.com/xapi/verbs/explored-scene',
  
  // 情感相关动作
  EXPRESSED_EMOTION: 'http://dl-engine.com/xapi/verbs/expressed-emotion',
  SHOWED_INTEREST: 'http://dl-engine.com/xapi/verbs/showed-interest',
  SHOWED_CONFUSION: 'http://dl-engine.com/xapi/verbs/showed-confusion',
  
  // 学习进度动作
  STARTED_LEARNING: 'http://dl-engine.com/xapi/verbs/started-learning',
  PAUSED_LEARNING: 'http://dl-engine.com/xapi/verbs/paused-learning',
  RESUMED_LEARNING: 'http://dl-engine.com/xapi/verbs/resumed-learning',
  MASTERED_CONCEPT: 'http://dl-engine.com/xapi/verbs/mastered-concept',
  STRUGGLED_WITH: 'http://dl-engine.com/xapi/verbs/struggled-with'
} as const;

// 扩展字段常量
export const DL_ENGINE_EXTENSIONS = {
  QUESTION: 'http://dl-engine.com/xapi/extensions/question',
  EMOTION: 'http://dl-engine.com/xapi/extensions/emotion',
  KNOWLEDGE_AREA: 'http://dl-engine.com/xapi/extensions/knowledge-area',
  STOPPING_POINTS: 'http://dl-engine.com/xapi/extensions/stopping-points',
  DIFFICULTY: 'http://dl-engine.com/xapi/extensions/difficulty',
  CONTENT_TYPE: 'http://dl-engine.com/xapi/extensions/content-type',
  DECISION_TIME: 'http://dl-engine.com/xapi/extensions/decision-time',
  TRIGGER: 'http://dl-engine.com/xapi/extensions/trigger',
  CONTEXT: 'http://dl-engine.com/xapi/extensions/context',
  SESSION_ID: 'http://dl-engine.com/xapi/extensions/session-id',
  AVATAR_ID: 'http://dl-engine.com/xapi/extensions/avatar-id',
  SCENE_ID: 'http://dl-engine.com/xapi/extensions/scene-id'
} as const;
