{"name": "knowledge-base-service", "version": "1.0.0", "description": "DL引擎知识库服务", "author": "DL Engine Team", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/microservices": "^10.0.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/event-emitter": "^2.0.0", "@nestjs/bull": "^10.0.0", "typeorm": "^0.3.17", "mysql2": "^3.6.0", "redis": "^4.6.0", "bull": "^4.11.0", "multer": "^1.4.5-lts.1", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0", "xlsx": "^0.18.5", "pptx-parser": "^1.0.0", "chromadb": "^1.7.0", "@tensorflow/tfjs-node": "^4.10.0", "@xenova/transformers": "^2.6.0", "jieba": "^0.3.2", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "uuid": "^9.0.0", "crypto": "^1.0.1", "fs-extra": "^11.1.1", "path": "^0.12.7", "mime-types": "^2.1.35", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@types/multer": "^1.4.7", "@types/uuid": "^9.0.2", "@types/fs-extra": "^11.0.1", "@types/mime-types": "^2.1.1", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}