import { 
  Entity, 
  PrimaryGeneratedColumn, 
  Column, 
  CreateDateColumn, 
  UpdateDateColumn,
  Index 
} from 'typeorm';

/**
 * 推荐记录实体
 * 存储个性化推荐记录
 */
@Entity('recommendations')
@Index(['userId', 'status'])
@Index(['knowledgeArea'])
@Index(['createdAt'])
export class RecommendationEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  @Index()
  userId: string;

  @Column({ name: 'content_id' })
  @Index()
  contentId: string;

  @Column({ 
    type: 'enum', 
    enum: ['concept', 'exercise', 'video', 'article', 'interactive', 'path', 'resource']
  })
  type: string;

  @Column()
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ name: 'relevance_score', type: 'decimal', precision: 3, scale: 2 })
  relevanceScore: number;

  @Column({ 
    type: 'enum', 
    enum: ['easy', 'medium', 'hard']
  })
  difficulty: string;

  @Column({ name: 'estimated_duration' })
  estimatedDuration: number;

  @Column({ type: 'text' })
  reason: string;

  @Column({ type: 'text' })
  tags: string;

  @Column({ name: 'knowledge_area' })
  @Index()
  knowledgeArea: string;

  @Column({ type: 'text' })
  prerequisites: string;

  @Column({ name: 'learning_objectives', type: 'text' })
  learningObjectives: string;

  @Column({ 
    type: 'enum', 
    enum: ['pending', 'accepted', 'rejected', 'ignored', 'completed'],
    default: 'pending'
  })
  @Index()
  status: string;

  @Column({ name: 'feedback_rating', nullable: true })
  feedbackRating: number;

  @Column({ name: 'feedback_comment', type: 'text', nullable: true })
  feedbackComment: string;

  @Column({ name: 'expires_at', type: 'datetime', nullable: true })
  expiresAt: Date;

  @Column({ name: 'viewed_at', type: 'datetime', nullable: true })
  viewedAt: Date;

  @Column({ name: 'accepted_at', type: 'datetime', nullable: true })
  acceptedAt: Date;

  @Column({ name: 'completed_at', type: 'datetime', nullable: true })
  completedAt: Date;

  @Column({ type: 'text' })
  metadata: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
