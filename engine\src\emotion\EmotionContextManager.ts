/**
 * 情感上下文管理系统
 * 管理长期情感记忆和上下文感知的情感状态
 */

import { EventEmitter } from '../utils/EventEmitter';

/**
 * 情感上下文类型
 */
export enum EmotionContextType {
  CONVERSATION = 'conversation',
  SESSION = 'session',
  RELATIONSHIP = 'relationship',
  ENVIRONMENT = 'environment',
  TASK = 'task',
}

/**
 * 情感状态记录
 */
export interface EmotionStateRecord {
  /** 记录ID */
  id: string;
  /** 用户ID */
  userId: string;
  /** 情感类型 */
  emotionType: string;
  /** 情感强度 */
  intensity: number;
  /** 持续时间（毫秒） */
  duration: number;
  /** 触发原因 */
  trigger: string;
  /** 上下文类型 */
  contextType: EmotionContextType;
  /** 上下文数据 */
  contextData: any;
  /** 时间戳 */
  timestamp: number;
  /** 衰减因子 */
  decayFactor: number;
}

/**
 * 情感模式
 */
export interface EmotionPattern {
  /** 模式ID */
  id: string;
  /** 模式名称 */
  name: string;
  /** 情感序列 */
  emotionSequence: string[];
  /** 时间间隔模式 */
  timeIntervals: number[];
  /** 强度模式 */
  intensityPattern: number[];
  /** 出现频率 */
  frequency: number;
  /** 置信度 */
  confidence: number;
  /** 最后出现时间 */
  lastOccurrence: number;
}

/**
 * 情感预测结果
 */
export interface EmotionPrediction {
  /** 预测的情感类型 */
  predictedEmotion: string;
  /** 预测强度 */
  predictedIntensity: number;
  /** 预测置信度 */
  confidence: number;
  /** 预测时间范围 */
  timeRange: {
    start: number;
    end: number;
  };
  /** 基于的模式 */
  basedOnPattern: string;
  /** 影响因素 */
  influencingFactors: string[];
}

/**
 * 上下文配置
 */
export interface EmotionContextConfig {
  /** 最大记录数量 */
  maxRecords: number;
  /** 记录保留时间（毫秒） */
  recordRetentionTime: number;
  /** 模式检测最小样本数 */
  minPatternSamples: number;
  /** 模式检测置信度阈值 */
  patternConfidenceThreshold: number;
  /** 预测时间窗口（毫秒） */
  predictionTimeWindow: number;
  /** 是否启用情感衰减 */
  enableEmotionDecay: boolean;
  /** 默认衰减因子 */
  defaultDecayFactor: number;
  /** 是否启用模式学习 */
  enablePatternLearning: boolean;
  /** 调试模式 */
  debug: boolean;
}

/**
 * 情感上下文管理器
 */
export class EmotionContextManager {
  /** 配置 */
  private config: EmotionContextConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 情感状态记录 */
  private emotionRecords: Map<string, EmotionStateRecord[]> = new Map();

  /** 检测到的情感模式 */
  private emotionPatterns: Map<string, EmotionPattern[]> = new Map();

  /** 当前情感状态 */
  private currentEmotionStates: Map<string, EmotionStateRecord> = new Map();

  /** 情感预测缓存 */
  private predictionCache: Map<string, EmotionPrediction[]> = new Map();

  /** 最后清理时间 */
  private lastCleanupTime: number = 0;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: Partial<EmotionContextConfig> = {}) {
    this.config = {
      maxRecords: 1000,
      recordRetentionTime: 7 * 24 * 60 * 60 * 1000, // 7天
      minPatternSamples: 5,
      patternConfidenceThreshold: 0.7,
      predictionTimeWindow: 60 * 60 * 1000, // 1小时
      enableEmotionDecay: true,
      defaultDecayFactor: 0.95,
      enablePatternLearning: true,
      debug: false,
      ...config,
    };
  }

  /**
   * 初始化管理器
   */
  public async initialize(): Promise<void> {
    if (this.initialized) return;

    // 加载历史数据
    await this.loadHistoricalData();

    // 启动定期任务
    this.startPeriodicTasks();

    this.initialized = true;

    if (this.config.debug) {
      console.log('情感上下文管理器初始化完成');
    }
  }

  /**
   * 记录情感状态
   * @param record 情感状态记录
   */
  public recordEmotionState(record: Omit<EmotionStateRecord, 'id' | 'timestamp'>): void {
    const fullRecord: EmotionStateRecord = {
      ...record,
      id: this.generateRecordId(),
      timestamp: Date.now(),
    };

    // 获取用户记录列表
    let userRecords = this.emotionRecords.get(record.userId);
    if (!userRecords) {
      userRecords = [];
      this.emotionRecords.set(record.userId, userRecords);
    }

    // 添加记录
    userRecords.push(fullRecord);

    // 限制记录数量
    if (userRecords.length > this.config.maxRecords) {
      userRecords.shift();
    }

    // 更新当前情感状态
    this.currentEmotionStates.set(record.userId, fullRecord);

    // 如果启用模式学习，分析模式
    if (this.config.enablePatternLearning) {
      this.analyzeEmotionPatterns(record.userId);
    }

    // 触发记录事件
    this.eventEmitter.emit('emotionRecorded', fullRecord);

    if (this.config.debug) {
      console.log('记录情感状态:', fullRecord);
    }
  }

  /**
   * 获取当前情感状态
   * @param userId 用户ID
   * @returns 当前情感状态
   */
  public getCurrentEmotionState(userId: string): EmotionStateRecord | null {
    const currentState = this.currentEmotionStates.get(userId);
    
    if (!currentState) return null;

    // 如果启用衰减，计算衰减后的强度
    if (this.config.enableEmotionDecay) {
      const decayedIntensity = this.calculateDecayedIntensity(currentState);
      return {
        ...currentState,
        intensity: decayedIntensity,
      };
    }

    return currentState;
  }

  /**
   * 获取情感历史
   * @param userId 用户ID
   * @param contextType 上下文类型（可选）
   * @param timeRange 时间范围（可选）
   * @returns 情感历史记录
   */
  public getEmotionHistory(
    userId: string,
    contextType?: EmotionContextType,
    timeRange?: { start: number; end: number }
  ): EmotionStateRecord[] {
    const userRecords = this.emotionRecords.get(userId) || [];
    
    let filteredRecords = userRecords;

    // 按上下文类型过滤
    if (contextType) {
      filteredRecords = filteredRecords.filter(record => record.contextType === contextType);
    }

    // 按时间范围过滤
    if (timeRange) {
      filteredRecords = filteredRecords.filter(
        record => record.timestamp >= timeRange.start && record.timestamp <= timeRange.end
      );
    }

    return filteredRecords.sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * 预测情感状态
   * @param userId 用户ID
   * @param contextType 上下文类型
   * @returns 情感预测结果
   */
  public predictEmotionState(
    userId: string,
    contextType: EmotionContextType
  ): EmotionPrediction[] {
    // 检查缓存
    const cacheKey = `${userId}:${contextType}`;
    const cachedPredictions = this.predictionCache.get(cacheKey);
    
    if (cachedPredictions && this.isCacheValid(cachedPredictions)) {
      return cachedPredictions;
    }

    // 获取用户的情感模式
    const userPatterns = this.emotionPatterns.get(userId) || [];
    const predictions: EmotionPrediction[] = [];

    // 基于模式进行预测
    for (const pattern of userPatterns) {
      if (pattern.confidence >= this.config.patternConfidenceThreshold) {
        const prediction = this.generatePredictionFromPattern(pattern, contextType);
        if (prediction) {
          predictions.push(prediction);
        }
      }
    }

    // 基于当前状态进行预测
    const currentState = this.getCurrentEmotionState(userId);
    if (currentState) {
      const statePrediction = this.generatePredictionFromCurrentState(currentState, contextType);
      if (statePrediction) {
        predictions.push(statePrediction);
      }
    }

    // 排序并缓存结果
    predictions.sort((a, b) => b.confidence - a.confidence);
    this.predictionCache.set(cacheKey, predictions);

    return predictions;
  }

  /**
   * 获取情感模式
   * @param userId 用户ID
   * @returns 情感模式列表
   */
  public getEmotionPatterns(userId: string): EmotionPattern[] {
    return this.emotionPatterns.get(userId) || [];
  }

  /**
   * 分析情感趋势
   * @param userId 用户ID
   * @param timeWindow 时间窗口（毫秒）
   * @returns 趋势分析结果
   */
  public analyzeEmotionTrend(
    userId: string,
    timeWindow: number = 24 * 60 * 60 * 1000 // 默认24小时
  ): {
    dominantEmotion: string;
    averageIntensity: number;
    emotionDistribution: Map<string, number>;
    trendDirection: 'increasing' | 'decreasing' | 'stable';
  } {
    const currentTime = Date.now();
    const startTime = currentTime - timeWindow;
    
    const recentRecords = this.getEmotionHistory(userId, undefined, {
      start: startTime,
      end: currentTime,
    });

    if (recentRecords.length === 0) {
      return {
        dominantEmotion: 'neutral',
        averageIntensity: 0,
        emotionDistribution: new Map(),
        trendDirection: 'stable',
      };
    }

    // 计算情感分布
    const emotionCounts = new Map<string, number>();
    let totalIntensity = 0;

    for (const record of recentRecords) {
      const count = emotionCounts.get(record.emotionType) || 0;
      emotionCounts.set(record.emotionType, count + 1);
      totalIntensity += record.intensity;
    }

    // 找出主导情感
    let dominantEmotion = '';
    let maxCount = 0;
    for (const [emotion, count] of emotionCounts) {
      if (count > maxCount) {
        maxCount = count;
        dominantEmotion = emotion;
      }
    }

    // 计算平均强度
    const averageIntensity = totalIntensity / recentRecords.length;

    // 分析趋势方向
    const trendDirection = this.calculateTrendDirection(recentRecords);

    // 计算情感分布百分比
    const emotionDistribution = new Map<string, number>();
    for (const [emotion, count] of emotionCounts) {
      emotionDistribution.set(emotion, count / recentRecords.length);
    }

    return {
      dominantEmotion,
      averageIntensity,
      emotionDistribution,
      trendDirection,
    };
  }

  /**
   * 分析情感模式
   * @param userId 用户ID
   */
  private analyzeEmotionPatterns(userId: string): void {
    const userRecords = this.emotionRecords.get(userId) || [];
    
    if (userRecords.length < this.config.minPatternSamples) {
      return; // 样本数量不足
    }

    // 获取最近的记录进行模式分析
    const recentRecords = userRecords.slice(-20); // 分析最近20条记录
    
    // 检测序列模式
    const patterns = this.detectSequencePatterns(recentRecords);
    
    // 更新用户模式
    this.emotionPatterns.set(userId, patterns);

    if (this.config.debug && patterns.length > 0) {
      console.log(`用户 ${userId} 检测到 ${patterns.length} 个情感模式`);
    }
  }

  /**
   * 检测序列模式
   * @param records 情感记录
   * @returns 检测到的模式
   */
  private detectSequencePatterns(records: EmotionStateRecord[]): EmotionPattern[] {
    const patterns: EmotionPattern[] = [];
    const minSequenceLength = 3;
    const maxSequenceLength = 5;

    // 检测不同长度的序列模式
    for (let length = minSequenceLength; length <= maxSequenceLength; length++) {
      const sequencePatterns = this.findSequencePatterns(records, length);
      patterns.push(...sequencePatterns);
    }

    return patterns.filter(pattern => pattern.confidence >= this.config.patternConfidenceThreshold);
  }

  /**
   * 查找指定长度的序列模式
   * @param records 情感记录
   * @param sequenceLength 序列长度
   * @returns 序列模式
   */
  private findSequencePatterns(records: EmotionStateRecord[], sequenceLength: number): EmotionPattern[] {
    const patterns: EmotionPattern[] = [];
    const sequenceMap = new Map<string, { count: number; intervals: number[]; intensities: number[] }>();

    // 提取序列
    for (let i = 0; i <= records.length - sequenceLength; i++) {
      const sequence = records.slice(i, i + sequenceLength);
      const emotionSequence = sequence.map(r => r.emotionType);
      const timeIntervals = sequence.slice(1).map((r, idx) => r.timestamp - sequence[idx].timestamp);
      const intensities = sequence.map(r => r.intensity);
      
      const sequenceKey = emotionSequence.join('-');
      
      if (!sequenceMap.has(sequenceKey)) {
        sequenceMap.set(sequenceKey, { count: 0, intervals: [], intensities: [] });
      }
      
      const sequenceData = sequenceMap.get(sequenceKey)!;
      sequenceData.count++;
      sequenceData.intervals.push(...timeIntervals);
      sequenceData.intensities.push(...intensities);
    }

    // 生成模式
    for (const [sequenceKey, data] of sequenceMap) {
      if (data.count >= 2) { // 至少出现2次才认为是模式
        const emotionSequence = sequenceKey.split('-');
        const avgIntervals = this.calculateAverageIntervals(data.intervals, emotionSequence.length - 1);
        const avgIntensities = this.calculateAverageIntensities(data.intensities, emotionSequence.length);
        
        const pattern: EmotionPattern = {
          id: this.generatePatternId(),
          name: `Pattern_${sequenceKey}`,
          emotionSequence,
          timeIntervals: avgIntervals,
          intensityPattern: avgIntensities,
          frequency: data.count,
          confidence: Math.min(0.95, data.count / (records.length - sequenceLength + 1)),
          lastOccurrence: Date.now(),
        };
        
        patterns.push(pattern);
      }
    }

    return patterns;
  }

  /**
   * 基于模式生成预测
   * @param pattern 情感模式
   * @param contextType 上下文类型
   * @returns 预测结果
   */
  private generatePredictionFromPattern(
    pattern: EmotionPattern,
    contextType: EmotionContextType
  ): EmotionPrediction | null {
    if (pattern.emotionSequence.length === 0) return null;

    const nextEmotion = pattern.emotionSequence[pattern.emotionSequence.length - 1];
    const nextIntensity = pattern.intensityPattern[pattern.intensityPattern.length - 1];
    const avgInterval = pattern.timeIntervals.reduce((sum, interval) => sum + interval, 0) / pattern.timeIntervals.length;

    const currentTime = Date.now();
    
    return {
      predictedEmotion: nextEmotion,
      predictedIntensity: nextIntensity,
      confidence: pattern.confidence * 0.8, // 降低一些置信度
      timeRange: {
        start: currentTime,
        end: currentTime + avgInterval,
      },
      basedOnPattern: pattern.name,
      influencingFactors: [`历史模式: ${pattern.name}`, `出现频率: ${pattern.frequency}`],
    };
  }

  /**
   * 基于当前状态生成预测
   * @param currentState 当前状态
   * @param contextType 上下文类型
   * @returns 预测结果
   */
  private generatePredictionFromCurrentState(
    currentState: EmotionStateRecord,
    contextType: EmotionContextType
  ): EmotionPrediction | null {
    // 简化的基于当前状态的预测
    const currentTime = Date.now();
    const timeSinceState = currentTime - currentState.timestamp;
    
    // 如果当前状态太旧，不进行预测
    if (timeSinceState > this.config.predictionTimeWindow) {
      return null;
    }

    // 预测情感会逐渐回归中性
    const decayedIntensity = this.calculateDecayedIntensity(currentState);
    
    return {
      predictedEmotion: decayedIntensity > 0.3 ? currentState.emotionType : 'neutral',
      predictedIntensity: Math.max(0.1, decayedIntensity * 0.8),
      confidence: 0.6,
      timeRange: {
        start: currentTime,
        end: currentTime + currentState.duration,
      },
      basedOnPattern: 'current_state',
      influencingFactors: ['当前情感状态', '情感衰减模型'],
    };
  }

  /**
   * 计算衰减后的强度
   * @param record 情感记录
   * @returns 衰减后的强度
   */
  private calculateDecayedIntensity(record: EmotionStateRecord): number {
    if (!this.config.enableEmotionDecay) {
      return record.intensity;
    }

    const currentTime = Date.now();
    const timePassed = currentTime - record.timestamp;
    const decayPeriods = timePassed / (60 * 1000); // 每分钟衰减一次
    
    return record.intensity * Math.pow(record.decayFactor, decayPeriods);
  }

  /**
   * 计算趋势方向
   * @param records 情感记录
   * @returns 趋势方向
   */
  private calculateTrendDirection(records: EmotionStateRecord[]): 'increasing' | 'decreasing' | 'stable' {
    if (records.length < 3) return 'stable';

    // 计算最近几条记录的强度趋势
    const recentIntensities = records.slice(-5).map(r => r.intensity);
    
    let increasingCount = 0;
    let decreasingCount = 0;
    
    for (let i = 1; i < recentIntensities.length; i++) {
      if (recentIntensities[i] > recentIntensities[i - 1]) {
        increasingCount++;
      } else if (recentIntensities[i] < recentIntensities[i - 1]) {
        decreasingCount++;
      }
    }

    if (increasingCount > decreasingCount) {
      return 'increasing';
    } else if (decreasingCount > increasingCount) {
      return 'decreasing';
    } else {
      return 'stable';
    }
  }

  /**
   * 计算平均时间间隔
   * @param intervals 时间间隔数组
   * @param expectedLength 期望长度
   * @returns 平均时间间隔
   */
  private calculateAverageIntervals(intervals: number[], expectedLength: number): number[] {
    const result: number[] = [];
    const chunkSize = Math.ceil(intervals.length / expectedLength);
    
    for (let i = 0; i < expectedLength; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, intervals.length);
      const chunk = intervals.slice(start, end);
      
      if (chunk.length > 0) {
        const avg = chunk.reduce((sum, val) => sum + val, 0) / chunk.length;
        result.push(avg);
      } else {
        result.push(0);
      }
    }
    
    return result;
  }

  /**
   * 计算平均强度
   * @param intensities 强度数组
   * @param expectedLength 期望长度
   * @returns 平均强度
   */
  private calculateAverageIntensities(intensities: number[], expectedLength: number): number[] {
    const result: number[] = [];
    const chunkSize = Math.ceil(intensities.length / expectedLength);
    
    for (let i = 0; i < expectedLength; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, intensities.length);
      const chunk = intensities.slice(start, end);
      
      if (chunk.length > 0) {
        const avg = chunk.reduce((sum, val) => sum + val, 0) / chunk.length;
        result.push(avg);
      } else {
        result.push(0);
      }
    }
    
    return result;
  }

  /**
   * 检查缓存是否有效
   * @param predictions 预测结果
   * @returns 是否有效
   */
  private isCacheValid(predictions: EmotionPrediction[]): boolean {
    if (predictions.length === 0) return false;
    
    const currentTime = Date.now();
    const cacheAge = currentTime - predictions[0].timeRange.start;
    
    return cacheAge < 5 * 60 * 1000; // 缓存5分钟有效
  }

  /**
   * 生成记录ID
   * @returns 记录ID
   */
  private generateRecordId(): string {
    return `emotion_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成模式ID
   * @returns 模式ID
   */
  private generatePatternId(): string {
    return `pattern_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 启动定期任务
   */
  private startPeriodicTasks(): void {
    // 每5分钟清理一次过期数据
    setInterval(() => {
      this.cleanupExpiredData();
    }, 5 * 60 * 1000);

    // 每小时更新一次模式
    setInterval(() => {
      this.updateAllPatterns();
    }, 60 * 60 * 1000);
  }

  /**
   * 清理过期数据
   */
  private cleanupExpiredData(): void {
    const currentTime = Date.now();
    const retentionTime = this.config.recordRetentionTime;

    // 清理过期的情感记录
    for (const [userId, records] of this.emotionRecords) {
      const validRecords = records.filter(
        record => currentTime - record.timestamp <= retentionTime
      );
      
      if (validRecords.length === 0) {
        this.emotionRecords.delete(userId);
      } else {
        this.emotionRecords.set(userId, validRecords);
      }
    }

    // 清理预测缓存
    this.predictionCache.clear();

    if (this.config.debug) {
      console.log('清理过期数据完成');
    }
  }

  /**
   * 更新所有用户的模式
   */
  private updateAllPatterns(): void {
    for (const userId of this.emotionRecords.keys()) {
      this.analyzeEmotionPatterns(userId);
    }

    if (this.config.debug) {
      console.log('更新所有用户模式完成');
    }
  }

  /**
   * 加载历史数据
   */
  private async loadHistoricalData(): Promise<void> {
    // 这里可以从数据库或文件加载历史数据
    // 目前保持空实现
    if (this.config.debug) {
      console.log('加载历史数据');
    }
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.emotionRecords.clear();
    this.emotionPatterns.clear();
    this.currentEmotionStates.clear();
    this.predictionCache.clear();
    this.eventEmitter.removeAllListeners();
    this.initialized = false;
  }
}
