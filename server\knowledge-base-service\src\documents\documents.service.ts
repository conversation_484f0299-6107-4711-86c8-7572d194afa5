import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as fs from 'fs-extra';
import * as crypto from 'crypto';
import { Document, DocumentStatus } from './entities/document.entity';
import { DocumentProcessorService } from './processors/document-processor.service';
import { VectorStoreService } from '../vector-store/vector-store.service';

export interface CreateDocumentDto {
  knowledgeBaseId: string;
  filename: string;
  content?: string;
  metadata?: any;
}

@Injectable()
export class DocumentsService {
  constructor(
    @InjectRepository(Document)
    private documentRepository: Repository<Document>,
    @InjectQueue('document-processing')
    private documentQueue: Queue,
    private documentProcessor: DocumentProcessorService,
    private vectorStoreService: VectorStoreService,
    private eventEmitter: EventEmitter2,
  ) {}

  /**
   * 上传文档
   */
  async uploadDocument(
    knowledgeBaseId: string,
    file: Express.Multer.File,
    metadata: any = {},
  ): Promise<Document> {
    try {
      // 计算文件哈希
      const fileBuffer = await fs.readFile(file.path);
      const hash = crypto.createHash('sha256').update(fileBuffer).digest('hex');

      // 创建文档记录
      const document = this.documentRepository.create({
        knowledgeBaseId,
        filename: file.originalname,
        filePath: file.path,
        fileSize: file.size,
        mimeType: file.mimetype,
        status: DocumentStatus.PROCESSING,
        metadata: {
          ...metadata,
          hash,
          uploadedAt: new Date().toISOString(),
        },
      });

      const savedDocument = await this.documentRepository.save(document);

      // 添加到处理队列
      await this.documentQueue.add('process-document', {
        documentId: savedDocument.id,
        filePath: file.path,
      });

      // 发送上传事件
      this.eventEmitter.emit('document.uploaded', {
        documentId: savedDocument.id,
        knowledgeBaseId,
        filename: file.originalname,
      });

      return savedDocument;
    } catch (error) {
      throw new BadRequestException(`文档上传失败: ${error.message}`);
    }
  }

  /**
   * 处理文档
   */
  async processDocument(documentId: string): Promise<void> {
    const document = await this.documentRepository.findOne({
      where: { id: documentId },
    });

    if (!document) {
      throw new NotFoundException('文档不存在');
    }

    try {
      // 更新状态为处理中
      document.status = DocumentStatus.PROCESSING;
      await this.documentRepository.save(document);

      // 处理文档内容
      const processedDoc = await this.documentProcessor.processDocument({
        path: document.filePath,
        originalname: document.filename,
        mimetype: document.mimeType,
        size: document.fileSize,
      } as Express.Multer.File);

      // 更新文档内容和元数据
      document.content = processedDoc.text;
      document.chunkCount = processedDoc.chunks.length;
      document.metadata = {
        ...document.metadata,
        ...processedDoc.metadata,
        processedAt: new Date().toISOString(),
      };

      // 添加向量到向量数据库
      await this.vectorStoreService.addDocument(
        document.knowledgeBaseId,
        document.id,
        processedDoc.chunks,
      );

      document.vectorCount = processedDoc.chunks.length;
      document.status = DocumentStatus.COMPLETED;

      await this.documentRepository.save(document);

      // 发送处理完成事件
      this.eventEmitter.emit('document.processed', {
        documentId: document.id,
        knowledgeBaseId: document.knowledgeBaseId,
        chunkCount: processedDoc.chunks.length,
      });

      // 清理临时文件
      await fs.remove(document.filePath);
    } catch (error) {
      // 更新状态为失败
      document.status = DocumentStatus.FAILED;
      document.errorMessage = error.message;
      await this.documentRepository.save(document);

      // 发送处理失败事件
      this.eventEmitter.emit('document.failed', {
        documentId: document.id,
        error: error.message,
      });

      throw error;
    }
  }

  /**
   * 获取文档列表
   */
  async findByKnowledgeBase(
    knowledgeBaseId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: Document[];
    total: number;
    page: number;
    limit: number;
  }> {
    const [data, total] = await this.documentRepository.findAndCount({
      where: { knowledgeBaseId },
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return { data, total, page, limit };
  }

  /**
   * 获取文档详情
   */
  async findOne(id: string): Promise<Document> {
    const document = await this.documentRepository.findOne({
      where: { id },
    });

    if (!document) {
      throw new NotFoundException('文档不存在');
    }

    return document;
  }

  /**
   * 删除文档
   */
  async remove(id: string): Promise<void> {
    const document = await this.findOne(id);

    // 从向量数据库删除
    await this.vectorStoreService.deleteDocument(
      document.knowledgeBaseId,
      document.id,
    );

    // 删除文件
    if (document.filePath && await fs.pathExists(document.filePath)) {
      await fs.remove(document.filePath);
    }

    // 删除数据库记录
    await this.documentRepository.remove(document);

    // 发送删除事件
    this.eventEmitter.emit('document.deleted', {
      documentId: id,
      knowledgeBaseId: document.knowledgeBaseId,
    });
  }

  /**
   * 重新处理文档
   */
  async reprocessDocument(id: string): Promise<void> {
    const document = await this.findOne(id);

    if (document.status === DocumentStatus.PROCESSING) {
      throw new BadRequestException('文档正在处理中');
    }

    // 重置状态
    document.status = DocumentStatus.PROCESSING;
    document.errorMessage = null;
    await this.documentRepository.save(document);

    // 添加到处理队列
    await this.documentQueue.add('process-document', {
      documentId: document.id,
      filePath: document.filePath,
    });
  }

  /**
   * 获取处理统计
   */
  async getProcessingStatistics(knowledgeBaseId: string): Promise<any> {
    const documents = await this.documentRepository.find({
      where: { knowledgeBaseId },
    });

    const statistics = {
      total: documents.length,
      completed: 0,
      processing: 0,
      failed: 0,
      totalSize: 0,
      totalChunks: 0,
      totalVectors: 0,
    };

    documents.forEach(doc => {
      switch (doc.status) {
        case DocumentStatus.COMPLETED:
          statistics.completed++;
          break;
        case DocumentStatus.PROCESSING:
          statistics.processing++;
          break;
        case DocumentStatus.FAILED:
          statistics.failed++;
          break;
      }

      statistics.totalSize += doc.fileSize || 0;
      statistics.totalChunks += doc.chunkCount || 0;
      statistics.totalVectors += doc.vectorCount || 0;
    });

    return statistics;
  }
}
