import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { KnowledgeBase } from '../../knowledge-base/entities/knowledge-base.entity';

export enum DocumentStatus {
  UPLOADING = 'uploading',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

@Entity('documents')
@Index(['knowledgeBaseId'])
@Index(['status'])
export class Document {
  @ApiProperty({ description: '文档ID' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '知识库ID' })
  @Column({ name: 'knowledge_base_id', length: 36 })
  knowledgeBaseId: string;

  @ApiProperty({ description: '文件名' })
  @Column({ length: 255 })
  filename: string;

  @ApiProperty({ description: '文件路径' })
  @Column({ name: 'file_path', length: 500, nullable: true })
  filePath: string;

  @ApiProperty({ description: '文件大小（字节）' })
  @Column({ name: 'file_size', type: 'bigint', nullable: true })
  fileSize: number;

  @ApiProperty({ description: 'MIME类型' })
  @Column({ name: 'mime_type', length: 100, nullable: true })
  mimeType: string;

  @ApiProperty({ description: '文档内容' })
  @Column({ type: 'longtext', nullable: true })
  content: string;

  @ApiProperty({ description: '文档摘要' })
  @Column({ type: 'text', nullable: true })
  summary: string;

  @ApiProperty({ description: '文档元数据' })
  @Column({ type: 'json', nullable: true })
  metadata: {
    title?: string;
    author?: string;
    category?: string;
    tags?: string[];
    language?: string;
    pageCount?: number;
    wordCount?: number;
    extractedAt?: string;
    processingTime?: number;
  };

  @ApiProperty({ description: '处理状态', enum: DocumentStatus })
  @Column({
    type: 'enum',
    enum: DocumentStatus,
    default: DocumentStatus.UPLOADING,
  })
  status: DocumentStatus;

  @ApiProperty({ description: '处理错误信息' })
  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string;

  @ApiProperty({ description: '文档块数量' })
  @Column({ name: 'chunk_count', type: 'int', default: 0 })
  chunkCount: number;

  @ApiProperty({ description: '向量数量' })
  @Column({ name: 'vector_count', type: 'int', default: 0 })
  vectorCount: number;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ApiProperty({ description: '关联知识库', type: () => KnowledgeBase })
  @ManyToOne(() => KnowledgeBase, (knowledgeBase) => knowledgeBase.documents)
  @JoinColumn({ name: 'knowledge_base_id' })
  knowledgeBase: KnowledgeBase;
}
