import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as sdk from 'microsoft-cognitiveservices-speech-sdk';
import * as fs from 'fs-extra';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

/**
 * 语音合成提供商
 */
export enum TTSProvider {
  AZURE = 'azure',
  GOOGLE = 'google',
  BAIDU = 'baidu',
  TENCENT = 'tencent',
  OPENAI = 'openai',
}

/**
 * 语音合成配置
 */
export interface SpeechSynthesisConfig {
  provider: TTSProvider;
  voice: string;
  language: string;
  rate?: number;        // 语速 0.5-2.0
  pitch?: number;       // 音调 0.5-2.0
  volume?: number;      // 音量 0.0-1.0
  style?: string;       // 语音风格
  emotion?: string;     // 情感
  outputFormat?: 'wav' | 'mp3' | 'ogg';
  sampleRate?: number;  // 采样率
}

/**
 * 语音合成结果
 */
export interface SpeechSynthesisResult {
  id: string;
  audioData: Buffer;
  duration: number;
  format: string;
  sampleRate: number;
  channels: number;
  voice: string;
  language: string;
  provider: TTSProvider;
  processingTime: number;
  phonemes?: PhonemeData[];
  visemes?: VisemeData[];
}

/**
 * 音素数据
 */
export interface PhonemeData {
  phoneme: string;
  startTime: number;
  endTime: number;
}

/**
 * 口型数据
 */
export interface VisemeData {
  viseme: string;
  startTime: number;
  endTime: number;
  intensity: number;
}

/**
 * 语音风格
 */
export interface VoiceStyle {
  name: string;
  description: string;
  emotions: string[];
  sampleRate: number[];
}

@Injectable()
export class SpeechSynthesisService {
  private azureSpeechConfig: sdk.SpeechConfig;
  private voiceCache: Map<string, any> = new Map();

  constructor(
    private configService: ConfigService,
    private eventEmitter: EventEmitter2,
  ) {
    this.initializeProviders();
  }

  /**
   * 初始化语音服务提供商
   */
  private initializeProviders(): void {
    // 初始化Azure语音服务
    const azureKey = this.configService.get<string>('AZURE_SPEECH_KEY');
    const azureRegion = this.configService.get<string>('AZURE_SPEECH_REGION');
    
    if (azureKey && azureRegion) {
      this.azureSpeechConfig = sdk.SpeechConfig.fromSubscription(azureKey, azureRegion);
    }
  }

  /**
   * 合成语音
   */
  async synthesizeSpeech(
    text: string,
    config: SpeechSynthesisConfig,
  ): Promise<SpeechSynthesisResult> {
    const startTime = Date.now();
    const resultId = uuidv4();

    try {
      switch (config.provider) {
        case TTSProvider.AZURE:
          return await this.synthesizeWithAzure(text, config, resultId, startTime);
        case TTSProvider.OPENAI:
          return await this.synthesizeWithOpenAI(text, config, resultId, startTime);
        default:
          throw new BadRequestException(`不支持的语音合成提供商: ${config.provider}`);
      }
    } catch (error) {
      throw new BadRequestException(`语音合成失败: ${error.message}`);
    }
  }

  /**
   * 使用Azure进行语音合成
   */
  private async synthesizeWithAzure(
    text: string,
    config: SpeechSynthesisConfig,
    resultId: string,
    startTime: number,
  ): Promise<SpeechSynthesisResult> {
    if (!this.azureSpeechConfig) {
      throw new Error('Azure语音服务未配置');
    }

    // 设置输出格式
    const outputFormat = this.getAzureOutputFormat(config.outputFormat || 'wav', config.sampleRate || 16000);
    this.azureSpeechConfig.speechSynthesisOutputFormat = outputFormat;

    // 创建合成器
    const synthesizer = new sdk.SpeechSynthesizer(this.azureSpeechConfig);

    // 构建SSML
    const ssml = this.buildSSML(text, config);

    return new Promise((resolve, reject) => {
      synthesizer.speakSsmlAsync(
        ssml,
        (result) => {
          const processingTime = Date.now() - startTime;

          if (result.reason === sdk.ResultReason.SynthesizingAudioCompleted) {
            // 提取音素和口型数据
            const phonemes = this.extractPhonemes(result);
            const visemes = this.extractVisemes(result);

            resolve({
              id: resultId,
              audioData: Buffer.from(result.audioData),
              duration: this.calculateDuration(result.audioData, config.sampleRate || 16000),
              format: config.outputFormat || 'wav',
              sampleRate: config.sampleRate || 16000,
              channels: 1,
              voice: config.voice,
              language: config.language,
              provider: TTSProvider.AZURE,
              processingTime,
              phonemes,
              visemes,
            });
          } else {
            reject(new Error(`合成失败: ${result.errorDetails}`));
          }

          synthesizer.close();
        },
        (error) => {
          synthesizer.close();
          reject(new Error(error));
        }
      );
    });
  }

  /**
   * 使用OpenAI进行语音合成
   */
  private async synthesizeWithOpenAI(
    text: string,
    config: SpeechSynthesisConfig,
    resultId: string,
    startTime: number,
  ): Promise<SpeechSynthesisResult> {
    // 这里需要实现OpenAI TTS API调用
    // 暂时返回模拟结果
    const processingTime = Date.now() - startTime;
    
    // 创建模拟音频数据
    const mockAudioData = Buffer.alloc(16000 * 3); // 3秒的16kHz音频
    
    return {
      id: resultId,
      audioData: mockAudioData,
      duration: 3.0,
      format: config.outputFormat || 'wav',
      sampleRate: config.sampleRate || 16000,
      channels: 1,
      voice: config.voice,
      language: config.language,
      provider: TTSProvider.OPENAI,
      processingTime,
    };
  }

  /**
   * 构建SSML
   */
  private buildSSML(text: string, config: SpeechSynthesisConfig): string {
    let ssml = `<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="${config.language}">`;
    
    // 添加语音选择
    ssml += `<voice name="${config.voice}">`;
    
    // 添加韵律控制
    if (config.rate || config.pitch || config.volume) {
      ssml += '<prosody';
      if (config.rate) {
        ssml += ` rate="${this.formatRate(config.rate)}"`;
      }
      if (config.pitch) {
        ssml += ` pitch="${this.formatPitch(config.pitch)}"`;
      }
      if (config.volume) {
        ssml += ` volume="${this.formatVolume(config.volume)}"`;
      }
      ssml += '>';
    }
    
    // 添加风格和情感
    if (config.style || config.emotion) {
      ssml += '<mstts:express-as';
      if (config.style) {
        ssml += ` style="${config.style}"`;
      }
      if (config.emotion) {
        ssml += ` emotion="${config.emotion}"`;
      }
      ssml += '>';
    }
    
    // 添加文本内容
    ssml += this.escapeXml(text);
    
    // 关闭标签
    if (config.style || config.emotion) {
      ssml += '</mstts:express-as>';
    }
    if (config.rate || config.pitch || config.volume) {
      ssml += '</prosody>';
    }
    ssml += '</voice>';
    ssml += '</speak>';
    
    return ssml;
  }

  /**
   * 格式化语速
   */
  private formatRate(rate: number): string {
    if (rate === 1.0) return 'medium';
    if (rate < 0.7) return 'x-slow';
    if (rate < 0.9) return 'slow';
    if (rate > 1.3) return 'x-fast';
    if (rate > 1.1) return 'fast';
    return `${Math.round(rate * 100)}%`;
  }

  /**
   * 格式化音调
   */
  private formatPitch(pitch: number): string {
    if (pitch === 1.0) return 'medium';
    if (pitch < 0.7) return 'x-low';
    if (pitch < 0.9) return 'low';
    if (pitch > 1.3) return 'x-high';
    if (pitch > 1.1) return 'high';
    return `${Math.round((pitch - 1) * 100)}%`;
  }

  /**
   * 格式化音量
   */
  private formatVolume(volume: number): string {
    if (volume === 1.0) return 'medium';
    if (volume < 0.3) return 'x-soft';
    if (volume < 0.6) return 'soft';
    if (volume > 0.8) return 'loud';
    return `${Math.round(volume * 100)}%`;
  }

  /**
   * 转义XML字符
   */
  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');
  }

  /**
   * 获取Azure输出格式
   */
  private getAzureOutputFormat(format: string, sampleRate: number): sdk.SpeechSynthesisOutputFormat {
    switch (format) {
      case 'wav':
        return sampleRate === 16000 
          ? sdk.SpeechSynthesisOutputFormat.Riff16Khz16BitMonoPcm
          : sdk.SpeechSynthesisOutputFormat.Riff24Khz16BitMonoPcm;
      case 'mp3':
        return sdk.SpeechSynthesisOutputFormat.Audio16Khz128KBitRateMonoMp3;
      default:
        return sdk.SpeechSynthesisOutputFormat.Riff16Khz16BitMonoPcm;
    }
  }

  /**
   * 提取音素数据
   */
  private extractPhonemes(result: sdk.SpeechSynthesisResult): PhonemeData[] {
    // 这里需要从Azure结果中提取音素数据
    // 暂时返回空数组
    return [];
  }

  /**
   * 提取口型数据
   */
  private extractVisemes(result: sdk.SpeechSynthesisResult): VisemeData[] {
    // 这里需要从Azure结果中提取口型数据
    // 暂时返回空数组
    return [];
  }

  /**
   * 计算音频时长
   */
  private calculateDuration(audioData: ArrayBuffer, sampleRate: number): number {
    // 假设16位单声道PCM
    const samples = audioData.byteLength / 2;
    return samples / sampleRate;
  }

  /**
   * 获取可用语音列表
   */
  async getAvailableVoices(provider: TTSProvider, language?: string): Promise<any[]> {
    switch (provider) {
      case TTSProvider.AZURE:
        return this.getAzureVoices(language);
      default:
        return [];
    }
  }

  /**
   * 获取Azure语音列表
   */
  private async getAzureVoices(language?: string): Promise<any[]> {
    // 这里应该调用Azure API获取语音列表
    // 暂时返回常用的中文语音
    const chineseVoices = [
      {
        name: 'zh-CN-XiaoxiaoNeural',
        displayName: '晓晓',
        gender: 'Female',
        language: 'zh-CN',
        styles: ['general', 'assistant', 'chat', 'customerservice'],
      },
      {
        name: 'zh-CN-YunxiNeural',
        displayName: '云希',
        gender: 'Male',
        language: 'zh-CN',
        styles: ['general', 'assistant', 'narration-relaxed'],
      },
      {
        name: 'zh-CN-YunyangNeural',
        displayName: '云扬',
        gender: 'Male',
        language: 'zh-CN',
        styles: ['general', 'customerservice'],
      },
    ];

    if (language && language.startsWith('zh')) {
      return chineseVoices;
    }

    return chineseVoices;
  }

  /**
   * 批量语音合成
   */
  async batchSynthesize(
    texts: string[],
    config: SpeechSynthesisConfig,
  ): Promise<SpeechSynthesisResult[]> {
    const results: SpeechSynthesisResult[] = [];
    
    for (const text of texts) {
      try {
        const result = await this.synthesizeSpeech(text, config);
        results.push(result);
      } catch (error) {
        console.error('批量合成中的文本处理失败:', error);
        // 继续处理下一个文本
      }
    }
    
    return results;
  }

  /**
   * 语音预览
   */
  async previewVoice(
    voice: string,
    language: string,
    provider: TTSProvider = TTSProvider.AZURE,
  ): Promise<SpeechSynthesisResult> {
    const previewText = language.startsWith('zh') 
      ? '您好，这是语音预览。' 
      : 'Hello, this is a voice preview.';

    return this.synthesizeSpeech(previewText, {
      provider,
      voice,
      language,
      rate: 1.0,
      pitch: 1.0,
      volume: 1.0,
    });
  }

  /**
   * 获取服务统计信息
   */
  getServiceStatistics(): {
    supportedProviders: TTSProvider[];
    totalSynthesized: number;
    cacheSize: number;
  } {
    return {
      supportedProviders: [TTSProvider.AZURE, TTSProvider.OPENAI],
      totalSynthesized: 0, // 这里应该从数据库或缓存中获取
      cacheSize: this.voiceCache.size,
    };
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.voiceCache.clear();
  }
}
