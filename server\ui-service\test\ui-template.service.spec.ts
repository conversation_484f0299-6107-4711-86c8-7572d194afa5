/**
 * UI模板服务测试套件
 */

import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { getRedisToken } from '@nestjs-modules/ioredis';
import { Types } from 'mongoose';
import { UITemplateService } from '../src/modules/ui-template/ui-template.service';
import { UITemplate, TemplateCategory, TemplateStatus, AccessLevel } from '../src/modules/ui-template/schemas/ui-template.schema';
import { UITemplateVersion, VersionType } from '../src/modules/ui-template/schemas/ui-template-version.schema';
import { CreateTemplateDto, UpdateTemplateDto, QueryTemplateDto } from '../src/modules/ui-template/dto/ui-template.dto';

describe('UITemplateService', () => {
  let service: UITemplateService;
  let templateModel: any;
  let versionModel: any;
  let redisClient: any;

  // 模拟数据
  const mockUserId = new Types.ObjectId();
  const mockTemplateId = new Types.ObjectId();
  const mockOrganizationId = new Types.ObjectId();

  const mockTemplate = {
    _id: mockTemplateId,
    name: '测试模板',
    description: '这是一个测试模板',
    category: TemplateCategory.BASIC,
    tags: ['测试', '基础'],
    elements: [],
    metadata: {
      canvasSize: { width: 800, height: 600 },
      gridSize: 20,
      snapToGrid: true,
      backgroundColor: '#ffffff'
    },
    status: TemplateStatus.DRAFT,
    accessLevel: AccessLevel.PRIVATE,
    createdBy: mockUserId,
    updatedBy: mockUserId,
    version: '1.0.0',
    statistics: {
      views: 0,
      downloads: 0,
      forks: 0,
      likes: 0,
      rating: 0,
      ratingCount: 0
    },
    settings: {
      allowFork: true,
      allowComments: true,
      allowRating: true,
      requireApproval: false
    },
    save: jest.fn().mockResolvedValue(this),
    canAccess: jest.fn().mockReturnValue(true),
    canEdit: jest.fn().mockReturnValue(true),
    incrementViews: jest.fn().mockResolvedValue(this),
    softDelete: jest.fn().mockResolvedValue(this),
    restore: jest.fn().mockResolvedValue(this)
  };

  const mockVersion = {
    _id: new Types.ObjectId(),
    templateId: mockTemplateId,
    version: '1.0.0',
    versionType: VersionType.MAJOR,
    description: '初始版本',
    elements: [],
    createdBy: mockUserId,
    save: jest.fn().mockResolvedValue(this)
  };

  beforeEach(async () => {
    // 模拟模型方法
    const mockTemplateModel = {
      new: jest.fn().mockImplementation(() => mockTemplate),
      constructor: jest.fn().mockImplementation(() => mockTemplate),
      find: jest.fn(),
      findById: jest.fn(),
      findOne: jest.fn(),
      findOneAndUpdate: jest.fn(),
      countDocuments: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      exec: jest.fn(),
      populate: jest.fn().mockReturnThis(),
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis()
    };

    const mockVersionModel = {
      new: jest.fn().mockImplementation(() => mockVersion),
      constructor: jest.fn().mockImplementation(() => mockVersion),
      find: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      exec: jest.fn(),
      populate: jest.fn().mockReturnThis(),
      sort: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis()
    };

    const mockRedisClient = {
      get: jest.fn(),
      set: jest.fn(),
      setex: jest.fn(),
      del: jest.fn()
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UITemplateService,
        {
          provide: getModelToken(UITemplate.name),
          useValue: mockTemplateModel
        },
        {
          provide: getModelToken(UITemplateVersion.name),
          useValue: mockVersionModel
        },
        {
          provide: getRedisToken(),
          useValue: mockRedisClient
        }
      ]
    }).compile();

    service = module.get<UITemplateService>(UITemplateService);
    templateModel = module.get(getModelToken(UITemplate.name));
    versionModel = module.get(getModelToken(UITemplateVersion.name));
    redisClient = module.get(getRedisToken());
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('应该成功创建模板', async () => {
      const createTemplateDto: CreateTemplateDto = {
        name: '新模板',
        description: '新模板描述',
        category: TemplateCategory.BASIC,
        tags: ['新建', '测试'],
        elements: [],
        metadata: {
          canvasSize: { width: 800, height: 600 },
          gridSize: 20,
          snapToGrid: true,
          backgroundColor: '#ffffff'
        }
      };

      // 模拟模板保存
      templateModel.prototype.save = jest.fn().mockResolvedValue(mockTemplate);
      
      // 模拟版本创建
      versionModel.prototype.save = jest.fn().mockResolvedValue(mockVersion);

      const result = await service.create(createTemplateDto, mockUserId);

      expect(result).toBeDefined();
      expect(templateModel.prototype.save).toHaveBeenCalled();
    });
  });

  describe('findAll', () => {
    it('应该返回模板列表', async () => {
      const queryDto: QueryTemplateDto = {
        page: 1,
        limit: 20,
        category: TemplateCategory.BASIC
      };

      const mockTemplates = [mockTemplate];
      const mockTotal = 1;

      templateModel.find.mockReturnValue({
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        populate: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockTemplates)
      });

      templateModel.countDocuments.mockResolvedValue(mockTotal);

      const result = await service.findAll(queryDto, mockUserId);

      expect(result).toEqual({
        templates: mockTemplates,
        total: mockTotal,
        page: 1,
        limit: 20
      });
      expect(templateModel.find).toHaveBeenCalled();
      expect(templateModel.countDocuments).toHaveBeenCalled();
    });

    it('应该支持搜索功能', async () => {
      const queryDto: QueryTemplateDto = {
        search: '测试',
        page: 1,
        limit: 20
      };

      templateModel.find.mockReturnValue({
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        populate: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([])
      });

      templateModel.countDocuments.mockResolvedValue(0);

      await service.findAll(queryDto, mockUserId);

      expect(templateModel.find).toHaveBeenCalledWith(
        expect.objectContaining({
          $text: { $search: '测试' }
        })
      );
    });
  });

  describe('findOne', () => {
    it('应该返回指定模板', async () => {
      // 模拟缓存未命中
      redisClient.get.mockResolvedValue(null);

      templateModel.findById.mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockTemplate)
      });

      const result = await service.findOne(mockTemplateId.toString(), mockUserId);

      expect(result).toEqual(mockTemplate);
      expect(templateModel.findById).toHaveBeenCalledWith(mockTemplateId.toString());
      expect(mockTemplate.incrementViews).toHaveBeenCalled();
      expect(redisClient.setex).toHaveBeenCalled();
    });

    it('应该从缓存返回模板', async () => {
      const cachedTemplate = JSON.stringify(mockTemplate);
      redisClient.get.mockResolvedValue(cachedTemplate);

      const result = await service.findOne(mockTemplateId.toString(), mockUserId);

      expect(result).toEqual(mockTemplate);
      expect(templateModel.findById).not.toHaveBeenCalled();
    });

    it('应该在模板不存在时抛出异常', async () => {
      redisClient.get.mockResolvedValue(null);
      templateModel.findById.mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(null)
      });

      await expect(
        service.findOne(mockTemplateId.toString(), mockUserId)
      ).rejects.toThrow('模板不存在');
    });
  });

  describe('update', () => {
    it('应该成功更新模板', async () => {
      const updateDto: UpdateTemplateDto = {
        name: '更新的模板名称',
        description: '更新的描述'
      };

      templateModel.findById.mockResolvedValue(mockTemplate);
      mockTemplate.save.mockResolvedValue(mockTemplate);

      const result = await service.update(mockTemplateId.toString(), updateDto, mockUserId);

      expect(result).toEqual(mockTemplate);
      expect(mockTemplate.save).toHaveBeenCalled();
      expect(redisClient.del).toHaveBeenCalled();
    });

    it('应该在更新元素时创建新版本', async () => {
      const updateDto: UpdateTemplateDto = {
        elements: [{ id: 'element-1', type: 'button', name: 'Button', x: 0, y: 0, width: 100, height: 30, properties: {} }],
        versionDescription: '更新UI元素'
      };

      templateModel.findById.mockResolvedValue(mockTemplate);
      versionModel.findOne.mockReturnValue({
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockVersion)
      });
      versionModel.prototype.save = jest.fn().mockResolvedValue(mockVersion);

      await service.update(mockTemplateId.toString(), updateDto, mockUserId);

      expect(versionModel.prototype.save).toHaveBeenCalled();
    });
  });

  describe('remove', () => {
    it('应该成功删除模板', async () => {
      templateModel.findById.mockResolvedValue(mockTemplate);

      await service.remove(mockTemplateId.toString(), mockUserId);

      expect(mockTemplate.softDelete).toHaveBeenCalledWith(mockUserId);
      expect(redisClient.del).toHaveBeenCalled();
    });

    it('应该在模板不存在时抛出异常', async () => {
      templateModel.findById.mockResolvedValue(null);

      await expect(
        service.remove(mockTemplateId.toString(), mockUserId)
      ).rejects.toThrow('模板不存在');
    });
  });

  describe('fork', () => {
    it('应该成功复制模板', async () => {
      const forkData = {
        name: '复制的模板',
        description: '复制的模板描述'
      };

      templateModel.findById.mockResolvedValue(mockTemplate);
      versionModel.findOne.mockReturnValue({
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockVersion)
      });
      templateModel.prototype.save = jest.fn().mockResolvedValue(mockTemplate);
      versionModel.prototype.save = jest.fn().mockResolvedValue(mockVersion);

      const result = await service.fork(mockTemplateId.toString(), forkData, mockUserId);

      expect(result).toBeDefined();
      expect(templateModel.prototype.save).toHaveBeenCalled();
      expect(mockTemplate.save).toHaveBeenCalled(); // 更新原模板统计
    });

    it('应该在不允许复制时抛出异常', async () => {
      const forkData = {
        name: '复制的模板'
      };

      const noForkTemplate = {
        ...mockTemplate,
        settings: { ...mockTemplate.settings, allowFork: false }
      };

      templateModel.findById.mockResolvedValue(noForkTemplate);

      await expect(
        service.fork(mockTemplateId.toString(), forkData, mockUserId)
      ).rejects.toThrow('该模板不允许复制');
    });
  });

  describe('publish', () => {
    it('应该成功发布模板', async () => {
      const draftTemplate = {
        ...mockTemplate,
        status: TemplateStatus.DRAFT
      };

      templateModel.findById.mockResolvedValue(draftTemplate);
      draftTemplate.save = jest.fn().mockResolvedValue(draftTemplate);

      const result = await service.publish(mockTemplateId.toString(), mockUserId);

      expect(result.status).toBe(TemplateStatus.PUBLISHED);
      expect(draftTemplate.save).toHaveBeenCalled();
    });

    it('应该在模板已发布时抛出异常', async () => {
      const publishedTemplate = {
        ...mockTemplate,
        status: TemplateStatus.PUBLISHED
      };

      templateModel.findById.mockResolvedValue(publishedTemplate);

      await expect(
        service.publish(mockTemplateId.toString(), mockUserId)
      ).rejects.toThrow('模板已经发布');
    });
  });

  describe('getVersionHistory', () => {
    it('应该返回版本历史', async () => {
      const mockVersions = [mockVersion];

      versionModel.find.mockReturnValue({
        sort: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        populate: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockVersions)
      });

      const result = await service.getVersionHistory(mockTemplateId.toString());

      expect(result).toEqual(mockVersions);
      expect(versionModel.find).toHaveBeenCalledWith({
        templateId: mockTemplateId
      });
    });
  });

  describe('权限控制', () => {
    it('应该正确检查访问权限', async () => {
      const privateTemplate = {
        ...mockTemplate,
        accessLevel: AccessLevel.PRIVATE,
        createdBy: new Types.ObjectId() // 不同的用户
      };

      redisClient.get.mockResolvedValue(null);
      templateModel.findById.mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(privateTemplate)
      });

      privateTemplate.canAccess = jest.fn().mockReturnValue(false);

      await expect(
        service.findOne(mockTemplateId.toString(), mockUserId)
      ).rejects.toThrow('没有访问权限');
    });

    it('应该正确检查编辑权限', async () => {
      const readOnlyTemplate = {
        ...mockTemplate,
        createdBy: new Types.ObjectId() // 不同的用户
      };

      templateModel.findById.mockResolvedValue(readOnlyTemplate);
      readOnlyTemplate.canEdit = jest.fn().mockReturnValue(false);

      await expect(
        service.update(mockTemplateId.toString(), { name: '新名称' }, mockUserId)
      ).rejects.toThrow('没有编辑权限');
    });
  });
});
