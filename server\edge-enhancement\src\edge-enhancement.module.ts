import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';

// 服务导入
import { IntelligentSchedulerService } from './intelligent-scheduler.service';
import { PredictiveCacheService } from './predictive-cache.service';
import { AdaptiveNetworkService } from './adaptive-network.service';

// 控制器导入
import { EdgeEnhancementController } from './edge-enhancement.controller';

/**
 * 边缘计算增强模块
 * 提供智能调度、预测性缓存和自适应网络传输功能
 */
@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // 事件发射器模块
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),
    
    // 定时任务模块
    ScheduleModule.forRoot(),
  ],
  
  controllers: [
    EdgeEnhancementController,
  ],
  
  providers: [
    IntelligentSchedulerService,
    PredictiveCacheService,
    AdaptiveNetworkService,
  ],
  
  exports: [
    IntelligentSchedulerService,
    PredictiveCacheService,
    AdaptiveNetworkService,
  ],
})
export class EdgeEnhancementModule {}
