/**
 * 增强的行为树引擎
 * 
 * 提供完整的行为树系统，支持复杂的AI行为逻辑和决策制定。
 * 包括各种行为节点类型、执行策略和调试功能。
 */

import { EventEmitter } from 'events';
import * as THREE from 'three';

/**
 * 行为节点状态
 */
export enum BehaviorNodeStatus {
  SUCCESS = 'success',
  FAILURE = 'failure',
  RUNNING = 'running',
  INVALID = 'invalid'
}

/**
 * 行为节点类型
 */
export enum BehaviorNodeType {
  // 复合节点
  SEQUENCE = 'sequence',           // 顺序执行
  SELECTOR = 'selector',           // 选择执行
  PARALLEL = 'parallel',           // 并行执行
  
  // 装饰节点
  INVERTER = 'inverter',           // 反转结果
  REPEATER = 'repeater',           // 重复执行
  RETRY = 'retry',                 // 重试执行
  TIMEOUT = 'timeout',             // 超时控制
  
  // 叶子节点
  ACTION = 'action',               // 动作节点
  CONDITION = 'condition',         // 条件节点
  WAIT = 'wait',                   // 等待节点
  
  // AI节点
  AI_DECISION = 'ai_decision',     // AI决策节点
  EMOTION_CHECK = 'emotion_check', // 情感检查节点
  MEMORY_ACCESS = 'memory_access'  // 记忆访问节点
}

/**
 * 黑板数据接口
 */
export interface BlackboardData {
  [key: string]: any;
}

/**
 * 黑板系统
 */
export class Blackboard {
  private data: BlackboardData = {};
  private eventEmitter = new EventEmitter();

  /**
   * 设置数据
   */
  public set(key: string, value: any): void {
    const oldValue = this.data[key];
    this.data[key] = value;
    this.eventEmitter.emit('dataChanged', { key, oldValue, newValue: value });
  }

  /**
   * 获取数据
   */
  public get<T = any>(key: string, defaultValue?: T): T {
    return this.data[key] !== undefined ? this.data[key] : defaultValue;
  }

  /**
   * 检查是否存在
   */
  public has(key: string): boolean {
    return this.data.hasOwnProperty(key);
  }

  /**
   * 删除数据
   */
  public delete(key: string): boolean {
    if (this.has(key)) {
      delete this.data[key];
      this.eventEmitter.emit('dataDeleted', { key });
      return true;
    }
    return false;
  }

  /**
   * 清空数据
   */
  public clear(): void {
    this.data = {};
    this.eventEmitter.emit('dataCleared');
  }

  /**
   * 监听数据变化
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
}

/**
 * 行为节点基类
 */
export abstract class BehaviorNode {
  public id: string;
  public name: string;
  public type: BehaviorNodeType;
  public parent: BehaviorNode | null = null;
  public children: BehaviorNode[] = [];
  public status: BehaviorNodeStatus = BehaviorNodeStatus.INVALID;
  public metadata: { [key: string]: any } = {};

  protected blackboard: Blackboard;
  protected eventEmitter = new EventEmitter();

  constructor(
    id: string,
    name: string,
    type: BehaviorNodeType,
    blackboard: Blackboard
  ) {
    this.id = id;
    this.name = name;
    this.type = type;
    this.blackboard = blackboard;
  }

  /**
   * 执行节点
   */
  public abstract execute(deltaTime: number): BehaviorNodeStatus;

  /**
   * 重置节点
   */
  public reset(): void {
    this.status = BehaviorNodeStatus.INVALID;
    for (const child of this.children) {
      child.reset();
    }
  }

  /**
   * 添加子节点
   */
  public addChild(child: BehaviorNode): void {
    child.parent = this;
    this.children.push(child);
  }

  /**
   * 移除子节点
   */
  public removeChild(child: BehaviorNode): boolean {
    const index = this.children.indexOf(child);
    if (index !== -1) {
      child.parent = null;
      this.children.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 获取根节点
   */
  public getRoot(): BehaviorNode {
    let root: BehaviorNode = this;
    while (root.parent) {
      root = root.parent;
    }
    return root;
  }

  /**
   * 触发事件
   */
  protected emit(event: string, data?: any): void {
    this.eventEmitter.emit(event, { node: this, data });
  }

  /**
   * 监听事件
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }
}

/**
 * 顺序节点 - 按顺序执行子节点
 */
export class SequenceNode extends BehaviorNode {
  private currentChildIndex = 0;

  constructor(id: string, name: string, blackboard: Blackboard) {
    super(id, name, BehaviorNodeType.SEQUENCE, blackboard);
  }

  public execute(deltaTime: number): BehaviorNodeStatus {
    if (this.children.length === 0) {
      return BehaviorNodeStatus.SUCCESS;
    }

    while (this.currentChildIndex < this.children.length) {
      const child = this.children[this.currentChildIndex];
      const childStatus = child.execute(deltaTime);

      switch (childStatus) {
        case BehaviorNodeStatus.SUCCESS:
          this.currentChildIndex++;
          break;
        case BehaviorNodeStatus.FAILURE:
          this.reset();
          return BehaviorNodeStatus.FAILURE;
        case BehaviorNodeStatus.RUNNING:
          return BehaviorNodeStatus.RUNNING;
      }
    }

    this.reset();
    return BehaviorNodeStatus.SUCCESS;
  }

  public reset(): void {
    super.reset();
    this.currentChildIndex = 0;
  }
}

/**
 * 选择节点 - 选择第一个成功的子节点
 */
export class SelectorNode extends BehaviorNode {
  private currentChildIndex = 0;

  constructor(id: string, name: string, blackboard: Blackboard) {
    super(id, name, BehaviorNodeType.SELECTOR, blackboard);
  }

  public execute(deltaTime: number): BehaviorNodeStatus {
    if (this.children.length === 0) {
      return BehaviorNodeStatus.FAILURE;
    }

    while (this.currentChildIndex < this.children.length) {
      const child = this.children[this.currentChildIndex];
      const childStatus = child.execute(deltaTime);

      switch (childStatus) {
        case BehaviorNodeStatus.SUCCESS:
          this.reset();
          return BehaviorNodeStatus.SUCCESS;
        case BehaviorNodeStatus.FAILURE:
          this.currentChildIndex++;
          break;
        case BehaviorNodeStatus.RUNNING:
          return BehaviorNodeStatus.RUNNING;
      }
    }

    this.reset();
    return BehaviorNodeStatus.FAILURE;
  }

  public reset(): void {
    super.reset();
    this.currentChildIndex = 0;
  }
}

/**
 * 并行节点 - 同时执行所有子节点
 */
export class ParallelNode extends BehaviorNode {
  private successThreshold: number;
  private failureThreshold: number;

  constructor(
    id: string, 
    name: string, 
    blackboard: Blackboard,
    successThreshold: number = 1,
    failureThreshold: number = 1
  ) {
    super(id, name, BehaviorNodeType.PARALLEL, blackboard);
    this.successThreshold = successThreshold;
    this.failureThreshold = failureThreshold;
  }

  public execute(deltaTime: number): BehaviorNodeStatus {
    if (this.children.length === 0) {
      return BehaviorNodeStatus.SUCCESS;
    }

    let successCount = 0;
    let failureCount = 0;
    let runningCount = 0;

    for (const child of this.children) {
      const childStatus = child.execute(deltaTime);
      
      switch (childStatus) {
        case BehaviorNodeStatus.SUCCESS:
          successCount++;
          break;
        case BehaviorNodeStatus.FAILURE:
          failureCount++;
          break;
        case BehaviorNodeStatus.RUNNING:
          runningCount++;
          break;
      }
    }

    if (successCount >= this.successThreshold) {
      this.reset();
      return BehaviorNodeStatus.SUCCESS;
    }

    if (failureCount >= this.failureThreshold) {
      this.reset();
      return BehaviorNodeStatus.FAILURE;
    }

    return BehaviorNodeStatus.RUNNING;
  }
}

/**
 * 动作节点 - 执行具体动作
 */
export abstract class ActionNode extends BehaviorNode {
  protected isExecuting = false;
  protected startTime = 0;

  constructor(id: string, name: string, blackboard: Blackboard) {
    super(id, name, BehaviorNodeType.ACTION, blackboard);
  }

  public execute(deltaTime: number): BehaviorNodeStatus {
    if (!this.isExecuting) {
      this.isExecuting = true;
      this.startTime = Date.now();
      this.onStart();
    }

    const status = this.onUpdate(deltaTime);

    if (status !== BehaviorNodeStatus.RUNNING) {
      this.isExecuting = false;
      this.onEnd(status);
    }

    return status;
  }

  public reset(): void {
    super.reset();
    if (this.isExecuting) {
      this.isExecuting = false;
      this.onEnd(BehaviorNodeStatus.INVALID);
    }
  }

  protected abstract onStart(): void;
  protected abstract onUpdate(deltaTime: number): BehaviorNodeStatus;
  protected abstract onEnd(status: BehaviorNodeStatus): void;
}

/**
 * 条件节点 - 检查条件
 */
export abstract class ConditionNode extends BehaviorNode {
  constructor(id: string, name: string, blackboard: Blackboard) {
    super(id, name, BehaviorNodeType.CONDITION, blackboard);
  }

  public execute(deltaTime: number): BehaviorNodeStatus {
    return this.checkCondition() ? BehaviorNodeStatus.SUCCESS : BehaviorNodeStatus.FAILURE;
  }

  protected abstract checkCondition(): boolean;
}

/**
 * 等待节点 - 等待指定时间
 */
export class WaitNode extends BehaviorNode {
  private waitTime: number;
  private elapsedTime = 0;

  constructor(id: string, name: string, blackboard: Blackboard, waitTime: number) {
    super(id, name, BehaviorNodeType.WAIT, blackboard);
    this.waitTime = waitTime;
  }

  public execute(deltaTime: number): BehaviorNodeStatus {
    this.elapsedTime += deltaTime;

    if (this.elapsedTime >= this.waitTime) {
      this.reset();
      return BehaviorNodeStatus.SUCCESS;
    }

    return BehaviorNodeStatus.RUNNING;
  }

  public reset(): void {
    super.reset();
    this.elapsedTime = 0;
  }
}

/**
 * 反转节点 - 反转子节点结果
 */
export class InverterNode extends BehaviorNode {
  constructor(id: string, name: string, blackboard: Blackboard) {
    super(id, name, BehaviorNodeType.INVERTER, blackboard);
  }

  public execute(deltaTime: number): BehaviorNodeStatus {
    if (this.children.length !== 1) {
      return BehaviorNodeStatus.FAILURE;
    }

    const childStatus = this.children[0].execute(deltaTime);

    switch (childStatus) {
      case BehaviorNodeStatus.SUCCESS:
        return BehaviorNodeStatus.FAILURE;
      case BehaviorNodeStatus.FAILURE:
        return BehaviorNodeStatus.SUCCESS;
      default:
        return childStatus;
    }
  }
}

/**
 * 重复节点 - 重复执行子节点
 */
export class RepeaterNode extends BehaviorNode {
  private repeatCount: number;
  private currentCount = 0;

  constructor(id: string, name: string, blackboard: Blackboard, repeatCount: number = -1) {
    super(id, name, BehaviorNodeType.REPEATER, blackboard);
    this.repeatCount = repeatCount; // -1 表示无限重复
  }

  public execute(deltaTime: number): BehaviorNodeStatus {
    if (this.children.length !== 1) {
      return BehaviorNodeStatus.FAILURE;
    }

    const child = this.children[0];
    const childStatus = child.execute(deltaTime);

    if (childStatus === BehaviorNodeStatus.RUNNING) {
      return BehaviorNodeStatus.RUNNING;
    }

    // 子节点执行完成，检查是否需要重复
    if (childStatus === BehaviorNodeStatus.SUCCESS || childStatus === BehaviorNodeStatus.FAILURE) {
      child.reset();
      this.currentCount++;

      if (this.repeatCount > 0 && this.currentCount >= this.repeatCount) {
        this.reset();
        return BehaviorNodeStatus.SUCCESS;
      }
    }

    return BehaviorNodeStatus.RUNNING;
  }

  public reset(): void {
    super.reset();
    this.currentCount = 0;
  }
}

/**
 * 重试节点 - 失败时重试执行子节点
 */
export class RetryNode extends BehaviorNode {
  private maxRetries: number;
  private currentRetries = 0;

  constructor(id: string, name: string, blackboard: Blackboard, maxRetries: number = 3) {
    super(id, name, BehaviorNodeType.RETRY, blackboard);
    this.maxRetries = maxRetries;
  }

  public execute(deltaTime: number): BehaviorNodeStatus {
    if (this.children.length !== 1) {
      return BehaviorNodeStatus.FAILURE;
    }

    const child = this.children[0];
    const childStatus = child.execute(deltaTime);

    switch (childStatus) {
      case BehaviorNodeStatus.SUCCESS:
        this.reset();
        return BehaviorNodeStatus.SUCCESS;

      case BehaviorNodeStatus.FAILURE:
        this.currentRetries++;
        if (this.currentRetries >= this.maxRetries) {
          this.reset();
          return BehaviorNodeStatus.FAILURE;
        }
        child.reset();
        return BehaviorNodeStatus.RUNNING;

      case BehaviorNodeStatus.RUNNING:
        return BehaviorNodeStatus.RUNNING;

      default:
        return BehaviorNodeStatus.FAILURE;
    }
  }

  public reset(): void {
    super.reset();
    this.currentRetries = 0;
  }
}

/**
 * 超时节点 - 限制子节点执行时间
 */
export class TimeoutNode extends BehaviorNode {
  private timeoutDuration: number;
  private startTime = 0;
  private isStarted = false;

  constructor(id: string, name: string, blackboard: Blackboard, timeoutDuration: number) {
    super(id, name, BehaviorNodeType.TIMEOUT, blackboard);
    this.timeoutDuration = timeoutDuration;
  }

  public execute(deltaTime: number): BehaviorNodeStatus {
    if (this.children.length !== 1) {
      return BehaviorNodeStatus.FAILURE;
    }

    if (!this.isStarted) {
      this.isStarted = true;
      this.startTime = Date.now();
    }

    // 检查是否超时
    const elapsedTime = Date.now() - this.startTime;
    if (elapsedTime >= this.timeoutDuration * 1000) {
      this.reset();
      return BehaviorNodeStatus.FAILURE;
    }

    const child = this.children[0];
    const childStatus = child.execute(deltaTime);

    if (childStatus !== BehaviorNodeStatus.RUNNING) {
      this.reset();
    }

    return childStatus;
  }

  public reset(): void {
    super.reset();
    this.isStarted = false;
    this.startTime = 0;
  }
}

/**
 * AI决策节点 - 使用AI进行决策
 */
export class AIDecisionNode extends BehaviorNode {
  private decisionFunction: (blackboard: Blackboard) => BehaviorNodeStatus;
  private aiModel: string;

  constructor(
    id: string,
    name: string,
    blackboard: Blackboard,
    decisionFunction: (blackboard: Blackboard) => BehaviorNodeStatus,
    aiModel: string = 'default'
  ) {
    super(id, name, BehaviorNodeType.AI_DECISION, blackboard);
    this.decisionFunction = decisionFunction;
    this.aiModel = aiModel;
  }

  public execute(_deltaTime: number): BehaviorNodeStatus {
    try {
      const decision = this.decisionFunction(this.blackboard);
      this.emit('aiDecisionMade', { decision, model: this.aiModel });
      return decision;
    } catch (error) {
      console.error(`AI决策节点 ${this.name} 执行失败:`, error);
      return BehaviorNodeStatus.FAILURE;
    }
  }
}

/**
 * 情感检查节点 - 检查情感状态
 */
export class EmotionCheckNode extends BehaviorNode {
  private emotionType: string;
  private threshold: number;
  private comparison: 'greater' | 'less' | 'equal';

  constructor(
    id: string,
    name: string,
    blackboard: Blackboard,
    emotionType: string,
    threshold: number,
    comparison: 'greater' | 'less' | 'equal' = 'greater'
  ) {
    super(id, name, BehaviorNodeType.EMOTION_CHECK, blackboard);
    this.emotionType = emotionType;
    this.threshold = threshold;
    this.comparison = comparison;
  }

  public execute(_deltaTime: number): BehaviorNodeStatus {
    const emotionValue = this.blackboard.get(`emotion_${this.emotionType}`, 0);

    let conditionMet = false;
    switch (this.comparison) {
      case 'greater':
        conditionMet = emotionValue > this.threshold;
        break;
      case 'less':
        conditionMet = emotionValue < this.threshold;
        break;
      case 'equal':
        conditionMet = Math.abs(emotionValue - this.threshold) < 0.01;
        break;
    }

    this.emit('emotionChecked', {
      emotionType: this.emotionType,
      value: emotionValue,
      threshold: this.threshold,
      result: conditionMet
    });

    return conditionMet ? BehaviorNodeStatus.SUCCESS : BehaviorNodeStatus.FAILURE;
  }
}

/**
 * 记忆访问节点 - 访问和操作记忆系统
 */
export class MemoryAccessNode extends BehaviorNode {
  private operation: 'read' | 'write' | 'search';
  private memoryKey: string;
  private memoryValue?: any;
  private searchQuery?: string;

  constructor(
    id: string,
    name: string,
    blackboard: Blackboard,
    operation: 'read' | 'write' | 'search',
    memoryKey: string,
    memoryValue?: any,
    searchQuery?: string
  ) {
    super(id, name, BehaviorNodeType.MEMORY_ACCESS, blackboard);
    this.operation = operation;
    this.memoryKey = memoryKey;
    this.memoryValue = memoryValue;
    this.searchQuery = searchQuery;
  }

  public execute(_deltaTime: number): BehaviorNodeStatus {
    try {
      switch (this.operation) {
        case 'read':
          const value = this.blackboard.get(`memory_${this.memoryKey}`);
          this.blackboard.set('lastMemoryRead', value);
          this.emit('memoryRead', { key: this.memoryKey, value });
          return value !== undefined ? BehaviorNodeStatus.SUCCESS : BehaviorNodeStatus.FAILURE;

        case 'write':
          this.blackboard.set(`memory_${this.memoryKey}`, this.memoryValue);
          this.emit('memoryWritten', { key: this.memoryKey, value: this.memoryValue });
          return BehaviorNodeStatus.SUCCESS;

        case 'search':
          const searchResults = this.searchMemory(this.searchQuery || '');
          this.blackboard.set('memorySearchResults', searchResults);
          this.emit('memorySearched', { query: this.searchQuery, results: searchResults });
          return searchResults.length > 0 ? BehaviorNodeStatus.SUCCESS : BehaviorNodeStatus.FAILURE;

        default:
          return BehaviorNodeStatus.FAILURE;
      }
    } catch (error) {
      console.error(`记忆访问节点 ${this.name} 执行失败:`, error);
      return BehaviorNodeStatus.FAILURE;
    }
  }

  private searchMemory(query: string): any[] {
    const results: any[] = [];
    const memoryKeys = Object.keys(this.blackboard['data'] || {})
      .filter(key => key.startsWith('memory_'));

    for (const key of memoryKeys) {
      const value = this.blackboard.get(key);
      if (typeof value === 'string' && value.toLowerCase().includes(query.toLowerCase())) {
        results.push({ key: key.replace('memory_', ''), value });
      }
    }

    return results;
  }
}

/**
 * 性能统计接口
 */
export interface PerformanceStats {
  totalExecutions: number;
  averageExecutionTime: number;
  successRate: number;
  failureRate: number;
  lastExecutionTime: number;
}

/**
 * 行为树序列化数据
 */
export interface SerializedBehaviorTree {
  id: string;
  name: string;
  type: BehaviorNodeType;
  metadata: { [key: string]: any };
  children: SerializedBehaviorTree[];
}

/**
 * 行为树引擎
 */
export class BehaviorTreeEngine {
  private trees: Map<string, BehaviorNode> = new Map();
  private blackboards: Map<string, Blackboard> = new Map();
  private isRunning = false;
  private debugMode = false;
  private eventEmitter = new EventEmitter();

  // 性能监控
  private performanceStats: Map<string, PerformanceStats> = new Map();
  private executionHistory: Map<string, Array<{ timestamp: number; status: BehaviorNodeStatus; executionTime: number }>> = new Map();

  /**
   * 创建行为树
   */
  public createTree(treeId: string, rootNode: BehaviorNode): void {
    this.trees.set(treeId, rootNode);

    if (!this.blackboards.has(treeId)) {
      this.blackboards.set(treeId, new Blackboard());
    }

    this.emit('treeCreated', { treeId, rootNode });
  }

  /**
   * 获取行为树
   */
  public getTree(treeId: string): BehaviorNode | undefined {
    return this.trees.get(treeId);
  }

  /**
   * 获取黑板
   */
  public getBlackboard(treeId: string): Blackboard | undefined {
    return this.blackboards.get(treeId);
  }

  /**
   * 执行行为树
   */
  public executeTree(treeId: string, deltaTime: number): BehaviorNodeStatus | null {
    const tree = this.trees.get(treeId);
    if (!tree) {
      return null;
    }

    const startTime = performance.now();
    const status = tree.execute(deltaTime);
    const executionTime = performance.now() - startTime;

    // 更新性能统计
    this.updatePerformanceStats(treeId, status, executionTime);

    if (this.debugMode) {
      console.log(`行为树 ${treeId} 执行状态: ${status}, 执行时间: ${executionTime.toFixed(2)}ms`);
    }

    this.emit('treeExecuted', { treeId, status, executionTime });
    return status;
  }

  /**
   * 重置行为树
   */
  public resetTree(treeId: string): void {
    const tree = this.trees.get(treeId);
    if (tree) {
      tree.reset();
      this.emit('treeReset', { treeId });
    }
  }

  /**
   * 删除行为树
   */
  public removeTree(treeId: string): boolean {
    const removed = this.trees.delete(treeId);
    if (removed) {
      this.blackboards.delete(treeId);
      this.emit('treeRemoved', { treeId });
    }
    return removed;
  }

  /**
   * 设置调试模式
   */
  public setDebugMode(enabled: boolean): void {
    this.debugMode = enabled;
  }

  /**
   * 启动引擎
   */
  public start(): void {
    this.isRunning = true;
    this.emit('engineStarted');
  }

  /**
   * 停止引擎
   */
  public stop(): void {
    this.isRunning = false;
    this.emit('engineStopped');
  }

  /**
   * 检查引擎是否运行
   */
  public getIsRunning(): boolean {
    return this.isRunning;
  }

  /**
   * 更新性能统计
   */
  private updatePerformanceStats(treeId: string, status: BehaviorNodeStatus, executionTime: number): void {
    // 初始化统计数据
    if (!this.performanceStats.has(treeId)) {
      this.performanceStats.set(treeId, {
        totalExecutions: 0,
        averageExecutionTime: 0,
        successRate: 0,
        failureRate: 0,
        lastExecutionTime: 0
      });
    }

    if (!this.executionHistory.has(treeId)) {
      this.executionHistory.set(treeId, []);
    }

    const stats = this.performanceStats.get(treeId)!;
    const history = this.executionHistory.get(treeId)!;

    // 更新统计数据
    stats.totalExecutions++;
    stats.lastExecutionTime = executionTime;

    // 计算平均执行时间
    stats.averageExecutionTime = (stats.averageExecutionTime * (stats.totalExecutions - 1) + executionTime) / stats.totalExecutions;

    // 添加到历史记录
    history.push({
      timestamp: Date.now(),
      status,
      executionTime
    });

    // 保持历史记录在合理范围内
    if (history.length > 1000) {
      history.shift();
    }

    // 计算成功率和失败率
    const successCount = history.filter(h => h.status === BehaviorNodeStatus.SUCCESS).length;
    const failureCount = history.filter(h => h.status === BehaviorNodeStatus.FAILURE).length;
    const totalCount = history.length;

    stats.successRate = totalCount > 0 ? successCount / totalCount : 0;
    stats.failureRate = totalCount > 0 ? failureCount / totalCount : 0;
  }

  /**
   * 获取性能统计
   */
  public getPerformanceStats(treeId: string): PerformanceStats | undefined {
    return this.performanceStats.get(treeId);
  }

  /**
   * 获取所有性能统计
   */
  public getAllPerformanceStats(): Map<string, PerformanceStats> {
    return new Map(this.performanceStats);
  }

  /**
   * 清除性能统计
   */
  public clearPerformanceStats(treeId?: string): void {
    if (treeId) {
      this.performanceStats.delete(treeId);
      this.executionHistory.delete(treeId);
    } else {
      this.performanceStats.clear();
      this.executionHistory.clear();
    }
  }

  /**
   * 序列化行为树
   */
  public serializeTree(treeId: string): SerializedBehaviorTree | null {
    const tree = this.trees.get(treeId);
    if (!tree) {
      return null;
    }

    return this.serializeNode(tree);
  }

  /**
   * 序列化节点
   */
  private serializeNode(node: BehaviorNode): SerializedBehaviorTree {
    return {
      id: node.id,
      name: node.name,
      type: node.type,
      metadata: { ...node.metadata },
      children: node.children.map(child => this.serializeNode(child))
    };
  }

  /**
   * 反序列化行为树
   */
  public deserializeTree(treeId: string, data: SerializedBehaviorTree, blackboard?: Blackboard): BehaviorNode | null {
    try {
      const bb = blackboard || this.blackboards.get(treeId) || new Blackboard();
      const rootNode = this.deserializeNode(data, bb);

      if (rootNode) {
        this.createTree(treeId, rootNode);
        if (blackboard) {
          this.blackboards.set(treeId, blackboard);
        }
      }

      return rootNode;
    } catch (error) {
      console.error(`反序列化行为树 ${treeId} 失败:`, error);
      return null;
    }
  }

  /**
   * 反序列化节点
   */
  private deserializeNode(data: SerializedBehaviorTree, blackboard: Blackboard): BehaviorNode | null {
    let node: BehaviorNode | null = null;

    // 根据类型创建节点
    switch (data.type) {
      case BehaviorNodeType.SEQUENCE:
        node = new SequenceNode(data.id, data.name, blackboard);
        break;
      case BehaviorNodeType.SELECTOR:
        node = new SelectorNode(data.id, data.name, blackboard);
        break;
      case BehaviorNodeType.PARALLEL:
        node = new ParallelNode(data.id, data.name, blackboard);
        break;
      case BehaviorNodeType.INVERTER:
        node = new InverterNode(data.id, data.name, blackboard);
        break;
      case BehaviorNodeType.REPEATER:
        node = new RepeaterNode(data.id, data.name, blackboard);
        break;
      case BehaviorNodeType.RETRY:
        node = new RetryNode(data.id, data.name, blackboard);
        break;
      case BehaviorNodeType.TIMEOUT:
        node = new TimeoutNode(data.id, data.name, blackboard, data.metadata.timeoutDuration || 5);
        break;
      case BehaviorNodeType.WAIT:
        node = new WaitNode(data.id, data.name, blackboard, data.metadata.waitTime || 1);
        break;
      // 注意：抽象节点需要具体实现类才能反序列化
      default:
        console.warn(`无法反序列化节点类型: ${data.type}`);
        return null;
    }

    if (node) {
      // 设置元数据
      node.metadata = { ...data.metadata };

      // 递归创建子节点
      for (const childData of data.children) {
        const childNode = this.deserializeNode(childData, blackboard);
        if (childNode) {
          node.addChild(childNode);
        }
      }
    }

    return node;
  }

  /**
   * 触发事件
   */
  private emit(event: string, data?: any): void {
    this.eventEmitter.emit(event, data);
  }

  /**
   * 监听事件
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 获取所有行为树ID
   */
  public getTreeIds(): string[] {
    return Array.from(this.trees.keys());
  }

  /**
   * 获取行为树节点数量
   */
  public getTreeNodeCount(treeId: string): number {
    const tree = this.trees.get(treeId);
    if (!tree) {
      return 0;
    }
    return this.countNodes(tree);
  }

  /**
   * 递归计算节点数量
   */
  private countNodes(node: BehaviorNode): number {
    let count = 1; // 当前节点
    for (const child of node.children) {
      count += this.countNodes(child);
    }
    return count;
  }

  /**
   * 查找节点
   */
  public findNode(treeId: string, nodeId: string): BehaviorNode | null {
    const tree = this.trees.get(treeId);
    if (!tree) {
      return null;
    }
    return this.searchNode(tree, nodeId);
  }

  /**
   * 递归搜索节点
   */
  private searchNode(node: BehaviorNode, nodeId: string): BehaviorNode | null {
    if (node.id === nodeId) {
      return node;
    }

    for (const child of node.children) {
      const found = this.searchNode(child, nodeId);
      if (found) {
        return found;
      }
    }

    return null;
  }

  /**
   * 验证行为树结构
   */
  public validateTree(treeId: string): { isValid: boolean; errors: string[] } {
    const tree = this.trees.get(treeId);
    if (!tree) {
      return { isValid: false, errors: ['行为树不存在'] };
    }

    const errors: string[] = [];
    this.validateNode(tree, errors);

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 递归验证节点
   */
  private validateNode(node: BehaviorNode, errors: string[]): void {
    // 检查装饰节点是否只有一个子节点
    if ([BehaviorNodeType.INVERTER, BehaviorNodeType.REPEATER, BehaviorNodeType.RETRY, BehaviorNodeType.TIMEOUT].includes(node.type)) {
      if (node.children.length !== 1) {
        errors.push(`装饰节点 ${node.name} (${node.id}) 必须有且仅有一个子节点`);
      }
    }

    // 检查叶子节点是否有子节点
    if ([BehaviorNodeType.ACTION, BehaviorNodeType.CONDITION, BehaviorNodeType.WAIT].includes(node.type)) {
      if (node.children.length > 0) {
        errors.push(`叶子节点 ${node.name} (${node.id}) 不应该有子节点`);
      }
    }

    // 检查复合节点是否有子节点
    if ([BehaviorNodeType.SEQUENCE, BehaviorNodeType.SELECTOR, BehaviorNodeType.PARALLEL].includes(node.type)) {
      if (node.children.length === 0) {
        errors.push(`复合节点 ${node.name} (${node.id}) 应该至少有一个子节点`);
      }
    }

    // 递归验证子节点
    for (const child of node.children) {
      this.validateNode(child, errors);
    }
  }

  /**
   * 获取调试信息
   */
  public getDebugInfo(treeId: string): any {
    const tree = this.trees.get(treeId);
    const blackboard = this.blackboards.get(treeId);
    const stats = this.performanceStats.get(treeId);

    return {
      treeExists: !!tree,
      nodeCount: tree ? this.getTreeNodeCount(treeId) : 0,
      blackboardData: blackboard ? { ...blackboard['data'] } : {},
      performanceStats: stats ? { ...stats } : null,
      validation: tree ? this.validateTree(treeId) : null
    };
  }
}
