import { NestFactory } from '@nestjs/core';
import { Transport } from '@nestjs/microservices';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EdgeGameServerModule } from './edge-game-server.module';
import { EdgeNodeRegistrationService } from './services/edge-node-registration.service';

/**
 * 边缘游戏服务器启动入口
 * 轻量级版本，专为边缘节点部署优化
 */
async function bootstrap() {
  const app = await NestFactory.create(EdgeGameServerModule);
  const configService = app.get(ConfigService);
  const logger = new Logger('EdgeGameServer');
  
  // 获取边缘节点配置
  const edgeNodeId = configService.get<string>('EDGE_NODE_ID', `edge-${Date.now()}`);
  const edgeRegion = configService.get<string>('EDGE_REGION', 'default');
  const centralHubUrl = configService.get<string>('CENTRAL_HUB_URL', 'http://central-hub:3000');
  
  // 配置轻量级微服务（仅保留核心功能）
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: configService.get<string>('EDGE_GAME_SERVER_HOST', '0.0.0.0'),
      port: configService.get<number>('EDGE_GAME_SERVER_PORT', 3030),
    },
  });
  
  // 全局配置
  app.setGlobalPrefix('api/edge');
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );
  
  // 启用CORS（边缘节点需要支持跨域）
  app.enableCors({
    origin: configService.get<string>('CORS_ORIGIN', '*'),
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });
  
  // 启动微服务
  await app.startAllMicroservices();
  
  // 启动HTTP服务
  const port = configService.get<number>('EDGE_HTTP_PORT', 8080);
  await app.listen(port);
  
  // 注册到中心节点
  const registrationService = app.get(EdgeNodeRegistrationService);
  await registrationService.registerToHub(centralHubUrl, {
    nodeId: edgeNodeId,
    region: edgeRegion,
    endpoint: `http://localhost:${port}`,
    capabilities: {
      maxUsers: configService.get<number>('MAX_USERS_PER_EDGE', 50),
      supportedFeatures: ['webrtc', 'realtime-sync', 'lightweight-rendering'],
      resources: {
        cpu: '1000m',
        memory: '1Gi',
        storage: '5Gi'
      }
    }
  });
  
  logger.log(`边缘游戏服务器已启动`);
  logger.log(`节点ID: ${edgeNodeId}`);
  logger.log(`区域: ${edgeRegion}`);
  logger.log(`HTTP端口: ${port}`);
  logger.log(`微服务端口: ${configService.get<number>('EDGE_GAME_SERVER_PORT', 3030)}`);
  logger.log(`中心节点: ${centralHubUrl}`);
}

bootstrap().catch((err) => {
  console.error('启动边缘游戏服务器失败:', err);
  process.exit(1);
});
