/**
 * BART模型演示脚本
 * 用于验证BARTModel的功能实现
 */
import { BARTModel, BARTModelConfig } from './BARTModel';

async function runBARTDemo() {
  console.log('🚀 开始BART模型功能演示...\n');

  // 创建模型配置
  const config: BARTModelConfig = {
    variant: 'large',
    maxLength: 512,
    minLength: 10,
    useBeamSearch: true,
    beamSize: 4,
    debug: true,
    dialogueContextLength: 5,
    qaConfidenceThreshold: 0.7
  };

  // 创建模型实例
  const model = new BARTModel(config);

  try {
    // 1. 初始化模型
    console.log('📋 1. 初始化模型...');
    const initResult = await model.initialize();
    console.log('初始化结果:', initResult ? '✅ 成功' : '❌ 失败');
    console.log('模型ID:', model.getId());
    console.log('模型类型:', model.getType());
    console.log();

    // 2. 文本生成
    console.log('📋 2. 文本生成测试...');
    const generateResult = await model.generateText('请生成一段关于人工智能的介绍');
    console.log('生成的文本:', generateResult);
    console.log();

    // 3. 文本摘要
    console.log('📋 3. 文本摘要测试...');
    const longText = `
      人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，
      并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、
      语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，
      应用领域也不断扩大，可以设想，未来人工智能带来的科技产品，将会是人类智慧的"容器"。
      BART模型是Facebook开发的一种序列到序列的预训练模型，特别适合文本摘要、翻译等任务。
    `.trim();
    
    const summaryResult = await model.summarizeText(longText, 80);
    console.log('原文长度:', longText.length);
    console.log('摘要:', summaryResult.summary);
    console.log('摘要长度:', summaryResult.length);
    console.log('压缩率:', (summaryResult.compressionRate * 100).toFixed(1) + '%');
    console.log();

    // 4. 翻译功能
    console.log('📋 4. 翻译功能测试...');
    const translationTexts = [
      { text: 'Hello, how are you today?', target: 'zh' },
      { text: '你好，今天天气怎么样？', target: 'en' },
      { text: 'Artificial intelligence is amazing', target: 'fr' }
    ];

    for (const item of translationTexts) {
      const translationResult = await model.translateText(item.text, item.target);
      console.log(`原文 (${translationResult.sourceLanguage}): "${item.text}"`);
      console.log(`译文 (${translationResult.targetLanguage}): "${translationResult.translatedText}"`);
      console.log(`置信度: ${translationResult.confidence.toFixed(2)}`);
      console.log();
    }

    // 5. 文本分类
    console.log('📋 5. 文本分类测试...');
    const classificationTexts = [
      '这个产品质量很好，我很满意！',
      '人工智能技术发展迅速，应用前景广阔',
      '今天的比赛非常精彩，球员表现出色'
    ];

    for (const text of classificationTexts) {
      const classificationResult = await model.classifyText(text);
      console.log(`文本: "${text}"`);
      console.log(`分类: ${classificationResult.label} (置信度: ${classificationResult.confidence.toFixed(2)})`);
      console.log();
    }

    // 6. 情感分析
    console.log('📋 6. 情感分析测试...');
    const emotionTexts = [
      '我今天心情很好！',
      '这让我感到很愤怒',
      '我对此感到惊讶',
      '这是一个普通的日子'
    ];

    for (const text of emotionTexts) {
      const emotionResult = await model.analyzeEmotion(text);
      console.log(`文本: "${text}"`);
      console.log(`主要情感: ${emotionResult.primaryEmotion} (强度: ${emotionResult.intensity.toFixed(2)})`);
      console.log();
    }

    // 7. 命名实体识别
    console.log('📋 7. 命名实体识别测试...');
    const entityText = '张三在北京的清华大学工作，他的电话是13800138000';
    const entityResult = await model.recognizeEntities(entityText);
    console.log('输入文本:', entityText);
    console.log('识别的实体:');
    entityResult.entities.forEach((entity, index) => {
      console.log(`  ${index + 1}. ${entity.text} (${entity.type}) - 置信度: ${entity.confidence.toFixed(2)}`);
    });
    console.log();

    // 8. 问答系统
    console.log('📋 8. 问答系统测试...');
    const questions = [
      { question: '什么是人工智能？', context: '人工智能是计算机科学的一个分支' },
      { question: '如何学习机器学习？', context: undefined },
      { question: '为什么BART模型适合摘要任务？', context: 'BART是序列到序列的预训练模型' }
    ];

    for (const item of questions) {
      const qaResult = await model.answerQuestion(item.question, { context: item.context });
      console.log(`问题: "${item.question}"`);
      console.log(`回答: "${qaResult.answer}"`);
      console.log(`置信度: ${qaResult.confidence.toFixed(2)}`);
      console.log();
    }

    // 9. 对话处理
    console.log('📋 9. 对话处理测试...');
    const dialogueInputs = [
      '你好',
      '你能帮我做什么？',
      '谢谢你的帮助',
      '再见'
    ];

    for (const input of dialogueInputs) {
      const dialogueResult = await model.processDialogue(input, 'session1', 'user1');
      console.log(`用户: "${input}"`);
      console.log(`助手: "${dialogueResult.response}"`);
      console.log(`意图: ${dialogueResult.intent}`);
      console.log();
    }

    // 10. 语言检测
    console.log('📋 10. 语言检测测试...');
    const languageTexts = [
      '这是一段中文文本',
      'This is an English text',
      'Ceci est un texte français',
      'Dies ist ein deutscher Text'
    ];

    for (const text of languageTexts) {
      const languageResult = await model.detectLanguage(text);
      console.log(`"${text}" -> 语言: ${languageResult.language} (置信度: ${languageResult.confidence.toFixed(2)})`);
    }
    console.log();

    // 11. 文本纠错
    console.log('📋 11. 文本纠错测试...');
    const errorTexts = [
      'teh quick brown fox',
      '你好吗',
      'recieve the message',
      '谢谢你'
    ];

    for (const text of errorTexts) {
      const correctionResult = await model.correctText(text);
      if (correctionResult.corrections.length > 0) {
        console.log(`原文: "${text}"`);
        console.log(`纠错: "${correctionResult.correctedText}"`);
        console.log(`错误数: ${correctionResult.statistics?.totalErrors || 0}`);
      } else {
        console.log(`"${text}" -> 无需纠错`);
      }
    }
    console.log();

    console.log('🎉 BART模型功能演示完成！');

  } catch (error) {
    console.error('❌ 演示过程中发生错误:', error);
  } finally {
    // 清理资源
    model.dispose();
    console.log('🧹 资源已清理');
  }
}

// 运行演示
if (require.main === module) {
  runBARTDemo().catch(console.error);
}

export { runBARTDemo };
