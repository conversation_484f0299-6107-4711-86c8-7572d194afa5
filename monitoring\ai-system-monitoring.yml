# AI系统监控配置
# 包含Prometheus、<PERSON><PERSON>和AlertManager配置

apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-ai-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
      - "/etc/prometheus/rules/*.yml"
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
    
    scrape_configs:
      # AI推荐服务监控
      - job_name: 'recommendation-service'
        static_configs:
          - targets: ['recommendation-service.ai-services:3009']
        metrics_path: '/metrics'
        scrape_interval: 10s
        scrape_timeout: 5s
      
      # AI行为分析服务监控
      - job_name: 'behavior-analysis-service'
        static_configs:
          - targets: ['behavior-analysis-service.ai-services:3010']
        metrics_path: '/metrics'
        scrape_interval: 15s
      
      # AI内容分析服务监控
      - job_name: 'content-analysis-service'
        static_configs:
          - targets: ['content-analysis-service.ai-services:3011']
        metrics_path: '/metrics'
        scrape_interval: 15s
      
      # AI模型服务监控
      - job_name: 'ai-model-service'
        static_configs:
          - targets: ['ai-model-service.ai-services:3008']
        metrics_path: '/metrics'
        scrape_interval: 10s

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-ai-rules
  namespace: monitoring
data:
  ai-system-rules.yml: |
    groups:
    - name: ai-recommendation-alerts
      rules:
      # 推荐系统性能告警
      - alert: RecommendationHighLatency
        expr: histogram_quantile(0.95, rate(recommendation_request_duration_seconds_bucket[5m])) > 2
        for: 2m
        labels:
          severity: warning
          service: recommendation
        annotations:
          summary: "推荐服务响应时间过高"
          description: "推荐服务95%分位响应时间超过2秒，当前值: {{ $value }}秒"
      
      - alert: RecommendationLowAccuracy
        expr: recommendation_accuracy_score < 0.7
        for: 5m
        labels:
          severity: critical
          service: recommendation
        annotations:
          summary: "推荐准确率过低"
          description: "推荐系统准确率低于70%，当前值: {{ $value }}"
      
      - alert: RecommendationHighErrorRate
        expr: rate(recommendation_errors_total[5m]) / rate(recommendation_requests_total[5m]) > 0.05
        for: 3m
        labels:
          severity: warning
          service: recommendation
        annotations:
          summary: "推荐服务错误率过高"
          description: "推荐服务错误率超过5%，当前值: {{ $value | humanizePercentage }}"
    
    - name: ai-model-alerts
      rules:
      # AI模型性能告警
      - alert: ModelInferenceHighLatency
        expr: histogram_quantile(0.95, rate(model_inference_duration_seconds_bucket[5m])) > 1
        for: 2m
        labels:
          severity: warning
          service: ai-model
        annotations:
          summary: "AI模型推理延迟过高"
          description: "AI模型推理95%分位延迟超过1秒，当前值: {{ $value }}秒"
      
      - alert: ModelMemoryUsageHigh
        expr: model_memory_usage_bytes / model_memory_limit_bytes > 0.9
        for: 5m
        labels:
          severity: critical
          service: ai-model
        annotations:
          summary: "AI模型内存使用率过高"
          description: "AI模型内存使用率超过90%，当前值: {{ $value | humanizePercentage }}"
      
      - alert: ModelThroughputLow
        expr: rate(model_inference_requests_total[5m]) < 10
        for: 10m
        labels:
          severity: warning
          service: ai-model
        annotations:
          summary: "AI模型吞吐量过低"
          description: "AI模型每秒处理请求数低于10，当前值: {{ $value }}"
    
    - name: ai-behavior-analysis-alerts
      rules:
      # 行为分析告警
      - alert: BehaviorAnalysisQueueBacklog
        expr: behavior_analysis_queue_size > 1000
        for: 5m
        labels:
          severity: warning
          service: behavior-analysis
        annotations:
          summary: "行为分析队列积压"
          description: "行为分析队列大小超过1000，当前值: {{ $value }}"
      
      - alert: AnomalyDetectionHighRate
        expr: rate(anomaly_detection_alerts_total[1h]) > 50
        for: 10m
        labels:
          severity: warning
          service: behavior-analysis
        annotations:
          summary: "异常检测告警频率过高"
          description: "每小时异常检测告警超过50次，可能存在系统问题"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-ai-dashboard
  namespace: monitoring
data:
  ai-system-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "AI系统监控仪表板",
        "tags": ["ai", "recommendation", "monitoring"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "推荐系统性能指标",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(recommendation_requests_total[5m])",
                "legendFormat": "请求率 (req/s)"
              },
              {
                "expr": "histogram_quantile(0.95, rate(recommendation_request_duration_seconds_bucket[5m]))",
                "legendFormat": "95%分位延迟"
              },
              {
                "expr": "rate(recommendation_errors_total[5m]) / rate(recommendation_requests_total[5m])",
                "legendFormat": "错误率"
              }
            ],
            "yAxes": [
              {
                "label": "请求/秒",
                "min": 0
              },
              {
                "label": "延迟(秒)",
                "min": 0
              }
            ],
            "gridPos": {
              "h": 8,
              "w": 12,
              "x": 0,
              "y": 0
            }
          },
          {
            "id": 2,
            "title": "推荐质量指标",
            "type": "stat",
            "targets": [
              {
                "expr": "recommendation_precision_score",
                "legendFormat": "精确率"
              },
              {
                "expr": "recommendation_recall_score",
                "legendFormat": "召回率"
              },
              {
                "expr": "recommendation_f1_score",
                "legendFormat": "F1分数"
              },
              {
                "expr": "recommendation_diversity_score",
                "legendFormat": "多样性"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "min": 0,
                "max": 1,
                "unit": "percentunit"
              }
            },
            "gridPos": {
              "h": 8,
              "w": 12,
              "x": 12,
              "y": 0
            }
          },
          {
            "id": 3,
            "title": "AI模型推理性能",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(model_inference_requests_total[5m])",
                "legendFormat": "推理请求率"
              },
              {
                "expr": "histogram_quantile(0.95, rate(model_inference_duration_seconds_bucket[5m]))",
                "legendFormat": "推理延迟(95%)"
              },
              {
                "expr": "model_memory_usage_bytes / 1024 / 1024 / 1024",
                "legendFormat": "内存使用(GB)"
              }
            ],
            "gridPos": {
              "h": 8,
              "w": 12,
              "x": 0,
              "y": 8
            }
          },
          {
            "id": 4,
            "title": "用户行为分析",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(user_interactions_total[5m])",
                "legendFormat": "用户交互率"
              },
              {
                "expr": "behavior_pattern_detection_rate",
                "legendFormat": "模式检测率"
              },
              {
                "expr": "rate(anomaly_detection_alerts_total[5m])",
                "legendFormat": "异常检测率"
              }
            ],
            "gridPos": {
              "h": 8,
              "w": 12,
              "x": 12,
              "y": 8
            }
          },
          {
            "id": 5,
            "title": "缓存性能",
            "type": "graph",
            "targets": [
              {
                "expr": "recommendation_cache_hit_rate",
                "legendFormat": "推荐缓存命中率"
              },
              {
                "expr": "model_cache_hit_rate",
                "legendFormat": "模型缓存命中率"
              },
              {
                "expr": "feature_cache_hit_rate",
                "legendFormat": "特征缓存命中率"
              }
            ],
            "yAxes": [
              {
                "label": "命中率",
                "min": 0,
                "max": 1,
                "unit": "percentunit"
              }
            ],
            "gridPos": {
              "h": 8,
              "w": 24,
              "x": 0,
              "y": 16
            }
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "30s"
      }
    }

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-ai-config
  namespace: monitoring
data:
  alertmanager.yml: |
    global:
      smtp_smarthost: 'smtp.gmail.com:587'
      smtp_from: '<EMAIL>'
    
    route:
      group_by: ['alertname', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'ai-team'
      routes:
      - match:
          severity: critical
        receiver: 'ai-team-critical'
      - match:
          service: recommendation
        receiver: 'recommendation-team'
      - match:
          service: ai-model
        receiver: 'ml-team'
    
    receivers:
    - name: 'ai-team'
      email_configs:
      - to: '<EMAIL>'
        subject: '[AI系统] {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          服务: {{ .Labels.service }}
          严重程度: {{ .Labels.severity }}
          时间: {{ .StartsAt }}
          {{ end }}
      webhook_configs:
      - url: 'http://slack-webhook-service:8080/alerts'
    
    - name: 'ai-team-critical'
      email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[紧急] AI系统严重告警 - {{ .GroupLabels.alertname }}'
        body: |
          🚨 紧急告警 🚨
          
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          服务: {{ .Labels.service }}
          时间: {{ .StartsAt }}
          {{ end }}
          
          请立即处理！
      webhook_configs:
      - url: 'http://pagerduty-webhook-service:8080/critical'
    
    - name: 'recommendation-team'
      email_configs:
      - to: '<EMAIL>'
        subject: '[推荐系统] {{ .GroupLabels.alertname }}'
        body: |
          推荐系统告警详情:
          {{ range .Alerts }}
          - {{ .Annotations.summary }}
          {{ end }}
    
    - name: 'ml-team'
      email_configs:
      - to: '<EMAIL>'
        subject: '[AI模型] {{ .GroupLabels.alertname }}'
        body: |
          AI模型告警详情:
          {{ range .Alerts }}
          - {{ .Annotations.summary }}
          {{ end }}

---
# AI系统性能测试配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-performance-test-config
  namespace: monitoring
data:
  load-test.js: |
    import http from 'k6/http';
    import { check, sleep } from 'k6';
    import { Rate } from 'k6/metrics';
    
    export let errorRate = new Rate('errors');
    
    export let options = {
      stages: [
        { duration: '2m', target: 100 }, // 2分钟内增加到100用户
        { duration: '5m', target: 100 }, // 保持100用户5分钟
        { duration: '2m', target: 200 }, // 2分钟内增加到200用户
        { duration: '5m', target: 200 }, // 保持200用户5分钟
        { duration: '2m', target: 0 },   // 2分钟内减少到0用户
      ],
      thresholds: {
        http_req_duration: ['p(95)<2000'], // 95%的请求在2秒内完成
        errors: ['rate<0.05'],             // 错误率低于5%
      },
    };
    
    export default function() {
      // 测试推荐API
      let recommendationResponse = http.post('http://recommendation-service.ai-services:3009/recommendations/assets', {
        userId: 'test-user-' + Math.floor(Math.random() * 1000),
        type: 'asset',
        count: 10,
        context: {
          projectId: 'test-project',
          currentActivity: 'modeling'
        }
      });
      
      check(recommendationResponse, {
        'recommendation status is 200': (r) => r.status === 200,
        'recommendation response time < 2s': (r) => r.timings.duration < 2000,
        'recommendation returns results': (r) => JSON.parse(r.body).length > 0,
      }) || errorRate.add(1);
      
      // 测试AI模型推理API
      let inferenceResponse = http.post('http://ai-model-service.ai-services:3008/inference', {
        modelId: 'test-model',
        input: {
          text: 'test input for AI model'
        }
      });
      
      check(inferenceResponse, {
        'inference status is 200': (r) => r.status === 200,
        'inference response time < 1s': (r) => r.timings.duration < 1000,
      }) || errorRate.add(1);
      
      sleep(1);
    }
