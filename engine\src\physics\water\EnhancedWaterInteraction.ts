/**
 * 增强的水体交互系统
 * 提供高质量的水体与物体交互效果，包括浮力、阻力、波浪、飞溅等
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import { PhysicsBody } from '../PhysicsBody';
import { WaterBodyComponent } from './WaterBodyComponent';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 水体交互配置
 */
export interface WaterInteractionConfig {
  /** 是否启用浮力 */
  enableBuoyancy: boolean;
  /** 是否启用阻力 */
  enableDrag: boolean;
  /** 是否启用波浪生成 */
  enableWaveGeneration: boolean;
  /** 是否启用飞溅效果 */
  enableSplash: boolean;
  /** 是否启用涟漪效果 */
  enableRipples: boolean;
  /** 水密度 */
  waterDensity: number;
  /** 重力加速度 */
  gravity: number;
  /** 阻力系数 */
  dragCoefficient: number;
  /** 波浪强度 */
  waveStrength: number;
  /** 飞溅阈值 */
  splashThreshold: number;
  /** 性能优化 */
  performance: {
    maxInteractingObjects: number;
    updateFrequency: number;
    enableSpatialHashing: boolean;
  };
}

/**
 * 水体交互数据
 */
export interface WaterInteractionData {
  /** 物体ID */
  objectId: string;
  /** 物理体 */
  physicsBody: PhysicsBody;
  /** 浸入体积 */
  submergedVolume: number;
  /** 浸入深度 */
  submersionDepth: number;
  /** 接触面积 */
  contactArea: number;
  /** 相对速度 */
  relativeVelocity: THREE.Vector3;
  /** 浮力 */
  buoyancyForce: THREE.Vector3;
  /** 阻力 */
  dragForce: THREE.Vector3;
  /** 是否在水中 */
  inWater: boolean;
  /** 进入水体时间 */
  entryTime: number;
  /** 最后更新时间 */
  lastUpdateTime: number;
}

/**
 * 波浪数据
 */
export interface WaveData {
  /** 波浪ID */
  id: string;
  /** 位置 */
  position: THREE.Vector3;
  /** 振幅 */
  amplitude: number;
  /** 频率 */
  frequency: number;
  /** 相位 */
  phase: number;
  /** 衰减率 */
  decay: number;
  /** 创建时间 */
  creationTime: number;
  /** 生存时间 */
  lifetime: number;
}

/**
 * 飞溅效果数据
 */
export interface SplashData {
  /** 飞溅ID */
  id: string;
  /** 位置 */
  position: THREE.Vector3;
  /** 强度 */
  intensity: number;
  /** 粒子数量 */
  particleCount: number;
  /** 速度范围 */
  velocityRange: { min: number; max: number };
  /** 生存时间 */
  lifetime: number;
  /** 创建时间 */
  creationTime: number;
}

/**
 * 增强水体交互系统
 */
export class EnhancedWaterInteraction extends System {
  public static readonly TYPE = 'EnhancedWaterInteraction';

  private config: WaterInteractionConfig;
  private waterBodies: Map<string, WaterBodyComponent>;
  private interactionData: Map<string, WaterInteractionData>;
  private waves: Map<string, WaveData>;
  private splashes: Map<string, SplashData>;
  private spatialHash: Map<string, Set<string>>;
  private performanceMonitor: PerformanceMonitor;
  private eventEmitter: EventEmitter;
  private time: number;

  constructor(config: WaterInteractionConfig) {
    super(0, { enabled: true });
    this.config = config;
    this.waterBodies = new Map();
    this.interactionData = new Map();
    this.waves = new Map();
    this.splashes = new Map();
    this.spatialHash = new Map();
    this.performanceMonitor = PerformanceMonitor.getInstance();
    this.eventEmitter = new EventEmitter();
    this.time = 0;

    this.initializeSystem();
  }

  /**
   * 初始化系统
   */
  private initializeSystem(): void {
    this.performanceMonitor.start();
    
    Debug.log('增强水体交互系统初始化完成', `浮力: ${this.config.enableBuoyancy}, 阻力: ${this.config.enableDrag}, 波浪生成: ${this.config.enableWaveGeneration}, 飞溅: ${this.config.enableSplash}`);
  }

  /**
   * 添加水体
   */
  public addWaterBody(id: string, waterBody: WaterBodyComponent): void {
    this.waterBodies.set(id, waterBody);
    Debug.log(`添加水体: ${id}`);
  }

  /**
   * 移除水体
   */
  public removeWaterBody(id: string): void {
    this.waterBodies.delete(id);
    Debug.log(`移除水体: ${id}`);
  }

  /**
   * 添加交互物体
   */
  public addInteractingObject(objectId: string, physicsBody: PhysicsBody): void {
    const interactionData: WaterInteractionData = {
      objectId,
      physicsBody,
      submergedVolume: 0,
      submersionDepth: 0,
      contactArea: 0,
      relativeVelocity: new THREE.Vector3(),
      buoyancyForce: new THREE.Vector3(),
      dragForce: new THREE.Vector3(),
      inWater: false,
      entryTime: 0,
      lastUpdateTime: this.time
    };

    this.interactionData.set(objectId, interactionData);
    Debug.log(`添加交互物体: ${objectId}`);
  }

  /**
   * 移除交互物体
   */
  public removeInteractingObject(objectId: string): void {
    this.interactionData.delete(objectId);
    Debug.log(`移除交互物体: ${objectId}`);
  }

  /**
   * 更新系统
   */
  public update(deltaTime: number): void {
    this.time += deltaTime;

    // 更新空间哈希
    if (this.config.performance.enableSpatialHashing) {
      this.updateSpatialHash();
    }

    // 更新物体与水体的交互
    this.updateWaterInteractions(deltaTime);

    // 更新波浪
    if (this.config.enableWaveGeneration) {
      this.updateWaves(deltaTime);
    }

    // 更新飞溅效果
    if (this.config.enableSplash) {
      this.updateSplashes(deltaTime);
    }

    // 清理过期数据
    this.cleanupExpiredData();
  }

  /**
   * 更新空间哈希
   */
  private updateSpatialHash(): void {
    this.spatialHash.clear();

    for (const [objectId, data] of this.interactionData.entries()) {
      const position = data.physicsBody.getPositionVector3();
      const hashKey = this.getHashKey(position);

      if (!this.spatialHash.has(hashKey)) {
        this.spatialHash.set(hashKey, new Set());
      }

      this.spatialHash.get(hashKey)!.add(objectId);
    }
  }

  /**
   * 获取哈希键
   */
  private getHashKey(position: THREE.Vector3): string {
    const cellSize = 10; // 10米网格
    const x = Math.floor(position.x / cellSize);
    const z = Math.floor(position.z / cellSize);
    return `${x},${z}`;
  }

  /**
   * 更新水体交互
   */
  private updateWaterInteractions(deltaTime: number): void {
    for (const [objectId, data] of this.interactionData.entries()) {
      // 检查物体是否与水体相交
      const waterBody = this.findIntersectingWaterBody(data.physicsBody);
      
      if (waterBody) {
        this.updateObjectWaterInteraction(data, waterBody, deltaTime);
      } else {
        // 物体不在水中
        if (data.inWater) {
          this.handleObjectExitWater(data);
        }
        data.inWater = false;
      }
    }
  }

  /**
   * 查找相交的水体
   */
  private findIntersectingWaterBody(physicsBody: PhysicsBody): WaterBodyComponent | null {
    const position = physicsBody.getPositionVector3();

    for (const waterBody of this.waterBodies.values()) {
      if (this.isPositionInWater(position, waterBody)) {
        return waterBody;
      }
    }

    return null;
  }

  /**
   * 检查位置是否在水中
   */
  private isPositionInWater(position: THREE.Vector3, waterBody: WaterBodyComponent): boolean {
    // 简化的水面高度检查
    const waterPosition = waterBody.getPosition();
    const waterSize = waterBody.getSize();
    const waterLevel = waterPosition.y + waterSize.height / 2;

    // 检查是否在水体范围内
    const inXRange = Math.abs(position.x - waterPosition.x) <= waterSize.width / 2;
    const inZRange = Math.abs(position.z - waterPosition.z) <= waterSize.depth / 2;
    const belowWaterLevel = position.y < waterLevel;

    return inXRange && inZRange && belowWaterLevel;
  }

  /**
   * 更新物体与水体的交互
   */
  private updateObjectWaterInteraction(
    data: WaterInteractionData,
    waterBody: WaterBodyComponent,
    deltaTime: number
  ): void {
    const position = data.physicsBody.getPositionVector3();
    const velocity = data.physicsBody.getVelocity();

    // 计算浸入数据
    this.calculateSubmersionData(data, waterBody);

    // 处理物体进入水体
    if (!data.inWater) {
      this.handleObjectEnterWater(data, waterBody);
    }

    data.inWater = true;

    // 计算浮力
    if (this.config.enableBuoyancy) {
      this.calculateBuoyancyForce(data, waterBody);
      data.physicsBody.applyForce(data.buoyancyForce);
    }

    // 计算阻力
    if (this.config.enableDrag) {
      this.calculateDragForce(data, waterBody);
      data.physicsBody.applyForce(data.dragForce);
    }

    // 生成波浪
    if (this.config.enableWaveGeneration && velocity.length() > 0.1) {
      this.generateWave(position, velocity.length());
    }

    data.lastUpdateTime = this.time;
  }

  /**
   * 计算浸入数据
   */
  private calculateSubmersionData(data: WaterInteractionData, waterBody: WaterBodyComponent): void {
    const position = data.physicsBody.getPositionVector3();

    // 获取水体信息
    const waterPosition = waterBody.getPosition();
    const waterSize = waterBody.getSize();
    const waterLevel = waterPosition.y + waterSize.height / 2;

    // 简化的物体边界计算（假设为1x1x1的立方体）
    const objectSize = 1.0;
    const objectBottom = position.y - objectSize / 2;

    // 计算浸入深度
    data.submersionDepth = Math.max(0, waterLevel - objectBottom);

    // 计算浸入体积（简化计算）
    const objectHeight = objectSize;
    const submersionRatio = Math.min(1, data.submersionDepth / objectHeight);
    data.submergedVolume = objectSize * objectSize * objectSize * submersionRatio;

    // 计算接触面积
    data.contactArea = objectSize * objectSize * submersionRatio;
  }

  /**
   * 计算浮力
   */
  private calculateBuoyancyForce(data: WaterInteractionData, _waterBody: WaterBodyComponent): void {
    // 阿基米德原理：浮力 = 水密度 × 重力 × 浸入体积
    const buoyancyMagnitude = this.config.waterDensity * this.config.gravity * data.submergedVolume;

    data.buoyancyForce.set(0, buoyancyMagnitude, 0);
  }

  /**
   * 计算阻力
   */
  private calculateDragForce(data: WaterInteractionData, _waterBody: WaterBodyComponent): void {
    const velocity = data.physicsBody.getVelocity();
    const speed = velocity.length();

    if (speed > 0) {
      // 阻力 = 0.5 × 阻力系数 × 水密度 × 速度² × 接触面积
      const dragMagnitude = 0.5 * this.config.dragCoefficient * this.config.waterDensity *
                           speed * speed * data.contactArea;

      // 阻力方向与速度方向相反
      data.dragForce.copy(velocity).normalize().multiplyScalar(-dragMagnitude);
    } else {
      data.dragForce.set(0, 0, 0);
    }
  }

  /**
   * 处理物体进入水体
   */
  private handleObjectEnterWater(data: WaterInteractionData, waterBody: WaterBodyComponent): void {
    data.entryTime = this.time;

    const velocity = data.physicsBody.getVelocity();
    const speed = velocity.length();
    const position = data.physicsBody.getPositionVector3();

    // 生成飞溅效果
    if (this.config.enableSplash && speed > this.config.splashThreshold) {
      this.generateSplash(position, speed);
    }

    // 生成涟漪
    if (this.config.enableRipples) {
      this.generateRipple(position, speed);
    }

    this.eventEmitter.emit('objectEnterWater', data.objectId, waterBody);
    Debug.log(`物体 ${data.objectId} 进入水体`);
  }

  /**
   * 处理物体离开水体
   */
  private handleObjectExitWater(data: WaterInteractionData): void {
    this.eventEmitter.emit('objectExitWater', data.objectId);
    Debug.log(`物体 ${data.objectId} 离开水体`);
  }

  /**
   * 生成波浪
   */
  private generateWave(position: THREE.Vector3, intensity: number): void {
    const waveId = `wave_${Date.now()}_${Math.random()}`;
    
    const wave: WaveData = {
      id: waveId,
      position: position.clone(),
      amplitude: intensity * this.config.waveStrength * 0.1,
      frequency: 2.0,
      phase: 0,
      decay: 0.95,
      creationTime: this.time,
      lifetime: 5.0
    };
    
    this.waves.set(waveId, wave);
  }

  /**
   * 生成飞溅效果
   */
  private generateSplash(position: THREE.Vector3, intensity: number): void {
    const splashId = `splash_${Date.now()}_${Math.random()}`;
    
    const splash: SplashData = {
      id: splashId,
      position: position.clone(),
      intensity,
      particleCount: Math.floor(intensity * 10),
      velocityRange: { min: 1, max: intensity * 2 },
      lifetime: 2.0,
      creationTime: this.time
    };
    
    this.splashes.set(splashId, splash);
    this.eventEmitter.emit('splashGenerated', splash);
  }

  /**
   * 生成涟漪
   */
  private generateRipple(position: THREE.Vector3, intensity: number): void {
    // 涟漪可以作为特殊类型的波浪
    this.generateWave(position, intensity * 0.5);
  }

  /**
   * 更新波浪
   */
  private updateWaves(deltaTime: number): void {
    for (const [waveId, wave] of this.waves.entries()) {
      const age = this.time - wave.creationTime;
      
      if (age > wave.lifetime) {
        this.waves.delete(waveId);
        continue;
      }
      
      // 更新波浪参数
      wave.amplitude *= wave.decay;
      wave.phase += deltaTime * wave.frequency;
    }
  }

  /**
   * 更新飞溅效果
   */
  private updateSplashes(_deltaTime: number): void {
    for (const [splashId, splash] of this.splashes.entries()) {
      const age = this.time - splash.creationTime;
      
      if (age > splash.lifetime) {
        this.splashes.delete(splashId);
        this.eventEmitter.emit('splashExpired', splash);
      }
    }
  }

  /**
   * 清理过期数据
   */
  private cleanupExpiredData(): void {
    // 清理长时间未更新的交互数据
    const timeout = 5.0; // 5秒超时
    
    for (const [objectId, data] of this.interactionData.entries()) {
      if (this.time - data.lastUpdateTime > timeout) {
        this.interactionData.delete(objectId);
        Debug.log(`清理过期交互数据: ${objectId}`);
      }
    }
  }

  /**
   * 获取波浪高度
   */
  public getWaveHeightAt(x: number, z: number): number {
    let totalHeight = 0;
    
    for (const wave of this.waves.values()) {
      const distance = Math.sqrt(
        Math.pow(x - wave.position.x, 2) + Math.pow(z - wave.position.z, 2)
      );
      
      const waveHeight = wave.amplitude * Math.sin(wave.frequency * distance + wave.phase);
      totalHeight += waveHeight;
    }
    
    return totalHeight;
  }

  /**
   * 获取交互统计
   */
  public getInteractionStats(): any {
    return {
      interactingObjects: this.interactionData.size,
      activeWaves: this.waves.size,
      activeSplashes: this.splashes.size,
      waterBodies: this.waterBodies.size
    };
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    this.waterBodies.clear();
    this.interactionData.clear();
    this.waves.clear();
    this.splashes.clear();
    this.spatialHash.clear();
    this.performanceMonitor.stop();
    
    Debug.log('增强水体交互系统已销毁');
  }
}
