/**
 * 情感分析服务
 * 提供文本情感分析功能
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * 情感类型枚举
 */
export enum EmotionType {
  HAPPY = 'happy',
  SAD = 'sad',
  ANGRY = 'angry',
  SURPRISED = 'surprised',
  FEAR = 'fear',
  DISGUSTED = 'disgusted',
  NEUTRAL = 'neutral',
  EXCITED = 'excited',
  CALM = 'calm',
  CONFUSED = 'confused',
}

/**
 * 情感分析结果
 */
export interface EmotionAnalysisResult {
  /** 主要情感类型 */
  type: EmotionType;
  /** 情感强度 (0-1) */
  intensity: number;
  /** 置信度 (0-1) */
  confidence: number;
  /** 详细分数 */
  details: { [key in EmotionType]: number };
}

/**
 * 情感词条目
 */
interface EmotionWord {
  word: string;
  emotion: EmotionType;
  intensity: number;
  weight: number;
}

@Injectable()
export class EmotionAnalysisService {
  private readonly logger = new Logger(EmotionAnalysisService.name);
  
  /** 情感词典 */
  private emotionDictionary: EmotionWord[] = [];
  
  /** 否定词集合 */
  private negationWords: Set<string> = new Set();
  
  /** 程度副词映射 */
  private intensityModifiers: Map<string, number> = new Map();

  constructor(private configService: ConfigService) {
    this.initializeEmotionDictionary();
    this.initializeNegationWords();
    this.initializeIntensityModifiers();
  }

  /**
   * 分析文本情感
   * @param text 待分析文本
   * @returns 情感分析结果
   */
  async analyzeText(text: string): Promise<EmotionAnalysisResult> {
    try {
      if (!text || text.trim().length === 0) {
        return this.createNeutralResult();
      }

      // 预处理文本
      const processedText = this.preprocessText(text);

      // 分词
      const tokens = this.tokenize(processedText);

      // 计算情感分数
      const emotionScores = this.calculateEmotionScores(tokens, processedText);

      // 处理否定
      const adjustedScores = this.handleNegation(tokens, emotionScores);

      // 获取主要情感
      const primaryEmotion = this.getPrimaryEmotion(adjustedScores);

      // 计算置信度
      const confidence = this.calculateConfidence(adjustedScores, tokens);

      const result: EmotionAnalysisResult = {
        type: primaryEmotion.type,
        intensity: primaryEmotion.intensity,
        confidence,
        details: adjustedScores,
      };

      this.logger.log(`情感分析完成: ${result.type} (强度: ${result.intensity}, 置信度: ${result.confidence})`);
      return result;
    } catch (error) {
      this.logger.error('情感分析失败:', error);
      return this.createNeutralResult();
    }
  }

  /**
   * 预处理文本
   * @param text 原始文本
   * @returns 处理后的文本
   */
  private preprocessText(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\u4e00-\u9fff\w\s]/g, '') // 保留中文、英文、数字
      .trim();
  }

  /**
   * 分词
   * @param text 文本
   * @returns 词语数组
   */
  private tokenize(text: string): string[] {
    // 简单的分词实现，实际应用中可以使用更复杂的分词算法
    return text.split(/\s+/).filter(token => token.length > 0);
  }

  /**
   * 计算情感分数
   * @param tokens 词语数组
   * @param text 原始文本
   * @returns 情感分数映射
   */
  private calculateEmotionScores(tokens: string[], text: string): { [key in EmotionType]: number } {
    const scores: { [key in EmotionType]: number } = {
      [EmotionType.HAPPY]: 0,
      [EmotionType.SAD]: 0,
      [EmotionType.ANGRY]: 0,
      [EmotionType.SURPRISED]: 0,
      [EmotionType.FEAR]: 0,
      [EmotionType.DISGUSTED]: 0,
      [EmotionType.NEUTRAL]: 0.5, // 默认中性分数
      [EmotionType.EXCITED]: 0,
      [EmotionType.CALM]: 0,
      [EmotionType.CONFUSED]: 0,
    };

    // 匹配情感词
    for (let i = 0; i < tokens.length; i++) {
      const token = tokens[i];
      
      // 查找匹配的情感词
      const emotionWords = this.emotionDictionary.filter(item => 
        item.word === token || token.includes(item.word) || item.word.includes(token)
      );

      for (const emotionWord of emotionWords) {
        let intensity = emotionWord.intensity * emotionWord.weight;

        // 检查程度副词
        if (i > 0) {
          const modifier = this.intensityModifiers.get(tokens[i - 1]);
          if (modifier) {
            intensity *= modifier;
          }
        }

        scores[emotionWord.emotion] += intensity;
      }
    }

    // 归一化分数
    const maxScore = Math.max(...Object.values(scores));
    if (maxScore > 0) {
      for (const emotion of Object.keys(scores) as EmotionType[]) {
        scores[emotion] = scores[emotion] / maxScore;
      }
    }

    return scores;
  }

  /**
   * 处理否定
   * @param tokens 词语数组
   * @param scores 原始分数
   * @returns 调整后的分数
   */
  private handleNegation(
    tokens: string[], 
    scores: { [key in EmotionType]: number }
  ): { [key in EmotionType]: number } {
    const adjustedScores = { ...scores };

    // 检查否定词
    for (let i = 0; i < tokens.length; i++) {
      const token = tokens[i];
      
      if (this.negationWords.has(token)) {
        // 影响后续2-3个词
        const affectedRange = Math.min(i + 3, tokens.length);
        
        for (let j = i + 1; j < affectedRange; j++) {
          const affectedToken = tokens[j];
          
          // 查找受影响的情感词
          const emotionWords = this.emotionDictionary.filter(item => 
            item.word === affectedToken || affectedToken.includes(item.word)
          );

          for (const emotionWord of emotionWords) {
            // 降低原情感分数
            adjustedScores[emotionWord.emotion] *= 0.3;
            
            // 可能增加相反情感的分数
            const oppositeEmotion = this.getOppositeEmotion(emotionWord.emotion);
            if (oppositeEmotion) {
              adjustedScores[oppositeEmotion] += emotionWord.intensity * 0.5;
            }
          }
        }
      }
    }

    return adjustedScores;
  }

  /**
   * 获取主要情感
   * @param scores 情感分数
   * @returns 主要情感信息
   */
  private getPrimaryEmotion(scores: { [key in EmotionType]: number }): {
    type: EmotionType;
    intensity: number;
  } {
    let primaryEmotion = EmotionType.NEUTRAL;
    let maxScore = scores[EmotionType.NEUTRAL];

    for (const [emotion, score] of Object.entries(scores)) {
      if (score > maxScore) {
        maxScore = score;
        primaryEmotion = emotion as EmotionType;
      }
    }

    return {
      type: primaryEmotion,
      intensity: Math.min(1.0, maxScore),
    };
  }

  /**
   * 计算置信度
   * @param scores 情感分数
   * @param tokens 词语数组
   * @returns 置信度
   */
  private calculateConfidence(
    scores: { [key in EmotionType]: number },
    tokens: string[]
  ): number {
    // 基于最高分数和词语数量计算置信度
    const maxScore = Math.max(...Object.values(scores));
    const emotionWordCount = this.countEmotionWords(tokens);
    
    // 基础置信度基于最高分数
    let confidence = maxScore;
    
    // 根据情感词数量调整置信度
    if (emotionWordCount > 0) {
      confidence = Math.min(1.0, confidence + (emotionWordCount - 1) * 0.1);
    } else {
      confidence = 0.3; // 没有情感词时的低置信度
    }
    
    // 根据文本长度调整
    const textLengthFactor = Math.min(1.0, tokens.length / 10); // 10个词达到最大长度因子
    confidence *= (0.5 + textLengthFactor * 0.5);
    
    return Math.max(0.1, Math.min(1.0, confidence));
  }

  /**
   * 统计情感词数量
   * @param tokens 词语数组
   * @returns 情感词数量
   */
  private countEmotionWords(tokens: string[]): number {
    let count = 0;
    
    for (const token of tokens) {
      const hasEmotionWord = this.emotionDictionary.some(item => 
        item.word === token || token.includes(item.word) || item.word.includes(token)
      );
      
      if (hasEmotionWord) {
        count++;
      }
    }
    
    return count;
  }

  /**
   * 获取相反情感
   * @param emotion 情感类型
   * @returns 相反情感类型
   */
  private getOppositeEmotion(emotion: EmotionType): EmotionType | null {
    const oppositeMap: { [key in EmotionType]?: EmotionType } = {
      [EmotionType.HAPPY]: EmotionType.SAD,
      [EmotionType.SAD]: EmotionType.HAPPY,
      [EmotionType.ANGRY]: EmotionType.CALM,
      [EmotionType.CALM]: EmotionType.ANGRY,
      [EmotionType.EXCITED]: EmotionType.CALM,
      [EmotionType.FEAR]: EmotionType.CALM,
    };

    return oppositeMap[emotion] || null;
  }

  /**
   * 创建中性结果
   * @returns 中性情感分析结果
   */
  private createNeutralResult(): EmotionAnalysisResult {
    const details: { [key in EmotionType]: number } = {
      [EmotionType.HAPPY]: 0,
      [EmotionType.SAD]: 0,
      [EmotionType.ANGRY]: 0,
      [EmotionType.SURPRISED]: 0,
      [EmotionType.FEAR]: 0,
      [EmotionType.DISGUSTED]: 0,
      [EmotionType.NEUTRAL]: 1.0,
      [EmotionType.EXCITED]: 0,
      [EmotionType.CALM]: 0,
      [EmotionType.CONFUSED]: 0,
    };

    return {
      type: EmotionType.NEUTRAL,
      intensity: 0.5,
      confidence: 0.5,
      details,
    };
  }

  /**
   * 初始化情感词典
   */
  private initializeEmotionDictionary(): void {
    this.emotionDictionary = [
      // 开心相关
      { word: '开心', emotion: EmotionType.HAPPY, intensity: 0.8, weight: 1.0 },
      { word: '高兴', emotion: EmotionType.HAPPY, intensity: 0.8, weight: 1.0 },
      { word: '快乐', emotion: EmotionType.HAPPY, intensity: 0.9, weight: 1.0 },
      { word: '愉快', emotion: EmotionType.HAPPY, intensity: 0.7, weight: 1.0 },
      { word: '兴奋', emotion: EmotionType.EXCITED, intensity: 0.9, weight: 1.0 },
      { word: '激动', emotion: EmotionType.EXCITED, intensity: 0.8, weight: 1.0 },
      { word: '满意', emotion: EmotionType.HAPPY, intensity: 0.6, weight: 1.0 },
      { word: '喜欢', emotion: EmotionType.HAPPY, intensity: 0.7, weight: 1.0 },
      { word: '爱', emotion: EmotionType.HAPPY, intensity: 0.9, weight: 1.0 },

      // 悲伤相关
      { word: '悲伤', emotion: EmotionType.SAD, intensity: 0.8, weight: 1.0 },
      { word: '难过', emotion: EmotionType.SAD, intensity: 0.8, weight: 1.0 },
      { word: '伤心', emotion: EmotionType.SAD, intensity: 0.9, weight: 1.0 },
      { word: '沮丧', emotion: EmotionType.SAD, intensity: 0.7, weight: 1.0 },
      { word: '失望', emotion: EmotionType.SAD, intensity: 0.6, weight: 1.0 },
      { word: '痛苦', emotion: EmotionType.SAD, intensity: 0.9, weight: 1.0 },
      { word: '哭', emotion: EmotionType.SAD, intensity: 0.8, weight: 1.0 },

      // 愤怒相关
      { word: '愤怒', emotion: EmotionType.ANGRY, intensity: 0.9, weight: 1.0 },
      { word: '生气', emotion: EmotionType.ANGRY, intensity: 0.8, weight: 1.0 },
      { word: '恼火', emotion: EmotionType.ANGRY, intensity: 0.7, weight: 1.0 },
      { word: '烦躁', emotion: EmotionType.ANGRY, intensity: 0.6, weight: 1.0 },
      { word: '讨厌', emotion: EmotionType.ANGRY, intensity: 0.7, weight: 1.0 },
      { word: '恨', emotion: EmotionType.ANGRY, intensity: 0.9, weight: 1.0 },

      // 惊讶相关
      { word: '惊讶', emotion: EmotionType.SURPRISED, intensity: 0.8, weight: 1.0 },
      { word: '震惊', emotion: EmotionType.SURPRISED, intensity: 0.9, weight: 1.0 },
      { word: '吃惊', emotion: EmotionType.SURPRISED, intensity: 0.7, weight: 1.0 },
      { word: '意外', emotion: EmotionType.SURPRISED, intensity: 0.6, weight: 1.0 },

      // 恐惧相关
      { word: '害怕', emotion: EmotionType.FEAR, intensity: 0.8, weight: 1.0 },
      { word: '恐惧', emotion: EmotionType.FEAR, intensity: 0.9, weight: 1.0 },
      { word: '担心', emotion: EmotionType.FEAR, intensity: 0.6, weight: 1.0 },
      { word: '紧张', emotion: EmotionType.FEAR, intensity: 0.7, weight: 1.0 },
      { word: '焦虑', emotion: EmotionType.FEAR, intensity: 0.7, weight: 1.0 },

      // 厌恶相关
      { word: '厌恶', emotion: EmotionType.DISGUSTED, intensity: 0.8, weight: 1.0 },
      { word: '恶心', emotion: EmotionType.DISGUSTED, intensity: 0.9, weight: 1.0 },
      { word: '反感', emotion: EmotionType.DISGUSTED, intensity: 0.7, weight: 1.0 },

      // 平静相关
      { word: '平静', emotion: EmotionType.CALM, intensity: 0.8, weight: 1.0 },
      { word: '冷静', emotion: EmotionType.CALM, intensity: 0.7, weight: 1.0 },
      { word: '安静', emotion: EmotionType.CALM, intensity: 0.6, weight: 1.0 },
      { word: '放松', emotion: EmotionType.CALM, intensity: 0.7, weight: 1.0 },

      // 困惑相关
      { word: '困惑', emotion: EmotionType.CONFUSED, intensity: 0.7, weight: 1.0 },
      { word: '迷惑', emotion: EmotionType.CONFUSED, intensity: 0.7, weight: 1.0 },
      { word: '不解', emotion: EmotionType.CONFUSED, intensity: 0.6, weight: 1.0 },
      { word: '疑惑', emotion: EmotionType.CONFUSED, intensity: 0.6, weight: 1.0 },
    ];

    this.logger.log(`初始化情感词典: ${this.emotionDictionary.length} 个词条`);
  }

  /**
   * 初始化否定词
   */
  private initializeNegationWords(): void {
    const negationWords = [
      '不', '没', '无', '非', '未', '否', '别', '勿',
      '不是', '没有', '不会', '不能', '不要', '不用',
      '并非', '并不', '绝不', '决不', '从不', '永不'
    ];

    this.negationWords = new Set(negationWords);
    this.logger.log(`初始化否定词: ${this.negationWords.size} 个`);
  }

  /**
   * 初始化程度副词
   */
  private initializeIntensityModifiers(): void {
    const modifiers = [
      ['非常', 1.5],
      ['很', 1.3],
      ['特别', 1.4],
      ['极其', 1.6],
      ['十分', 1.4],
      ['相当', 1.2],
      ['比较', 1.1],
      ['有点', 0.8],
      ['稍微', 0.7],
      ['略微', 0.6],
      ['一点', 0.7],
      ['超级', 1.7],
      ['巨', 1.6],
      ['超', 1.5],
    ];

    this.intensityModifiers = new Map(modifiers as [string, number][]);
    this.logger.log(`初始化程度副词: ${this.intensityModifiers.size} 个`);
  }
}
