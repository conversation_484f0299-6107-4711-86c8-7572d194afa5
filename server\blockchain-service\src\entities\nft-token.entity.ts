/**
 * NFT令牌实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { BlockchainAsset } from './blockchain-asset.entity';

@Entity('nft_tokens')
@Index(['contractAddress', 'tokenId'], { unique: true })
@Index(['owner'])
@Index(['creator'])
@Index(['chainId'])
export class NFTToken {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'token_id', type: 'varchar', length: 255 })
  @Index()
  tokenId: string;

  @Column({ name: 'contract_address', type: 'varchar', length: 42 })
  @Index()
  contractAddress: string;

  @Column({ name: 'chain_id', type: 'int' })
  chainId: number;

  @Column({ name: 'owner_address', type: 'varchar', length: 42 })
  @Index()
  ownerAddress: string;

  @Column({ name: 'creator_address', type: 'varchar', length: 42 })
  @Index()
  creatorAddress: string;

  @Column({ name: 'token_uri', type: 'text' })
  tokenURI: string;

  @Column({ name: 'metadata', type: 'json' })
  metadata: {
    name: string;
    description: string;
    image: string;
    external_url?: string;
    attributes: Array<{
      trait_type: string;
      value: string | number;
      display_type?: string;
      max_value?: number;
    }>;
    animation_url?: string;
    background_color?: string;
    youtube_url?: string;
    dl_engine_data: {
      scene_id?: string;
      asset_type: string;
      engine_version: string;
      creation_timestamp: number;
      creator_address: string;
      license_type: string;
      educational_metadata?: any;
      technical_metadata?: any;
      interaction_metadata?: any;
    };
  };

  @Column({ name: 'ipfs_hash', type: 'varchar', length: 255, nullable: true })
  ipfsHash?: string;

  @Column({ name: 'mint_transaction_hash', type: 'varchar', length: 66 })
  mintTransactionHash: string;

  @Column({ name: 'mint_block_number', type: 'bigint', nullable: true })
  mintBlockNumber?: number;

  @Column({ name: 'transfer_count', type: 'int', default: 0 })
  transferCount: number;

  @Column({ name: 'last_transfer_at', type: 'timestamp', nullable: true })
  lastTransferAt?: Date;

  @Column({ name: 'is_for_sale', type: 'boolean', default: false })
  isForSale: boolean;

  @Column({ name: 'price', type: 'decimal', precision: 36, scale: 18, nullable: true })
  price?: string;

  @Column({ name: 'price_currency', type: 'varchar', length: 10, nullable: true })
  priceCurrency?: string;

  @Column({ name: 'royalty_recipient', type: 'varchar', length: 42, nullable: true })
  royaltyRecipient?: string;

  @Column({ name: 'royalty_percentage', type: 'decimal', precision: 5, scale: 2, nullable: true })
  royaltyPercentage?: number;

  @Column({ name: 'view_count', type: 'int', default: 0 })
  viewCount: number;

  @Column({ name: 'like_count', type: 'int', default: 0 })
  likeCount: number;

  @Column({ name: 'is_verified', type: 'boolean', default: false })
  isVerified: boolean;

  @Column({ name: 'is_featured', type: 'boolean', default: false })
  isFeatured: boolean;

  @Column({ name: 'status', type: 'enum', enum: ['active', 'burned', 'locked'], default: 'active' })
  status: 'active' | 'burned' | 'locked';

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'owner_address', referencedColumnName: 'walletAddress' })
  owner?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'creator_address', referencedColumnName: 'walletAddress' })
  creator?: User;

  @ManyToOne(() => BlockchainAsset, { nullable: true })
  @JoinColumn({ name: 'asset_id' })
  asset?: BlockchainAsset;

  // 虚拟属性
  get shortTokenId(): string {
    if (this.tokenId.length <= 8) return this.tokenId;
    return `${this.tokenId.slice(0, 4)}...${this.tokenId.slice(-4)}`;
  }

  get shortContractAddress(): string {
    return `${this.contractAddress.slice(0, 6)}...${this.contractAddress.slice(-4)}`;
  }

  get assetType(): string {
    return this.metadata?.dl_engine_data?.asset_type || 'unknown';
  }

  get licenseType(): string {
    return this.metadata?.dl_engine_data?.license_type || 'unknown';
  }

  get educationalLevel(): string | undefined {
    return this.metadata?.dl_engine_data?.educational_metadata?.grade_level;
  }

  get subject(): string | undefined {
    return this.metadata?.dl_engine_data?.educational_metadata?.subject;
  }

  get estimatedValue(): number {
    if (!this.price) return 0;
    return parseFloat(this.price);
  }

  get isEducational(): boolean {
    return !!this.metadata?.dl_engine_data?.educational_metadata;
  }

  get hasRoyalty(): boolean {
    return !!(this.royaltyRecipient && this.royaltyPercentage && this.royaltyPercentage > 0);
  }

  get ageInDays(): number {
    const now = new Date();
    const created = new Date(this.createdAt);
    return Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24));
  }

  // 方法
  updateMetadata(newMetadata: Partial<NFTToken['metadata']>): void {
    this.metadata = { ...this.metadata, ...newMetadata };
  }

  incrementViewCount(): void {
    this.viewCount += 1;
  }

  incrementLikeCount(): void {
    this.likeCount += 1;
  }

  decrementLikeCount(): void {
    if (this.likeCount > 0) {
      this.likeCount -= 1;
    }
  }

  setForSale(price: string, currency: string = 'ETH'): void {
    this.isForSale = true;
    this.price = price;
    this.priceCurrency = currency;
  }

  removeFromSale(): void {
    this.isForSale = false;
    this.price = null;
    this.priceCurrency = null;
  }

  recordTransfer(): void {
    this.transferCount += 1;
    this.lastTransferAt = new Date();
  }

  setRoyalty(recipient: string, percentage: number): void {
    if (percentage < 0 || percentage > 100) {
      throw new Error('版税百分比必须在0-100之间');
    }
    this.royaltyRecipient = recipient;
    this.royaltyPercentage = percentage;
  }

  calculateRoyalty(salePrice: string): string {
    if (!this.hasRoyalty) return '0';
    const price = parseFloat(salePrice);
    const royalty = (price * this.royaltyPercentage!) / 100;
    return royalty.toString();
  }

  verify(): void {
    this.isVerified = true;
  }

  unverify(): void {
    this.isVerified = false;
  }

  feature(): void {
    this.isFeatured = true;
  }

  unfeature(): void {
    this.isFeatured = false;
  }

  burn(): void {
    this.status = 'burned';
    this.isForSale = false;
    this.price = null;
    this.priceCurrency = null;
  }

  lock(): void {
    this.status = 'locked';
    this.isForSale = false;
  }

  unlock(): void {
    if (this.status === 'locked') {
      this.status = 'active';
    }
  }

  toJSON() {
    return {
      id: this.id,
      tokenId: this.tokenId,
      contractAddress: this.contractAddress,
      chainId: this.chainId,
      ownerAddress: this.ownerAddress,
      creatorAddress: this.creatorAddress,
      tokenURI: this.tokenURI,
      metadata: this.metadata,
      ipfsHash: this.ipfsHash,
      mintTransactionHash: this.mintTransactionHash,
      mintBlockNumber: this.mintBlockNumber,
      transferCount: this.transferCount,
      lastTransferAt: this.lastTransferAt,
      isForSale: this.isForSale,
      price: this.price,
      priceCurrency: this.priceCurrency,
      royaltyRecipient: this.royaltyRecipient,
      royaltyPercentage: this.royaltyPercentage,
      viewCount: this.viewCount,
      likeCount: this.likeCount,
      isVerified: this.isVerified,
      isFeatured: this.isFeatured,
      status: this.status,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      // 虚拟属性
      shortTokenId: this.shortTokenId,
      shortContractAddress: this.shortContractAddress,
      assetType: this.assetType,
      licenseType: this.licenseType,
      educationalLevel: this.educationalLevel,
      subject: this.subject,
      estimatedValue: this.estimatedValue,
      isEducational: this.isEducational,
      hasRoyalty: this.hasRoyalty,
      ageInDays: this.ageInDays,
    };
  }
}
