/**
 * 数学节点实现
 * 包含基础运算、高级数学函数、向量运算等
 */
import { Node, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 加法节点
 */
export class AddNode extends Node {
  public static readonly TYPE = 'math/add';

  constructor(options: any) {
    super({
      ...options,
      type: AddNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      dataType: 'number',
      description: '第一个数',
      defaultValue: 0
    });
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      dataType: 'number',
      description: '第二个数',
      defaultValue: 0
    });
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'number',
      description: '相加结果'
    });
  }

  protected executeImpl(): any {
    const a = this.getInputValue('a') || 0;
    const b = this.getInputValue('b') || 0;
    const result = a + b;

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 减法节点
 */
export class SubtractNode extends Node {
  public static readonly TYPE = 'math/subtract';

  constructor(options: any) {
    super({
      ...options,
      type: SubtractNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      dataType: 'number',
      description: '被减数',
      defaultValue: 0
    });
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      dataType: 'number',
      description: '减数',
      defaultValue: 0
    });
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'number',
      description: '相减结果'
    });
  }

  protected executeImpl(): any {
    const a = this.getInputValue('a') || 0;
    const b = this.getInputValue('b') || 0;
    const result = a - b;

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 乘法节点
 */
export class MultiplyNode extends Node {
  public static readonly TYPE = 'math/multiply';

  constructor(options: any) {
    super({
      ...options,
      type: MultiplyNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      dataType: 'number',
      description: '第一个因数',
      defaultValue: 1
    });
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      dataType: 'number',
      description: '第二个因数',
      defaultValue: 1
    });
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'number',
      description: '相乘结果'
    });
  }

  protected executeImpl(): any {
    const a = this.getInputValue('a') || 1;
    const b = this.getInputValue('b') || 1;
    const result = a * b;

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 除法节点
 */
export class DivideNode extends Node {
  public static readonly TYPE = 'math/divide';

  constructor(options: any) {
    super({
      ...options,
      type: DivideNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'a',
      type: SocketType.DATA,
      dataType: 'number',
      description: '被除数',
      defaultValue: 0
    });
    this.addInput({
      name: 'b',
      type: SocketType.DATA,
      dataType: 'number',
      description: '除数',
      defaultValue: 1
    });
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'number',
      description: '相除结果'
    });
  }

  protected executeImpl(): any {
    const a = this.getInputValue('a') || 0;
    const b = this.getInputValue('b') || 1;

    if (b === 0) {
      throw new Error('除数不能为零');
    }

    const result = a / b;
    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 幂运算节点
 */
export class PowerNode extends Node {
  public static readonly TYPE = 'math/power';

  constructor(options: any) {
    super({
      ...options,
      type: PowerNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'base',
      type: SocketType.DATA,
      dataType: 'number',
      description: '底数',
      defaultValue: 0
    });
    this.addInput({
      name: 'exponent',
      type: SocketType.DATA,
      dataType: 'number',
      description: '指数',
      defaultValue: 1
    });
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'number',
      description: '幂运算结果'
    });
  }

  protected executeImpl(): any {
    const base = this.getInputValue('base') || 0;
    const exponent = this.getInputValue('exponent') || 1;
    const result = Math.pow(base, exponent);

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 平方根节点
 */
export class SqrtNode extends Node {
  public static readonly TYPE = 'math/sqrt';

  constructor(options: any) {
    super({
      ...options,
      type: SqrtNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'number',
      description: '输入值',
      defaultValue: 0
    });
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'number',
      description: '平方根结果'
    });
  }

  protected executeImpl(): any {
    const value = this.getInputValue('value') || 0;

    if (value < 0) {
      throw new Error('不能计算负数的平方根');
    }

    const result = Math.sqrt(value);
    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 三角函数节点
 */
export class TrigonometricNode extends Node {
  public static readonly TYPE = 'math/trigonometric';

  constructor(options: any) {
    super({
      ...options,
      type: TrigonometricNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'number',
      description: '输入值（弧度）',
      defaultValue: 0
    });
    this.addInput({
      name: 'function',
      type: SocketType.DATA,
      dataType: 'string',
      description: '三角函数类型',
      defaultValue: 'sin'
    });
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'number',
      description: '三角函数结果'
    });
  }

  protected executeImpl(): any {
    const value = this.getInputValue('value') || 0;
    const func = this.getInputValue('function') || 'sin';
    
    let result: number;
    
    switch (func) {
      case 'sin':
        result = Math.sin(value);
        break;
      case 'cos':
        result = Math.cos(value);
        break;
      case 'tan':
        result = Math.tan(value);
        break;
      case 'asin':
        result = Math.asin(value);
        break;
      case 'acos':
        result = Math.acos(value);
        break;
      case 'atan':
        result = Math.atan(value);
        break;
      default:
        throw new Error(`不支持的三角函数: ${func}`);
    }
    
    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 向量数学节点
 */
export class VectorMathNode extends Node {
  public static readonly TYPE = 'math/vectorMath';

  constructor(options: any) {
    super({
      ...options,
      type: VectorMathNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'vectorA',
      type: SocketType.DATA,
      dataType: 'vector3',
      description: '第一个向量',
      defaultValue: { x: 0, y: 0, z: 0 }
    });
    this.addInput({
      name: 'vectorB',
      type: SocketType.DATA,
      dataType: 'vector3',
      description: '第二个向量',
      defaultValue: { x: 0, y: 0, z: 0 }
    });
    this.addInput({
      name: 'operation',
      type: SocketType.DATA,
      dataType: 'string',
      description: '向量操作类型',
      defaultValue: 'add'
    });
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'any',
      description: '向量运算结果'
    });
  }

  protected executeImpl(): any {
    const vectorA = this.getInputValue('vectorA') || { x: 0, y: 0, z: 0 };
    const vectorB = this.getInputValue('vectorB') || { x: 0, y: 0, z: 0 };
    const operation = this.getInputValue('operation') || 'add';
    
    let result: any;
    
    switch (operation) {
      case 'add':
        result = {
          x: vectorA.x + vectorB.x,
          y: vectorA.y + vectorB.y,
          z: vectorA.z + vectorB.z
        };
        break;
      case 'subtract':
        result = {
          x: vectorA.x - vectorB.x,
          y: vectorA.y - vectorB.y,
          z: vectorA.z - vectorB.z
        };
        break;
      case 'dot':
        result = vectorA.x * vectorB.x + vectorA.y * vectorB.y + vectorA.z * vectorB.z;
        break;
      case 'cross':
        result = {
          x: vectorA.y * vectorB.z - vectorA.z * vectorB.y,
          y: vectorA.z * vectorB.x - vectorA.x * vectorB.z,
          z: vectorA.x * vectorB.y - vectorA.y * vectorB.x
        };
        break;
      case 'normalize':
        const length = Math.sqrt(vectorA.x * vectorA.x + vectorA.y * vectorA.y + vectorA.z * vectorA.z);
        if (length === 0) {
          result = { x: 0, y: 0, z: 0 };
        } else {
          result = {
            x: vectorA.x / length,
            y: vectorA.y / length,
            z: vectorA.z / length
          };
        }
        break;
      case 'length':
        result = Math.sqrt(vectorA.x * vectorA.x + vectorA.y * vectorA.y + vectorA.z * vectorA.z);
        break;
      default:
        throw new Error(`不支持的向量操作: ${operation}`);
    }
    
    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 随机数节点
 */
export class RandomNode extends Node {
  public static readonly TYPE = 'math/random';

  constructor(options: any) {
    super({
      ...options,
      type: RandomNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'min',
      type: SocketType.DATA,
      dataType: 'number',
      description: '最小值',
      defaultValue: 0
    });
    this.addInput({
      name: 'max',
      type: SocketType.DATA,
      dataType: 'number',
      description: '最大值',
      defaultValue: 1
    });
    this.addInput({
      name: 'integer',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '是否为整数',
      defaultValue: false
    });
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'number',
      description: '随机数结果'
    });
  }

  protected executeImpl(): any {
    const min = this.getInputValue('min') || 0;
    const max = this.getInputValue('max') || 1;
    const integer = this.getInputValue('integer') || false;
    
    let result = Math.random() * (max - min) + min;
    
    if (integer) {
      result = Math.floor(result);
    }
    
    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 插值节点
 */
export class InterpolationNode extends Node {
  public static readonly TYPE = 'math/interpolation';

  constructor(options: any) {
    super({
      ...options,
      type: InterpolationNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'from',
      type: SocketType.DATA,
      dataType: 'number',
      description: '起始值',
      defaultValue: 0
    });
    this.addInput({
      name: 'to',
      type: SocketType.DATA,
      dataType: 'number',
      description: '结束值',
      defaultValue: 1
    });
    this.addInput({
      name: 't',
      type: SocketType.DATA,
      dataType: 'number',
      description: '插值参数(0-1)',
      defaultValue: 0.5
    });
    this.addInput({
      name: 'type',
      type: SocketType.DATA,
      dataType: 'string',
      description: '插值类型',
      defaultValue: 'linear'
    });
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'number',
      description: '插值结果'
    });
  }

  protected executeImpl(): any {
    const from = this.getInputValue('from') || 0;
    const to = this.getInputValue('to') || 1;
    const t = Math.max(0, Math.min(1, this.getInputValue('t') || 0.5)); // 限制在0-1范围
    const type = this.getInputValue('type') || 'linear';
    
    let result: number;
    
    switch (type) {
      case 'linear':
        result = from + (to - from) * t;
        break;
      case 'smoothstep':
        const smoothT = t * t * (3 - 2 * t);
        result = from + (to - from) * smoothT;
        break;
      case 'smootherstep':
        const smootherT = t * t * t * (t * (t * 6 - 15) + 10);
        result = from + (to - from) * smootherT;
        break;
      default:
        result = from + (to - from) * t;
    }
    
    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 数学常量节点
 */
export class MathConstantNode extends Node {
  public static readonly TYPE = 'math/constant';

  constructor(options: any) {
    super({
      ...options,
      type: MathConstantNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'constant',
      type: SocketType.DATA,
      dataType: 'string',
      description: '数学常量类型',
      defaultValue: 'PI'
    });
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'number',
      description: '常量值'
    });
  }

  protected executeImpl(): any {
    const constant = this.getInputValue('constant') || 'PI';
    
    let value: number;
    
    switch (constant) {
      case 'PI':
        value = Math.PI;
        break;
      case 'E':
        value = Math.E;
        break;
      case 'SQRT2':
        value = Math.SQRT2;
        break;
      case 'LN2':
        value = Math.LN2;
        break;
      case 'LN10':
        value = Math.LN10;
        break;
      default:
        value = 0;
    }
    
    this.setOutputValue('value', value);
    return value;
  }
}

/**
 * 注册数学节点到注册表
 */
export function registerMathNodes(registry: NodeRegistry): void {
  registry.register(AddNode.TYPE, AddNode);
  registry.register(SubtractNode.TYPE, SubtractNode);
  registry.register(MultiplyNode.TYPE, MultiplyNode);
  registry.register(DivideNode.TYPE, DivideNode);
  registry.register(PowerNode.TYPE, PowerNode);
  registry.register(SqrtNode.TYPE, SqrtNode);
  registry.register(TrigonometricNode.TYPE, TrigonometricNode);
  registry.register(VectorMathNode.TYPE, VectorMathNode);
  registry.register(RandomNode.TYPE, RandomNode);
  registry.register(InterpolationNode.TYPE, InterpolationNode);
  registry.register(MathConstantNode.TYPE, MathConstantNode);
}
