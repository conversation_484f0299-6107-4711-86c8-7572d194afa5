/**
 * 性能监控中间件
 */

import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
import * as os from 'os';

export interface PerformanceMetrics {
  requestId: string;
  method: string;
  url: string;
  statusCode: number;
  responseTime: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: number;
  timestamp: number;
  userAgent?: string;
  ip?: string;
}

@Injectable()
export class PerformanceMiddleware implements NestMiddleware {
  private readonly logger = new Logger(PerformanceMiddleware.name);
  private readonly metrics: PerformanceMetrics[] = [];
  private readonly maxMetricsHistory = 1000;
  private cpuUsageHistory: number[] = [];

  constructor(private readonly configService: ConfigService) {
    // 定期清理旧的性能数据
    setInterval(() => {
      this.cleanupOldMetrics();
    }, 60000); // 每分钟清理一次

    // 定期收集CPU使用率
    setInterval(() => {
      this.collectCPUUsage();
    }, 5000); // 每5秒收集一次
  }

  use(req: Request, res: Response, next: NextFunction): void {
    const startTime = Date.now();
    const requestId = this.generateRequestId();
    
    // 添加请求ID到请求对象
    (req as any).requestId = requestId;

    // 记录请求开始
    this.logger.debug(`[${requestId}] ${req.method} ${req.url} - 请求开始`);

    // 监听响应完成事件
    res.on('finish', () => {
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      const metrics: PerformanceMetrics = {
        requestId,
        method: req.method,
        url: req.url,
        statusCode: res.statusCode,
        responseTime,
        memoryUsage: process.memoryUsage(),
        cpuUsage: this.getCurrentCPUUsage(),
        timestamp: startTime,
        userAgent: req.get('User-Agent'),
        ip: this.getClientIP(req),
      };

      this.recordMetrics(metrics);
      this.logPerformance(metrics);
      this.checkPerformanceThresholds(metrics);
    });

    next();
  }

  /**
   * 记录性能指标
   */
  private recordMetrics(metrics: PerformanceMetrics): void {
    this.metrics.push(metrics);
    
    // 保持历史记录在限制范围内
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics.splice(0, this.metrics.length - this.maxMetricsHistory);
    }
  }

  /**
   * 记录性能日志
   */
  private logPerformance(metrics: PerformanceMetrics): void {
    const { requestId, method, url, statusCode, responseTime, memoryUsage } = metrics;
    
    let logLevel: 'log' | 'warn' | 'error' = 'log';
    
    // 根据响应时间和状态码确定日志级别
    if (responseTime > 5000 || statusCode >= 500) {
      logLevel = 'error';
    } else if (responseTime > 2000 || statusCode >= 400) {
      logLevel = 'warn';
    }

    const memoryMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    
    this.logger[logLevel](
      `[${requestId}] ${method} ${url} - ${statusCode} - ${responseTime}ms - ${memoryMB}MB`
    );
  }

  /**
   * 检查性能阈值
   */
  private checkPerformanceThresholds(metrics: PerformanceMetrics): void {
    const thresholds = {
      responseTime: this.configService.get('PERFORMANCE_RESPONSE_TIME_THRESHOLD', 2000),
      memoryUsage: this.configService.get('PERFORMANCE_MEMORY_THRESHOLD', 500 * 1024 * 1024), // 500MB
      cpuUsage: this.configService.get('PERFORMANCE_CPU_THRESHOLD', 80), // 80%
    };

    const alerts: string[] = [];

    if (metrics.responseTime > thresholds.responseTime) {
      alerts.push(`响应时间过长: ${metrics.responseTime}ms`);
    }

    if (metrics.memoryUsage.heapUsed > thresholds.memoryUsage) {
      alerts.push(`内存使用过高: ${Math.round(metrics.memoryUsage.heapUsed / 1024 / 1024)}MB`);
    }

    if (metrics.cpuUsage > thresholds.cpuUsage) {
      alerts.push(`CPU使用率过高: ${metrics.cpuUsage.toFixed(2)}%`);
    }

    if (alerts.length > 0) {
      this.logger.warn(
        `[${metrics.requestId}] 性能告警: ${alerts.join(', ')}`
      );
      
      // 这里可以发送告警通知
      this.sendPerformanceAlert(metrics, alerts);
    }
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(): {
    totalRequests: number;
    averageResponseTime: number;
    errorRate: number;
    slowRequestRate: number;
    memoryUsage: NodeJS.MemoryUsage;
    cpuUsage: number;
    recentMetrics: PerformanceMetrics[];
  } {
    const totalRequests = this.metrics.length;
    const errorRequests = this.metrics.filter(m => m.statusCode >= 400).length;
    const slowRequests = this.metrics.filter(m => m.responseTime > 2000).length;
    
    const totalResponseTime = this.metrics.reduce((sum, m) => sum + m.responseTime, 0);
    const averageResponseTime = totalRequests > 0 ? totalResponseTime / totalRequests : 0;
    
    const errorRate = totalRequests > 0 ? (errorRequests / totalRequests) * 100 : 0;
    const slowRequestRate = totalRequests > 0 ? (slowRequests / totalRequests) * 100 : 0;

    return {
      totalRequests,
      averageResponseTime,
      errorRate,
      slowRequestRate,
      memoryUsage: process.memoryUsage(),
      cpuUsage: this.getCurrentCPUUsage(),
      recentMetrics: this.metrics.slice(-10), // 最近10个请求
    };
  }

  /**
   * 获取热点API
   */
  getHotspotAPIs(limit: number = 10): Array<{
    endpoint: string;
    count: number;
    averageResponseTime: number;
    errorRate: number;
  }> {
    const endpointStats = new Map<string, {
      count: number;
      totalResponseTime: number;
      errorCount: number;
    }>();

    // 统计每个端点的访问情况
    for (const metric of this.metrics) {
      const endpoint = this.normalizeEndpoint(metric.url);
      const stats = endpointStats.get(endpoint) || {
        count: 0,
        totalResponseTime: 0,
        errorCount: 0,
      };

      stats.count++;
      stats.totalResponseTime += metric.responseTime;
      if (metric.statusCode >= 400) {
        stats.errorCount++;
      }

      endpointStats.set(endpoint, stats);
    }

    // 转换为结果格式并排序
    const results = Array.from(endpointStats.entries())
      .map(([endpoint, stats]) => ({
        endpoint,
        count: stats.count,
        averageResponseTime: stats.totalResponseTime / stats.count,
        errorRate: (stats.errorCount / stats.count) * 100,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);

    return results;
  }

  /**
   * 获取慢查询
   */
  getSlowRequests(threshold: number = 2000, limit: number = 10): PerformanceMetrics[] {
    return this.metrics
      .filter(m => m.responseTime > threshold)
      .sort((a, b) => b.responseTime - a.responseTime)
      .slice(0, limit);
  }

  /**
   * 重置性能统计
   */
  resetStats(): void {
    this.metrics.length = 0;
    this.cpuUsageHistory.length = 0;
    this.logger.log('性能统计已重置');
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取客户端IP
   */
  private getClientIP(req: Request): string {
    return (
      (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      'unknown'
    );
  }

  /**
   * 收集CPU使用率
   */
  private collectCPUUsage(): void {
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    for (const cpu of cpus) {
      for (const type in cpu.times) {
        totalTick += cpu.times[type as keyof typeof cpu.times];
      }
      totalIdle += cpu.times.idle;
    }

    const idle = totalIdle / cpus.length;
    const total = totalTick / cpus.length;
    const usage = 100 - ~~(100 * idle / total);

    this.cpuUsageHistory.push(usage);
    
    // 保持历史记录在合理范围内
    if (this.cpuUsageHistory.length > 60) { // 保留5分钟的数据
      this.cpuUsageHistory.shift();
    }
  }

  /**
   * 获取当前CPU使用率
   */
  private getCurrentCPUUsage(): number {
    if (this.cpuUsageHistory.length === 0) {
      return 0;
    }
    
    // 返回最近几次的平均值
    const recentSamples = this.cpuUsageHistory.slice(-3);
    const sum = recentSamples.reduce((acc, val) => acc + val, 0);
    return sum / recentSamples.length;
  }

  /**
   * 规范化端点路径
   */
  private normalizeEndpoint(url: string): string {
    // 移除查询参数
    const path = url.split('?')[0];
    
    // 替换动态参数为占位符
    return path.replace(/\/[0-9a-f-]{36}/g, '/:id') // UUID
               .replace(/\/\d+/g, '/:id') // 数字ID
               .replace(/\/0x[a-fA-F0-9]+/g, '/:address'); // 以太坊地址
  }

  /**
   * 清理旧的性能数据
   */
  private cleanupOldMetrics(): void {
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    const initialLength = this.metrics.length;
    
    // 移除一小时前的数据
    for (let i = this.metrics.length - 1; i >= 0; i--) {
      if (this.metrics[i].timestamp < oneHourAgo) {
        this.metrics.splice(0, i + 1);
        break;
      }
    }

    const removedCount = initialLength - this.metrics.length;
    if (removedCount > 0) {
      this.logger.debug(`清理了 ${removedCount} 条旧的性能数据`);
    }
  }

  /**
   * 发送性能告警
   */
  private sendPerformanceAlert(metrics: PerformanceMetrics, alerts: string[]): void {
    // 这里可以集成告警系统，如邮件、短信、Slack等
    const alertData = {
      timestamp: new Date(metrics.timestamp).toISOString(),
      requestId: metrics.requestId,
      endpoint: `${metrics.method} ${metrics.url}`,
      alerts,
      metrics: {
        responseTime: metrics.responseTime,
        memoryUsage: Math.round(metrics.memoryUsage.heapUsed / 1024 / 1024),
        cpuUsage: metrics.cpuUsage,
      },
    };

    // 发送到日志系统
    this.logger.error('性能告警', JSON.stringify(alertData, null, 2));
    
    // TODO: 集成实际的告警系统
    // await this.alertService.sendAlert(alertData);
  }
}
