import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as sdk from 'microsoft-cognitiveservices-speech-sdk';
import * as fs from 'fs-extra';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

/**
 * 语音识别提供商
 */
export enum SpeechProvider {
  AZURE = 'azure',
  GOOGLE = 'google',
  BAIDU = 'baidu',
  TENCENT = 'tencent',
  OPENAI = 'openai',
}

/**
 * 语音识别配置
 */
export interface SpeechRecognitionConfig {
  provider: SpeechProvider;
  language: string;
  sampleRate?: number;
  channels?: number;
  enablePunctuation?: boolean;
  enableWordTimestamps?: boolean;
  enableSpeakerDiarization?: boolean;
  maxSpeakers?: number;
}

/**
 * 语音识别结果
 */
export interface SpeechRecognitionResult {
  id: string;
  text: string;
  confidence: number;
  language: string;
  duration: number;
  words?: WordTimestamp[];
  speakers?: SpeakerSegment[];
  provider: SpeechProvider;
  processingTime: number;
}

/**
 * 词时间戳
 */
export interface WordTimestamp {
  word: string;
  startTime: number;
  endTime: number;
  confidence: number;
}

/**
 * 说话人片段
 */
export interface SpeakerSegment {
  speaker: string;
  startTime: number;
  endTime: number;
  text: string;
}

/**
 * 实时识别会话
 */
export interface RealtimeSession {
  id: string;
  recognizer: sdk.SpeechRecognizer;
  isActive: boolean;
  language: string;
  onResult?: (result: Partial<SpeechRecognitionResult>) => void;
  onError?: (error: string) => void;
}

@Injectable()
export class SpeechRecognitionService {
  private azureSpeechConfig: sdk.SpeechConfig;
  private realtimeSessions: Map<string, RealtimeSession> = new Map();

  constructor(
    private configService: ConfigService,
    private eventEmitter: EventEmitter2,
  ) {
    this.initializeProviders();
  }

  /**
   * 初始化语音服务提供商
   */
  private initializeProviders(): void {
    // 初始化Azure语音服务
    const azureKey = this.configService.get<string>('AZURE_SPEECH_KEY');
    const azureRegion = this.configService.get<string>('AZURE_SPEECH_REGION');
    
    if (azureKey && azureRegion) {
      this.azureSpeechConfig = sdk.SpeechConfig.fromSubscription(azureKey, azureRegion);
    }
  }

  /**
   * 识别音频文件
   */
  async recognizeAudioFile(
    audioBuffer: Buffer,
    config: SpeechRecognitionConfig,
  ): Promise<SpeechRecognitionResult> {
    const startTime = Date.now();
    const resultId = uuidv4();

    try {
      switch (config.provider) {
        case SpeechProvider.AZURE:
          return await this.recognizeWithAzure(audioBuffer, config, resultId, startTime);
        case SpeechProvider.OPENAI:
          return await this.recognizeWithOpenAI(audioBuffer, config, resultId, startTime);
        default:
          throw new BadRequestException(`不支持的语音识别提供商: ${config.provider}`);
      }
    } catch (error) {
      throw new BadRequestException(`语音识别失败: ${error.message}`);
    }
  }

  /**
   * 使用Azure进行语音识别
   */
  private async recognizeWithAzure(
    audioBuffer: Buffer,
    config: SpeechRecognitionConfig,
    resultId: string,
    startTime: number,
  ): Promise<SpeechRecognitionResult> {
    if (!this.azureSpeechConfig) {
      throw new Error('Azure语音服务未配置');
    }

    // 设置语言
    this.azureSpeechConfig.speechRecognitionLanguage = config.language;
    
    // 启用详细输出
    this.azureSpeechConfig.outputFormat = sdk.OutputFormat.Detailed;

    // 创建音频配置
    const audioConfig = sdk.AudioConfig.fromWavFileInput(audioBuffer);

    // 创建识别器
    const recognizer = new sdk.SpeechRecognizer(this.azureSpeechConfig, audioConfig);

    return new Promise((resolve, reject) => {
      recognizer.recognizeOnceAsync(
        (result) => {
          const processingTime = Date.now() - startTime;

          if (result.reason === sdk.ResultReason.RecognizedSpeech) {
            // 解析详细结果
            const detailedResult = JSON.parse(result.json);
            
            const words: WordTimestamp[] = [];
            if (config.enableWordTimestamps && detailedResult.NBest?.[0]?.Words) {
              for (const word of detailedResult.NBest[0].Words) {
                words.push({
                  word: word.Word,
                  startTime: word.Offset / 10000000, // 转换为秒
                  endTime: (word.Offset + word.Duration) / 10000000,
                  confidence: word.Confidence || 1.0,
                });
              }
            }

            resolve({
              id: resultId,
              text: result.text,
              confidence: detailedResult.NBest?.[0]?.Confidence || 1.0,
              language: config.language,
              duration: result.duration / 10000000, // 转换为秒
              words: words.length > 0 ? words : undefined,
              provider: SpeechProvider.AZURE,
              processingTime,
            });
          } else {
            reject(new Error(`识别失败: ${result.errorDetails}`));
          }

          recognizer.close();
        },
        (error) => {
          recognizer.close();
          reject(new Error(error));
        }
      );
    });
  }

  /**
   * 使用OpenAI Whisper进行语音识别
   */
  private async recognizeWithOpenAI(
    audioBuffer: Buffer,
    config: SpeechRecognitionConfig,
    resultId: string,
    startTime: number,
  ): Promise<SpeechRecognitionResult> {
    // 这里需要实现OpenAI Whisper API调用
    // 暂时返回模拟结果
    const processingTime = Date.now() - startTime;
    
    return {
      id: resultId,
      text: '这是OpenAI Whisper的模拟识别结果',
      confidence: 0.95,
      language: config.language,
      duration: 3.0,
      provider: SpeechProvider.OPENAI,
      processingTime,
    };
  }

  /**
   * 开始实时语音识别
   */
  async startRealtimeRecognition(
    config: SpeechRecognitionConfig,
    onResult?: (result: Partial<SpeechRecognitionResult>) => void,
    onError?: (error: string) => void,
  ): Promise<string> {
    if (!this.azureSpeechConfig) {
      throw new Error('Azure语音服务未配置');
    }

    const sessionId = uuidv4();

    // 设置语言
    this.azureSpeechConfig.speechRecognitionLanguage = config.language;

    // 创建音频配置（从麦克风）
    const audioConfig = sdk.AudioConfig.fromDefaultMicrophoneInput();

    // 创建识别器
    const recognizer = new sdk.SpeechRecognizer(this.azureSpeechConfig, audioConfig);

    // 设置事件处理器
    recognizer.recognizing = (s, e) => {
      if (onResult) {
        onResult({
          id: sessionId,
          text: e.result.text,
          confidence: 0.5, // 中间结果置信度较低
          language: config.language,
          provider: config.provider,
        });
      }
    };

    recognizer.recognized = (s, e) => {
      if (e.result.reason === sdk.ResultReason.RecognizedSpeech && onResult) {
        onResult({
          id: sessionId,
          text: e.result.text,
          confidence: 0.9,
          language: config.language,
          provider: config.provider,
        });
      }
    };

    recognizer.canceled = (s, e) => {
      if (onError) {
        onError(`识别被取消: ${e.errorDetails}`);
      }
      this.stopRealtimeRecognition(sessionId);
    };

    recognizer.sessionStopped = (s, e) => {
      this.stopRealtimeRecognition(sessionId);
    };

    // 开始连续识别
    recognizer.startContinuousRecognitionAsync();

    // 保存会话
    this.realtimeSessions.set(sessionId, {
      id: sessionId,
      recognizer,
      isActive: true,
      language: config.language,
      onResult,
      onError,
    });

    return sessionId;
  }

  /**
   * 停止实时语音识别
   */
  async stopRealtimeRecognition(sessionId: string): Promise<void> {
    const session = this.realtimeSessions.get(sessionId);
    if (!session) {
      return;
    }

    session.isActive = false;
    session.recognizer.stopContinuousRecognitionAsync();
    session.recognizer.close();
    
    this.realtimeSessions.delete(sessionId);
  }

  /**
   * 获取支持的语言列表
   */
  getSupportedLanguages(provider: SpeechProvider): string[] {
    switch (provider) {
      case SpeechProvider.AZURE:
        return [
          'zh-CN', 'zh-TW', 'zh-HK',
          'en-US', 'en-GB', 'en-AU',
          'ja-JP', 'ko-KR',
          'fr-FR', 'de-DE', 'es-ES',
        ];
      case SpeechProvider.OPENAI:
        return [
          'zh', 'en', 'ja', 'ko',
          'fr', 'de', 'es', 'it',
          'pt', 'ru', 'ar', 'hi',
        ];
      default:
        return ['zh-CN', 'en-US'];
    }
  }

  /**
   * 音频格式转换
   */
  async convertAudioFormat(
    inputBuffer: Buffer,
    targetFormat: 'wav' | 'mp3' | 'flac',
    sampleRate?: number,
  ): Promise<Buffer> {
    // 这里需要使用FFmpeg进行音频格式转换
    // 暂时返回原始buffer
    return inputBuffer;
  }

  /**
   * 音频质量检测
   */
  async analyzeAudioQuality(audioBuffer: Buffer): Promise<{
    duration: number;
    sampleRate: number;
    channels: number;
    bitRate: number;
    snr?: number; // 信噪比
    volume: number;
    quality: 'excellent' | 'good' | 'fair' | 'poor';
  }> {
    // 这里需要实现音频质量分析
    // 暂时返回模拟结果
    return {
      duration: 3.0,
      sampleRate: 16000,
      channels: 1,
      bitRate: 128,
      volume: 0.7,
      quality: 'good',
    };
  }

  /**
   * 批量语音识别
   */
  async batchRecognize(
    audioFiles: Buffer[],
    config: SpeechRecognitionConfig,
  ): Promise<SpeechRecognitionResult[]> {
    const results: SpeechRecognitionResult[] = [];
    
    for (const audioBuffer of audioFiles) {
      try {
        const result = await this.recognizeAudioFile(audioBuffer, config);
        results.push(result);
      } catch (error) {
        console.error('批量识别中的文件处理失败:', error);
        // 继续处理下一个文件
      }
    }
    
    return results;
  }

  /**
   * 获取实时会话状态
   */
  getRealtimeSessionStatus(sessionId: string): {
    isActive: boolean;
    language?: string;
  } | null {
    const session = this.realtimeSessions.get(sessionId);
    if (!session) {
      return null;
    }

    return {
      isActive: session.isActive,
      language: session.language,
    };
  }

  /**
   * 获取服务统计信息
   */
  getServiceStatistics(): {
    activeRealtimeSessions: number;
    supportedProviders: SpeechProvider[];
    totalProcessed: number;
  } {
    return {
      activeRealtimeSessions: this.realtimeSessions.size,
      supportedProviders: [SpeechProvider.AZURE, SpeechProvider.OPENAI],
      totalProcessed: 0, // 这里应该从数据库或缓存中获取
    };
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    // 停止所有实时会话
    for (const sessionId of this.realtimeSessions.keys()) {
      await this.stopRealtimeRecognition(sessionId);
    }
  }
}
