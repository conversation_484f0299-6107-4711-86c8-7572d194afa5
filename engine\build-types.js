const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('开始生成类型声明文件...');

try {
  // 运行 TypeScript 编译器生成类型声明文件
  execSync('npx tsc --declaration --emitDeclarationOnly --outDir dist/types', { 
    stdio: 'inherit',
    cwd: __dirname 
  });
  
  // 将主要的类型声明文件复制到 dist/index.d.ts
  const typesIndexPath = path.join(__dirname, 'dist/types/index.d.ts');
  const distIndexPath = path.join(__dirname, 'dist/index.d.ts');
  
  if (fs.existsSync(typesIndexPath)) {
    fs.copyFileSync(typesIndexPath, distIndexPath);
    console.log('类型声明文件生成成功！');
  } else {
    console.error('类型声明文件生成失败：找不到 dist/types/index.d.ts');
  }
} catch (error) {
  console.error('构建类型声明文件时出错：', error.message);
  process.exit(1);
}
