/**
 * 路径验证器
 */
import * as THREE from 'three';
import { AvatarPath } from './AvatarPath';
import { PathPoint } from './PathPoint';

/**
 * 验证结果接口
 */
export interface ValidationResult {
  /** 是否有效 */
  valid: boolean;
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
  /** 建议信息 */
  suggestions: string[];
}

/**
 * 验证选项
 */
export interface ValidationOptions {
  /** 最小路径点数量 */
  minPoints?: number;
  /** 最大路径点数量 */
  maxPoints?: number;
  /** 最小速度 */
  minSpeed?: number;
  /** 最大速度 */
  maxSpeed?: number;
  /** 最大等待时间 */
  maxWaitTime?: number;
  /** 最小点间距离 */
  minPointDistance?: number;
  /** 最大点间距离 */
  maxPointDistance?: number;
  /** 是否检查碰撞 */
  checkCollisions?: boolean;
  /** 是否检查可达性 */
  checkReachability?: boolean;
  /** 地面高度容差 */
  groundTolerance?: number;
}

/**
 * 路径验证器类
 */
export class PathValidator {
  /** 默认验证选项 */
  private static readonly DEFAULT_OPTIONS: ValidationOptions = {
    minPoints: 2,
    maxPoints: 1000,
    minSpeed: 0.1,
    maxSpeed: 20.0,
    maxWaitTime: 300,
    minPointDistance: 0.1,
    maxPointDistance: 100.0,
    checkCollisions: false,
    checkReachability: false,
    groundTolerance: 0.5
  };

  /**
   * 验证路径
   * @param path 要验证的路径
   * @param options 验证选项
   * @returns 验证结果
   */
  public static validatePath(path: AvatarPath, options: ValidationOptions = {}): ValidationResult {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    // 验证基本信息
    this.validateBasicInfo(path, result);

    // 验证路径点数量
    this.validatePointCount(path, opts, result);

    // 验证路径点
    this.validatePoints(path, opts, result);

    // 验证路径连续性
    this.validateContinuity(path, opts, result);

    // 验证性能
    this.validatePerformance(path, result);

    // 设置最终验证状态
    result.valid = result.errors.length === 0;

    return result;
  }

  /**
   * 验证基本信息
   * @param path 路径
   * @param result 验证结果
   */
  private static validateBasicInfo(path: AvatarPath, result: ValidationResult): void {
    // 验证路径名称
    if (!path.name || path.name.trim() === '') {
      result.errors.push('路径名称不能为空');
    } else if (path.name.length > 100) {
      result.warnings.push('路径名称过长，建议不超过100个字符');
    }

    // 验证数字人ID
    if (!path.avatarId || path.avatarId.trim() === '') {
      result.errors.push('必须指定关联的数字人ID');
    }

    // 验证路径ID
    if (!path.id || path.id.trim() === '') {
      result.errors.push('路径ID不能为空');
    }
  }

  /**
   * 验证路径点数量
   * @param path 路径
   * @param options 验证选项
   * @param result 验证结果
   */
  private static validatePointCount(path: AvatarPath, options: ValidationOptions, result: ValidationResult): void {
    const pointCount = path.points.length;

    if (pointCount < options.minPoints!) {
      result.errors.push(`路径点数量不足，至少需要${options.minPoints}个点，当前有${pointCount}个`);
    }

    if (pointCount > options.maxPoints!) {
      result.errors.push(`路径点数量过多，最多允许${options.maxPoints}个点，当前有${pointCount}个`);
    }

    if (pointCount > 100) {
      result.warnings.push('路径点数量较多，可能影响性能');
    }
  }

  /**
   * 验证路径点
   * @param path 路径
   * @param options 验证选项
   * @param result 验证结果
   */
  private static validatePoints(path: AvatarPath, options: ValidationOptions, result: ValidationResult): void {
    path.points.forEach((point, index) => {
      // 验证路径点本身
      const pointValidation = point.validate();
      if (!pointValidation.valid) {
        result.errors.push(`路径点${index + 1}: ${pointValidation.errors.join(', ')}`);
      }

      // 验证速度范围
      if (point.speed < options.minSpeed!) {
        result.errors.push(`路径点${index + 1}的速度过低: ${point.speed} < ${options.minSpeed}`);
      }
      if (point.speed > options.maxSpeed!) {
        result.errors.push(`路径点${index + 1}的速度过高: ${point.speed} > ${options.maxSpeed}`);
      }

      // 验证等待时间
      if (point.waitTime > options.maxWaitTime!) {
        result.warnings.push(`路径点${index + 1}的等待时间过长: ${point.waitTime}秒`);
      }

      // 验证位置
      if (this.isPositionInvalid(point.position)) {
        result.errors.push(`路径点${index + 1}的位置无效`);
      }

      // 验证朝向目标
      if (point.lookAt && this.isPositionInvalid(point.lookAt)) {
        result.errors.push(`路径点${index + 1}的朝向目标位置无效`);
      }

      // 验证动画名称
      if (!point.animation || point.animation.trim() === '') {
        result.warnings.push(`路径点${index + 1}没有指定动画`);
      }

      // 验证触发器
      point.triggers.forEach((trigger, triggerIndex) => {
        if (!trigger.type || trigger.type.trim() === '') {
          result.errors.push(`路径点${index + 1}的触发器${triggerIndex + 1}类型无效`);
        }
        if (trigger.delay && trigger.delay < 0) {
          result.errors.push(`路径点${index + 1}的触发器${triggerIndex + 1}延迟时间不能为负数`);
        }
      });
    });
  }

  /**
   * 验证路径连续性
   * @param path 路径
   * @param options 验证选项
   * @param result 验证结果
   */
  private static validateContinuity(path: AvatarPath, options: ValidationOptions, result: ValidationResult): void {
    for (let i = 0; i < path.points.length - 1; i++) {
      const point1 = path.points[i];
      const point2 = path.points[i + 1];
      const distance = point1.distanceTo(point2);

      // 验证点间距离
      if (distance < options.minPointDistance!) {
        result.warnings.push(`路径点${i + 1}和${i + 2}距离过近: ${distance.toFixed(2)}米`);
      }
      if (distance > options.maxPointDistance!) {
        result.warnings.push(`路径点${i + 1}和${i + 2}距离过远: ${distance.toFixed(2)}米`);
      }

      // 验证高度差
      const heightDiff = Math.abs(point1.position.y - point2.position.y);
      if (heightDiff > 5.0) {
        result.warnings.push(`路径点${i + 1}和${i + 2}高度差过大: ${heightDiff.toFixed(2)}米`);
      }

      // 验证速度变化
      const speedDiff = Math.abs(point1.speed - point2.speed);
      if (speedDiff > 5.0) {
        result.warnings.push(`路径点${i + 1}和${i + 2}速度变化过大: ${speedDiff.toFixed(2)}米/秒`);
      }
    }
  }

  /**
   * 验证性能
   * @param path 路径
   * @param result 验证结果
   */
  private static validatePerformance(path: AvatarPath, result: ValidationResult): void {
    // 验证总持续时间
    if (path.totalDuration > 3600) {
      result.warnings.push(`路径总持续时间过长: ${(path.totalDuration / 60).toFixed(1)}分钟`);
    }

    // 验证总长度
    if (path.totalLength > 1000) {
      result.warnings.push(`路径总长度过长: ${path.totalLength.toFixed(1)}米`);
    }

    // 验证触发器数量
    const totalTriggers = path.points.reduce((sum, point) => sum + point.triggers.length, 0);
    if (totalTriggers > 100) {
      result.warnings.push(`触发器数量过多: ${totalTriggers}个，可能影响性能`);
    }

    // 验证复杂度
    const complexity = this.calculatePathComplexity(path);
    if (complexity > 0.8) {
      result.warnings.push('路径复杂度较高，可能影响性能');
    }
  }

  /**
   * 计算路径复杂度
   * @param path 路径
   * @returns 复杂度 (0-1)
   */
  private static calculatePathComplexity(path: AvatarPath): number {
    if (path.points.length < 2) return 0;

    let complexity = 0;

    // 基于路径点数量
    complexity += Math.min(path.points.length / 100, 0.3);

    // 基于方向变化
    let totalAngleChange = 0;
    for (let i = 1; i < path.points.length - 1; i++) {
      const v1 = path.points[i].position.clone().sub(path.points[i - 1].position).normalize();
      const v2 = path.points[i + 1].position.clone().sub(path.points[i].position).normalize();
      const angle = Math.acos(Math.max(-1, Math.min(1, v1.dot(v2))));
      totalAngleChange += angle;
    }
    complexity += Math.min(totalAngleChange / (Math.PI * path.points.length), 0.3);

    // 基于速度变化
    let totalSpeedChange = 0;
    for (let i = 1; i < path.points.length; i++) {
      totalSpeedChange += Math.abs(path.points[i].speed - path.points[i - 1].speed);
    }
    complexity += Math.min(totalSpeedChange / (10 * path.points.length), 0.2);

    // 基于触发器数量
    const triggerCount = path.points.reduce((sum, point) => sum + point.triggers.length, 0);
    complexity += Math.min(triggerCount / 50, 0.2);

    return Math.min(complexity, 1);
  }

  /**
   * 检查位置是否无效
   * @param position 位置
   * @returns 是否无效
   */
  private static isPositionInvalid(position: THREE.Vector3): boolean {
    return isNaN(position.x) || isNaN(position.y) || isNaN(position.z) ||
           !isFinite(position.x) || !isFinite(position.y) || !isFinite(position.z);
  }

  /**
   * 快速验证路径（仅检查关键错误）
   * @param path 路径
   * @returns 是否有效
   */
  public static quickValidate(path: AvatarPath): boolean {
    // 检查基本信息
    if (!path.name || !path.avatarId || !path.id) return false;

    // 检查路径点数量
    if (path.points.length < 2) return false;

    // 检查路径点基本有效性
    for (const point of path.points) {
      if (this.isPositionInvalid(point.position) || point.speed <= 0) {
        return false;
      }
    }

    return true;
  }

  /**
   * 获取路径质量评分
   * @param path 路径
   * @returns 质量评分 (0-100)
   */
  public static getQualityScore(path: AvatarPath): number {
    const validation = this.validatePath(path);
    
    let score = 100;

    // 扣除错误分数
    score -= validation.errors.length * 20;

    // 扣除警告分数
    score -= validation.warnings.length * 5;

    // 基于复杂度调整
    const complexity = this.calculatePathComplexity(path);
    if (complexity > 0.5) {
      score -= (complexity - 0.5) * 20;
    }

    // 基于路径平滑度调整
    const smoothness = this.calculatePathSmoothness(path);
    score += smoothness * 10;

    return Math.max(0, Math.min(100, score));
  }

  /**
   * 计算路径平滑度
   * @param path 路径
   * @returns 平滑度 (0-1)
   */
  private static calculatePathSmoothness(path: AvatarPath): number {
    if (path.points.length < 3) return 1;

    let totalSmoothness = 0;
    let validSegments = 0;

    for (let i = 1; i < path.points.length - 1; i++) {
      const v1 = path.points[i].position.clone().sub(path.points[i - 1].position).normalize();
      const v2 = path.points[i + 1].position.clone().sub(path.points[i].position).normalize();
      
      const dot = v1.dot(v2);
      const smoothness = (dot + 1) / 2; // 将 [-1, 1] 映射到 [0, 1]
      
      totalSmoothness += smoothness;
      validSegments++;
    }

    return validSegments > 0 ? totalSmoothness / validSegments : 1;
  }
}
