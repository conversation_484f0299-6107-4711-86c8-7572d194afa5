/**
 * 自然语言场景生成器示例
 * 展示如何使用NLPSceneGenerator从自然语言描述生成3D场景
 */
import { Engine } from '../../src/core/Engine';
import { World } from '../../src/core/World';
import { NLPSceneGenerator } from '../../src/ai/NLPSceneGenerator';
import { RenderSystem } from '../../src/rendering/RenderSystem';
import { Renderer } from '../../src/rendering/Renderer';
import { Camera, CameraType } from '../../src/rendering/Camera';
import { Entity } from '../../src/core/Entity';
import { Transform } from '../../src/scene/Transform';

export class NLPSceneGeneratorExample {
  private engine: Engine;
  private world: World;
  private nlpGenerator: NLPSceneGenerator;
  private renderer: Renderer;
  private renderSystem: RenderSystem;
  private camera: Camera;

  constructor() {
    // 创建引擎
    this.engine = new Engine({
      canvas: document.getElementById('canvas') as HTMLCanvasElement,
      autoStart: false,
      debug: true
    });

    // 获取世界
    this.world = this.engine.getWorld();

    // 创建渲染器
    this.renderer = new Renderer({
      canvas: this.engine.getCanvas(),
      antialias: true,
      alpha: true
    });

    // 创建渲染系统
    this.renderSystem = new RenderSystem(this.renderer);
    this.world.addSystem(this.renderSystem);

    // 创建NLP场景生成器
    this.nlpGenerator = new NLPSceneGenerator();
    this.world.addSystem(this.nlpGenerator);

    // 创建相机
    this.setupCamera();
  }

  /**
   * 设置相机
   */
  private setupCamera(): void {
    const cameraEntity = new Entity('主相机');
    this.camera = new Camera({
      type: CameraType.PERSPECTIVE,
      fov: 75,
      aspect: window.innerWidth / window.innerHeight,
      near: 0.1,
      far: 1000,
      position: { x: 5, y: 5, z: 10 },
      lookAt: { x: 0, y: 0, z: 0 }
    });
    cameraEntity.addComponent(this.camera);

    // 设置相机变换
    const cameraTransform = new Transform();
    cameraTransform.setPosition(5, 5, 10);
    cameraTransform.lookAt(0, 0, 0);
    cameraEntity.addComponent(cameraTransform);

    // 设置为活跃相机
    this.renderSystem.setActiveCamera(this.camera);
  }

  /**
   * 初始化示例
   */
  public async initialize(): Promise<void> {
    console.log('初始化自然语言场景生成器示例...');

    // 初始化引擎
    await this.engine.initialize();

    // 启动引擎
    this.engine.start();

    console.log('示例初始化完成');
  }

  /**
   * 生成场景示例
   */
  public async generateSceneExamples(): Promise<void> {
    console.log('开始生成场景示例...');

    // 示例1：现代办公室
    await this.generateScene(
      '创建一个现代简约的办公室，包含一张木质桌子、两把椅子、一台电脑和一盏台灯，整体风格明亮温馨',
      {
        style: 'minimalist',
        quality: 80,
        maxObjects: 10,
        constraints: {
          maxPolygons: 50000,
          targetFrameRate: 60
        }
      },
      '现代办公室'
    );

    // 等待3秒
    await this.delay(3000);

    // 示例2：温馨客厅
    await this.generateScene(
      '生成一个温馨的客厅场景，有一个舒适的沙发、茶几、电视和一些绿色植物，营造放松的氛围',
      {
        style: 'realistic',
        quality: 90,
        maxObjects: 15,
        constraints: {
          maxPolygons: 80000,
          targetFrameRate: 60
        }
      },
      '温馨客厅'
    );

    // 等待3秒
    await this.delay(3000);

    // 示例3：科幻实验室
    await this.generateScene(
      '创建一个未来科幻风格的实验室，包含金属桌子、高科技设备、蓝色灯光，整体氛围神秘冷酷',
      {
        style: 'scifi',
        quality: 85,
        maxObjects: 12,
        constraints: {
          maxPolygons: 60000,
          targetFrameRate: 60
        }
      },
      '科幻实验室'
    );
  }

  /**
   * 生成单个场景
   */
  private async generateScene(
    description: string,
    options: any,
    sceneName: string
  ): Promise<void> {
    console.log(`\n=== 生成场景: ${sceneName} ===`);
    console.log(`描述: ${description}`);

    try {
      // 添加进度回调
      const optionsWithProgress = {
        ...options,
        onProgress: (progress: number) => {
          console.log(`${sceneName} 生成进度: ${progress}%`);
        }
      };

      // 生成场景
      const scene = await this.nlpGenerator.generateSceneFromNaturalLanguage(
        description,
        optionsWithProgress
      );

      // 设置为活跃场景
      this.renderSystem.setActiveScene(scene);

      console.log(`${sceneName} 生成完成！`);
      console.log(`场景实体数量: ${scene.getEntities().length}`);

    } catch (error) {
      console.error(`生成${sceneName}失败:`, error);
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    console.log('清理资源...');
    this.engine.dispose();
  }
}

// 使用示例
async function runExample() {
  const example = new NLPSceneGeneratorExample();
  
  try {
    await example.initialize();
    await example.generateSceneExamples();
  } catch (error) {
    console.error('示例运行失败:', error);
  }
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.addEventListener('load', runExample);
}

// 导出示例类
export default NLPSceneGeneratorExample;
