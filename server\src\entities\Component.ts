/**
 * Component.ts
 * 
 * 组件实体定义
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  JoinColumn
} from 'typeorm';
import { User } from './User';
import { ComponentRating } from './ComponentRating';
import { ComponentDownload } from './ComponentDownload';

@Entity('components')
@Index(['category', 'isPublic', 'isApproved'])
@Index(['author', 'name'], { unique: true })
export class Component {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  @Index()
  name: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ length: 50 })
  @Index()
  category: string;

  @Column({ type: 'simple-array' })
  @Index()
  tags: string[];

  @Column({ length: 20 })
  version: string;

  @Column({ type: 'jsonb' })
  componentData: any;

  @Column({ nullable: true })
  filePath?: string;

  @Column({ nullable: true })
  previewImage?: string;

  @Column({ type: 'text', nullable: true })
  documentation?: string;

  @Column({ length: 50, default: 'MIT' })
  license: string;

  @Column({ type: 'simple-array', default: () => "'{}'" })
  dependencies: string[];

  @ManyToOne(() => User, user => user.components, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'authorId' })
  author: User;

  @Column({ default: 0 })
  @Index()
  downloads: number;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0 })
  @Index()
  rating: number;

  @Column({ default: 0 })
  ratingCount: number;

  @Column({ default: true })
  @Index()
  isPublic: boolean;

  @Column({ default: false })
  @Index()
  isApproved: boolean;

  @Column({ default: false })
  isFeatured: boolean;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: {
    size?: number;
    complexity?: 'simple' | 'medium' | 'complex';
    framework?: string[];
    minVersion?: string;
    maxVersion?: string;
    screenshots?: string[];
    demoUrl?: string;
    sourceUrl?: string;
  };

  @OneToMany(() => ComponentRating, rating => rating.component, { cascade: true })
  ratings: ComponentRating[];

  @OneToMany(() => ComponentDownload, download => download.component, { cascade: true })
  downloads_history: ComponentDownload[];

  @CreateDateColumn()
  @Index()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 虚拟字段
  get averageRating(): number {
    return this.rating;
  }

  get isPopular(): boolean {
    return this.downloads > 100 && this.rating > 4.0;
  }

  get tagString(): string {
    return this.tags.join(', ');
  }
}

/**
 * ComponentRating.ts
 * 
 * 组件评分实体
 */
@Entity('component_ratings')
@Index(['component', 'user'], { unique: true })
export class ComponentRating {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Component, component => component.ratings, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'componentId' })
  component: Component;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'int', width: 1 })
  rating: number; // 1-5

  @Column({ type: 'text', nullable: true })
  comment?: string;

  @Column({ default: false })
  isHelpful: boolean;

  @Column({ default: 0 })
  helpfulCount: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

/**
 * ComponentDownload.ts
 * 
 * 组件下载记录实体
 */
@Entity('component_downloads')
@Index(['component', 'downloadedAt'])
@Index(['user', 'downloadedAt'])
export class ComponentDownload {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Component, component => component.downloads_history, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'componentId' })
  component: Component;

  @ManyToOne(() => User, { onDelete: 'SET NULL', nullable: true })
  @JoinColumn({ name: 'userId' })
  user?: User;

  @Column({ nullable: true })
  ipAddress?: string;

  @Column({ nullable: true })
  userAgent?: string;

  @Column({ nullable: true })
  referer?: string;

  @CreateDateColumn()
  @Index()
  downloadedAt: Date;
}

/**
 * ComponentCategory.ts
 * 
 * 组件分类实体
 */
@Entity('component_categories')
export class ComponentCategory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 50, unique: true })
  name: string;

  @Column({ length: 200 })
  description: string;

  @Column({ nullable: true })
  icon?: string;

  @Column({ nullable: true })
  color?: string;

  @Column({ default: 0 })
  sortOrder: number;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: 0 })
  componentCount: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

/**
 * ComponentTag.ts
 * 
 * 组件标签实体
 */
@Entity('component_tags')
export class ComponentTag {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 30, unique: true })
  name: string;

  @Column({ length: 100, nullable: true })
  description?: string;

  @Column({ nullable: true })
  color?: string;

  @Column({ default: 0 })
  usageCount: number;

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

/**
 * ComponentCollection.ts
 * 
 * 组件收藏夹实体
 */
@Entity('component_collections')
export class ComponentCollection {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'simple-array' })
  componentIds: string[];

  @Column({ default: false })
  isPublic: boolean;

  @Column({ default: 0 })
  viewCount: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  get componentCount(): number {
    return this.componentIds.length;
  }
}

/**
 * ComponentReport.ts
 * 
 * 组件举报实体
 */
@Entity('component_reports')
export class ComponentReport {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Component, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'componentId' })
  component: Component;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'reporterId' })
  reporter: User;

  @Column({ length: 50 })
  reason: string; // 'spam', 'inappropriate', 'copyright', 'malicious', 'other'

  @Column({ type: 'text' })
  description: string;

  @Column({ default: 'pending' })
  status: string; // 'pending', 'reviewed', 'resolved', 'dismissed'

  @ManyToOne(() => User, { onDelete: 'SET NULL', nullable: true })
  @JoinColumn({ name: 'reviewerId' })
  reviewer?: User;

  @Column({ type: 'text', nullable: true })
  reviewNote?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
