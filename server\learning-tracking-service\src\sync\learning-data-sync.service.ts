import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { XAPIStatement } from '../xapi/interfaces/xapi.interface';
import { LearninglocketClientService } from '../learninglocker/learninglocker-client.service';
import { LearningRecord } from '../entities/learning-record.entity';

/**
 * 同步状态枚举
 */
export enum SyncStatus {
  PENDING = 'pending',
  SYNCING = 'syncing',
  SYNCED = 'synced',
  FAILED = 'failed',
  RETRY = 'retry'
}

/**
 * 同步统计信息
 */
export interface SyncStats {
  totalRecords: number;
  syncedRecords: number;
  failedRecords: number;
  pendingRecords: number;
  lastSyncTime: Date;
  syncRate: number; // 同步成功率
}

/**
 * 学习数据同步服务
 * 负责将本地学习记录同步到Learninglocker
 */
@Injectable()
export class LearningDataSyncService {
  private readonly logger = new Logger(LearningDataSyncService.name);
  private readonly batchSize: number;
  private readonly maxRetries: number;
  private readonly retryDelay: number;
  private isRunning: boolean = false;

  constructor(
    @InjectRepository(LearningRecord)
    private readonly learningRecordRepository: Repository<LearningRecord>,
    private readonly learninglocketClient: LearninglocketClientService,
    private readonly configService: ConfigService,
    @InjectQueue('learning-sync')
    private readonly syncQueue: Queue,
  ) {
    this.batchSize = this.configService.get<number>('SYNC_BATCH_SIZE', 50);
    this.maxRetries = this.configService.get<number>('SYNC_MAX_RETRIES', 3);
    this.retryDelay = this.configService.get<number>('SYNC_RETRY_DELAY', 5000);
  }

  /**
   * 接收并处理xAPI语句
   * @param statement xAPI语句
   */
  async receiveStatement(statement: XAPIStatement): Promise<void> {
    try {
      // 保存到本地数据库
      const record = await this.saveToLocalDatabase(statement);

      // 添加到同步队列
      await this.addToSyncQueue(record);

      this.logger.debug(`接收并保存xAPI语句: ${statement.id}`);
    } catch (error) {
      this.logger.error(`处理xAPI语句失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 批量接收xAPI语句
   * @param statements xAPI语句数组
   */
  async receiveStatements(statements: XAPIStatement[]): Promise<void> {
    try {
      const records: LearningRecord[] = [];

      // 批量保存到本地数据库
      for (const statement of statements) {
        const record = await this.saveToLocalDatabase(statement);
        records.push(record);
      }

      // 批量添加到同步队列
      await this.addBatchToSyncQueue(records);

      this.logger.log(`批量接收并保存 ${statements.length} 条xAPI语句`);
    } catch (error) {
      this.logger.error(`批量处理xAPI语句失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 保存到本地数据库
   * @param statement xAPI语句
   * @returns 学习记录实体
   */
  private async saveToLocalDatabase(statement: XAPIStatement): Promise<LearningRecord> {
    const record = this.learningRecordRepository.create({
      statementId: statement.id,
      userId: this.extractUserId(statement.actor),
      verb: statement.verb.id,
      objectId: this.extractObjectId(statement.object),
      result: statement.result ? JSON.stringify(statement.result) : null,
      context: statement.context ? JSON.stringify(statement.context) : null,
      timestamp: new Date(statement.timestamp),
      rawStatement: JSON.stringify(statement),
      syncStatus: SyncStatus.PENDING,
      retryCount: 0
    });

    return await this.learningRecordRepository.save(record);
  }

  /**
   * 添加到同步队列
   * @param record 学习记录
   */
  private async addToSyncQueue(record: LearningRecord): Promise<void> {
    await this.syncQueue.add('sync-single', { recordId: record.id }, {
      delay: 1000, // 延迟1秒执行
      attempts: this.maxRetries,
      backoff: {
        type: 'exponential',
        delay: this.retryDelay,
      },
    });
  }

  /**
   * 批量添加到同步队列
   * @param records 学习记录数组
   */
  private async addBatchToSyncQueue(records: LearningRecord[]): Promise<void> {
    const recordIds = records.map(r => r.id);
    
    await this.syncQueue.add('sync-batch', { recordIds }, {
      delay: 2000, // 延迟2秒执行
      attempts: this.maxRetries,
      backoff: {
        type: 'exponential',
        delay: this.retryDelay,
      },
    });
  }

  /**
   * 同步单个记录到Learninglocker
   * @param recordId 记录ID
   */
  async syncSingleRecord(recordId: string): Promise<void> {
    try {
      const record = await this.learningRecordRepository.findOne({
        where: { id: recordId }
      });

      if (!record) {
        throw new Error(`未找到学习记录: ${recordId}`);
      }

      if (record.syncStatus === SyncStatus.SYNCED) {
        this.logger.debug(`记录已同步，跳过: ${recordId}`);
        return;
      }

      // 更新同步状态
      await this.updateSyncStatus(record, SyncStatus.SYNCING);

      // 解析xAPI语句
      const statement: XAPIStatement = JSON.parse(record.rawStatement);

      // 发送到Learninglocker
      await this.learninglocketClient.sendStatement(statement);

      // 更新为已同步
      await this.updateSyncStatus(record, SyncStatus.SYNCED);

      this.logger.debug(`成功同步记录到Learninglocker: ${recordId}`);
    } catch (error) {
      this.logger.error(`同步记录失败: ${recordId}, ${error.message}`);
      
      const record = await this.learningRecordRepository.findOne({
        where: { id: recordId }
      });

      if (record) {
        await this.handleSyncFailure(record, error.message);
      }

      throw error;
    }
  }

  /**
   * 批量同步记录到Learninglocker
   * @param recordIds 记录ID数组
   */
  async syncBatchRecords(recordIds: string[]): Promise<void> {
    try {
      const records = await this.learningRecordRepository.findByIds(recordIds);
      
      if (records.length === 0) {
        this.logger.warn(`未找到要同步的记录: ${recordIds.join(', ')}`);
        return;
      }

      // 过滤未同步的记录
      const unsyncedRecords = records.filter(r => r.syncStatus !== SyncStatus.SYNCED);
      
      if (unsyncedRecords.length === 0) {
        this.logger.debug('所有记录已同步，跳过批量同步');
        return;
      }

      // 更新同步状态
      await this.updateBatchSyncStatus(unsyncedRecords, SyncStatus.SYNCING);

      // 解析xAPI语句
      const statements: XAPIStatement[] = unsyncedRecords.map(record => 
        JSON.parse(record.rawStatement)
      );

      // 批量发送到Learninglocker
      await this.learninglocketClient.sendStatements(statements);

      // 更新为已同步
      await this.updateBatchSyncStatus(unsyncedRecords, SyncStatus.SYNCED);

      this.logger.log(`成功批量同步 ${unsyncedRecords.length} 条记录到Learninglocker`);
    } catch (error) {
      this.logger.error(`批量同步记录失败: ${error.message}`);
      
      const records = await this.learningRecordRepository.findByIds(recordIds);
      await this.handleBatchSyncFailure(records, error.message);

      throw error;
    }
  }

  /**
   * 定时同步任务
   * 每5分钟执行一次
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async scheduledSync(): Promise<void> {
    if (this.isRunning) {
      this.logger.debug('同步任务正在运行，跳过本次执行');
      return;
    }

    this.isRunning = true;

    try {
      await this.syncPendingRecords();
    } catch (error) {
      this.logger.error(`定时同步任务失败: ${error.message}`, error.stack);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 同步待处理的记录
   */
  async syncPendingRecords(): Promise<void> {
    try {
      const pendingRecords = await this.learningRecordRepository.find({
        where: [
          { syncStatus: SyncStatus.PENDING },
          { syncStatus: SyncStatus.FAILED }
        ],
        take: this.batchSize,
        order: { createdAt: 'ASC' }
      });

      if (pendingRecords.length === 0) {
        this.logger.debug('没有待同步的记录');
        return;
      }

      this.logger.log(`开始同步 ${pendingRecords.length} 条待处理记录`);

      // 分批同步
      const batchSize = 10;
      for (let i = 0; i < pendingRecords.length; i += batchSize) {
        const batch = pendingRecords.slice(i, i + batchSize);
        const recordIds = batch.map(r => r.id);
        
        try {
          await this.syncBatchRecords(recordIds);
        } catch (error) {
          this.logger.error(`批次同步失败: ${error.message}`);
          // 继续处理下一批
        }
      }

      this.logger.log('定时同步任务完成');
    } catch (error) {
      this.logger.error(`同步待处理记录失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新同步状态
   * @param record 学习记录
   * @param status 同步状态
   */
  private async updateSyncStatus(record: LearningRecord, status: SyncStatus): Promise<void> {
    record.syncStatus = status;
    record.lastSyncAttempt = new Date();
    
    if (status === SyncStatus.SYNCED) {
      record.syncedAt = new Date();
    }

    await this.learningRecordRepository.save(record);
  }

  /**
   * 批量更新同步状态
   * @param records 学习记录数组
   * @param status 同步状态
   */
  private async updateBatchSyncStatus(records: LearningRecord[], status: SyncStatus): Promise<void> {
    const now = new Date();
    
    records.forEach(record => {
      record.syncStatus = status;
      record.lastSyncAttempt = now;
      
      if (status === SyncStatus.SYNCED) {
        record.syncedAt = now;
      }
    });

    await this.learningRecordRepository.save(records);
  }

  /**
   * 处理同步失败
   * @param record 学习记录
   * @param errorMessage 错误信息
   */
  private async handleSyncFailure(record: LearningRecord, errorMessage: string): Promise<void> {
    record.retryCount++;
    record.lastError = errorMessage;
    record.lastSyncAttempt = new Date();

    if (record.retryCount >= this.maxRetries) {
      record.syncStatus = SyncStatus.FAILED;
      this.logger.error(`记录同步失败，已达最大重试次数: ${record.id}`);
    } else {
      record.syncStatus = SyncStatus.RETRY;
      this.logger.warn(`记录同步失败，将重试: ${record.id}, 重试次数: ${record.retryCount}`);
    }

    await this.learningRecordRepository.save(record);
  }

  /**
   * 处理批量同步失败
   * @param records 学习记录数组
   * @param errorMessage 错误信息
   */
  private async handleBatchSyncFailure(records: LearningRecord[], errorMessage: string): Promise<void> {
    const now = new Date();
    
    records.forEach(record => {
      if (record.syncStatus !== SyncStatus.SYNCED) {
        record.retryCount++;
        record.lastError = errorMessage;
        record.lastSyncAttempt = now;

        if (record.retryCount >= this.maxRetries) {
          record.syncStatus = SyncStatus.FAILED;
        } else {
          record.syncStatus = SyncStatus.RETRY;
        }
      }
    });

    await this.learningRecordRepository.save(records);
  }

  /**
   * 获取同步统计信息
   * @returns 同步统计
   */
  async getSyncStats(): Promise<SyncStats> {
    const [totalRecords, syncedRecords, failedRecords, pendingRecords] = await Promise.all([
      this.learningRecordRepository.count(),
      this.learningRecordRepository.count({ where: { syncStatus: SyncStatus.SYNCED } }),
      this.learningRecordRepository.count({ where: { syncStatus: SyncStatus.FAILED } }),
      this.learningRecordRepository.count({ 
        where: [
          { syncStatus: SyncStatus.PENDING },
          { syncStatus: SyncStatus.RETRY }
        ]
      })
    ]);

    const lastSyncRecord = await this.learningRecordRepository.findOne({
      where: { syncStatus: SyncStatus.SYNCED },
      order: { syncedAt: 'DESC' }
    });

    const syncRate = totalRecords > 0 ? syncedRecords / totalRecords : 0;

    return {
      totalRecords,
      syncedRecords,
      failedRecords,
      pendingRecords,
      lastSyncTime: lastSyncRecord?.syncedAt || new Date(0),
      syncRate
    };
  }

  /**
   * 强制重新同步失败的记录
   */
  async retrySyncFailedRecords(): Promise<void> {
    const failedRecords = await this.learningRecordRepository.find({
      where: { syncStatus: SyncStatus.FAILED },
      take: this.batchSize
    });

    if (failedRecords.length === 0) {
      this.logger.log('没有失败的记录需要重试');
      return;
    }

    this.logger.log(`开始重试同步 ${failedRecords.length} 条失败记录`);

    // 重置状态和重试次数
    failedRecords.forEach(record => {
      record.syncStatus = SyncStatus.PENDING;
      record.retryCount = 0;
      record.lastError = null;
    });

    await this.learningRecordRepository.save(failedRecords);

    // 添加到同步队列
    await this.addBatchToSyncQueue(failedRecords);
  }

  /**
   * 提取用户ID
   * @param actor 行为者
   * @returns 用户ID
   */
  private extractUserId(actor: any): string {
    if (actor.account) {
      return actor.account.name;
    }
    if (actor.mbox) {
      return actor.mbox.replace('mailto:', '');
    }
    return actor.name || 'unknown';
  }

  /**
   * 提取对象ID
   * @param object 对象
   * @returns 对象ID
   */
  private extractObjectId(object: any): string {
    return object.id || 'unknown';
  }
}
