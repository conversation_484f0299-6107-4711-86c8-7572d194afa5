import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  ParseIntPipe,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { UITemplateService } from './ui-template.service';
import {
  CreateTemplateDto,
  UpdateTemplateDto,
  QueryTemplateDto,
  ForkTemplateDto,
  RateTemplateDto,
  TemplateResponseDto,
  TemplateListResponseDto,
} from './dto/ui-template.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Types } from 'mongoose';

@ApiTags('UI模板管理')
@Controller('ui-templates')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UITemplateController {
  constructor(private readonly templateService: UITemplateService) {}

  @Post()
  @ApiOperation({ summary: '创建UI模板' })
  @ApiResponse({
    status: 201,
    description: '模板创建成功',
    type: TemplateResponseDto,
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 401, description: '未授权' })
  async create(
    @Body(ValidationPipe) createTemplateDto: CreateTemplateDto,
    @Request() req: any,
  ): Promise<TemplateResponseDto> {
    const userId = new Types.ObjectId(req.user.id);
    return await this.templateService.create(createTemplateDto, userId);
  }

  @Get()
  @ApiOperation({ summary: '获取UI模板列表' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: TemplateListResponseDto,
  })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'category', required: false, description: '模板分类' })
  @ApiQuery({ name: 'status', required: false, description: '模板状态' })
  @ApiQuery({ name: 'search', required: false, description: '搜索关键词' })
  async findAll(
    @Query(ValidationPipe) query: QueryTemplateDto,
    @Request() req: any,
  ): Promise<TemplateListResponseDto> {
    const userId = new Types.ObjectId(req.user.id);
    const userOrganizationId = req.user.organizationId ? new Types.ObjectId(req.user.organizationId) : undefined;
    const userTeamId = req.user.teamId ? new Types.ObjectId(req.user.teamId) : undefined;

    const result = await this.templateService.findAll(query, userId, userOrganizationId, userTeamId);
    
    return {
      ...result,
      totalPages: Math.ceil(result.total / result.limit),
      hasNext: result.page * result.limit < result.total,
      hasPrev: result.page > 1,
    };
  }

  @Get('popular')
  @ApiOperation({ summary: '获取热门模板' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: [TemplateResponseDto],
  })
  @ApiQuery({ name: 'limit', required: false, description: '数量限制' })
  async findPopular(
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 10,
  ): Promise<TemplateResponseDto[]> {
    // 这里可以调用模板服务的获取热门模板方法
    // return await this.templateService.findPopular(limit);
    return [];
  }

  @Get('recent')
  @ApiOperation({ summary: '获取最新模板' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: [TemplateResponseDto],
  })
  @ApiQuery({ name: 'limit', required: false, description: '数量限制' })
  async findRecent(
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 10,
  ): Promise<TemplateResponseDto[]> {
    // 这里可以调用模板服务的获取最新模板方法
    // return await this.templateService.findRecent(limit);
    return [];
  }

  @Get('search')
  @ApiOperation({ summary: '搜索模板' })
  @ApiResponse({
    status: 200,
    description: '搜索成功',
    type: TemplateListResponseDto,
  })
  @ApiQuery({ name: 'q', required: true, description: '搜索关键词' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  async search(
    @Query('q') query: string,
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 20,
    @Request() req: any,
  ): Promise<TemplateListResponseDto> {
    const userId = new Types.ObjectId(req.user.id);
    const userOrganizationId = req.user.organizationId ? new Types.ObjectId(req.user.organizationId) : undefined;
    const userTeamId = req.user.teamId ? new Types.ObjectId(req.user.teamId) : undefined;

    const queryDto: QueryTemplateDto = {
      search: query,
      page,
      limit,
    };

    const result = await this.templateService.findAll(queryDto, userId, userOrganizationId, userTeamId);
    
    return {
      ...result,
      totalPages: Math.ceil(result.total / result.limit),
      hasNext: result.page * result.limit < result.total,
      hasPrev: result.page > 1,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: '获取指定UI模板' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: TemplateResponseDto,
  })
  @ApiResponse({ status: 404, description: '模板不存在' })
  @ApiResponse({ status: 403, description: '没有访问权限' })
  @ApiParam({ name: 'id', description: '模板ID' })
  async findOne(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<TemplateResponseDto> {
    const userId = new Types.ObjectId(req.user.id);
    const userOrganizationId = req.user.organizationId ? new Types.ObjectId(req.user.organizationId) : undefined;
    const userTeamId = req.user.teamId ? new Types.ObjectId(req.user.teamId) : undefined;

    return await this.templateService.findOne(id, userId, userOrganizationId, userTeamId);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新UI模板' })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: TemplateResponseDto,
  })
  @ApiResponse({ status: 404, description: '模板不存在' })
  @ApiResponse({ status: 403, description: '没有编辑权限' })
  @ApiParam({ name: 'id', description: '模板ID' })
  async update(
    @Param('id') id: string,
    @Body(ValidationPipe) updateTemplateDto: UpdateTemplateDto,
    @Request() req: any,
  ): Promise<TemplateResponseDto> {
    const userId = new Types.ObjectId(req.user.id);
    const userRole = req.user.role;

    return await this.templateService.update(id, updateTemplateDto, userId, userRole);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除UI模板' })
  @ApiResponse({ status: 204, description: '删除成功' })
  @ApiResponse({ status: 404, description: '模板不存在' })
  @ApiResponse({ status: 403, description: '没有删除权限' })
  @ApiParam({ name: 'id', description: '模板ID' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<void> {
    const userId = new Types.ObjectId(req.user.id);
    const userRole = req.user.role;

    await this.templateService.remove(id, userId, userRole);
  }

  @Post(':id/fork')
  @ApiOperation({ summary: '复制模板' })
  @ApiResponse({
    status: 201,
    description: '复制成功',
    type: TemplateResponseDto,
  })
  @ApiResponse({ status: 404, description: '原模板不存在' })
  @ApiResponse({ status: 403, description: '该模板不允许复制' })
  @ApiParam({ name: 'id', description: '原模板ID' })
  async fork(
    @Param('id') id: string,
    @Body(ValidationPipe) forkTemplateDto: ForkTemplateDto,
    @Request() req: any,
  ): Promise<TemplateResponseDto> {
    const userId = new Types.ObjectId(req.user.id);

    return await this.templateService.fork(id, forkTemplateDto, userId);
  }

  @Post(':id/publish')
  @ApiOperation({ summary: '发布模板' })
  @ApiResponse({
    status: 200,
    description: '发布成功',
    type: TemplateResponseDto,
  })
  @ApiResponse({ status: 404, description: '模板不存在' })
  @ApiResponse({ status: 403, description: '没有发布权限' })
  @ApiResponse({ status: 400, description: '模板已经发布' })
  @ApiParam({ name: 'id', description: '模板ID' })
  async publish(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<TemplateResponseDto> {
    const userId = new Types.ObjectId(req.user.id);
    const userRole = req.user.role;

    return await this.templateService.publish(id, userId, userRole);
  }

  @Post(':id/rate')
  @ApiOperation({ summary: '评价模板' })
  @ApiResponse({ status: 200, description: '评价成功' })
  @ApiResponse({ status: 404, description: '模板不存在' })
  @ApiResponse({ status: 400, description: '评分无效' })
  @ApiParam({ name: 'id', description: '模板ID' })
  async rate(
    @Param('id') id: string,
    @Body(ValidationPipe) rateTemplateDto: RateTemplateDto,
    @Request() req: any,
  ): Promise<{ message: string }> {
    // 这里可以实现评价功能
    // await this.templateService.rate(id, rateTemplateDto, req.user.id);
    return { message: '评价成功' };
  }

  @Get(':id/versions')
  @ApiOperation({ summary: '获取模板版本历史' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '模板不存在' })
  @ApiParam({ name: 'id', description: '模板ID' })
  @ApiQuery({ name: 'limit', required: false, description: '数量限制' })
  async getVersionHistory(
    @Param('id') id: string,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 50,
    @Request() req: any,
  ): Promise<any[]> {
    return await this.templateService.getVersionHistory(id, limit);
  }

  @Post(':id/restore')
  @ApiOperation({ summary: '恢复已删除的模板' })
  @ApiResponse({
    status: 200,
    description: '恢复成功',
    type: TemplateResponseDto,
  })
  @ApiResponse({ status: 404, description: '模板不存在或未被删除' })
  @ApiResponse({ status: 403, description: '没有恢复权限' })
  @ApiParam({ name: 'id', description: '模板ID' })
  @UseGuards(RolesGuard)
  @Roles('admin', 'owner')
  async restore(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<TemplateResponseDto> {
    const userId = new Types.ObjectId(req.user.id);
    const userRole = req.user.role;

    return await this.templateService.restore(id, userId, userRole);
  }

  @Get(':id/download')
  @ApiOperation({ summary: '下载模板' })
  @ApiResponse({ status: 200, description: '下载成功' })
  @ApiResponse({ status: 404, description: '模板不存在' })
  @ApiResponse({ status: 403, description: '没有下载权限' })
  @ApiParam({ name: 'id', description: '模板ID' })
  async download(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<any> {
    const userId = new Types.ObjectId(req.user.id);
    const userOrganizationId = req.user.organizationId ? new Types.ObjectId(req.user.organizationId) : undefined;
    const userTeamId = req.user.teamId ? new Types.ObjectId(req.user.teamId) : undefined;

    const template = await this.templateService.findOne(id, userId, userOrganizationId, userTeamId);
    
    // 增加下载次数
    // await this.templateService.incrementDownloads(id);

    return {
      template,
      downloadUrl: `/api/ui-templates/${id}/export`,
      message: '模板下载成功'
    };
  }

  @Get(':id/export')
  @ApiOperation({ summary: '导出模板' })
  @ApiResponse({ status: 200, description: '导出成功' })
  @ApiResponse({ status: 404, description: '模板不存在' })
  @ApiParam({ name: 'id', description: '模板ID' })
  async export(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<any> {
    const userId = new Types.ObjectId(req.user.id);
    const userOrganizationId = req.user.organizationId ? new Types.ObjectId(req.user.organizationId) : undefined;
    const userTeamId = req.user.teamId ? new Types.ObjectId(req.user.teamId) : undefined;

    const template = await this.templateService.findOne(id, userId, userOrganizationId, userTeamId);
    const versions = await this.templateService.getVersionHistory(id, 1);

    return {
      template: {
        id: template.id,
        name: template.name,
        description: template.description,
        category: template.category,
        tags: template.tags,
        elements: versions[0]?.elements || [],
        metadata: template.metadata,
        version: template.version,
        exportedAt: new Date(),
      },
      format: 'DL_TEMPLATE_V1',
      exportedBy: req.user.id,
    };
  }
}
