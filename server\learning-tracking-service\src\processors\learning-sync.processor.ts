import { Processor, Process } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { LearningDataSyncService } from '../sync/learning-data-sync.service';

/**
 * 学习数据同步队列处理器
 * 处理异步的学习数据同步任务
 */
@Processor('learning-sync')
export class LearningSyncProcessor {
  private readonly logger = new Logger(LearningSyncProcessor.name);

  constructor(
    private readonly syncService: LearningDataSyncService,
  ) {}

  /**
   * 处理单个记录同步任务
   * @param job 队列任务
   */
  @Process('sync-single')
  async handleSyncSingle(job: Job<{ recordId: string }>) {
    const { recordId } = job.data;
    
    try {
      this.logger.debug(`开始同步单个记录: ${recordId}`);
      
      await this.syncService.syncSingleRecord(recordId);
      
      this.logger.debug(`单个记录同步完成: ${recordId}`);
      
      return { success: true, recordId };
    } catch (error) {
      this.logger.error(`单个记录同步失败: ${recordId}, ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 处理批量记录同步任务
   * @param job 队列任务
   */
  @Process('sync-batch')
  async handleSyncBatch(job: Job<{ recordIds: string[] }>) {
    const { recordIds } = job.data;
    
    try {
      this.logger.debug(`开始批量同步记录: ${recordIds.length}条`);
      
      await this.syncService.syncBatchRecords(recordIds);
      
      this.logger.debug(`批量记录同步完成: ${recordIds.length}条`);
      
      return { success: true, count: recordIds.length };
    } catch (error) {
      this.logger.error(`批量记录同步失败: ${recordIds.length}条, ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 处理定时同步任务
   * @param job 队列任务
   */
  @Process('scheduled-sync')
  async handleScheduledSync(job: Job) {
    try {
      this.logger.log('开始执行定时同步任务');
      
      await this.syncService.syncPendingRecords();
      
      this.logger.log('定时同步任务完成');
      
      return { success: true, timestamp: new Date() };
    } catch (error) {
      this.logger.error(`定时同步任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 处理重试同步任务
   * @param job 队列任务
   */
  @Process('retry-sync')
  async handleRetrySync(job: Job) {
    try {
      this.logger.log('开始执行重试同步任务');
      
      await this.syncService.retrySyncFailedRecords();
      
      this.logger.log('重试同步任务完成');
      
      return { success: true, timestamp: new Date() };
    } catch (error) {
      this.logger.error(`重试同步任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
