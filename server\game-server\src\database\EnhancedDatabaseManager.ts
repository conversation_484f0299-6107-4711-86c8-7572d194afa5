/**
 * 增强的数据库管理器
 * 专为支持100+并发用户优化的数据库连接和查询管理系统
 */
import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Pool, PoolClient, PoolConfig } from 'pg';
import { Redis } from 'ioredis';

// 数据库连接池配置
interface DatabasePoolConfig extends PoolConfig {
  // 基础配置
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  
  // 连接池配置
  min: number;
  max: number;
  idleTimeoutMillis: number;
  connectionTimeoutMillis: number;
  
  // 性能优化配置
  statement_timeout: number;
  query_timeout: number;
  keepAlive: boolean;
  keepAliveInitialDelayMillis: number;
}

// Redis缓存配置
interface RedisCacheConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  keyPrefix: string;
  retryDelayOnFailover: number;
  maxRetriesPerRequest: number;
  lazyConnect: boolean;
  keepAlive: number;
}

// 查询统计信息
interface QueryStats {
  totalQueries: number;
  successfulQueries: number;
  failedQueries: number;
  averageExecutionTime: number;
  slowQueries: number;
  cacheHits: number;
  cacheMisses: number;
  cacheHitRate: number;
}

// 查询缓存项
interface CacheItem {
  data: any;
  timestamp: number;
  ttl: number;
  hits: number;
}

/**
 * 增强的数据库管理器类
 */
@Injectable()
export class EnhancedDatabaseManager implements OnModuleDestroy {
  private readonly logger = new Logger(EnhancedDatabaseManager.name);
  
  // 数据库连接池
  private primaryPool: Pool;
  private readReplicaPools: Pool[] = [];
  
  // Redis缓存
  private redisClient: Redis;
  private redisCluster?: Redis.Cluster;
  
  // 内存缓存
  private memoryCache: Map<string, CacheItem> = new Map();
  private maxMemoryCacheSize: number;
  
  // 查询统计
  private queryStats: QueryStats = {
    totalQueries: 0,
    successfulQueries: 0,
    failedQueries: 0,
    averageExecutionTime: 0,
    slowQueries: 0,
    cacheHits: 0,
    cacheMisses: 0,
    cacheHitRate: 0,
  };
  
  // 配置
  private readonly slowQueryThreshold: number;
  private readonly cacheDefaultTTL: number;
  private readonly enableQueryCache: boolean;
  private readonly enableReadReplicas: boolean;
  
  // 定时器
  private statsTimer?: NodeJS.Timeout;
  private cacheCleanupTimer?: NodeJS.Timeout;
  
  constructor(private readonly configService: ConfigService) {
    // 读取配置
    this.slowQueryThreshold = this.configService.get<number>('SLOW_QUERY_THRESHOLD', 1000);
    this.cacheDefaultTTL = this.configService.get<number>('CACHE_DEFAULT_TTL', 300);
    this.enableQueryCache = this.configService.get<boolean>('ENABLE_QUERY_CACHE', true);
    this.enableReadReplicas = this.configService.get<boolean>('ENABLE_READ_REPLICAS', true);
    this.maxMemoryCacheSize = this.configService.get<number>('MAX_MEMORY_CACHE_SIZE', 10000);
    
    this.initialize();
  }
  
  /**
   * 初始化数据库管理器
   */
  private async initialize(): Promise<void> {
    try {
      // 初始化主数据库连接池
      await this.initializePrimaryPool();
      
      // 初始化读副本连接池
      if (this.enableReadReplicas) {
        await this.initializeReadReplicas();
      }
      
      // 初始化Redis缓存
      await this.initializeRedisCache();
      
      // 启动统计监控
      this.startStatsMonitoring();
      
      // 启动缓存清理
      this.startCacheCleanup();
      
      this.logger.log('增强数据库管理器初始化完成');
      
    } catch (error) {
      this.logger.error('数据库管理器初始化失败:', error);
      throw error;
    }
  }
  
  /**
   * 初始化主数据库连接池
   */
  private async initializePrimaryPool(): Promise<void> {
    const config: DatabasePoolConfig = {
      host: this.configService.get<string>('DB_HOST', 'localhost'),
      port: this.configService.get<number>('DB_PORT', 5432),
      database: this.configService.get<string>('DB_NAME', 'gameserver'),
      user: this.configService.get<string>('DB_USER', 'postgres'),
      password: this.configService.get<string>('DB_PASSWORD', ''),
      
      // 优化的连接池配置
      min: this.configService.get<number>('DB_POOL_MIN', 10),
      max: this.configService.get<number>('DB_POOL_MAX', 50), // 增加到50个连接
      idleTimeoutMillis: this.configService.get<number>('DB_IDLE_TIMEOUT', 30000),
      connectionTimeoutMillis: this.configService.get<number>('DB_CONNECTION_TIMEOUT', 10000),
      
      // 性能优化配置
      statement_timeout: this.configService.get<number>('DB_STATEMENT_TIMEOUT', 30000),
      query_timeout: this.configService.get<number>('DB_QUERY_TIMEOUT', 30000),
      keepAlive: true,
      keepAliveInitialDelayMillis: 10000,
      
      // SSL配置
      ssl: this.configService.get<boolean>('DB_SSL', false) ? {
        rejectUnauthorized: false,
      } : false,
    };
    
    this.primaryPool = new Pool(config);
    
    // 设置连接池事件监听器
    this.setupPoolListeners(this.primaryPool, 'primary');
    
    // 测试连接
    const client = await this.primaryPool.connect();
    await client.query('SELECT 1');
    client.release();
    
    this.logger.log('主数据库连接池初始化完成');
  }
  
  /**
   * 初始化读副本连接池
   */
  private async initializeReadReplicas(): Promise<void> {
    const replicaHosts = this.configService.get<string>('DB_READ_REPLICA_HOSTS', '');
    
    if (!replicaHosts) {
      this.logger.warn('未配置读副本主机，跳过读副本初始化');
      return;
    }
    
    const hosts = replicaHosts.split(',');
    
    for (let i = 0; i < hosts.length; i++) {
      const host = hosts[i].trim();
      
      const config: DatabasePoolConfig = {
        host,
        port: this.configService.get<number>('DB_PORT', 5432),
        database: this.configService.get<string>('DB_NAME', 'gameserver'),
        user: this.configService.get<string>('DB_USER', 'postgres'),
        password: this.configService.get<string>('DB_PASSWORD', ''),
        
        // 读副本连接池配置（可以更大）
        min: this.configService.get<number>('DB_REPLICA_POOL_MIN', 5),
        max: this.configService.get<number>('DB_REPLICA_POOL_MAX', 30),
        idleTimeoutMillis: this.configService.get<number>('DB_IDLE_TIMEOUT', 30000),
        connectionTimeoutMillis: this.configService.get<number>('DB_CONNECTION_TIMEOUT', 10000),
        
        statement_timeout: this.configService.get<number>('DB_STATEMENT_TIMEOUT', 30000),
        query_timeout: this.configService.get<number>('DB_QUERY_TIMEOUT', 30000),
        keepAlive: true,
        keepAliveInitialDelayMillis: 10000,
        
        ssl: this.configService.get<boolean>('DB_SSL', false) ? {
          rejectUnauthorized: false,
        } : false,
      };
      
      const pool = new Pool(config);
      this.setupPoolListeners(pool, `replica-${i}`);
      
      // 测试连接
      try {
        const client = await pool.connect();
        await client.query('SELECT 1');
        client.release();
        
        this.readReplicaPools.push(pool);
        this.logger.log(`读副本 ${i} (${host}) 连接池初始化完成`);
        
      } catch (error) {
        this.logger.error(`读副本 ${i} (${host}) 连接失败:`, error);
        await pool.end();
      }
    }
  }
  
  /**
   * 初始化Redis缓存
   */
  private async initializeRedisCache(): Promise<void> {
    const redisConfig: RedisCacheConfig = {
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: this.configService.get<number>('REDIS_PORT', 6379),
      password: this.configService.get<string>('REDIS_PASSWORD'),
      db: this.configService.get<number>('REDIS_DB', 0),
      keyPrefix: this.configService.get<string>('REDIS_KEY_PREFIX', 'gameserver:'),
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      keepAlive: 30000,
    };
    
    // 检查是否使用Redis集群
    const clusterNodes = this.configService.get<string>('REDIS_CLUSTER_NODES');
    
    if (clusterNodes) {
      // 使用Redis集群
      const nodes = clusterNodes.split(',').map(node => {
        const [host, port] = node.trim().split(':');
        return { host, port: parseInt(port) || 6379 };
      });
      
      this.redisCluster = new Redis.Cluster(nodes, {
        redisOptions: redisConfig,
        enableOfflineQueue: false,
      });
      
      this.redisClient = this.redisCluster as any;
      
    } else {
      // 使用单个Redis实例
      this.redisClient = new Redis(redisConfig);
    }
    
    // 设置Redis事件监听器
    this.redisClient.on('connect', () => {
      this.logger.log('Redis缓存连接成功');
    });
    
    this.redisClient.on('error', (error) => {
      this.logger.error('Redis缓存连接错误:', error);
    });
    
    // 测试Redis连接
    await this.redisClient.ping();
    this.logger.log('Redis缓存初始化完成');
  }
  
  /**
   * 设置连接池事件监听器
   */
  private setupPoolListeners(pool: Pool, name: string): void {
    pool.on('connect', (client) => {
      this.logger.debug(`${name} 数据库连接建立`);
    });
    
    pool.on('acquire', (client) => {
      this.logger.debug(`${name} 数据库连接获取`);
    });
    
    pool.on('error', (error, client) => {
      this.logger.error(`${name} 数据库连接错误:`, error);
    });
    
    pool.on('remove', (client) => {
      this.logger.debug(`${name} 数据库连接移除`);
    });
  }
  
  /**
   * 执行查询（带缓存）
   */
  public async query(
    sql: string,
    params: any[] = [],
    options: {
      useCache?: boolean;
      cacheTTL?: number;
      useReadReplica?: boolean;
      cacheKey?: string;
    } = {}
  ): Promise<any> {
    const startTime = Date.now();
    const cacheKey = options.cacheKey || this.generateCacheKey(sql, params);
    
    try {
      // 检查缓存
      if (options.useCache !== false && this.enableQueryCache) {
        const cachedResult = await this.getFromCache(cacheKey);
        if (cachedResult !== null) {
          this.queryStats.cacheHits++;
          this.updateQueryStats(startTime, true);
          return cachedResult;
        }
        this.queryStats.cacheMisses++;
      }
      
      // 选择连接池
      const pool = this.selectPool(options.useReadReplica);
      
      // 执行查询
      const result = await pool.query(sql, params);
      
      // 缓存结果
      if (options.useCache !== false && this.enableQueryCache) {
        const ttl = options.cacheTTL || this.cacheDefaultTTL;
        await this.setCache(cacheKey, result.rows, ttl);
      }
      
      this.updateQueryStats(startTime, true);
      return result.rows;
      
    } catch (error) {
      this.updateQueryStats(startTime, false);
      this.logger.error('查询执行失败:', error);
      throw error;
    }
  }
  
  /**
   * 执行事务
   */
  public async transaction<T>(
    callback: (client: PoolClient) => Promise<T>
  ): Promise<T> {
    const client = await this.primaryPool.connect();
    
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
      
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
      
    } finally {
      client.release();
    }
  }
  
  /**
   * 选择连接池
   */
  private selectPool(useReadReplica?: boolean): Pool {
    if (useReadReplica && this.readReplicaPools.length > 0) {
      // 负载均衡选择读副本
      const index = Math.floor(Math.random() * this.readReplicaPools.length);
      return this.readReplicaPools[index];
    }
    
    return this.primaryPool;
  }
  
  /**
   * 生成缓存键
   */
  private generateCacheKey(sql: string, params: any[]): string {
    const hash = require('crypto')
      .createHash('md5')
      .update(sql + JSON.stringify(params))
      .digest('hex');
    return `query:${hash}`;
  }
  
  /**
   * 从缓存获取数据
   */
  private async getFromCache(key: string): Promise<any> {
    try {
      // 先检查内存缓存
      const memoryItem = this.memoryCache.get(key);
      if (memoryItem && Date.now() - memoryItem.timestamp < memoryItem.ttl * 1000) {
        memoryItem.hits++;
        return memoryItem.data;
      }
      
      // 检查Redis缓存
      const redisData = await this.redisClient.get(key);
      if (redisData) {
        const data = JSON.parse(redisData);
        
        // 同时存储到内存缓存
        this.setMemoryCache(key, data, this.cacheDefaultTTL);
        
        return data;
      }
      
      return null;
      
    } catch (error) {
      this.logger.error('缓存读取失败:', error);
      return null;
    }
  }
  
  /**
   * 设置缓存
   */
  private async setCache(key: string, data: any, ttl: number): Promise<void> {
    try {
      // 设置Redis缓存
      await this.redisClient.setex(key, ttl, JSON.stringify(data));
      
      // 设置内存缓存
      this.setMemoryCache(key, data, ttl);
      
    } catch (error) {
      this.logger.error('缓存设置失败:', error);
    }
  }
  
  /**
   * 设置内存缓存
   */
  private setMemoryCache(key: string, data: any, ttl: number): void {
    // 检查缓存大小限制
    if (this.memoryCache.size >= this.maxMemoryCacheSize) {
      // 移除最旧的缓存项
      const oldestKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(oldestKey);
    }
    
    this.memoryCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
      hits: 0,
    });
  }
  
  /**
   * 更新查询统计
   */
  private updateQueryStats(startTime: number, success: boolean): void {
    const executionTime = Date.now() - startTime;
    
    this.queryStats.totalQueries++;
    
    if (success) {
      this.queryStats.successfulQueries++;
    } else {
      this.queryStats.failedQueries++;
    }
    
    if (executionTime > this.slowQueryThreshold) {
      this.queryStats.slowQueries++;
    }
    
    // 更新平均执行时间
    this.queryStats.averageExecutionTime = 
      (this.queryStats.averageExecutionTime * (this.queryStats.totalQueries - 1) + executionTime) / 
      this.queryStats.totalQueries;
    
    // 更新缓存命中率
    this.queryStats.cacheHitRate = 
      this.queryStats.cacheHits / (this.queryStats.cacheHits + this.queryStats.cacheMisses);
  }
  
  /**
   * 启动统计监控
   */
  private startStatsMonitoring(): void {
    this.statsTimer = setInterval(() => {
      this.logger.debug('数据库统计:', this.queryStats);
    }, 60000); // 每分钟输出一次统计
  }
  
  /**
   * 启动缓存清理
   */
  private startCacheCleanup(): void {
    this.cacheCleanupTimer = setInterval(() => {
      this.cleanupMemoryCache();
    }, 300000); // 每5分钟清理一次
  }
  
  /**
   * 清理内存缓存
   */
  private cleanupMemoryCache(): void {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, item] of this.memoryCache) {
      if (now - item.timestamp > item.ttl * 1000) {
        this.memoryCache.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      this.logger.debug(`清理了 ${cleanedCount} 个过期的内存缓存项`);
    }
  }
  
  /**
   * 获取统计信息
   */
  public getStats(): QueryStats {
    return { ...this.queryStats };
  }
  
  /**
   * 获取连接池状态
   */
  public getPoolStatus(): {
    primary: { total: number; idle: number; waiting: number };
    replicas: Array<{ total: number; idle: number; waiting: number }>;
  } {
    const primary = {
      total: this.primaryPool.totalCount,
      idle: this.primaryPool.idleCount,
      waiting: this.primaryPool.waitingCount,
    };
    
    const replicas = this.readReplicaPools.map(pool => ({
      total: pool.totalCount,
      idle: pool.idleCount,
      waiting: pool.waitingCount,
    }));
    
    return { primary, replicas };
  }
  
  /**
   * 模块销毁时清理资源
   */
  async onModuleDestroy(): Promise<void> {
    // 清除定时器
    if (this.statsTimer) {
      clearInterval(this.statsTimer);
    }
    if (this.cacheCleanupTimer) {
      clearInterval(this.cacheCleanupTimer);
    }
    
    // 关闭连接池
    await this.primaryPool.end();
    
    for (const pool of this.readReplicaPools) {
      await pool.end();
    }
    
    // 关闭Redis连接
    if (this.redisCluster) {
      await this.redisCluster.disconnect();
    } else {
      await this.redisClient.disconnect();
    }
    
    this.logger.log('数据库管理器已清理');
  }
}
