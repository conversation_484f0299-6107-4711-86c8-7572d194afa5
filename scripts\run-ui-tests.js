#!/usr/bin/env node

/**
 * UI组件测试运行脚本
 * 统一运行底层引擎、编辑器和服务器端的UI组件测试
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${title}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSubSection(title) {
  log(`\n${'-'.repeat(40)}`, 'blue');
  log(`  ${title}`, 'yellow');
  log('-'.repeat(40), 'blue');
}

// 测试配置
const testConfigs = [
  {
    name: '底层引擎UI组件测试',
    cwd: path.join(__dirname, '../engine'),
    command: 'npm',
    args: ['test', '--', '--testPathPattern=tests/ui'],
    env: { NODE_ENV: 'test' }
  },
  {
    name: '编辑器UI组件测试',
    cwd: path.join(__dirname, '../editor'),
    command: 'npm',
    args: ['test', '--', '--testPathPattern=tests/ui', '--watchAll=false'],
    env: { NODE_ENV: 'test', CI: 'true' }
  },
  {
    name: '服务器端UI服务测试',
    cwd: path.join(__dirname, '../server/ui-service'),
    command: 'npm',
    args: ['test', '--', '--testPathPattern=test'],
    env: { NODE_ENV: 'test' }
  }
];

// 运行单个测试套件
function runTest(config) {
  return new Promise((resolve, reject) => {
    logSubSection(`运行 ${config.name}`);
    
    // 检查目录是否存在
    if (!fs.existsSync(config.cwd)) {
      log(`❌ 目录不存在: ${config.cwd}`, 'red');
      resolve({ success: false, config, error: '目录不存在' });
      return;
    }

    // 检查package.json是否存在
    const packageJsonPath = path.join(config.cwd, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      log(`❌ package.json不存在: ${packageJsonPath}`, 'red');
      resolve({ success: false, config, error: 'package.json不存在' });
      return;
    }

    const child = spawn(config.command, config.args, {
      cwd: config.cwd,
      env: { ...process.env, ...config.env },
      stdio: 'pipe'
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      // 实时输出测试进度
      if (output.includes('PASS') || output.includes('FAIL') || output.includes('Test Suites')) {
        process.stdout.write(output);
      }
    });

    child.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      // 输出错误信息
      if (output.includes('Error') || output.includes('Failed')) {
        process.stderr.write(output);
      }
    });

    child.on('close', (code) => {
      if (code === 0) {
        log(`✅ ${config.name} 测试通过`, 'green');
        resolve({ success: true, config, stdout, stderr });
      } else {
        log(`❌ ${config.name} 测试失败 (退出码: ${code})`, 'red');
        resolve({ success: false, config, stdout, stderr, exitCode: code });
      }
    });

    child.on('error', (error) => {
      log(`❌ ${config.name} 运行错误: ${error.message}`, 'red');
      resolve({ success: false, config, error: error.message });
    });
  });
}

// 生成测试报告
function generateReport(results) {
  logSection('测试报告');
  
  const totalTests = results.length;
  const passedTests = results.filter(r => r.success).length;
  const failedTests = totalTests - passedTests;
  
  log(`总测试套件: ${totalTests}`, 'bright');
  log(`通过: ${passedTests}`, 'green');
  log(`失败: ${failedTests}`, failedTests > 0 ? 'red' : 'green');
  log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`, 
      passedTests === totalTests ? 'green' : 'yellow');

  // 详细结果
  log('\n详细结果:', 'bright');
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    const color = result.success ? 'green' : 'red';
    log(`${index + 1}. ${status} ${result.config.name}`, color);
    
    if (!result.success && result.error) {
      log(`   错误: ${result.error}`, 'red');
    }
    
    if (!result.success && result.exitCode) {
      log(`   退出码: ${result.exitCode}`, 'red');
    }
  });

  // 失败的测试详情
  const failedResults = results.filter(r => !r.success);
  if (failedResults.length > 0) {
    log('\n失败测试详情:', 'red');
    failedResults.forEach((result, index) => {
      log(`\n${index + 1}. ${result.config.name}:`, 'red');
      if (result.stderr) {
        log('错误输出:', 'yellow');
        log(result.stderr.slice(-500), 'red'); // 显示最后500字符
      }
    });
  }

  return passedTests === totalTests;
}

// 检查依赖
function checkDependencies() {
  logSection('检查测试环境');
  
  const checks = [
    { name: 'Node.js版本', check: () => process.version },
    { name: 'npm版本', check: () => {
      try {
        const { execSync } = require('child_process');
        return execSync('npm --version', { encoding: 'utf8' }).trim();
      } catch (e) {
        return '未安装';
      }
    }}
  ];

  checks.forEach(({ name, check }) => {
    try {
      const result = check();
      log(`✅ ${name}: ${result}`, 'green');
    } catch (error) {
      log(`❌ ${name}: 检查失败`, 'red');
    }
  });
}

// 主函数
async function main() {
  const startTime = Date.now();
  
  log('🚀 开始运行UI组件测试套件', 'bright');
  
  // 检查环境
  checkDependencies();
  
  // 运行所有测试
  logSection('运行测试');
  
  const results = [];
  for (const config of testConfigs) {
    const result = await runTest(config);
    results.push(result);
  }
  
  // 生成报告
  const allPassed = generateReport(results);
  
  // 总结
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(1);
  
  logSection('测试完成');
  log(`总耗时: ${duration}秒`, 'bright');
  
  if (allPassed) {
    log('🎉 所有测试都通过了！', 'green');
    process.exit(0);
  } else {
    log('💥 有测试失败，请检查上面的错误信息', 'red');
    process.exit(1);
  }
}

// 处理命令行参数
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  log('UI组件测试运行器', 'bright');
  log('\n用法:');
  log('  node run-ui-tests.js [选项]');
  log('\n选项:');
  log('  --help, -h     显示帮助信息');
  log('  --verbose, -v  显示详细输出');
  log('\n示例:');
  log('  node run-ui-tests.js');
  log('  node run-ui-tests.js --verbose');
  process.exit(0);
}

// 设置详细模式
if (args.includes('--verbose') || args.includes('-v')) {
  process.env.VERBOSE = 'true';
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  log('未处理的Promise拒绝:', 'red');
  log(reason, 'red');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  log('未捕获的异常:', 'red');
  log(error.message, 'red');
  process.exit(1);
});

// 运行主函数
main().catch((error) => {
  log('运行测试时发生错误:', 'red');
  log(error.message, 'red');
  process.exit(1);
});
