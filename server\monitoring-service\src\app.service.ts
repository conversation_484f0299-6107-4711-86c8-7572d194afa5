import { Injectable } from '@nestjs/common';

/**
 * 应用主服务
 * 提供基本的应用信息和健康状态
 */
@Injectable()
export class AppService {
  /**
   * 获取欢迎信息
   */
  getHello(): string {
    return '监控服务运行正常！';
  }

  /**
   * 获取健康状态
   */
  getHealth(): object {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'monitoring-service',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version,
    };
  }

  /**
   * 获取版本信息
   */
  getVersion(): object {
    return {
      service: 'monitoring-service',
      version: '1.0.0',
      buildTime: new Date().toISOString(),
      nodeVersion: process.version,
      environment: process.env.NODE_ENV || 'development',
    };
  }
}
