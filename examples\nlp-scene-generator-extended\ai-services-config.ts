/**
 * AI服务配置示例
 * 展示如何配置不同类型的AI服务用于NLP场景生成
 */

import { ExternalAIServiceConfig, AICapability } from '../../engine/src/ai/NLPSceneGenerator';

/**
 * OpenAI GPT-4 服务配置
 */
export const openAIConfig: ExternalAIServiceConfig = {
  name: 'openai_gpt4',
  endpoint: 'https://api.openai.com/v1',
  apiKey: process.env.OPENAI_API_KEY || 'your-openai-api-key',
  model: 'gpt-4',
  capabilities: [
    AICapability.TEXT_UNDERSTANDING,
    AICapability.SCENE_PLANNING,
    AICapability.OPTIMIZATION
  ],
  rateLimits: {
    requestsPerMinute: 60,
    requestsPerHour: 1000,
    maxConcurrent: 5
  }
};

/**
 * 百度文心一言服务配置
 */
export const baiduErnieConfig: ExternalAIServiceConfig = {
  name: 'baidu_ernie',
  endpoint: 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop',
  apiKey: process.env.BAIDU_API_KEY || 'your-baidu-api-key',
  model: 'ernie-bot-4',
  capabilities: [
    AICapability.TEXT_UNDERSTANDING,
    AICapability.SCENE_PLANNING
  ],
  rateLimits: {
    requestsPerMinute: 100,
    requestsPerHour: 2000,
    maxConcurrent: 8
  },
  fallbackService: 'openai_gpt4'
};

/**
 * 阿里云通义千问服务配置
 */
export const alibabaQwenConfig: ExternalAIServiceConfig = {
  name: 'alibaba_qwen',
  endpoint: 'https://dashscope.aliyuncs.com/api/v1',
  apiKey: process.env.ALIBABA_API_KEY || 'your-alibaba-api-key',
  model: 'qwen-max',
  capabilities: [
    AICapability.TEXT_UNDERSTANDING,
    AICapability.SCENE_PLANNING,
    AICapability.STYLE_TRANSFER
  ],
  rateLimits: {
    requestsPerMinute: 120,
    requestsPerHour: 3000,
    maxConcurrent: 10
  },
  fallbackService: 'baidu_ernie'
};

/**
 * 本地BERT模型服务配置
 */
export const localBertConfig: ExternalAIServiceConfig = {
  name: 'local_bert',
  endpoint: 'http://localhost:8080/api',
  model: 'bert-base-chinese',
  capabilities: [
    AICapability.TEXT_UNDERSTANDING
  ],
  rateLimits: {
    requestsPerMinute: 200,
    requestsPerHour: 10000,
    maxConcurrent: 15
  },
  fallbackService: 'alibaba_qwen'
};

/**
 * 本地Stable Diffusion服务配置（用于对象生成）
 */
export const localStableDiffusionConfig: ExternalAIServiceConfig = {
  name: 'local_stable_diffusion',
  endpoint: 'http://localhost:7860/api/v1',
  model: 'stable-diffusion-xl',
  capabilities: [
    AICapability.OBJECT_GENERATION,
    AICapability.STYLE_TRANSFER
  ],
  rateLimits: {
    requestsPerMinute: 10,
    requestsPerHour: 200,
    maxConcurrent: 2
  }
};

/**
 * 自定义企业AI服务配置
 */
export const enterpriseAIConfig: ExternalAIServiceConfig = {
  name: 'enterprise_ai',
  endpoint: 'https://ai.yourcompany.com/api/v2',
  apiKey: process.env.ENTERPRISE_API_KEY || 'your-enterprise-api-key',
  model: 'company-scene-generator-v2',
  capabilities: [
    AICapability.TEXT_UNDERSTANDING,
    AICapability.SCENE_PLANNING,
    AICapability.OBJECT_GENERATION,
    AICapability.STYLE_TRANSFER,
    AICapability.OPTIMIZATION
  ],
  rateLimits: {
    requestsPerMinute: 500,
    requestsPerHour: 20000,
    maxConcurrent: 20
  },
  fallbackService: 'openai_gpt4'
};

/**
 * AI服务配置管理器
 */
export class AIServiceConfigManager {
  private configs: Map<string, ExternalAIServiceConfig> = new Map();

  constructor() {
    this.loadDefaultConfigs();
  }

  /**
   * 加载默认配置
   */
  private loadDefaultConfigs(): void {
    this.configs.set('openai_gpt4', openAIConfig);
    this.configs.set('baidu_ernie', baiduErnieConfig);
    this.configs.set('alibaba_qwen', alibabaQwenConfig);
    this.configs.set('local_bert', localBertConfig);
    this.configs.set('local_stable_diffusion', localStableDiffusionConfig);
    this.configs.set('enterprise_ai', enterpriseAIConfig);
  }

  /**
   * 获取配置
   */
  public getConfig(name: string): ExternalAIServiceConfig | undefined {
    return this.configs.get(name);
  }

  /**
   * 获取所有配置
   */
  public getAllConfigs(): ExternalAIServiceConfig[] {
    return Array.from(this.configs.values());
  }

  /**
   * 根据能力筛选服务
   */
  public getServicesByCapability(capability: AICapability): ExternalAIServiceConfig[] {
    return this.getAllConfigs().filter(config => 
      config.capabilities.includes(capability)
    );
  }

  /**
   * 获取推荐的服务配置组合
   */
  public getRecommendedServices(): {
    primary: ExternalAIServiceConfig;
    fallback: ExternalAIServiceConfig;
    local: ExternalAIServiceConfig;
  } {
    return {
      primary: enterpriseAIConfig,
      fallback: openAIConfig,
      local: localBertConfig
    };
  }

  /**
   * 验证服务配置
   */
  public validateConfig(config: ExternalAIServiceConfig): boolean {
    // 检查必需字段
    if (!config.name || !config.endpoint || !config.model) {
      return false;
    }

    // 检查能力列表
    if (!config.capabilities || config.capabilities.length === 0) {
      return false;
    }

    // 检查速率限制
    if (!config.rateLimits || 
        config.rateLimits.requestsPerMinute <= 0 ||
        config.rateLimits.requestsPerHour <= 0) {
      return false;
    }

    return true;
  }

  /**
   * 添加自定义配置
   */
  public addConfig(config: ExternalAIServiceConfig): boolean {
    if (!this.validateConfig(config)) {
      console.error('无效的AI服务配置:', config);
      return false;
    }

    this.configs.set(config.name, config);
    console.log(`已添加AI服务配置: ${config.name}`);
    return true;
  }

  /**
   * 移除配置
   */
  public removeConfig(name: string): boolean {
    const removed = this.configs.delete(name);
    if (removed) {
      console.log(`已移除AI服务配置: ${name}`);
    }
    return removed;
  }

  /**
   * 更新配置
   */
  public updateConfig(name: string, updates: Partial<ExternalAIServiceConfig>): boolean {
    const existing = this.configs.get(name);
    if (!existing) {
      console.error(`未找到AI服务配置: ${name}`);
      return false;
    }

    const updated = { ...existing, ...updates };
    if (!this.validateConfig(updated)) {
      console.error('更新后的配置无效');
      return false;
    }

    this.configs.set(name, updated);
    console.log(`已更新AI服务配置: ${name}`);
    return true;
  }

  /**
   * 导出配置到JSON
   */
  public exportToJSON(): string {
    const configArray = this.getAllConfigs().map(config => ({
      ...config,
      apiKey: config.apiKey ? '***' : undefined // 隐藏API密钥
    }));
    return JSON.stringify(configArray, null, 2);
  }

  /**
   * 从JSON导入配置
   */
  public importFromJSON(jsonString: string): boolean {
    try {
      const configArray = JSON.parse(jsonString) as ExternalAIServiceConfig[];
      let importedCount = 0;

      for (const config of configArray) {
        if (this.addConfig(config)) {
          importedCount++;
        }
      }

      console.log(`成功导入 ${importedCount} 个AI服务配置`);
      return importedCount > 0;
    } catch (error) {
      console.error('导入AI服务配置失败:', error);
      return false;
    }
  }

  /**
   * 测试服务连接
   */
  public async testConnection(name: string): Promise<boolean> {
    const config = this.configs.get(name);
    if (!config) {
      console.error(`未找到AI服务配置: ${name}`);
      return false;
    }

    try {
      const response = await fetch(`${config.endpoint}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
        },
        timeout: 5000
      });

      const isHealthy = response.ok;
      console.log(`AI服务 ${name} 连接测试: ${isHealthy ? '成功' : '失败'}`);
      return isHealthy;
    } catch (error) {
      console.error(`AI服务 ${name} 连接测试失败:`, error);
      return false;
    }
  }

  /**
   * 批量测试所有服务连接
   */
  public async testAllConnections(): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>();
    const configs = this.getAllConfigs();

    console.log('开始测试所有AI服务连接...');

    for (const config of configs) {
      const isHealthy = await this.testConnection(config.name);
      results.set(config.name, isHealthy);
    }

    const healthyCount = Array.from(results.values()).filter(Boolean).length;
    console.log(`连接测试完成: ${healthyCount}/${configs.length} 个服务可用`);

    return results;
  }
}

/**
 * 使用示例
 */
export function exampleUsage() {
  const manager = new AIServiceConfigManager();

  // 获取推荐的服务配置
  const recommended = manager.getRecommendedServices();
  console.log('推荐的AI服务配置:', recommended);

  // 根据能力筛选服务
  const textUnderstandingServices = manager.getServicesByCapability(AICapability.TEXT_UNDERSTANDING);
  console.log('支持文本理解的服务:', textUnderstandingServices.map(s => s.name));

  // 添加自定义配置
  const customConfig: ExternalAIServiceConfig = {
    name: 'custom_ai',
    endpoint: 'https://custom-ai.example.com/api',
    model: 'custom-model-v1',
    capabilities: [AICapability.TEXT_UNDERSTANDING],
    rateLimits: {
      requestsPerMinute: 50,
      requestsPerHour: 1000,
      maxConcurrent: 3
    }
  };

  manager.addConfig(customConfig);

  // 导出配置
  const configJSON = manager.exportToJSON();
  console.log('导出的配置:', configJSON);

  // 测试连接（异步）
  manager.testAllConnections().then(results => {
    console.log('连接测试结果:', results);
  });
}

export default AIServiceConfigManager;
