/**
 * 自然语言场景生成器
 * 基于自然语言描述生成3D场景
 * 支持自定义风格、自定义对象类型、集成外部AI服务等扩展功能
 */
import * as THREE from 'three';
import { System } from '../core/System';
import { Scene } from '../scene/Scene';
import { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';
import { Transform } from '../scene/Transform';
import { Light, LightType } from '../rendering/Light';
import { MeshComponent } from '../rendering/MeshComponent';
import { MaterialFactory, MaterialType } from '../rendering/materials/MaterialFactory';

/**
 * 自定义风格配置接口
 */
export interface CustomStyleConfig {
  name: string;
  description: string;
  materialPresets: MaterialPreset[];
  lightingPresets: LightingPreset[];
  objectModifiers: ObjectModifier[];
  atmosphereSettings: AtmosphereSettings;
  colorPalette: string[];
}

/**
 * 材质预设接口
 */
export interface MaterialPreset {
  name: string;
  type: MaterialType;
  properties: { [key: string]: any };
  applicableObjects: string[];
}

/**
 * 光照预设接口
 */
export interface LightingPreset {
  name: string;
  ambientIntensity: number;
  directionalIntensity: number;
  colorTemperature: number;
  shadowSettings: ShadowSettings;
}

/**
 * 阴影设置接口
 */
export interface ShadowSettings {
  enabled: boolean;
  quality: 'low' | 'medium' | 'high';
  softness: number;
  bias: number;
}

/**
 * 对象修饰器接口
 */
export interface ObjectModifier {
  objectType: string;
  scaleMultiplier: number;
  positionOffset: THREE.Vector3;
  rotationOffset: THREE.Euler;
  additionalComponents?: string[];
}

/**
 * 氛围设置接口
 */
export interface AtmosphereSettings {
  fogDensity: number;
  fogColor: string;
  skyboxType: string;
  environmentMap?: string;
  postProcessingEffects: string[];
}

/**
 * 自定义对象类型接口
 */
export interface CustomObjectType {
  name: string;
  category: string;
  description: string;
  geometryFactory: (params: any) => THREE.BufferGeometry;
  defaultMaterial: string;
  boundingBox: THREE.Box3;
  tags: string[];
  complexity: number; // 1-10
  additionalComponents?: string[]; // 可选的额外组件类型
}

/**
 * 外部AI服务配置接口
 */
export interface ExternalAIServiceConfig {
  name: string;
  endpoint: string;
  apiKey?: string;
  model: string;
  capabilities: AICapability[];
  rateLimits: RateLimitConfig;
  fallbackService?: string;
}

/**
 * AI能力枚举
 */
export enum AICapability {
  TEXT_UNDERSTANDING = 'text_understanding',
  SCENE_PLANNING = 'scene_planning',
  OBJECT_GENERATION = 'object_generation',
  STYLE_TRANSFER = 'style_transfer',
  OPTIMIZATION = 'optimization'
}

/**
 * 速率限制配置接口
 */
export interface RateLimitConfig {
  requestsPerMinute: number;
  requestsPerHour: number;
  maxConcurrent: number;
}

/**
 * 扩展的生成选项接口
 */
export interface GenerationOptions {
  style: 'realistic' | 'cartoon' | 'minimalist' | 'scifi' | 'fantasy' | string; // 支持自定义风格
  quality: number; // 1-100
  maxObjects: number;
  constraints: {
    maxPolygons: number;
    targetFrameRate: number;
  };
  onProgress?: (progress: number) => void;
  customStyle?: CustomStyleConfig;
  customObjects?: CustomObjectType[];
  aiServices?: string[]; // 要使用的AI服务名称列表
  enableAdvancedFeatures?: boolean;
  seedValue?: number; // 随机种子，用于可重现的生成
  templateScene?: string; // 模板场景ID
}

/**
 * 扩展的语言理解结果接口
 */
export interface LanguageUnderstanding {
  entities: Array<{
    text: string;
    type: 'OBJECT' | 'LOCATION' | 'ATTRIBUTE' | 'ACTION' | 'CUSTOM';
    confidence: number;
    metadata?: { [key: string]: any };
  }>;
  intent: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  keywords: string[];
  style: string;
  complexity: number; // 场景复杂度评分 1-10
  spatialRelations: SpatialRelation[];
  temporalContext?: TemporalContext;
  culturalContext?: CulturalContext;
  emotionalTone: EmotionalTone;
}

/**
 * 空间关系接口
 */
export interface SpatialRelation {
  subject: string;
  predicate: string;
  object: string;
  confidence: number;
}

/**
 * 时间上下文接口
 */
export interface TemporalContext {
  timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
  season: 'spring' | 'summer' | 'autumn' | 'winter';
  era: string;
  duration?: number;
}

/**
 * 文化上下文接口
 */
export interface CulturalContext {
  region: string;
  style: string;
  traditions: string[];
  architecture: string;
}

/**
 * 情感色调接口
 */
export interface EmotionalTone {
  primary: string;
  secondary: string[];
  intensity: number; // 0-1
  valence: number; // -1 to 1 (negative to positive)
  arousal: number; // 0-1 (calm to excited)
}

/**
 * 性能指标接口
 */
interface PerformanceMetrics {
  totalGenerations: number;
  averageGenerationTime: number;
  cacheHitRate: number;
  errorRate: number;
}

export class NLPSceneGenerator extends System {
  static readonly NAME = 'NLPSceneGenerator';

  private eventEmitter: EventEmitter;
  private cache: Map<string, Scene>;
  private _isInitialized: boolean = false;

  // 扩展的私有属性
  private customStyles: Map<string, CustomStyleConfig> = new Map();
  private customObjects: Map<string, CustomObjectType> = new Map();
  private aiServices: Map<string, ExternalAIServiceConfig> = new Map();
  private materialFactory: MaterialFactory;
  private requestQueue: Map<string, Promise<any>> = new Map();
  private performanceMetrics: PerformanceMetrics = {
    totalGenerations: 0,
    averageGenerationTime: 0,
    cacheHitRate: 0,
    errorRate: 0
  };

  // 速率限制相关
  private rateLimitCounters: Map<string, {
    requestsThisMinute: number;
    requestsThisHour: number;
    currentConcurrent: number;
    lastMinuteReset: number;
    lastHourReset: number;
  }> = new Map();

  constructor() {
    super(350); // 系统优先级
    this.eventEmitter = new EventEmitter();
    this.cache = new Map();
    this.materialFactory = new MaterialFactory();

    // 初始化默认风格
    this.initializeDefaultStyles();

    // 初始化默认对象类型
    this.initializeDefaultObjects();
  }

  public initialize(): void {
    if (this._isInitialized) return;

    console.log('初始化自然语言场景生成器...');

    // 初始化AI服务
    this.initializeAIServices();

    this._isInitialized = true;
  }

  /**
   * 初始化默认风格
   */
  private initializeDefaultStyles(): void {
    // 现代简约风格
    this.customStyles.set('modern', {
      name: 'modern',
      description: '现代简约风格',
      materialPresets: [
        {
          name: 'modern_metal',
          type: MaterialType.PHYSICAL,
          properties: { color: 0xC0C0C0, roughness: 0.1, metalness: 1.0 },
          applicableObjects: ['桌子', '椅子', '灯']
        },
        {
          name: 'modern_glass',
          type: MaterialType.PHYSICAL,
          properties: { color: 0xFFFFFF, roughness: 0.0, metalness: 0.0, transmission: 0.9 },
          applicableObjects: ['窗户', '桌面']
        }
      ],
      lightingPresets: [{
        name: 'modern_lighting',
        ambientIntensity: 0.4,
        directionalIntensity: 0.8,
        colorTemperature: 6500,
        shadowSettings: { enabled: true, quality: 'high', softness: 0.5, bias: -0.0001 }
      }],
      objectModifiers: [],
      atmosphereSettings: {
        fogDensity: 0.0,
        fogColor: '#ffffff',
        skyboxType: 'gradient',
        postProcessingEffects: ['bloom', 'ssao']
      },
      colorPalette: ['#ffffff', '#f0f0f0', '#c0c0c0', '#808080', '#404040']
    });

    // 工业风格
    this.customStyles.set('industrial', {
      name: 'industrial',
      description: '工业风格',
      materialPresets: [
        {
          name: 'industrial_metal',
          type: MaterialType.STANDARD,
          properties: { color: 0x444444, roughness: 0.8, metalness: 0.9 },
          applicableObjects: ['管道', '机器', '结构']
        }
      ],
      lightingPresets: [{
        name: 'industrial_lighting',
        ambientIntensity: 0.2,
        directionalIntensity: 1.2,
        colorTemperature: 4000,
        shadowSettings: { enabled: true, quality: 'medium', softness: 0.3, bias: -0.0005 }
      }],
      objectModifiers: [],
      atmosphereSettings: {
        fogDensity: 0.1,
        fogColor: '#666666',
        skyboxType: 'urban',
        postProcessingEffects: ['vignette', 'grain']
      },
      colorPalette: ['#444444', '#666666', '#888888', '#aa6c39', '#cc9966']
    });
  }

  /**
   * 初始化默认对象类型
   */
  private initializeDefaultObjects(): void {
    // 添加一些自定义对象类型
    this.customObjects.set('modern_chair', {
      name: 'modern_chair',
      category: 'furniture',
      description: '现代风格椅子',
      geometryFactory: (params) => {
        // 创建组合几何体
        const geometries: THREE.BufferGeometry[] = [];

        // 椅子座位
        const seatGeometry = new THREE.BoxGeometry(0.5, 0.05, 0.5);
        seatGeometry.translate(0, 0.4, 0);
        geometries.push(seatGeometry);

        // 椅子靠背
        const backGeometry = new THREE.BoxGeometry(0.5, 0.8, 0.05);
        backGeometry.translate(0, 0.8, -0.225);
        geometries.push(backGeometry);

        // 椅子腿（4条）
        const legPositions = [
          [-0.2, 0.2, -0.2], [0.2, 0.2, -0.2],
          [-0.2, 0.2, 0.2], [0.2, 0.2, 0.2]
        ];

        legPositions.forEach(pos => {
          const legGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.4);
          legGeometry.translate(pos[0], pos[1], pos[2]);
          geometries.push(legGeometry);
        });

        // 合并所有几何体
        const mergedGeometry = this.mergeGeometries(geometries);
        return mergedGeometry || seatGeometry;
      },
      defaultMaterial: 'modern_metal',
      boundingBox: new THREE.Box3(new THREE.Vector3(-0.3, 0, -0.3), new THREE.Vector3(0.3, 0.8, 0.3)),
      tags: ['furniture', 'seating', 'modern'],
      complexity: 3
    });

    this.customObjects.set('industrial_pipe', {
      name: 'industrial_pipe',
      category: 'structure',
      description: '工业管道',
      geometryFactory: (params) => {
        return new THREE.CylinderGeometry(0.1, 0.1, params.length || 2);
      },
      defaultMaterial: 'industrial_metal',
      boundingBox: new THREE.Box3(new THREE.Vector3(-0.1, 0, -0.1), new THREE.Vector3(0.1, 2, 0.1)),
      tags: ['industrial', 'pipe', 'structure'],
      complexity: 2
    });
  }

  /**
   * 初始化AI服务
   */
  private initializeAIServices(): void {
    // 配置默认的AI服务（示例）
    this.aiServices.set('openai', {
      name: 'openai',
      endpoint: 'https://api.openai.com/v1',
      model: 'gpt-4',
      capabilities: [AICapability.TEXT_UNDERSTANDING, AICapability.SCENE_PLANNING],
      rateLimits: {
        requestsPerMinute: 60,
        requestsPerHour: 1000,
        maxConcurrent: 5
      }
    });

    this.aiServices.set('local_nlp', {
      name: 'local_nlp',
      endpoint: 'http://localhost:8080/api',
      model: 'bert-base-chinese',
      capabilities: [AICapability.TEXT_UNDERSTANDING],
      rateLimits: {
        requestsPerMinute: 120,
        requestsPerHour: 5000,
        maxConcurrent: 10
      },
      fallbackService: 'openai'
    });
  }

  /**
   * 注册自定义风格
   */
  public registerCustomStyle(style: CustomStyleConfig): void {
    this.customStyles.set(style.name, style);
    console.log(`已注册自定义风格: ${style.name}`);
  }

  /**
   * 注册自定义对象类型
   */
  public registerCustomObject(objectType: CustomObjectType): void {
    this.customObjects.set(objectType.name, objectType);
    console.log(`已注册自定义对象类型: ${objectType.name}`);
  }

  /**
   * 注册AI服务
   */
  public registerAIService(service: ExternalAIServiceConfig): void {
    this.aiServices.set(service.name, service);
    console.log(`已注册AI服务: ${service.name}`);
  }

  /**
   * 获取已注册的自定义风格列表
   */
  public getCustomStyles(): string[] {
    return Array.from(this.customStyles.keys());
  }

  /**
   * 获取已注册的自定义对象类型列表
   */
  public getCustomObjects(): string[] {
    return Array.from(this.customObjects.keys());
  }

  /**
   * 获取已注册的AI服务列表
   */
  public getAIServices(): string[] {
    return Array.from(this.aiServices.keys());
  }

  /**
   * 获取性能指标
   */
  public getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.cache.clear();
    console.log('场景生成缓存已清除');
  }

  /**
   * 设置缓存大小限制
   */
  public setCacheLimit(limit: number): void {
    if (this.cache.size > limit) {
      const entries = Array.from(this.cache.entries());
      const toDelete = entries.slice(0, this.cache.size - limit);
      toDelete.forEach(([key]) => this.cache.delete(key));
    }
  }

  /**
   * 从自然语言生成场景（扩展版本）
   */
  public async generateSceneFromNaturalLanguage(
    userInput: string,
    options: GenerationOptions = {
      style: 'realistic',
      quality: 80,
      maxObjects: 50,
      constraints: {
        maxPolygons: 100000,
        targetFrameRate: 60
      }
    }
  ): Promise<Scene> {
    if (!this._isInitialized) {
      throw new Error('NLPSceneGenerator 未初始化');
    }

    const startTime = Date.now();

    try {
      // 预处理选项
      const processedOptions = await this.preprocessOptions(options);

      // 生成缓存键（包含自定义选项）
      const cacheKey = this.generateEnhancedCacheKey(userInput, processedOptions);

      // 检查缓存
      if (this.cache.has(cacheKey)) {
        console.log('从缓存返回场景');
        this.performanceMetrics.cacheHitRate++;
        return this.cache.get(cacheKey)!;
      }

      // 第一步：增强的自然语言理解 (15%)
      options.onProgress?.(15);
      const understanding = await this.understandText(userInput);

      // 第二步：应用自定义风格和对象 (25%)
      options.onProgress?.(25);
      await this.applyCustomizations(understanding, processedOptions);

      // 第三步：智能场景规划 (45%)
      options.onProgress?.(45);
      const scenePlan = await this.planSceneWithAI(understanding, processedOptions);

      // 第四步：生成3D内容 (75%)
      options.onProgress?.(75);
      const scene = await this.generateEnhancedScene(scenePlan, processedOptions);

      // 第五步：后处理和优化 (90%)
      options.onProgress?.(90);
      await this.optimizeSceneWithAI(scene, understanding, processedOptions);

      // 第六步：应用后处理效果 (100%)
      options.onProgress?.(100);
      await this.applyPostProcessing(scene, processedOptions);

      // 缓存结果
      this.cache.set(cacheKey, scene);
      this.setCacheLimit(100); // 限制缓存大小

      // 更新性能指标
      const totalTime = Date.now() - startTime;
      this.updatePerformanceMetrics('generation', totalTime);

      // 触发事件
      this.eventEmitter.emit('sceneGenerated', {
        userInput,
        scene,
        understanding,
        options: processedOptions,
        metrics: {
          processingTime: totalTime,
          cacheHit: false
        }
      });

      return scene;

    } catch (error) {
      console.error('自然语言场景生成失败:', error);
      this.performanceMetrics.errorRate++;
      this.eventEmitter.emit('generationError', {
        userInput,
        error,
        options
      });
      throw error;
    }
  }

  /**
   * 预处理生成选项
   */
  private async preprocessOptions(options: GenerationOptions): Promise<GenerationOptions> {
    const processed = { ...options };

    // 处理自定义风格
    if (processed.customStyle) {
      this.registerCustomStyle(processed.customStyle);
      processed.style = processed.customStyle.name;
    }

    // 处理自定义对象
    if (processed.customObjects) {
      processed.customObjects.forEach(obj => {
        this.registerCustomObject(obj);
      });
    }

    // 设置随机种子
    if (processed.seedValue) {
      Math.random = this.createSeededRandom(processed.seedValue);
    }

    return processed;
  }

  /**
   * 创建带种子的随机数生成器
   */
  private createSeededRandom(seed: number): () => number {
    let currentSeed = seed;
    return function() {
      currentSeed = (currentSeed * 9301 + 49297) % 233280;
      return currentSeed / 233280;
    };
  }

  /**
   * 生成增强的缓存键
   */
  private generateEnhancedCacheKey(userInput: string, options: GenerationOptions): string {
    const baseKey = `${userInput}_${options.style}_${options.quality}_${options.maxObjects}`;
    const customKey = options.customStyle ? `_custom_${options.customStyle.name}` : '';
    const objectsKey = options.customObjects ? `_objects_${options.customObjects.length}` : '';
    const seedKey = options.seedValue ? `_seed_${options.seedValue}` : '';

    return baseKey + customKey + objectsKey + seedKey;
  }

  /**
   * 应用自定义设置
   */
  private async applyCustomizations(
    understanding: LanguageUnderstanding,
    options: GenerationOptions
  ): Promise<void> {
    // 应用自定义风格的影响
    if (options.customStyle) {
      const style = this.customStyles.get(options.style);
      if (style) {
        // 调整理解结果以匹配自定义风格
        understanding.style = style.name;

        // 根据风格调整情感色调
        if (style.colorPalette.includes('#ff0000')) {
          understanding.emotionalTone.arousal = Math.max(understanding.emotionalTone.arousal, 0.7);
        }
      }
    }

    // 应用自定义对象类型的影响
    if (options.customObjects) {
      options.customObjects.forEach(customObj => {
        // 检查是否有匹配的实体
        understanding.entities.forEach(entity => {
          if (customObj.tags.some(tag => entity.text.includes(tag))) {
            entity.metadata = entity.metadata || {};
            entity.metadata.customType = customObj.name;
          }
        });
      });
    }
  }

  /**
   * 理解自然语言文本（扩展版本）
   */
  public async understandText(text: string): Promise<LanguageUnderstanding> {
    const startTime = Date.now();

    try {
      // 基础分析
      const entities = this.extractEntities(text);
      const intent = this.classifyIntent(text);
      const sentiment = this.analyzeSentiment(text);
      const keywords = this.extractKeywords(text);
      const style = this.inferStyle(text);

      // 扩展分析
      const complexity = this.analyzeComplexity(text, entities);
      const spatialRelations = this.extractSpatialRelations(text, entities);
      const temporalContext = this.extractTemporalContext(text);
      const culturalContext = this.extractCulturalContext(text);
      const emotionalTone = this.analyzeEmotionalTone(text, sentiment);

      const understanding: LanguageUnderstanding = {
        entities,
        intent,
        sentiment,
        keywords,
        style,
        complexity,
        spatialRelations,
        temporalContext,
        culturalContext,
        emotionalTone
      };

      // 如果启用了AI服务，尝试使用外部AI增强理解
      if (this.aiServices.size > 0) {
        await this.enhanceUnderstandingWithAI(understanding, text);
      }

      // 更新性能指标
      const processingTime = Date.now() - startTime;
      this.updatePerformanceMetrics('understanding', processingTime);

      return understanding;

    } catch (error) {
      console.error('文本理解失败:', error);
      this.performanceMetrics.errorRate++;
      throw error;
    }
  }

  /**
   * 分析场景复杂度
   */
  private analyzeComplexity(text: string, entities: LanguageUnderstanding['entities']): number {
    let complexity = 1;

    // 基于实体数量
    complexity += Math.min(entities.length * 0.5, 3);

    // 基于文本长度
    complexity += Math.min(text.length / 50, 2);

    // 基于特殊关键词
    const complexKeywords = ['复杂', '详细', '精致', '多层', '丰富'];
    const complexCount = complexKeywords.filter(keyword => text.includes(keyword)).length;
    complexity += complexCount * 0.5;

    // 基于空间关系复杂度
    const spatialWords = ['上面', '下面', '旁边', '中间', '周围', '对面', '里面', '外面'];
    const spatialCount = spatialWords.filter(word => text.includes(word)).length;
    complexity += spatialCount * 0.3;

    return Math.min(Math.max(complexity, 1), 10);
  }

  /**
   * 提取空间关系
   */
  private extractSpatialRelations(text: string, entities: LanguageUnderstanding['entities']): SpatialRelation[] {
    const relations: SpatialRelation[] = [];
    const spatialPredicates = [
      { pattern: /(.+?)在(.+?)上面/, predicate: 'on_top_of' },
      { pattern: /(.+?)在(.+?)下面/, predicate: 'below' },
      { pattern: /(.+?)在(.+?)旁边/, predicate: 'beside' },
      { pattern: /(.+?)在(.+?)中间/, predicate: 'between' },
      { pattern: /(.+?)围绕(.+?)/, predicate: 'around' },
      { pattern: /(.+?)对面是(.+?)/, predicate: 'opposite' },
      { pattern: /(.+?)里面有(.+?)/, predicate: 'contains' }
    ];

    spatialPredicates.forEach(({ pattern, predicate }) => {
      const matches = text.match(pattern);
      if (matches && matches.length >= 3) {
        relations.push({
          subject: matches[1].trim(),
          predicate,
          object: matches[2].trim(),
          confidence: 0.8
        });
      }
    });

    return relations;
  }

  /**
   * 提取时间上下文
   */
  private extractTemporalContext(text: string): TemporalContext | undefined {
    const timePatterns = {
      morning: /早上|上午|清晨|黎明/,
      afternoon: /下午|午后/,
      evening: /傍晚|黄昏|晚上/,
      night: /夜晚|深夜|午夜/
    };

    const seasonPatterns = {
      spring: /春天|春季|春日/,
      summer: /夏天|夏季|夏日/,
      autumn: /秋天|秋季|秋日/,
      winter: /冬天|冬季|冬日/
    };

    let timeOfDay: TemporalContext['timeOfDay'] | undefined;
    let season: TemporalContext['season'] | undefined;

    // 检测时间
    for (const [time, pattern] of Object.entries(timePatterns)) {
      if (pattern.test(text)) {
        timeOfDay = time as TemporalContext['timeOfDay'];
        break;
      }
    }

    // 检测季节
    for (const [s, pattern] of Object.entries(seasonPatterns)) {
      if (pattern.test(text)) {
        season = s as TemporalContext['season'];
        break;
      }
    }

    if (timeOfDay || season) {
      return {
        timeOfDay: timeOfDay || 'afternoon',
        season: season || 'spring',
        era: 'modern'
      };
    }

    return undefined;
  }

  /**
   * 提取文化上下文
   */
  private extractCulturalContext(text: string): CulturalContext | undefined {
    const culturalPatterns = {
      chinese: /中式|中国|传统|古典|明清|唐宋/,
      western: /西式|欧式|美式|现代|简约/,
      japanese: /日式|和风|榻榻米|禅意/,
      industrial: /工业|厂房|机械|钢铁/
    };

    for (const [culture, pattern] of Object.entries(culturalPatterns)) {
      if (pattern.test(text)) {
        return {
          region: culture,
          style: culture,
          traditions: [],
          architecture: culture
        };
      }
    }

    return undefined;
  }

  /**
   * 分析情感色调
   */
  private analyzeEmotionalTone(text: string, sentiment: 'positive' | 'negative' | 'neutral'): EmotionalTone {
    const emotionWords = {
      joy: ['快乐', '愉快', '开心', '欢乐', '喜悦'],
      calm: ['平静', '安静', '宁静', '祥和', '舒适'],
      excitement: ['激动', '兴奋', '热烈', '活跃', '动感'],
      melancholy: ['忧郁', '悲伤', '沉重', '阴郁', '凄凉'],
      mystery: ['神秘', '诡异', '幽暗', '隐秘', '朦胧']
    };

    let primary = 'neutral';
    let intensity = 0.5;
    let valence = 0;
    let arousal = 0.5;

    // 分析主要情感
    for (const [emotion, words] of Object.entries(emotionWords)) {
      const count = words.filter(word => text.includes(word)).length;
      if (count > 0) {
        primary = emotion;
        intensity = Math.min(count * 0.3 + 0.3, 1.0);
        break;
      }
    }

    // 设置valence和arousal
    switch (sentiment) {
      case 'positive':
        valence = 0.7;
        break;
      case 'negative':
        valence = -0.7;
        break;
      default:
        valence = 0;
    }

    // 基于情感类型设置arousal
    if (['excitement', 'joy'].includes(primary)) {
      arousal = 0.8;
    } else if (['calm', 'melancholy'].includes(primary)) {
      arousal = 0.2;
    }

    return {
      primary,
      secondary: [],
      intensity,
      valence,
      arousal
    };
  }

  /**
   * 使用AI服务增强理解
   */
  private async enhanceUnderstandingWithAI(understanding: LanguageUnderstanding, text: string): Promise<void> {
    // 选择最适合的AI服务
    const aiService = this.selectBestAIService([AICapability.TEXT_UNDERSTANDING]);

    if (!aiService) {
      return;
    }

    try {
      // 构建请求
      const requestKey = `enhance_${text.substring(0, 50)}`;

      // 检查是否已有相同请求在处理
      if (this.requestQueue.has(requestKey)) {
        await this.requestQueue.get(requestKey);
        return;
      }

      // 创建请求Promise
      const requestPromise = this.callAIService(aiService, {
        text,
        task: 'enhance_understanding',
        current_understanding: understanding
      });

      this.requestQueue.set(requestKey, requestPromise);

      const aiResult = await requestPromise;

      // 合并AI增强的结果
      if (aiResult && aiResult.enhanced_entities) {
        understanding.entities.push(...aiResult.enhanced_entities);
      }

      if (aiResult && aiResult.enhanced_relations) {
        understanding.spatialRelations.push(...aiResult.enhanced_relations);
      }

      this.requestQueue.delete(requestKey);

    } catch (error) {
      console.warn('AI增强理解失败，使用基础理解结果:', error);
    }
  }

  /**
   * 选择最佳AI服务
   */
  private selectBestAIService(requiredCapabilities: AICapability[]): ExternalAIServiceConfig | null {
    for (const [name, config] of this.aiServices) {
      const hasAllCapabilities = requiredCapabilities.every(cap =>
        config.capabilities.includes(cap)
      );

      if (hasAllCapabilities) {
        return config;
      }
    }

    return null;
  }

  /**
   * 调用AI服务
   */
  private async callAIService(service: ExternalAIServiceConfig, payload: any): Promise<any> {
    // 检查速率限制
    if (!this.checkRateLimit(service)) {
      throw new Error(`AI服务 ${service.name} 速率限制超出`);
    }

    try {
      const response = await fetch(`${service.endpoint}/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(service.apiKey && { 'Authorization': `Bearer ${service.apiKey}` })
        },
        body: JSON.stringify({
          model: service.model,
          ...payload
        })
      });

      if (!response.ok) {
        throw new Error(`AI服务请求失败: ${response.status}`);
      }

      const result = await response.json();

      // 释放并发计数
      this.releaseConcurrentCount(service.name);

      return result;

    } catch (error) {
      // 释放并发计数
      this.releaseConcurrentCount(service.name);

      // 尝试fallback服务
      if (service.fallbackService) {
        const fallbackService = this.aiServices.get(service.fallbackService);
        if (fallbackService) {
          return await this.callAIService(fallbackService, payload);
        }
      }

      throw error;
    }
  }

  /**
   * 检查速率限制
   */
  private checkRateLimit(service: ExternalAIServiceConfig): boolean {
    const now = Date.now();
    const serviceName = service.name;

    // 获取或创建速率限制计数器
    if (!this.rateLimitCounters.has(serviceName)) {
      this.rateLimitCounters.set(serviceName, {
        requestsThisMinute: 0,
        requestsThisHour: 0,
        currentConcurrent: 0,
        lastMinuteReset: now,
        lastHourReset: now
      });
    }

    const counter = this.rateLimitCounters.get(serviceName)!;

    // 重置分钟计数器
    if (now - counter.lastMinuteReset >= 60000) {
      counter.requestsThisMinute = 0;
      counter.lastMinuteReset = now;
    }

    // 重置小时计数器
    if (now - counter.lastHourReset >= 3600000) {
      counter.requestsThisHour = 0;
      counter.lastHourReset = now;
    }

    // 检查限制
    if (counter.requestsThisMinute >= service.rateLimits.requestsPerMinute) {
      return false;
    }
    if (counter.requestsThisHour >= service.rateLimits.requestsPerHour) {
      return false;
    }
    if (counter.currentConcurrent >= service.rateLimits.maxConcurrent) {
      return false;
    }

    // 增加计数器
    counter.requestsThisMinute++;
    counter.requestsThisHour++;
    counter.currentConcurrent++;

    return true;
  }

  /**
   * 释放并发计数
   */
  private releaseConcurrentCount(serviceName: string): void {
    const counter = this.rateLimitCounters.get(serviceName);
    if (counter && counter.currentConcurrent > 0) {
      counter.currentConcurrent--;
    }
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(_operation: string, processingTime: number): void {
    this.performanceMetrics.totalGenerations++;

    // 更新平均处理时间
    const currentAvg = this.performanceMetrics.averageGenerationTime;
    const newAvg = (currentAvg * (this.performanceMetrics.totalGenerations - 1) + processingTime) /
                   this.performanceMetrics.totalGenerations;
    this.performanceMetrics.averageGenerationTime = newAvg;
  }

  /**
   * 提取实体
   */
  private extractEntities(text: string): LanguageUnderstanding['entities'] {
    const entities: LanguageUnderstanding['entities'] = [];

    // 对象实体
    const objectPatterns = [
      /桌子|椅子|沙发|床|柜子|书架/g,
      /电脑|电视|灯|植物|花|树/g,
      /汽车|建筑|房子|门|窗户/g
    ];

    objectPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          entities.push({
            text: match,
            type: 'OBJECT',
            confidence: 0.8
          });
        });
      }
    });

    // 位置实体
    const locationPatterns = [
      /办公室|客厅|卧室|厨房|浴室/g,
      /学校|图书馆|咖啡厅|餐厅|商店/g,
      /公园|广场|街道|花园/g
    ];

    locationPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          entities.push({
            text: match,
            type: 'LOCATION',
            confidence: 0.9
          });
        });
      }
    });

    // 属性实体
    const attributePatterns = [
      /现代|古典|简约|豪华|温馨/g,
      /明亮|昏暗|宽敞|狭小|舒适/g,
      /红色|蓝色|绿色|白色|黑色/g
    ];

    attributePatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          entities.push({
            text: match,
            type: 'ATTRIBUTE',
            confidence: 0.7
          });
        });
      }
    });

    return entities;
  }

  /**
   * 分类意图
   */
  private classifyIntent(text: string): string {
    if (/创建|建造|制作|生成/.test(text)) return 'CREATE';
    if (/修改|改变|调整|更新/.test(text)) return 'MODIFY';
    if (/删除|移除|清除/.test(text)) return 'DELETE';
    return 'CREATE'; // 默认为创建意图
  }

  /**
   * 分析情感
   */
  private analyzeSentiment(text: string): 'positive' | 'negative' | 'neutral' {
    const positiveWords = ['美丽', '温馨', '舒适', '明亮', '愉快', '漂亮'];
    const negativeWords = ['阴暗', '破旧', '肮脏', '混乱', '压抑'];

    const positiveCount = positiveWords.filter(word => text.includes(word)).length;
    const negativeCount = negativeWords.filter(word => text.includes(word)).length;

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  /**
   * 提取关键词
   */
  private extractKeywords(text: string): string[] {
    // 简化的关键词提取
    const words = text.split(/\s+|，|。|、/);
    return words.filter(word => word.length > 1 && !/的|了|在|和|与|或/.test(word));
  }

  /**
   * 推断风格
   */
  private inferStyle(text: string): string {
    if (/科幻|未来|太空|机器人/.test(text)) return 'scifi';
    if (/卡通|可爱|童话|动画/.test(text)) return 'cartoon';
    if (/简约|极简|现代|简洁/.test(text)) return 'minimalist';
    if (/魔法|奇幻|龙|城堡/.test(text)) return 'fantasy';
    return 'realistic';
  }

  /**
   * 智能场景规划（使用AI增强）
   */
  private async planSceneWithAI(
    understanding: LanguageUnderstanding,
    options: GenerationOptions
  ): Promise<any> {
    // 基础规划
    const basePlan = {
      layout: this.planLayout(understanding),
      objects: this.planObjects(understanding, options),
      lighting: this.planLighting(understanding),
      materials: this.planMaterials(understanding),
      atmosphere: this.planAtmosphere(understanding)
    };

    // 如果有AI服务可用，尝试增强规划
    const aiService = this.selectBestAIService([AICapability.SCENE_PLANNING]);
    if (aiService) {
      try {
        const aiEnhancement = await this.callAIService(aiService, {
          task: 'enhance_scene_plan',
          understanding,
          base_plan: basePlan,
          options
        });

        if (aiEnhancement && aiEnhancement.enhanced_plan) {
          // 合并AI增强的规划
          return this.mergePlans(basePlan, aiEnhancement.enhanced_plan);
        }
      } catch (error) {
        console.warn('AI场景规划增强失败，使用基础规划:', error);
      }
    }

    return basePlan;
  }

  /**
   * 合并规划结果
   */
  private mergePlans(basePlan: any, enhancedPlan: any): any {
    return {
      layout: enhancedPlan.layout || basePlan.layout,
      objects: [...basePlan.objects, ...(enhancedPlan.additional_objects || [])],
      lighting: enhancedPlan.lighting || basePlan.lighting,
      materials: [...basePlan.materials, ...(enhancedPlan.additional_materials || [])],
      atmosphere: { ...basePlan.atmosphere, ...enhancedPlan.atmosphere }
    };
  }

  /**
   * 生成增强场景
   */
  private async generateEnhancedScene(plan: any, options: GenerationOptions): Promise<Scene> {
    const scene = new Scene();
    scene.name = `Generated Scene ${Date.now()}`;

    // 创建地面
    this.createGround(scene, plan.layout);

    // 创建对象（包括自定义对象）
    plan.objects.forEach((objPlan: any) => {
      this.createEnhancedObject(scene, objPlan, options);
    });

    // 设置光照
    this.setupLighting(scene, plan.lighting);

    // 应用材质（包括自定义材质）
    this.applyEnhancedMaterials(scene, plan.materials, options);

    // 设置氛围
    this.setupAtmosphere(scene, plan.atmosphere);

    return scene;
  }

  /**
   * 创建增强对象（支持自定义对象类型）
   */
  private createEnhancedObject(scene: Scene, objPlan: any, options: GenerationOptions): void {
    // 检查是否是自定义对象类型
    const customObject = this.customObjects.get(objPlan.type);
    if (customObject) {
      this.createCustomObject(scene, objPlan, customObject);
      return;
    }

    // 使用原有的对象创建逻辑
    this.createObject(scene, objPlan);
  }

  /**
   * 创建自定义对象
   */
  private createCustomObject(scene: Scene, objPlan: any, customType: CustomObjectType): void {
    const entity = new Entity(customType.name);

    // 添加变换组件
    const transform = new Transform();
    transform.setPosition(objPlan.position.x, objPlan.position.y, objPlan.position.z);
    transform.setRotation(objPlan.rotation.x, objPlan.rotation.y, objPlan.rotation.z);
    transform.setScale(objPlan.scale.x, objPlan.scale.y, objPlan.scale.z);
    entity.addComponent(transform);

    // 使用自定义几何体工厂
    const geometry = customType.geometryFactory(objPlan);

    // 获取自定义材质
    let material: THREE.Material;
    const customStyle = this.customStyles.get(objPlan.style);
    if (customStyle) {
      const materialPreset = customStyle.materialPresets.find(p =>
        p.applicableObjects.includes(customType.name)
      );
      if (materialPreset) {
        material = this.materialFactory.createMaterial(materialPreset.type, materialPreset.properties);
      } else {
        material = this.materialFactory.createMaterial(customType.defaultMaterial);
      }
    } else {
      material = this.materialFactory.createMaterial(customType.defaultMaterial);
    }

    // 创建网格组件
    const meshComponent = new MeshComponent({
      geometry: geometry,
      material: material
    });
    entity.addComponent(meshComponent);

    // 添加自定义组件
    if (customType.additionalComponents) {
      customType.additionalComponents.forEach(componentType => {
        // 这里可以根据组件类型添加相应的组件
        console.log(`添加自定义组件: ${componentType}`);
      });
    }

    // 添加到场景
    scene.addEntity(entity);
  }

  /**
   * 应用增强材质
   */
  private applyEnhancedMaterials(scene: Scene, materials: any[], options: GenerationOptions): void {
    // 获取场景中的所有实体
    const entities = scene.getEntities();
    const materialFactory = this.materialFactory;

    // 应用自定义风格的材质
    if (options.customStyle) {
      const customStyle = this.customStyles.get(options.style);
      if (customStyle) {
        entities.forEach(entity => {
          const meshComponent = entity.getComponent('MeshComponent') as MeshComponent;
          if (meshComponent && entity.name !== 'Ground') {
            // 查找适用的材质预设
            const applicablePreset = customStyle.materialPresets.find(preset =>
              preset.applicableObjects.some(obj => entity.name.includes(obj))
            );

            if (applicablePreset) {
              const material = materialFactory.createMaterial(
                applicablePreset.type,
                applicablePreset.properties
              );
              meshComponent.setMaterial(material);
            }
          }
        });
        return;
      }
    }

    // 使用原有的材质应用逻辑
    this.applyMaterials(scene, materials);
  }

  /**
   * AI增强的场景优化
   */
  private async optimizeSceneWithAI(
    scene: Scene,
    understanding: LanguageUnderstanding,
    options: GenerationOptions
  ): Promise<void> {
    // 基础优化
    await this.optimizeScene(scene, understanding);

    // AI增强优化
    const aiService = this.selectBestAIService([AICapability.OPTIMIZATION]);
    if (aiService) {
      try {
        const sceneData = this.extractSceneData(scene);
        const optimizationSuggestions = await this.callAIService(aiService, {
          task: 'optimize_scene',
          scene_data: sceneData,
          understanding,
          options
        });

        if (optimizationSuggestions && optimizationSuggestions.suggestions) {
          await this.applyOptimizationSuggestions(scene, optimizationSuggestions.suggestions);
        }
      } catch (error) {
        console.warn('AI场景优化失败，使用基础优化:', error);
      }
    }
  }

  /**
   * 提取场景数据用于AI分析
   */
  private extractSceneData(scene: Scene): any {
    const entities = scene.getEntities();
    return {
      entityCount: entities.length,
      entities: entities.map(entity => ({
        name: entity.name,
        position: entity.getTransform()?.getPosition(),
        hasTransform: !!entity.getTransform(),
        hasMesh: !!entity.getComponent('MeshComponent'),
        hasLight: !!entity.getComponent('Light')
      })),
      complexity: this.calculateSceneComplexity(scene)
    };
  }

  /**
   * 计算场景复杂度
   */
  private calculateSceneComplexity(scene: Scene): number {
    const entities = scene.getEntities();
    let complexity = 0;

    entities.forEach(entity => {
      const meshComponent = entity.getComponent('MeshComponent') as MeshComponent;
      if (meshComponent && meshComponent.geometry) {
        const geometry = meshComponent.geometry;
        if (geometry.attributes.position) {
          complexity += geometry.attributes.position.count / 3; // 三角形数量
        }
      }
    });

    return complexity;
  }

  /**
   * 应用优化建议
   */
  private async applyOptimizationSuggestions(scene: Scene, suggestions: any[]): Promise<void> {
    for (const suggestion of suggestions) {
      switch (suggestion.type) {
        case 'reduce_polygons':
          await this.reducePolygons(scene, suggestion.target, suggestion.factor);
          break;
        case 'merge_objects':
          await this.mergeObjects(scene, suggestion.objects);
          break;
        case 'optimize_materials':
          await this.optimizeMaterials(scene, suggestion.materials);
          break;
        case 'adjust_lighting':
          await this.adjustLighting(scene, suggestion.lighting);
          break;
      }
    }
  }

  /**
   * 减少多边形数量
   */
  private async reducePolygons(scene: Scene, targetEntity: string, factor: number): Promise<void> {
    const entities = scene.getEntities();
    const entity = entities.find(e => e.name === targetEntity);

    if (entity) {
      const meshComponent = entity.getComponent('MeshComponent') as MeshComponent;
      if (meshComponent && meshComponent.geometry instanceof THREE.BufferGeometry) {
        // 实现几何体简化算法
        const originalGeometry = meshComponent.geometry;
        const simplifiedGeometry = this.simplifyGeometry(originalGeometry, factor);

        if (simplifiedGeometry) {
          meshComponent.setGeometry(simplifiedGeometry);
          console.log(`已简化实体 ${targetEntity} 的几何体，简化因子: ${factor}`);
        }
      }
    }
  }

  /**
   * 简化几何体
   */
  private simplifyGeometry(geometry: THREE.BufferGeometry, factor: number): THREE.BufferGeometry | null {
    try {
      // 对于基础几何体，可以通过减少分段数来简化
      if (geometry instanceof THREE.BoxGeometry) {
        const params = geometry.parameters;
        const newWidthSegments = Math.max(1, Math.floor((params.widthSegments || 1) * factor));
        const newHeightSegments = Math.max(1, Math.floor((params.heightSegments || 1) * factor));
        const newDepthSegments = Math.max(1, Math.floor((params.depthSegments || 1) * factor));

        return new THREE.BoxGeometry(
          params.width,
          params.height,
          params.depth,
          newWidthSegments,
          newHeightSegments,
          newDepthSegments
        );
      } else if (geometry instanceof THREE.SphereGeometry) {
        const params = geometry.parameters;
        const newWidthSegments = Math.max(3, Math.floor((params.widthSegments || 8) * factor));
        const newHeightSegments = Math.max(2, Math.floor((params.heightSegments || 6) * factor));

        return new THREE.SphereGeometry(
          params.radius,
          newWidthSegments,
          newHeightSegments,
          params.phiStart,
          params.phiLength,
          params.thetaStart,
          params.thetaLength
        );
      } else if (geometry instanceof THREE.CylinderGeometry) {
        const params = geometry.parameters;
        const newRadialSegments = Math.max(3, Math.floor((params.radialSegments || 8) * factor));
        const newHeightSegments = Math.max(1, Math.floor((params.heightSegments || 1) * factor));

        return new THREE.CylinderGeometry(
          params.radiusTop,
          params.radiusBottom,
          params.height,
          newRadialSegments,
          newHeightSegments,
          params.openEnded,
          params.thetaStart,
          params.thetaLength
        );
      }

      // 对于其他类型的几何体，暂时返回原几何体
      console.warn(`几何体类型 ${geometry.constructor.name} 暂不支持简化`);
      return null;
    } catch (error) {
      console.error('几何体简化失败:', error);
      return null;
    }
  }

  /**
   * 合并对象
   */
  private async mergeObjects(scene: Scene, objectNames: string[]): Promise<void> {
    const entities = scene.getEntities();
    const toMerge = entities.filter(e => objectNames.includes(e.name));

    if (toMerge.length > 1) {
      // 实现对象合并逻辑
      console.log(`合并对象: ${objectNames.join(', ')}`);
    }
  }

  /**
   * 优化材质
   */
  private async optimizeMaterials(scene: Scene, materialOptimizations: any[]): Promise<void> {
    materialOptimizations.forEach(opt => {
      console.log(`优化材质: ${opt.material}, 优化类型: ${opt.optimization}`);
    });
  }

  /**
   * 调整光照
   */
  private async adjustLighting(scene: Scene, lightingAdjustments: any): Promise<void> {
    const entities = scene.getEntities();
    entities.forEach(entity => {
      const lightComponent = entity.getComponent('Light') as Light;
      if (lightComponent) {
        if (lightingAdjustments.intensity) {
          lightComponent.setIntensity(lightingAdjustments.intensity);
        }
        if (lightingAdjustments.color) {
          lightComponent.setColor(lightingAdjustments.color);
        }
      }
    });
  }

  /**
   * 应用后处理效果
   */
  private async applyPostProcessing(scene: Scene, options: GenerationOptions): Promise<void> {
    // 应用自定义风格的后处理效果
    if (options.customStyle) {
      const customStyle = this.customStyles.get(options.style);
      if (customStyle && customStyle.atmosphereSettings.postProcessingEffects.length > 0) {
        customStyle.atmosphereSettings.postProcessingEffects.forEach(effect => {
          console.log(`应用后处理效果: ${effect}`);
          // 这里可以实现具体的后处理效果
          this.applyPostProcessingEffect(scene, effect);
        });
      }
    }
  }

  /**
   * 应用具体的后处理效果
   */
  private applyPostProcessingEffect(scene: Scene, effect: string): void {
    const threeScene = scene.getThreeScene();

    switch (effect) {
      case 'bloom':
        console.log('应用泛光效果');
        // 增强发光材质的发光强度
        this.enhanceEmissiveMaterials(threeScene);
        break;
      case 'ssao':
        console.log('应用屏幕空间环境光遮蔽');
        // 调整环境光强度以配合SSAO效果
        this.adjustAmbientForSSAO(threeScene);
        break;
      case 'vignette':
        console.log('应用暗角效果');
        // 暗角效果通常在渲染器层面实现，这里记录设置
        break;
      case 'grain':
        console.log('应用噪点效果');
        // 噪点效果通常在渲染器层面实现，这里记录设置
        break;
      default:
        console.log(`未知的后处理效果: ${effect}`);
    }
  }

  /**
   * 增强发光材质
   */
  private enhanceEmissiveMaterials(threeScene: THREE.Scene): void {
    threeScene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.material) {
        const material = object.material as any;
        if (material.emissive && material.emissive instanceof THREE.Color) {
          // 增强发光强度
          material.emissiveIntensity = (material.emissiveIntensity || 0) + 0.2;
        }
      }
    });
  }

  /**
   * 调整环境光以配合SSAO
   */
  private adjustAmbientForSSAO(threeScene: THREE.Scene): void {
    threeScene.traverse((object) => {
      if (object instanceof THREE.AmbientLight) {
        // 降低环境光强度以配合SSAO效果
        object.intensity *= 0.8;
      }
    });
  }

  /**
   * 合并多个几何体
   */
  private mergeGeometries(geometries: THREE.BufferGeometry[]): THREE.BufferGeometry | null {
    if (geometries.length === 0) return null;
    if (geometries.length === 1) return geometries[0];

    try {
      // 使用Three.js的BufferGeometryUtils合并几何体
      // 注意：这需要导入BufferGeometryUtils
      const mergedGeometry = new THREE.BufferGeometry();

      // 简化实现：将所有几何体的顶点合并
      const positions: number[] = [];
      const normals: number[] = [];
      const uvs: number[] = [];
      const indices: number[] = [];

      let vertexOffset = 0;

      geometries.forEach(geometry => {
        const positionAttribute = geometry.attributes.position;
        const normalAttribute = geometry.attributes.normal;
        const uvAttribute = geometry.attributes.uv;
        const indexAttribute = geometry.index;

        if (positionAttribute) {
          for (let i = 0; i < positionAttribute.count; i++) {
            positions.push(
              positionAttribute.getX(i),
              positionAttribute.getY(i),
              positionAttribute.getZ(i)
            );
          }
        }

        if (normalAttribute) {
          for (let i = 0; i < normalAttribute.count; i++) {
            normals.push(
              normalAttribute.getX(i),
              normalAttribute.getY(i),
              normalAttribute.getZ(i)
            );
          }
        }

        if (uvAttribute) {
          for (let i = 0; i < uvAttribute.count; i++) {
            uvs.push(
              uvAttribute.getX(i),
              uvAttribute.getY(i)
            );
          }
        }

        if (indexAttribute) {
          for (let i = 0; i < indexAttribute.count; i++) {
            indices.push(indexAttribute.getX(i) + vertexOffset);
          }
        }

        vertexOffset += positionAttribute ? positionAttribute.count : 0;
      });

      // 设置合并后的属性
      mergedGeometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
      if (normals.length > 0) {
        mergedGeometry.setAttribute('normal', new THREE.Float32BufferAttribute(normals, 3));
      }
      if (uvs.length > 0) {
        mergedGeometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
      }
      if (indices.length > 0) {
        mergedGeometry.setIndex(indices);
      }

      // 计算法线（如果没有）
      if (normals.length === 0) {
        mergedGeometry.computeVertexNormals();
      }

      return mergedGeometry;
    } catch (error) {
      console.error('几何体合并失败:', error);
      return geometries[0]; // 返回第一个几何体作为备选
    }
  }

  /**
   * 规划布局
   */
  private planLayout(understanding: LanguageUnderstanding): any {
    const locations = understanding.entities.filter(e => e.type === 'LOCATION');

    if (locations.length > 0) {
      const location = locations[0].text;

      // 根据位置类型返回不同的布局
      switch (location) {
        case '办公室':
          return {
            type: 'office',
            size: { width: 10, height: 3, depth: 8 },
            zones: ['work_area', 'meeting_area', 'storage_area']
          };
        case '客厅':
          return {
            type: 'living_room',
            size: { width: 12, height: 3, depth: 10 },
            zones: ['seating_area', 'entertainment_area', 'decoration_area']
          };
        case '图书馆':
          return {
            type: 'library',
            size: { width: 20, height: 4, depth: 15 },
            zones: ['reading_area', 'book_storage', 'study_area']
          };
        default:
          return {
            type: 'generic',
            size: { width: 10, height: 3, depth: 10 },
            zones: ['main_area']
          };
      }
    }

    return {
      type: 'generic',
      size: { width: 10, height: 3, depth: 10 },
      zones: ['main_area']
    };
  }

  /**
   * 规划对象
   */
  private planObjects(
    understanding: LanguageUnderstanding,
    options: GenerationOptions
  ): any[] {
    const objects = understanding.entities.filter(e => e.type === 'OBJECT');
    const plannedObjects = [];

    // 限制对象数量
    const maxObjects = Math.min(objects.length, options.maxObjects || 50);

    for (let i = 0; i < maxObjects; i++) {
      const obj = objects[i % objects.length];
      plannedObjects.push({
        type: obj.text,
        position: this.generateRandomPosition(),
        rotation: this.generateRandomRotation(),
        scale: this.generateRandomScale(),
        material: this.selectMaterial(obj.text)
      });
    }

    return plannedObjects;
  }

  /**
   * 生成随机位置
   */
  private generateRandomPosition(): { x: number, y: number, z: number } {
    return {
      x: (Math.random() - 0.5) * 10,
      y: 0,
      z: (Math.random() - 0.5) * 10
    };
  }

  /**
   * 生成随机旋转
   */
  private generateRandomRotation(): { x: number, y: number, z: number } {
    return {
      x: 0,
      y: Math.random() * Math.PI * 2,
      z: 0
    };
  }

  /**
   * 生成随机缩放
   */
  private generateRandomScale(): { x: number, y: number, z: number } {
    const scale = 0.8 + Math.random() * 0.4; // 0.8 到 1.2
    return { x: scale, y: scale, z: scale };
  }

  /**
   * 选择材质
   */
  private selectMaterial(objectType: string): string {
    const materialMap: { [key: string]: string } = {
      '桌子': 'wood',
      '椅子': 'fabric',
      '沙发': 'leather',
      '电脑': 'plastic',
      '植物': 'organic',
      '灯': 'metal'
    };

    return materialMap[objectType] || 'default';
  }

  /**
   * 规划光照
   */
  private planLighting(understanding: LanguageUnderstanding): any {
    const sentiment = understanding.sentiment;
    const attributes = understanding.entities.filter(e => e.type === 'ATTRIBUTE');

    let intensity = 1.0;
    let color = '#ffffff';
    let warmth = 0.5;

    // 根据情感调整光照
    if (sentiment === 'positive') {
      intensity = 1.2;
      warmth = 0.7;
    } else if (sentiment === 'negative') {
      intensity = 0.6;
      warmth = 0.3;
    }

    // 根据属性调整光照
    attributes.forEach(attr => {
      switch (attr.text) {
        case '明亮':
          intensity = 1.5;
          break;
        case '昏暗':
          intensity = 0.4;
          break;
        case '温馨':
          warmth = 0.8;
          color = '#fff8dc';
          break;
      }
    });

    return {
      ambient: {
        intensity: intensity * 0.3,
        color
      },
      directional: {
        intensity: intensity * 0.7,
        color,
        direction: { x: -1, y: -1, z: -1 }
      },
      warmth
    };
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(userInput: string, options: GenerationOptions): string {
    return `${userInput}_${options.style}_${options.quality}_${options.maxObjects}`;
  }

  /**
   * 规划材质
   */
  private planMaterials(understanding: LanguageUnderstanding): any[] {
    const style = understanding.style;
    const materials = [];

    switch (style) {
      case 'realistic':
        materials.push(
          { name: 'wood', type: 'physical', roughness: 0.8, metalness: 0.0 },
          { name: 'metal', type: 'physical', roughness: 0.2, metalness: 1.0 },
          { name: 'fabric', type: 'physical', roughness: 0.9, metalness: 0.0 }
        );
        break;
      case 'cartoon':
        materials.push(
          { name: 'toon', type: 'toon', color: '#ff6b6b' },
          { name: 'bright', type: 'basic', color: '#4ecdc4' }
        );
        break;
      default:
        materials.push(
          { name: 'default', type: 'standard', color: '#cccccc' }
        );
    }

    return materials;
  }

  /**
   * 规划氛围
   */
  private planAtmosphere(understanding: LanguageUnderstanding): any {
    return {
      fog: understanding.sentiment === 'negative' ? 0.1 : 0.0,
      skybox: understanding.style === 'scifi' ? 'space' : 'default',
      ambientSound: this.selectAmbientSound(understanding)
    };
  }

  /**
   * 选择环境音效
   */
  private selectAmbientSound(understanding: LanguageUnderstanding): string {
    const locations = understanding.entities.filter(e => e.type === 'LOCATION');

    if (locations.length > 0) {
      const location = locations[0].text;

      switch (location) {
        case '办公室': return 'office_ambient';
        case '咖啡厅': return 'cafe_ambient';
        case '图书馆': return 'library_ambient';
        case '公园': return 'nature_ambient';
        default: return 'default_ambient';
      }
    }

    return 'default_ambient';
  }

  /**
   * 生成场景
   */
  private async generateScene(plan: any, options: GenerationOptions): Promise<Scene> {
    const scene = new Scene();
    scene.name = `Generated Scene ${Date.now()}`;

    // 创建地面
    this.createGround(scene, plan.layout);

    // 创建对象
    plan.objects.forEach((objPlan: any) => {
      this.createObject(scene, objPlan);
    });

    // 设置光照
    this.setupLighting(scene, plan.lighting);

    // 应用材质
    this.applyMaterials(scene, plan.materials);

    // 设置氛围
    this.setupAtmosphere(scene, plan.atmosphere);

    return scene;
  }

  /**
   * 创建地面
   */
  private createGround(scene: Scene, layout: any): void {
    const groundEntity = new Entity('Ground');

    // 添加变换组件
    const transform = new Transform();
    transform.setPosition(0, -0.1, 0);
    transform.setScale(layout.size.width, 0.2, layout.size.depth);
    groundEntity.addComponent(transform);

    // 创建地面几何体
    const groundGeometry = new THREE.BoxGeometry(1, 1, 1);

    // 创建地面材质
    const materialFactory = new MaterialFactory();
    const groundMaterial = materialFactory.createStandardMaterial({
      color: 0x808080,
      roughness: 0.8,
      metalness: 0.2
    });

    // 创建网格组件
    const meshComponent = new MeshComponent({
      geometry: groundGeometry,
      material: groundMaterial
    });
    groundEntity.addComponent(meshComponent);

    // 添加到场景
    scene.addEntity(groundEntity);
  }

  /**
   * 创建对象
   */
  private createObject(scene: Scene, objPlan: any): void {
    const entity = new Entity(objPlan.type);

    // 添加变换组件
    const transform = new Transform();
    transform.setPosition(objPlan.position.x, objPlan.position.y, objPlan.position.z);
    transform.setRotation(objPlan.rotation.x, objPlan.rotation.y, objPlan.rotation.z);
    transform.setScale(objPlan.scale.x, objPlan.scale.y, objPlan.scale.z);
    entity.addComponent(transform);

    // 根据对象类型创建几何体
    let geometry: THREE.BufferGeometry;
    switch (objPlan.type) {
      case '桌子':
        geometry = new THREE.BoxGeometry(2, 0.1, 1);
        break;
      case '椅子':
        geometry = new THREE.BoxGeometry(0.5, 1, 0.5);
        break;
      case '沙发':
        geometry = new THREE.BoxGeometry(2, 0.8, 1);
        break;
      case '电脑':
        geometry = new THREE.BoxGeometry(0.4, 0.3, 0.05);
        break;
      case '植物':
        geometry = new THREE.ConeGeometry(0.3, 1, 8);
        break;
      case '灯':
        geometry = new THREE.SphereGeometry(0.2, 16, 16);
        break;
      case '汽车':
        geometry = new THREE.BoxGeometry(4, 1.5, 2);
        break;
      case '建筑':
      case '房子':
        geometry = new THREE.BoxGeometry(5, 3, 5);
        break;
      default:
        geometry = new THREE.BoxGeometry(1, 1, 1);
    }

    // 创建材质
    const materialFactory = new MaterialFactory();
    let material: THREE.Material;

    switch (objPlan.material) {
      case 'wood':
        material = materialFactory.createStandardMaterial({
          color: 0x8B4513,
          roughness: 0.8,
          metalness: 0.1
        });
        break;
      case 'metal':
        material = materialFactory.createPhysicalMaterial({
          color: 0xC0C0C0,
          roughness: 0.2,
          metalness: 1.0
        });
        break;
      case 'fabric':
        material = materialFactory.createStandardMaterial({
          color: 0x4169E1,
          roughness: 0.9,
          metalness: 0.0
        });
        break;
      case 'plastic':
        material = materialFactory.createStandardMaterial({
          color: 0x333333,
          roughness: 0.3,
          metalness: 0.0
        });
        break;
      case 'organic':
        material = materialFactory.createStandardMaterial({
          color: 0x228B22,
          roughness: 0.9,
          metalness: 0.0
        });
        break;
      case 'leather':
        material = materialFactory.createStandardMaterial({
          color: 0x8B4513,
          roughness: 0.7,
          metalness: 0.0
        });
        break;
      default:
        material = materialFactory.createStandardMaterial({
          color: 0xCCCCCC,
          roughness: 0.5,
          metalness: 0.2
        });
    }

    // 创建网格组件
    const meshComponent = new MeshComponent({
      geometry: geometry,
      material: material
    });
    entity.addComponent(meshComponent);

    // 添加到场景
    scene.addEntity(entity);
  }

  /**
   * 设置光照
   */
  private setupLighting(scene: Scene, lighting: any): void {
    // 创建环境光
    const ambientEntity = new Entity('AmbientLight');
    const ambientTransform = new Transform();
    ambientEntity.addComponent(ambientTransform);

    const ambientLight = new Light({
      type: LightType.AMBIENT,
      color: lighting.ambient.color,
      intensity: lighting.ambient.intensity
    });
    ambientEntity.addComponent(ambientLight);
    scene.addEntity(ambientEntity);

    // 创建方向光
    const directionalEntity = new Entity('DirectionalLight');
    const directionalTransform = new Transform();
    directionalTransform.setPosition(
      lighting.directional.direction.x * 10,
      lighting.directional.direction.y * 10,
      lighting.directional.direction.z * 10
    );
    directionalEntity.addComponent(directionalTransform);

    const directionalLight = new Light({
      type: LightType.DIRECTIONAL,
      color: lighting.directional.color,
      intensity: lighting.directional.intensity,
      castShadow: true
    });
    directionalEntity.addComponent(directionalLight);
    scene.addEntity(directionalEntity);

    // 如果需要，添加点光源增强氛围
    if (lighting.warmth > 0.6) {
      const pointEntity = new Entity('WarmPointLight');
      const pointTransform = new Transform();
      pointTransform.setPosition(0, 3, 0);
      pointEntity.addComponent(pointTransform);

      const pointLight = new Light({
        type: LightType.POINT,
        color: '#fff8dc',
        intensity: lighting.warmth * 0.5,
        distance: 10,
        decay: 2
      } as any); // 临时使用any类型，因为接口定义可能不完整
      pointEntity.addComponent(pointLight);
      scene.addEntity(pointEntity);
    }
  }

  /**
   * 应用材质
   */
  private applyMaterials(scene: Scene, materials: any[]): void {
    // 获取场景中的所有实体
    const entities = scene.getEntities();
    const materialFactory = new MaterialFactory();

    // 为每个实体应用合适的材质
    entities.forEach(entity => {
      const meshComponent = entity.getComponent('MeshComponent') as MeshComponent;
      if (meshComponent && entity.name !== 'Ground') {
        // 根据材质列表选择合适的材质
        const materialData = materials.find(m => m.name === 'default') || materials[0];

        if (materialData) {
          let material: THREE.Material;

          switch (materialData.type) {
            case 'physical':
              material = materialFactory.createPhysicalMaterial({
                color: materialData.color || 0xcccccc,
                roughness: materialData.roughness || 0.5,
                metalness: materialData.metalness || 0.0
              });
              break;
            case 'toon':
              material = materialFactory.createToonMaterial({
                color: materialData.color || 0xff6b6b
              });
              break;
            case 'basic':
              material = materialFactory.createBasicMaterial({
                color: materialData.color || 0x4ecdc4
              });
              break;
            default:
              material = materialFactory.createStandardMaterial({
                color: materialData.color || 0xcccccc
              });
          }

          // 更新网格组件的材质
          meshComponent.setMaterial(material);
        }
      }
    });
  }

  /**
   * 设置氛围
   */
  private setupAtmosphere(scene: Scene, atmosphere: any): void {
    // 设置雾效
    if (atmosphere.fog > 0) {
      const threeScene = scene.getThreeScene();
      if (threeScene) {
        const fogNear = 5;
        const fogFar = 50 * (1 + atmosphere.fog);
        threeScene.fog = new THREE.Fog(0x888888, fogNear, fogFar);
      }
    }

    // 设置天空盒
    if (atmosphere.skybox && atmosphere.skybox !== 'default') {
      const threeScene = scene.getThreeScene();
      if (threeScene) {
        let skyboxColor: THREE.Color;
        switch (atmosphere.skybox) {
          case 'space':
            skyboxColor = new THREE.Color(0x000011);
            break;
          case 'sunset':
            skyboxColor = new THREE.Color(0xff6b35);
            break;
          case 'dawn':
            skyboxColor = new THREE.Color(0x87ceeb);
            break;
          default:
            skyboxColor = new THREE.Color(0x87ceeb);
        }
        threeScene.background = skyboxColor;
      }
    }

    // 设置环境音效
    if (atmosphere.ambientSound) {
      console.log(`设置环境音效: ${atmosphere.ambientSound}`);
      this.setupAmbientSound(scene, atmosphere.ambientSound);
    }
  }

  /**
   * 优化场景
   */
  private async optimizeScene(scene: Scene, understanding: LanguageUnderstanding): Promise<void> {
    // 根据情感调整场景
    if (understanding.sentiment === 'positive') {
      this.enhanceBrightness(scene);
    } else if (understanding.sentiment === 'negative') {
      this.reduceBrightness(scene);
    }

    // 性能优化
    await this.optimizePerformance(scene);
  }

  /**
   * 增强亮度
   */
  private enhanceBrightness(scene: Scene): void {
    // 获取场景中的所有光源实体
    const entities = scene.getEntities();
    entities.forEach(entity => {
      const lightComponent = entity.getComponent('Light') as Light;
      if (lightComponent) {
        // 增加光源强度
        const currentIntensity = lightComponent.getThreeLight().intensity;
        lightComponent.setIntensity(currentIntensity * 1.2);
      }
    });

    // 调整环境光
    const threeScene = scene.getThreeScene();
    if (threeScene && threeScene.background instanceof THREE.Color) {
      threeScene.background.multiplyScalar(1.1);
    }
  }

  /**
   * 降低亮度
   */
  private reduceBrightness(scene: Scene): void {
    // 获取场景中的所有光源实体
    const entities = scene.getEntities();
    entities.forEach(entity => {
      const lightComponent = entity.getComponent('Light') as Light;
      if (lightComponent) {
        // 降低光源强度
        const currentIntensity = lightComponent.getThreeLight().intensity;
        lightComponent.setIntensity(currentIntensity * 0.8);
      }
    });

    // 调整环境光
    const threeScene = scene.getThreeScene();
    if (threeScene && threeScene.background instanceof THREE.Color) {
      threeScene.background.multiplyScalar(0.9);
    }
  }

  /**
   * 性能优化
   */
  private async optimizePerformance(scene: Scene): Promise<void> {
    const entities = scene.getEntities();
    let totalPolygons = 0;

    // 统计多边形数量
    entities.forEach(entity => {
      const meshComponent = entity.getComponent('MeshComponent') as MeshComponent;
      if (meshComponent) {
        const geometry = meshComponent.geometry;
        if (geometry && geometry.attributes.position) {
          totalPolygons += geometry.attributes.position.count / 3;
        }
      }
    });

    console.log(`场景总多边形数: ${totalPolygons}`);

    // 如果多边形数量过多，进行优化
    if (totalPolygons > 100000) {
      console.log('场景复杂度过高，进行性能优化...');

      // 简化远距离对象的几何体
      entities.forEach(entity => {
        const transform = entity.getTransform();
        const meshComponent = entity.getComponent('MeshComponent') as MeshComponent;

        if (transform && meshComponent) {
          const position = transform.getPosition();
          const distance = position.length();

          // 距离原点较远的对象使用简化几何体
          if (distance > 10) {
            const geometry = meshComponent.geometry;
            if (geometry instanceof THREE.BoxGeometry) {
              // 减少分段数
              const simplifiedGeometry = new THREE.BoxGeometry(
                geometry.parameters.width,
                geometry.parameters.height,
                geometry.parameters.depth,
                1, 1, 1 // 最少分段
              );
              meshComponent.setGeometry(simplifiedGeometry);
            }
          }
        }
      });
    }
  }

  /**
   * 设置环境音效
   */
  private setupAmbientSound(scene: Scene, soundType: string): void {
    // 创建音频实体
    const audioEntity = new Entity(`AmbientSound_${soundType}`);

    // 添加变换组件
    const transform = new Transform();
    audioEntity.addComponent(transform);

    // 这里可以添加音频组件，当音频系统可用时
    // const audioComponent = new AudioComponent({
    //   source: this.getAmbientSoundPath(soundType),
    //   loop: true,
    //   volume: 0.3,
    //   autoPlay: true
    // });
    // audioEntity.addComponent(audioComponent);

    // 暂时只记录音效设置
    console.log(`已设置环境音效实体: ${soundType}`);

    // 添加到场景
    scene.addEntity(audioEntity);
  }

  /**
   * 获取环境音效路径
   */
  private getAmbientSoundPath(soundType: string): string {
    const soundPaths: { [key: string]: string } = {
      'office_ambient': 'sounds/ambient/office.mp3',
      'cafe_ambient': 'sounds/ambient/cafe.mp3',
      'library_ambient': 'sounds/ambient/library.mp3',
      'nature_ambient': 'sounds/ambient/nature.mp3',
      'default_ambient': 'sounds/ambient/default.mp3'
    };

    return soundPaths[soundType] || soundPaths['default_ambient'];
  }

  /**
   * 添加事件监听器
   */
  public addEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   */
  public removeEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
}
