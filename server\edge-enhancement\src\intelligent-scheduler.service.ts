import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConfigService } from '@nestjs/config';

/**
 * 负载预测数据接口
 */
export interface LoadPrediction {
  expectedLoad: number;
  confidence: number;
  timestamp: Date;
  recommendedActions: Action[];
  riskLevel: 'low' | 'medium' | 'high';
}

/**
 * 推荐动作接口
 */
export interface Action {
  type: 'SCALE_OUT' | 'SCALE_IN' | 'MIGRATE' | 'OPTIMIZE' | 'ALERT';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  estimatedImpact: number;
  description: string;
  estimatedCost: number;
}

/**
 * 历史负载数据接口
 */
export interface LoadData {
  nodeId: string;
  timestamp: Date;
  cpuUsage: number;
  memoryUsage: number;
  networkUsage: number;
  userCount: number;
  responseTime: number;
  errorRate: number;
}

/**
 * 边缘节点接口
 */
export interface EdgeNode {
  id: string;
  region: string;
  endpoint: string;
  capabilities: {
    maxUsers: number;
    cpu: string;
    memory: string;
    storage: string;
  };
  currentLoad: {
    cpuUsage: number;
    memoryUsage: number;
    userCount: number;
  };
  status: 'online' | 'offline' | 'maintenance';
  location: {
    latitude: number;
    longitude: number;
    city: string;
    country: string;
  };
}

/**
 * 流量分配计划接口
 */
export interface DistributionPlan {
  assignments: Assignment[];
  expectedLatency: number;
  expectedThroughput: number;
  riskScore: number;
  estimatedCost: number;
  confidence: number;
}

/**
 * 请求分配接口
 */
export interface Assignment {
  requestId: string;
  nodeId: string;
  estimatedLatency: number;
  confidence: number;
  priority: number;
}

/**
 * 请求接口
 */
export interface Request {
  id: string;
  userId: string;
  type: string;
  priority: number;
  estimatedLoad: number;
  location?: {
    latitude: number;
    longitude: number;
  };
  requirements: {
    minCpu?: number;
    minMemory?: number;
    maxLatency?: number;
  };
}

/**
 * 智能调度服务
 * 基于机器学习的负载预测和强化学习的流量分配
 */
@Injectable()
export class IntelligentSchedulerService {
  private readonly logger = new Logger(IntelligentSchedulerService.name);
  
  // 历史数据存储
  private historicalData: Map<string, LoadData[]> = new Map();
  
  // 预测模型参数
  private modelWeights: Map<string, number[]> = new Map();
  
  // Q-Learning参数
  private qTable: Map<string, Map<string, number>> = new Map();
  private learningRate = 0.1;
  private discountFactor = 0.9;
  private explorationRate = 0.1;
  
  // 性能指标
  private predictionAccuracy = 0.85;
  private totalPredictions = 0;
  private correctPredictions = 0;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initializeModel();
  }

  /**
   * 初始化预测模型
   */
  private initializeModel(): void {
    this.logger.log('初始化智能调度模型');
    
    // 初始化模型权重
    const features = ['cpu', 'memory', 'network', 'users', 'time'];
    for (const feature of features) {
      this.modelWeights.set(feature, [Math.random(), Math.random(), Math.random()]);
    }
    
    this.logger.log('智能调度模型初始化完成');
  }

  /**
   * 预测节点负载
   * @param nodeId 节点ID
   * @param timeWindow 预测时间窗口（分钟）
   * @returns 负载预测结果
   */
  async predictLoad(nodeId: string, timeWindow: number = 30): Promise<LoadPrediction> {
    const features = this.extractFeatures(nodeId, timeWindow);
    const prediction = this.calculatePrediction(features);
    
    const loadPrediction: LoadPrediction = {
      expectedLoad: prediction.load,
      confidence: prediction.confidence,
      timestamp: new Date(),
      recommendedActions: this.generateRecommendations(prediction),
      riskLevel: this.calculateRiskLevel(prediction.load)
    };
    
    // 记录预测结果用于后续验证
    this.recordPrediction(nodeId, loadPrediction);
    
    this.logger.debug(`节点 ${nodeId} 负载预测: ${prediction.load.toFixed(2)} (置信度: ${prediction.confidence.toFixed(2)})`);
    
    return loadPrediction;
  }

  /**
   * 智能流量分配
   * @param incomingRequests 待分配的请求
   * @param availableNodes 可用节点列表
   * @returns 分配计划
   */
  async distributeTraffic(
    incomingRequests: Request[],
    availableNodes: EdgeNode[]
  ): Promise<DistributionPlan> {
    const state = this.getCurrentState(availableNodes);
    const action = await this.selectAction(state, availableNodes);
    
    const plan = this.createDistributionPlan(incomingRequests, availableNodes, action);
    
    // 记录决策结果用于强化学习
    this.recordDecision(state, action, plan);
    
    // 触发分配完成事件
    this.eventEmitter.emit('traffic.distributed', {
      plan,
      requestCount: incomingRequests.length,
      nodeCount: availableNodes.length
    });
    
    this.logger.debug(`流量分配完成: ${incomingRequests.length} 个请求分配到 ${availableNodes.length} 个节点`);
    
    return plan;
  }

  /**
   * 添加历史负载数据
   * @param nodeId 节点ID
   * @param loadData 负载数据
   */
  addLoadData(nodeId: string, loadData: LoadData): void {
    if (!this.historicalData.has(nodeId)) {
      this.historicalData.set(nodeId, []);
    }
    
    const nodeData = this.historicalData.get(nodeId)!;
    nodeData.push(loadData);
    
    // 保持最近1000条记录
    if (nodeData.length > 1000) {
      nodeData.shift();
    }
    
    // 更新模型权重
    this.updateModelWeights(nodeId, loadData);
  }

  /**
   * 提取特征向量
   * @param nodeId 节点ID
   * @param timeWindow 时间窗口
   * @returns 特征向量
   */
  private extractFeatures(nodeId: string, timeWindow: number): number[] {
    const nodeData = this.historicalData.get(nodeId) || [];
    const cutoffTime = new Date(Date.now() - timeWindow * 60 * 1000);
    
    const recentData = nodeData.filter(data => data.timestamp >= cutoffTime);
    
    if (recentData.length === 0) {
      return [0, 0, 0, 0, 0]; // 默认特征向量
    }
    
    // 计算统计特征
    const avgCpu = recentData.reduce((sum, data) => sum + data.cpuUsage, 0) / recentData.length;
    const avgMemory = recentData.reduce((sum, data) => sum + data.memoryUsage, 0) / recentData.length;
    const avgNetwork = recentData.reduce((sum, data) => sum + data.networkUsage, 0) / recentData.length;
    const avgUsers = recentData.reduce((sum, data) => sum + data.userCount, 0) / recentData.length;
    const timeOfDay = new Date().getHours() / 24; // 归一化时间
    
    return [avgCpu, avgMemory, avgNetwork, avgUsers, timeOfDay];
  }

  /**
   * 计算负载预测
   * @param features 特征向量
   * @returns 预测结果
   */
  private calculatePrediction(features: number[]): { load: number; confidence: number } {
    // 简化的线性回归预测
    let prediction = 0;
    const featureNames = ['cpu', 'memory', 'network', 'users', 'time'];
    
    for (let i = 0; i < features.length; i++) {
      const weights = this.modelWeights.get(featureNames[i]) || [0, 0, 0];
      prediction += features[i] * weights[0] + Math.pow(features[i], 2) * weights[1] + weights[2];
    }
    
    // 归一化到 [0, 1] 范围
    prediction = Math.max(0, Math.min(1, prediction));
    
    // 计算置信度（基于历史准确率和数据质量）
    const confidence = this.predictionAccuracy * (features.reduce((sum, f) => sum + f, 0) / features.length);
    
    return {
      load: prediction,
      confidence: Math.max(0.1, Math.min(0.99, confidence))
    };
  }

  /**
   * 生成推荐动作
   * @param prediction 预测结果
   * @returns 推荐动作列表
   */
  private generateRecommendations(prediction: { load: number; confidence: number }): Action[] {
    const actions: Action[] = [];
    
    if (prediction.load > 0.8 && prediction.confidence > 0.7) {
      actions.push({
        type: 'SCALE_OUT',
        priority: 'HIGH',
        estimatedImpact: 0.3,
        description: '负载过高，建议扩容',
        estimatedCost: 100
      });
    }
    
    if (prediction.load > 0.9 && prediction.confidence > 0.8) {
      actions.push({
        type: 'ALERT',
        priority: 'CRITICAL',
        estimatedImpact: 0.5,
        description: '负载严重过高，需要立即处理',
        estimatedCost: 0
      });
    }
    
    if (prediction.load < 0.3 && prediction.confidence > 0.6) {
      actions.push({
        type: 'SCALE_IN',
        priority: 'LOW',
        estimatedImpact: 0.2,
        description: '负载较低，可以考虑缩容',
        estimatedCost: -50
      });
    }
    
    if (prediction.load > 0.6 && prediction.load < 0.8) {
      actions.push({
        type: 'OPTIMIZE',
        priority: 'MEDIUM',
        estimatedImpact: 0.15,
        description: '负载适中，建议优化配置',
        estimatedCost: 20
      });
    }
    
    return actions;
  }

  /**
   * 计算风险等级
   * @param load 预测负载
   * @returns 风险等级
   */
  private calculateRiskLevel(load: number): 'low' | 'medium' | 'high' {
    if (load < 0.5) return 'low';
    if (load < 0.8) return 'medium';
    return 'high';
  }

  /**
   * 记录预测结果
   * @param nodeId 节点ID
   * @param prediction 预测结果
   */
  private recordPrediction(nodeId: string, prediction: LoadPrediction): void {
    this.totalPredictions++;
    
    // 这里应该在实际负载数据到达后验证预测准确性
    // 简化实现，假设有一定的准确率
    if (Math.random() < this.predictionAccuracy) {
      this.correctPredictions++;
    }
    
    // 更新准确率
    this.predictionAccuracy = this.correctPredictions / this.totalPredictions;
  }

  /**
   * 获取当前系统状态
   * @param nodes 节点列表
   * @returns 状态字符串
   */
  private getCurrentState(nodes: EdgeNode[]): string {
    const avgLoad = nodes.reduce((sum, node) => 
      sum + (node.currentLoad.cpuUsage + node.currentLoad.memoryUsage) / 2, 0) / nodes.length;
    
    const loadLevel = avgLoad < 0.3 ? 'low' : avgLoad < 0.7 ? 'medium' : 'high';
    const nodeCount = nodes.length;
    
    return `${loadLevel}_${nodeCount}`;
  }

  /**
   * 选择动作（强化学习）
   * @param state 当前状态
   * @param nodes 可用节点
   * @returns 选择的动作
   */
  private async selectAction(state: string, nodes: EdgeNode[]): Promise<string> {
    if (!this.qTable.has(state)) {
      this.qTable.set(state, new Map());
    }
    
    const stateActions = this.qTable.get(state)!;
    const availableActions = ['round_robin', 'least_load', 'geographic', 'hybrid'];
    
    // ε-贪婪策略
    if (Math.random() < this.explorationRate) {
      // 探索：随机选择动作
      return availableActions[Math.floor(Math.random() * availableActions.length)];
    } else {
      // 利用：选择Q值最高的动作
      let bestAction = availableActions[0];
      let bestValue = stateActions.get(bestAction) || 0;
      
      for (const action of availableActions) {
        const value = stateActions.get(action) || 0;
        if (value > bestValue) {
          bestValue = value;
          bestAction = action;
        }
      }
      
      return bestAction;
    }
  }

  /**
   * 创建分配计划
   * @param requests 请求列表
   * @param nodes 节点列表
   * @param action 选择的动作
   * @returns 分配计划
   */
  private createDistributionPlan(
    requests: Request[],
    nodes: EdgeNode[],
    action: string
  ): DistributionPlan {
    const assignments: Assignment[] = [];
    let totalLatency = 0;
    let totalThroughput = 0;
    
    for (const request of requests) {
      const selectedNode = this.selectNodeForRequest(request, nodes, action);
      const estimatedLatency = this.calculateLatency(request, selectedNode);
      
      assignments.push({
        requestId: request.id,
        nodeId: selectedNode.id,
        estimatedLatency,
        confidence: 0.8,
        priority: request.priority
      });
      
      totalLatency += estimatedLatency;
      totalThroughput += this.calculateThroughput(selectedNode);
    }
    
    return {
      assignments,
      expectedLatency: totalLatency / requests.length,
      expectedThroughput: totalThroughput,
      riskScore: this.calculatePlanRiskScore(assignments, nodes),
      estimatedCost: this.calculatePlanCost(assignments),
      confidence: 0.85
    };
  }

  /**
   * 为请求选择节点
   * @param request 请求
   * @param nodes 可用节点
   * @param strategy 选择策略
   * @returns 选择的节点
   */
  private selectNodeForRequest(request: Request, nodes: EdgeNode[], strategy: string): EdgeNode {
    const availableNodes = nodes.filter(node => 
      node.status === 'online' && 
      node.currentLoad.userCount < node.capabilities.maxUsers
    );
    
    if (availableNodes.length === 0) {
      return nodes[0]; // 回退到第一个节点
    }
    
    switch (strategy) {
      case 'least_load':
        return availableNodes.reduce((best, current) => 
          (current.currentLoad.cpuUsage + current.currentLoad.memoryUsage) < 
          (best.currentLoad.cpuUsage + best.currentLoad.memoryUsage) ? current : best
        );
      
      case 'geographic':
        if (request.location) {
          return availableNodes.reduce((best, current) => {
            const currentDistance = this.calculateDistance(request.location!, current.location);
            const bestDistance = this.calculateDistance(request.location!, best.location);
            return currentDistance < bestDistance ? current : best;
          });
        }
        return availableNodes[0];
      
      case 'hybrid':
        return availableNodes.reduce((best, current) => {
          const currentScore = this.calculateHybridScore(request, current);
          const bestScore = this.calculateHybridScore(request, best);
          return currentScore > bestScore ? current : best;
        });
      
      default: // round_robin
        return availableNodes[Math.floor(Math.random() * availableNodes.length)];
    }
  }

  /**
   * 计算混合评分
   * @param request 请求
   * @param node 节点
   * @returns 评分
   */
  private calculateHybridScore(request: Request, node: EdgeNode): number {
    const loadScore = 1 - (node.currentLoad.cpuUsage + node.currentLoad.memoryUsage) / 2;
    const capacityScore = (node.capabilities.maxUsers - node.currentLoad.userCount) / node.capabilities.maxUsers;
    
    let distanceScore = 1;
    if (request.location) {
      const distance = this.calculateDistance(request.location, node.location);
      distanceScore = 1 / (1 + distance / 1000); // 距离越近分数越高
    }
    
    return loadScore * 0.4 + capacityScore * 0.3 + distanceScore * 0.3;
  }

  /**
   * 计算地理距离
   * @param loc1 位置1
   * @param loc2 位置2
   * @returns 距离（公里）
   */
  private calculateDistance(
    loc1: { latitude: number; longitude: number },
    loc2: { latitude: number; longitude: number }
  ): number {
    const R = 6371; // 地球半径（公里）
    const dLat = (loc2.latitude - loc1.latitude) * Math.PI / 180;
    const dLon = (loc2.longitude - loc1.longitude) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(loc1.latitude * Math.PI / 180) * Math.cos(loc2.latitude * Math.PI / 180) *
              Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  /**
   * 计算延迟
   * @param request 请求
   * @param node 节点
   * @returns 预估延迟（毫秒）
   */
  private calculateLatency(request: Request, node: EdgeNode): number {
    let baseLatency = 10; // 基础延迟
    
    // 根据节点负载调整延迟
    const loadFactor = (node.currentLoad.cpuUsage + node.currentLoad.memoryUsage) / 2;
    baseLatency += loadFactor * 50;
    
    // 根据地理距离调整延迟
    if (request.location) {
      const distance = this.calculateDistance(request.location, node.location);
      baseLatency += distance * 0.1; // 每公里增加0.1ms
    }
    
    return Math.max(5, baseLatency); // 最小延迟5ms
  }

  /**
   * 计算吞吐量
   * @param node 节点
   * @returns 预估吞吐量
   */
  private calculateThroughput(node: EdgeNode): number {
    const loadFactor = 1 - (node.currentLoad.cpuUsage + node.currentLoad.memoryUsage) / 2;
    return loadFactor * 1000; // 基础吞吐量1000 req/s
  }

  /**
   * 计算计划风险评分
   * @param assignments 分配列表
   * @param nodes 节点列表
   * @returns 风险评分
   */
  private calculatePlanRiskScore(assignments: Assignment[], nodes: EdgeNode[]): number {
    let riskScore = 0;
    
    // 计算负载分布风险
    const nodeLoads = new Map<string, number>();
    for (const assignment of assignments) {
      const currentLoad = nodeLoads.get(assignment.nodeId) || 0;
      nodeLoads.set(assignment.nodeId, currentLoad + 1);
    }
    
    // 检查负载不均衡
    const loads = Array.from(nodeLoads.values());
    const avgLoad = loads.reduce((sum, load) => sum + load, 0) / loads.length;
    const variance = loads.reduce((sum, load) => sum + Math.pow(load - avgLoad, 2), 0) / loads.length;
    
    riskScore += variance * 0.1; // 方差越大风险越高
    
    return Math.min(1, riskScore);
  }

  /**
   * 计算计划成本
   * @param assignments 分配列表
   * @returns 预估成本
   */
  private calculatePlanCost(assignments: Assignment[]): number {
    return assignments.length * 0.01; // 每个请求0.01成本单位
  }

  /**
   * 记录决策结果（强化学习）
   * @param state 状态
   * @param action 动作
   * @param plan 分配计划
   */
  private recordDecision(state: string, action: string, plan: DistributionPlan): void {
    if (!this.qTable.has(state)) {
      this.qTable.set(state, new Map());
    }
    
    const stateActions = this.qTable.get(state)!;
    const currentQ = stateActions.get(action) || 0;
    
    // 计算奖励（基于计划质量）
    const reward = this.calculateReward(plan);
    
    // Q-Learning更新
    const newQ = currentQ + this.learningRate * (reward - currentQ);
    stateActions.set(action, newQ);
    
    this.logger.debug(`Q-Learning更新: 状态=${state}, 动作=${action}, 奖励=${reward.toFixed(2)}, Q值=${newQ.toFixed(2)}`);
  }

  /**
   * 计算奖励
   * @param plan 分配计划
   * @returns 奖励值
   */
  private calculateReward(plan: DistributionPlan): number {
    let reward = 0;
    
    // 延迟越低奖励越高
    reward += Math.max(0, 100 - plan.expectedLatency) / 100;
    
    // 吞吐量越高奖励越高
    reward += Math.min(1, plan.expectedThroughput / 1000);
    
    // 风险越低奖励越高
    reward += 1 - plan.riskScore;
    
    // 成本越低奖励越高
    reward += Math.max(0, 1 - plan.estimatedCost / 10);
    
    return reward / 4; // 归一化到 [0, 1]
  }

  /**
   * 更新模型权重
   * @param nodeId 节点ID
   * @param loadData 负载数据
   */
  private updateModelWeights(nodeId: string, loadData: LoadData): void {
    // 简化的在线学习更新
    const features = [loadData.cpuUsage, loadData.memoryUsage, loadData.networkUsage, 
                     loadData.userCount / 100, new Date().getHours() / 24];
    const target = loadData.responseTime / 1000; // 归一化响应时间作为目标
    
    const featureNames = ['cpu', 'memory', 'network', 'users', 'time'];
    
    for (let i = 0; i < features.length; i++) {
      const weights = this.modelWeights.get(featureNames[i]) || [0, 0, 0];
      const prediction = features[i] * weights[0] + Math.pow(features[i], 2) * weights[1] + weights[2];
      const error = target - prediction;
      
      // 梯度下降更新
      weights[0] += this.learningRate * error * features[i];
      weights[1] += this.learningRate * error * Math.pow(features[i], 2);
      weights[2] += this.learningRate * error;
      
      this.modelWeights.set(featureNames[i], weights);
    }
  }

  /**
   * 获取调度统计信息
   * @returns 统计信息
   */
  getStatistics(): any {
    return {
      totalPredictions: this.totalPredictions,
      predictionAccuracy: this.predictionAccuracy,
      qTableSize: this.qTable.size,
      historicalDataSize: Array.from(this.historicalData.values())
        .reduce((sum, data) => sum + data.length, 0),
      learningRate: this.learningRate,
      explorationRate: this.explorationRate
    };
  }

  /**
   * 重置学习参数
   */
  resetLearning(): void {
    this.qTable.clear();
    this.totalPredictions = 0;
    this.correctPredictions = 0;
    this.predictionAccuracy = 0.85;
    this.logger.log('学习参数已重置');
  }
}
