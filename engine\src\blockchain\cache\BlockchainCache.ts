/**
 * 区块链缓存系统
 */

import { EventEmitter } from '../../utils/EventEmitter';
import { PerformanceMonitor } from '../monitoring/PerformanceMonitor';

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

export interface CacheConfig {
  maxSize: number;
  defaultTTL: number;
  cleanupInterval: number;
  enableCompression: boolean;
  enablePersistence: boolean;
}

export class BlockchainCache extends EventEmitter {
  private cache: Map<string, CacheEntry<any>> = new Map();
  private config: CacheConfig;
  private performanceMonitor?: PerformanceMonitor;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private compressionEnabled: boolean = false;

  constructor(config: Partial<CacheConfig> = {}, performanceMonitor?: PerformanceMonitor) {
    super();
    
    this.config = {
      maxSize: 1000,
      defaultTTL: 5 * 60 * 1000, // 5分钟
      cleanupInterval: 60 * 1000, // 1分钟
      enableCompression: false,
      enablePersistence: false,
      ...config,
    };
    
    this.performanceMonitor = performanceMonitor;
    this.compressionEnabled = this.config.enableCompression && this.isCompressionSupported();
    
    this.startCleanupTimer();
    
    if (this.config.enablePersistence) {
      this.loadFromPersistence();
    }
  }

  /**
   * 设置缓存项
   */
  set<T>(key: string, data: T, ttl?: number): void {
    const now = Date.now();
    const entryTTL = ttl || this.config.defaultTTL;
    
    // 检查缓存大小限制
    if (this.cache.size >= this.config.maxSize && !this.cache.has(key)) {
      this.evictLRU();
    }

    const entry: CacheEntry<T> = {
      data: this.compressionEnabled ? this.compress(data) : data,
      timestamp: now,
      ttl: entryTTL,
      accessCount: 0,
      lastAccessed: now,
    };

    this.cache.set(key, entry);
    
    this.emit('cacheSet', { key, size: this.cache.size });
  }

  /**
   * 获取缓存项
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.performanceMonitor?.recordCacheMiss();
      return null;
    }

    const now = Date.now();
    
    // 检查是否过期
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.performanceMonitor?.recordCacheMiss();
      return null;
    }

    // 更新访问统计
    entry.accessCount++;
    entry.lastAccessed = now;
    
    this.performanceMonitor?.recordCacheHit();
    
    const data = this.compressionEnabled ? this.decompress(entry.data) : entry.data;
    return data;
  }

  /**
   * 检查缓存项是否存在且未过期
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return false;
    }

    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * 删除缓存项
   */
  delete(key: string): boolean {
    const result = this.cache.delete(key);
    
    if (result) {
      this.emit('cacheDelete', { key, size: this.cache.size });
    }
    
    return result;
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.emit('cacheClear');
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    memoryUsage: number;
    oldestEntry: number;
    newestEntry: number;
  } {
    let totalAccess = 0;
    let totalHits = 0;
    let oldestTimestamp = Date.now();
    let newestTimestamp = 0;
    let memoryUsage = 0;

    for (const [key, entry] of this.cache) {
      totalAccess += entry.accessCount;
      totalHits += entry.accessCount;
      
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
      }
      
      if (entry.timestamp > newestTimestamp) {
        newestTimestamp = entry.timestamp;
      }

      // 估算内存使用（简化计算）
      memoryUsage += this.estimateSize(key) + this.estimateSize(entry);
    }

    const hitRate = totalAccess > 0 ? (totalHits / totalAccess) * 100 : 0;

    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hitRate,
      memoryUsage,
      oldestEntry: oldestTimestamp,
      newestEntry: newestTimestamp,
    };
  }

  /**
   * 获取热门缓存项
   */
  getHotKeys(limit: number = 10): Array<{ key: string; accessCount: number; lastAccessed: number }> {
    const entries = Array.from(this.cache.entries())
      .map(([key, entry]) => ({
        key,
        accessCount: entry.accessCount,
        lastAccessed: entry.lastAccessed,
      }))
      .sort((a, b) => b.accessCount - a.accessCount)
      .slice(0, limit);

    return entries;
  }

  /**
   * 预热缓存
   */
  async warmup(keys: string[], dataLoader: (key: string) => Promise<any>): Promise<void> {
    console.log(`开始预热缓存，共 ${keys.length} 个键`);
    
    const promises = keys.map(async (key) => {
      try {
        if (!this.has(key)) {
          const data = await dataLoader(key);
          this.set(key, data);
        }
      } catch (error) {
        console.error(`预热缓存失败，键: ${key}`, error);
      }
    });

    await Promise.all(promises);
    console.log('缓存预热完成');
  }

  /**
   * 批量设置缓存
   */
  setBatch<T>(entries: Array<{ key: string; data: T; ttl?: number }>): void {
    for (const entry of entries) {
      this.set(entry.key, entry.data, entry.ttl);
    }
  }

  /**
   * 批量获取缓存
   */
  getBatch<T>(keys: string[]): Map<string, T | null> {
    const result = new Map<string, T | null>();
    
    for (const key of keys) {
      result.set(key, this.get<T>(key));
    }
    
    return result;
  }

  /**
   * 设置缓存配置
   */
  updateConfig(config: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...config };
    
    // 如果最大大小减少，需要清理缓存
    if (this.cache.size > this.config.maxSize) {
      this.evictToSize(this.config.maxSize);
    }
  }

  /**
   * 销毁缓存
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    if (this.config.enablePersistence) {
      this.saveToPersistence();
    }

    this.cache.clear();
    this.removeAllListeners();
  }

  /**
   * LRU淘汰策略
   */
  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.emit('cacheEvict', { key: oldestKey, reason: 'LRU' });
    }
  }

  /**
   * 淘汰到指定大小
   */
  private evictToSize(targetSize: number): void {
    while (this.cache.size > targetSize) {
      this.evictLRU();
    }
  }

  /**
   * 清理过期项
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache) {
      if (now - entry.timestamp > entry.ttl) {
        expiredKeys.push(key);
      }
    }

    for (const key of expiredKeys) {
      this.cache.delete(key);
      this.emit('cacheEvict', { key, reason: 'expired' });
    }

    if (expiredKeys.length > 0) {
      console.log(`清理了 ${expiredKeys.length} 个过期缓存项`);
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * 检查是否支持压缩
   */
  private isCompressionSupported(): boolean {
    return typeof CompressionStream !== 'undefined' && typeof DecompressionStream !== 'undefined';
  }

  /**
   * 压缩数据
   */
  private compress(data: any): any {
    if (!this.compressionEnabled) {
      return data;
    }

    try {
      const jsonString = JSON.stringify(data);
      // 这里应该实现实际的压缩逻辑
      // 由于浏览器环境限制，这里只是示例
      return jsonString;
    } catch (error) {
      console.error('压缩数据失败:', error);
      return data;
    }
  }

  /**
   * 解压数据
   */
  private decompress(compressedData: any): any {
    if (!this.compressionEnabled) {
      return compressedData;
    }

    try {
      // 这里应该实现实际的解压逻辑
      return JSON.parse(compressedData);
    } catch (error) {
      console.error('解压数据失败:', error);
      return compressedData;
    }
  }

  /**
   * 估算数据大小
   */
  private estimateSize(data: any): number {
    try {
      return JSON.stringify(data).length * 2; // 粗略估算（UTF-16）
    } catch {
      return 0;
    }
  }

  /**
   * 从持久化存储加载
   */
  private loadFromPersistence(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        const stored = localStorage.getItem('blockchain_cache');
        if (stored) {
          const data = JSON.parse(stored);
          for (const [key, entry] of Object.entries(data)) {
            this.cache.set(key, entry as CacheEntry<any>);
          }
          console.log(`从持久化存储加载了 ${this.cache.size} 个缓存项`);
        }
      }
    } catch (error) {
      console.error('从持久化存储加载缓存失败:', error);
    }
  }

  /**
   * 保存到持久化存储
   */
  private saveToPersistence(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        const data = Object.fromEntries(this.cache);
        localStorage.setItem('blockchain_cache', JSON.stringify(data));
        console.log(`保存了 ${this.cache.size} 个缓存项到持久化存储`);
      }
    } catch (error) {
      console.error('保存缓存到持久化存储失败:', error);
    }
  }
}
