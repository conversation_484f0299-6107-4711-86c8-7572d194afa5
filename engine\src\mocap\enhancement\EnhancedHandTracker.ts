/**
 * 增强手部追踪器
 * 提供更精确的手部追踪、精细手指动作识别和手部状态分析
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { LandmarkData } from '../types/LandmarkData';
import { HandResults } from '../mediapipe/MediaPipePoseDetector';
import { AdvancedGestureRecognizer, AdvancedGestureResult, AdvancedGestureType } from './AdvancedGestureRecognizer';
import { Vector3, Quaternion, Matrix4 } from 'three';

/**
 * 手指状态
 */
export enum FingerState {
  EXTENDED = 'extended',
  BENT = 'bent',
  CURVED = 'curved',
  TOUCHING = 'touching'
}

/**
 * 手部姿态
 */
export interface HandPose {
  /** 手腕位置 */
  wristPosition: Vector3;
  /** 手腕旋转 */
  wristRotation: Quaternion;
  /** 手掌方向 */
  palmDirection: Vector3;
  /** 手掌法向量 */
  palmNormal: Vector3;
  /** 手指状态 */
  fingerStates: {
    thumb: FingerState;
    index: FingerState;
    middle: FingerState;
    ring: FingerState;
    pinky: FingerState;
  };
  /** 手指位置 */
  fingerPositions: {
    thumb: Vector3[];
    index: Vector3[];
    middle: Vector3[];
    ring: Vector3[];
    pinky: Vector3[];
  };
  /** 手部开合度 */
  openness: number;
  /** 手部紧张度 */
  tension: number;
}

/**
 * 手指关节角度
 */
export interface FingerJointAngles {
  /** MCP关节角度 */
  mcp: number;
  /** PIP关节角度 */
  pip: number;
  /** DIP关节角度 */
  dip: number;
  /** 总弯曲角度 */
  totalBend: number;
}

/**
 * 手部运动分析
 */
export interface HandMotionAnalysis {
  /** 速度 */
  velocity: Vector3;
  /** 加速度 */
  acceleration: Vector3;
  /** 角速度 */
  angularVelocity: Vector3;
  /** 运动模式 */
  motionPattern: HandMotionPattern;
  /** 稳定性 */
  stability: number;
  /** 颤抖程度 */
  tremor: number;
}

/**
 * 手部运动模式
 */
export enum HandMotionPattern {
  STATIC = 'static',
  LINEAR = 'linear',
  CIRCULAR = 'circular',
  OSCILLATING = 'oscillating',
  RANDOM = 'random'
}

/**
 * 增强手部追踪配置
 */
export interface EnhancedHandTrackingConfig {
  /** 是否启用精细手指追踪 */
  enableFineFingerTracking: boolean;
  /** 是否启用手部运动分析 */
  enableMotionAnalysis: boolean;
  /** 是否启用手部姿态估计 */
  enablePoseEstimation: boolean;
  /** 是否启用手指碰撞检测 */
  enableFingerCollisionDetection: boolean;
  /** 平滑系数 */
  smoothingFactor: number;
  /** 运动历史大小 */
  motionHistorySize: number;
  /** 稳定性阈值 */
  stabilityThreshold: number;
  /** 颤抖检测阈值 */
  tremorThreshold: number;
  /** 手指弯曲阈值 */
  fingerBendThreshold: number;
}

/**
 * 增强手部追踪器
 */
export class EnhancedHandTracker extends EventEmitter {
  private config: EnhancedHandTrackingConfig;
  private gestureRecognizer: AdvancedGestureRecognizer;
  private handHistory: Map<string, HandPose[]> = new Map();
  private motionHistory: Map<string, HandMotionAnalysis[]> = new Map();
  private lastProcessTime = 0;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: EnhancedHandTrackingConfig = {
    enableFineFingerTracking: true,
    enableMotionAnalysis: true,
    enablePoseEstimation: true,
    enableFingerCollisionDetection: true,
    smoothingFactor: 0.7,
    motionHistorySize: 30,
    stabilityThreshold: 0.01,
    tremorThreshold: 0.005,
    fingerBendThreshold: 0.5
  };

  constructor(config: Partial<EnhancedHandTrackingConfig> = {}) {
    super();
    this.config = { ...EnhancedHandTracker.DEFAULT_CONFIG, ...config };
    this.gestureRecognizer = new AdvancedGestureRecognizer();
  }

  /**
   * 处理手部追踪
   */
  public processHandTracking(handResults: HandResults): {
    leftHand?: EnhancedHandData;
    rightHand?: EnhancedHandData;
  } {
    const result: { leftHand?: EnhancedHandData; rightHand?: EnhancedHandData } = {};

    try {
      // 处理左手
      if (handResults.leftHand) {
        result.leftHand = this.processHand(handResults.leftHand, 'left');
      }

      // 处理右手
      if (handResults.rightHand) {
        result.rightHand = this.processHand(handResults.rightHand, 'right');
      }

      // 检测手指碰撞
      if (this.config.enableFingerCollisionDetection && result.leftHand && result.rightHand) {
        this.detectFingerCollisions(result.leftHand, result.rightHand);
      }

      this.emit('handTrackingProcessed', result);
      return result;

    } catch (error) {
      Debug.error('EnhancedHandTracker', '手部追踪处理失败', error);
      return {};
    }
  }

  /**
   * 处理单手数据
   */
  private processHand(handLandmarks: LandmarkData[], handType: 'left' | 'right'): EnhancedHandData {
    // 计算手部姿态
    const handPose = this.calculateHandPose(handLandmarks);
    
    // 分析手指关节角度
    const fingerAngles = this.analyzeFingerJointAngles(handLandmarks);
    
    // 运动分析
    let motionAnalysis: HandMotionAnalysis | null = null;
    if (this.config.enableMotionAnalysis) {
      motionAnalysis = this.analyzeHandMotion(handPose, handType);
    }
    
    // 手势识别
    const gesture = this.gestureRecognizer.recognizeGesture(handLandmarks, handType);
    
    // 更新历史数据
    this.updateHandHistory(handType, handPose);
    if (motionAnalysis) {
      this.updateMotionHistory(handType, motionAnalysis);
    }

    return {
      handType,
      landmarks: handLandmarks,
      pose: handPose,
      fingerAngles,
      motionAnalysis,
      gesture,
      timestamp: Date.now()
    };
  }

  /**
   * 计算手部姿态
   */
  private calculateHandPose(handLandmarks: LandmarkData[]): HandPose {
    // 计算手腕位置和旋转
    const wristPosition = this.calculateWristPosition(handLandmarks);
    const wristRotation = this.calculateWristRotation(handLandmarks);
    
    // 计算手掌方向和法向量
    const palmDirection = this.calculatePalmDirection(handLandmarks);
    const palmNormal = this.calculatePalmNormal(handLandmarks);
    
    // 分析手指状态
    const fingerStates = this.analyzeFingerStates(handLandmarks);
    
    // 计算手指位置
    const fingerPositions = this.calculateFingerPositions(handLandmarks);
    
    // 计算手部开合度和紧张度
    const openness = this.calculateHandOpenness(handLandmarks);
    const tension = this.calculateHandTension(handLandmarks);

    return {
      wristPosition,
      wristRotation,
      palmDirection,
      palmNormal,
      fingerStates,
      fingerPositions,
      openness,
      tension
    };
  }

  /**
   * 计算手腕位置
   */
  private calculateWristPosition(handLandmarks: LandmarkData[]): Vector3 {
    const wrist = handLandmarks[0]; // MediaPipe手腕关键点
    return new Vector3(
      (wrist.x - 0.5) * 2,
      -(wrist.y - 0.5) * 2,
      wrist.z || 0
    );
  }

  /**
   * 计算手腕旋转
   */
  private calculateWristRotation(handLandmarks: LandmarkData[]): Quaternion {
    const wrist = handLandmarks[0];
    const middleMcp = handLandmarks[9];
    const indexMcp = handLandmarks[5];

    if (!wrist || !middleMcp || !indexMcp) {
      return new Quaternion();
    }

    // 计算手部坐标系
    const forward = new Vector3(
      middleMcp.x - wrist.x,
      middleMcp.y - wrist.y,
      (middleMcp.z || 0) - (wrist.z || 0)
    ).normalize();

    const right = new Vector3(
      indexMcp.x - wrist.x,
      indexMcp.y - wrist.y,
      (indexMcp.z || 0) - (wrist.z || 0)
    ).normalize();

    const up = new Vector3().crossVectors(forward, right).normalize();

    // 构建旋转矩阵并转换为四元数
    const matrix = new Matrix4().makeBasis(right, up, forward);
    return new Quaternion().setFromRotationMatrix(matrix);
  }

  /**
   * 计算手掌方向
   */
  private calculatePalmDirection(handLandmarks: LandmarkData[]): Vector3 {
    const wrist = handLandmarks[0];
    const middleMcp = handLandmarks[9];

    if (!wrist || !middleMcp) {
      return new Vector3(0, 1, 0);
    }

    return new Vector3(
      middleMcp.x - wrist.x,
      middleMcp.y - wrist.y,
      (middleMcp.z || 0) - (wrist.z || 0)
    ).normalize();
  }

  /**
   * 计算手掌法向量
   */
  private calculatePalmNormal(handLandmarks: LandmarkData[]): Vector3 {
    const wrist = handLandmarks[0];
    const indexMcp = handLandmarks[5];
    const pinkyMcp = handLandmarks[17];

    if (!wrist || !indexMcp || !pinkyMcp) {
      return new Vector3(0, 0, 1);
    }

    const vec1 = new Vector3(
      indexMcp.x - wrist.x,
      indexMcp.y - wrist.y,
      (indexMcp.z || 0) - (wrist.z || 0)
    );

    const vec2 = new Vector3(
      pinkyMcp.x - wrist.x,
      pinkyMcp.y - wrist.y,
      (pinkyMcp.z || 0) - (wrist.z || 0)
    );

    return new Vector3().crossVectors(vec1, vec2).normalize();
  }

  /**
   * 分析手指状态
   */
  private analyzeFingerStates(handLandmarks: LandmarkData[]): any {
    const fingerIndices = {
      thumb: [1, 2, 3, 4],
      index: [5, 6, 7, 8],
      middle: [9, 10, 11, 12],
      ring: [13, 14, 15, 16],
      pinky: [17, 18, 19, 20]
    };

    const states: any = {};

    for (const [fingerName, indices] of Object.entries(fingerIndices)) {
      const fingerLandmarks = indices.map(i => handLandmarks[i]).filter(Boolean);
      if (fingerLandmarks.length >= 3) {
        states[fingerName] = this.analyzeFingerState(fingerLandmarks);
      } else {
        states[fingerName] = FingerState.EXTENDED;
      }
    }

    return states;
  }

  /**
   * 分析单个手指状态
   */
  private analyzeFingerState(fingerLandmarks: LandmarkData[]): FingerState {
    if (fingerLandmarks.length < 3) return FingerState.EXTENDED;

    // 计算手指弯曲度
    const bendAngle = this.calculateFingerBendAngle(fingerLandmarks);
    
    if (bendAngle > this.config.fingerBendThreshold * 1.5) {
      return FingerState.BENT;
    } else if (bendAngle > this.config.fingerBendThreshold) {
      return FingerState.CURVED;
    } else {
      return FingerState.EXTENDED;
    }
  }

  /**
   * 计算手指弯曲角度
   */
  private calculateFingerBendAngle(fingerLandmarks: LandmarkData[]): number {
    if (fingerLandmarks.length < 3) return 0;

    const mcp = fingerLandmarks[0];
    const pip = fingerLandmarks[1];
    const tip = fingerLandmarks[fingerLandmarks.length - 1];

    const vec1 = new Vector3(pip.x - mcp.x, pip.y - mcp.y, (pip.z || 0) - (mcp.z || 0));
    const vec2 = new Vector3(tip.x - pip.x, tip.y - pip.y, (tip.z || 0) - (pip.z || 0));

    return vec1.angleTo(vec2);
  }

  /**
   * 计算手指位置
   */
  private calculateFingerPositions(handLandmarks: LandmarkData[]): any {
    const fingerIndices = {
      thumb: [1, 2, 3, 4],
      index: [5, 6, 7, 8],
      middle: [9, 10, 11, 12],
      ring: [13, 14, 15, 16],
      pinky: [17, 18, 19, 20]
    };

    const positions: any = {};

    for (const [fingerName, indices] of Object.entries(fingerIndices)) {
      positions[fingerName] = indices.map(i => {
        const landmark = handLandmarks[i];
        return landmark ? new Vector3(
          (landmark.x - 0.5) * 2,
          -(landmark.y - 0.5) * 2,
          landmark.z || 0
        ) : new Vector3();
      });
    }

    return positions;
  }

  /**
   * 计算手部开合度
   */
  private calculateHandOpenness(handLandmarks: LandmarkData[]): number {
    const fingerTips = [4, 8, 12, 16, 20];
    const wrist = handLandmarks[0];

    if (!wrist) return 0;

    let totalDistance = 0;
    let validFingers = 0;

    for (const tipIndex of fingerTips) {
      const tip = handLandmarks[tipIndex];
      if (tip) {
        const distance = Math.sqrt(
          Math.pow(tip.x - wrist.x, 2) + 
          Math.pow(tip.y - wrist.y, 2)
        );
        totalDistance += distance;
        validFingers++;
      }
    }

    return validFingers > 0 ? totalDistance / validFingers : 0;
  }

  /**
   * 计算手部紧张度
   */
  private calculateHandTension(handLandmarks: LandmarkData[]): number {
    // 基于手指弯曲度和手掌紧缩程度计算紧张度
    const fingerTips = [4, 8, 12, 16, 20];
    const fingerMcps = [2, 5, 9, 13, 17];
    
    let totalTension = 0;
    let validPairs = 0;

    for (let i = 0; i < fingerTips.length; i++) {
      const tip = handLandmarks[fingerTips[i]];
      const mcp = handLandmarks[fingerMcps[i]];
      
      if (tip && mcp) {
        const distance = Math.sqrt(
          Math.pow(tip.x - mcp.x, 2) + 
          Math.pow(tip.y - mcp.y, 2)
        );
        totalTension += 1 - distance; // 距离越小，紧张度越高
        validPairs++;
      }
    }

    return validPairs > 0 ? Math.max(0, totalTension / validPairs) : 0;
  }

  /**
   * 分析手指关节角度
   */
  private analyzeFingerJointAngles(handLandmarks: LandmarkData[]): {
    thumb: FingerJointAngles;
    index: FingerJointAngles;
    middle: FingerJointAngles;
    ring: FingerJointAngles;
    pinky: FingerJointAngles;
  } {
    const fingerIndices = {
      thumb: [1, 2, 3, 4],
      index: [5, 6, 7, 8],
      middle: [9, 10, 11, 12],
      ring: [13, 14, 15, 16],
      pinky: [17, 18, 19, 20]
    };

    const angles: any = {};

    for (const [fingerName, indices] of Object.entries(fingerIndices)) {
      const fingerLandmarks = indices.map(i => handLandmarks[i]).filter(Boolean);
      angles[fingerName] = this.calculateFingerJointAngles(fingerLandmarks);
    }

    return angles;
  }

  /**
   * 计算单个手指的关节角度
   */
  private calculateFingerJointAngles(fingerLandmarks: LandmarkData[]): FingerJointAngles {
    if (fingerLandmarks.length < 4) {
      return { mcp: 0, pip: 0, dip: 0, totalBend: 0 };
    }

    const [mcp, pip, dip, tip] = fingerLandmarks;

    // 计算MCP关节角度
    const mcpAngle = this.calculateJointAngle(mcp, pip, dip);

    // 计算PIP关节角度
    const pipAngle = this.calculateJointAngle(pip, dip, tip);

    // DIP关节角度（简化计算）
    const dipAngle = pipAngle * 0.6; // DIP通常是PIP的60%

    // 总弯曲角度
    const totalBend = mcpAngle + pipAngle + dipAngle;

    return {
      mcp: mcpAngle,
      pip: pipAngle,
      dip: dipAngle,
      totalBend
    };
  }

  /**
   * 计算关节角度
   */
  private calculateJointAngle(joint1: LandmarkData, joint2: LandmarkData, joint3: LandmarkData): number {
    const vec1 = new Vector3(
      joint1.x - joint2.x,
      joint1.y - joint2.y,
      (joint1.z || 0) - (joint2.z || 0)
    );

    const vec2 = new Vector3(
      joint3.x - joint2.x,
      joint3.y - joint2.y,
      (joint3.z || 0) - (joint2.z || 0)
    );

    return vec1.angleTo(vec2);
  }

  /**
   * 分析手部运动
   */
  private analyzeHandMotion(handPose: HandPose, handType: 'left' | 'right'): HandMotionAnalysis {
    const history = this.handHistory.get(handType) || [];

    if (history.length < 2) {
      return {
        velocity: new Vector3(),
        acceleration: new Vector3(),
        angularVelocity: new Vector3(),
        motionPattern: HandMotionPattern.STATIC,
        stability: 1.0,
        tremor: 0.0
      };
    }

    // 计算速度
    const velocity = this.calculateVelocity(history);

    // 计算加速度
    const acceleration = this.calculateAcceleration(history);

    // 计算角速度
    const angularVelocity = this.calculateAngularVelocity(history);

    // 分析运动模式
    const motionPattern = this.analyzeMotionPattern(history);

    // 计算稳定性
    const stability = this.calculateStability(history);

    // 检测颤抖
    const tremor = this.detectTremor(history);

    return {
      velocity,
      acceleration,
      angularVelocity,
      motionPattern,
      stability,
      tremor
    };
  }

  /**
   * 计算速度
   */
  private calculateVelocity(history: HandPose[]): Vector3 {
    if (history.length < 2) return new Vector3();

    const recent = history.slice(-5); // 使用最近5帧
    if (recent.length < 2) return new Vector3();

    const totalDisplacement = recent[recent.length - 1].wristPosition.clone()
      .sub(recent[0].wristPosition);

    const timeSpan = (recent.length - 1) * 33; // 假设33ms每帧

    return totalDisplacement.divideScalar(timeSpan / 1000);
  }

  /**
   * 计算加速度
   */
  private calculateAcceleration(history: HandPose[]): Vector3 {
    if (history.length < 3) return new Vector3();

    const velocities: Vector3[] = [];

    for (let i = 1; i < Math.min(history.length, 6); i++) {
      const displacement = history[i].wristPosition.clone().sub(history[i - 1].wristPosition);
      velocities.push(displacement.divideScalar(0.033)); // 33ms每帧
    }

    if (velocities.length < 2) return new Vector3();

    const velocityChange = velocities[velocities.length - 1].clone()
      .sub(velocities[0]);

    return velocityChange.divideScalar((velocities.length - 1) * 0.033);
  }

  /**
   * 计算角速度
   */
  private calculateAngularVelocity(history: HandPose[]): Vector3 {
    if (history.length < 2) return new Vector3();

    const recent = history.slice(-3);
    if (recent.length < 2) return new Vector3();

    // 简化的角速度计算
    const rotationChange = recent[recent.length - 1].wristRotation.clone()
      .multiply(recent[0].wristRotation.clone().invert());

    const axis = new Vector3();
    const angle = 2 * Math.acos(Math.abs(rotationChange.w));

    // 计算旋转轴
    const sinHalfAngle = Math.sqrt(1 - rotationChange.w * rotationChange.w);
    if (sinHalfAngle > 0.001) {
      axis.set(
        rotationChange.x / sinHalfAngle,
        rotationChange.y / sinHalfAngle,
        rotationChange.z / sinHalfAngle
      );
    } else {
      axis.set(1, 0, 0); // 默认轴
    }

    const timeSpan = (recent.length - 1) * 0.033;

    return axis.multiplyScalar(angle / timeSpan);
  }

  /**
   * 分析运动模式
   */
  private analyzeMotionPattern(history: HandPose[]): HandMotionPattern {
    if (history.length < 5) return HandMotionPattern.STATIC;

    const positions = history.slice(-10).map(pose => pose.wristPosition);

    // 计算总位移
    const totalDisplacement = positions[positions.length - 1].clone()
      .sub(positions[0]).length();

    if (totalDisplacement < 0.01) {
      return HandMotionPattern.STATIC;
    }

    // 分析运动轨迹
    const directions: Vector3[] = [];
    for (let i = 1; i < positions.length; i++) {
      const direction = positions[i].clone().sub(positions[i - 1]).normalize();
      directions.push(direction);
    }

    // 检测线性运动
    if (this.isLinearMotion(directions)) {
      return HandMotionPattern.LINEAR;
    }

    // 检测圆形运动
    if (this.isCircularMotion(positions)) {
      return HandMotionPattern.CIRCULAR;
    }

    // 检测振荡运动
    if (this.isOscillatingMotion(positions)) {
      return HandMotionPattern.OSCILLATING;
    }

    return HandMotionPattern.RANDOM;
  }

  /**
   * 检测线性运动
   */
  private isLinearMotion(directions: Vector3[]): boolean {
    if (directions.length < 3) return false;

    let consistentDirection = 0;
    const threshold = 0.8;

    for (let i = 1; i < directions.length; i++) {
      const similarity = directions[i].dot(directions[i - 1]);
      if (similarity > threshold) {
        consistentDirection++;
      }
    }

    return consistentDirection / (directions.length - 1) > 0.7;
  }

  /**
   * 检测圆形运动
   */
  private isCircularMotion(positions: Vector3[]): boolean {
    if (positions.length < 8) return false;

    // 计算中心点
    const center = positions.reduce((sum, pos) => sum.add(pos), new Vector3())
      .divideScalar(positions.length);

    // 检查距离变化
    const distances = positions.map(pos => pos.distanceTo(center));
    const avgDistance = distances.reduce((sum, d) => sum + d, 0) / distances.length;
    const variance = distances.reduce((sum, d) => sum + Math.pow(d - avgDistance, 2), 0) / distances.length;

    return variance / (avgDistance * avgDistance) < 0.1; // 距离变化小于10%
  }

  /**
   * 检测振荡运动
   */
  private isOscillatingMotion(positions: Vector3[]): boolean {
    if (positions.length < 6) return false;

    // 检测方向变化
    let directionChanges = 0;
    let lastDirection = new Vector3();

    for (let i = 1; i < positions.length; i++) {
      const currentDirection = positions[i].clone().sub(positions[i - 1]).normalize();

      if (i > 1) {
        const dot = currentDirection.dot(lastDirection);
        if (dot < 0) { // 方向相反
          directionChanges++;
        }
      }

      lastDirection = currentDirection;
    }

    return directionChanges >= 2; // 至少2次方向变化
  }

  /**
   * 计算稳定性
   */
  private calculateStability(history: HandPose[]): number {
    if (history.length < 3) return 1.0;

    const recent = history.slice(-10);
    const positions = recent.map(pose => pose.wristPosition);

    // 计算位置方差
    const center = positions.reduce((sum, pos) => sum.add(pos), new Vector3())
      .divideScalar(positions.length);

    const variance = positions.reduce((sum, pos) => {
      return sum + pos.distanceToSquared(center);
    }, 0) / positions.length;

    // 稳定性与方差成反比
    return Math.max(0, 1 - variance / this.config.stabilityThreshold);
  }

  /**
   * 检测颤抖
   */
  private detectTremor(history: HandPose[]): number {
    if (history.length < 5) return 0;

    const recent = history.slice(-15);
    const positions = recent.map(pose => pose.wristPosition);

    // 计算高频振动
    let totalVariation = 0;
    for (let i = 2; i < positions.length; i++) {
      const acceleration = positions[i].clone()
        .sub(positions[i - 1].clone().multiplyScalar(2))
        .add(positions[i - 2]);

      totalVariation += acceleration.length();
    }

    const avgVariation = totalVariation / (positions.length - 2);

    // 颤抖程度
    return Math.min(1, avgVariation / this.config.tremorThreshold);
  }

  /**
   * 检测手指碰撞
   */
  private detectFingerCollisions(leftHand: EnhancedHandData, rightHand: EnhancedHandData): void {
    const collisionThreshold = 0.05; // 5cm
    const collisions: FingerCollision[] = [];

    // 检查所有手指组合
    const fingerNames = ['thumb', 'index', 'middle', 'ring', 'pinky'];

    for (const leftFinger of fingerNames) {
      for (const rightFinger of fingerNames) {
        const leftPositions = leftHand.pose.fingerPositions[leftFinger];
        const rightPositions = rightHand.pose.fingerPositions[rightFinger];

        for (let i = 0; i < leftPositions.length; i++) {
          for (let j = 0; j < rightPositions.length; j++) {
            const distance = leftPositions[i].distanceTo(rightPositions[j]);

            if (distance < collisionThreshold) {
              collisions.push({
                leftFinger,
                rightFinger,
                leftJoint: i,
                rightJoint: j,
                distance,
                timestamp: Date.now()
              });
            }
          }
        }
      }
    }

    if (collisions.length > 0) {
      this.emit('fingerCollisions', collisions);
    }
  }

  /**
   * 更新手部历史
   */
  private updateHandHistory(handType: 'left' | 'right', handPose: HandPose): void {
    if (!this.handHistory.has(handType)) {
      this.handHistory.set(handType, []);
    }

    const history = this.handHistory.get(handType)!;
    history.push(handPose);

    // 限制历史大小
    if (history.length > this.config.motionHistorySize) {
      history.shift();
    }
  }

  /**
   * 更新运动历史
   */
  private updateMotionHistory(handType: 'left' | 'right', motionAnalysis: HandMotionAnalysis): void {
    if (!this.motionHistory.has(handType)) {
      this.motionHistory.set(handType, []);
    }

    const history = this.motionHistory.get(handType)!;
    history.push(motionAnalysis);

    // 限制历史大小
    if (history.length > this.config.motionHistorySize) {
      history.shift();
    }
  }

  /**
   * 获取手部历史
   */
  public getHandHistory(handType: 'left' | 'right'): HandPose[] {
    return this.handHistory.get(handType) || [];
  }

  /**
   * 获取运动历史
   */
  public getMotionHistory(handType: 'left' | 'right'): HandMotionAnalysis[] {
    return this.motionHistory.get(handType) || [];
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<EnhancedHandTrackingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  /**
   * 获取配置
   */
  public getConfig(): EnhancedHandTrackingConfig {
    return { ...this.config };
  }

  /**
   * 重置追踪器
   */
  public reset(): void {
    this.handHistory.clear();
    this.motionHistory.clear();
    this.gestureRecognizer.reset();
    this.emit('reset');
  }

  /**
   * 获取追踪统计
   */
  public getTrackingStats(): any {
    return {
      leftHandHistory: this.handHistory.get('left')?.length || 0,
      rightHandHistory: this.handHistory.get('right')?.length || 0,
      leftMotionHistory: this.motionHistory.get('left')?.length || 0,
      rightMotionHistory: this.motionHistory.get('right')?.length || 0,
      gestureStats: this.gestureRecognizer.getRecognitionStats()
    };
  }
}

/**
 * 增强手部数据
 */
export interface EnhancedHandData {
  handType: 'left' | 'right';
  landmarks: LandmarkData[];
  pose: HandPose;
  fingerAngles: {
    thumb: FingerJointAngles;
    index: FingerJointAngles;
    middle: FingerJointAngles;
    ring: FingerJointAngles;
    pinky: FingerJointAngles;
  };
  motionAnalysis: HandMotionAnalysis | null;
  gesture: AdvancedGestureResult | null;
  timestamp: number;
}

/**
 * 手指碰撞信息
 */
export interface FingerCollision {
  leftFinger: string;
  rightFinger: string;
  leftJoint: number;
  rightJoint: number;
  distance: number;
  timestamp: number;
}
