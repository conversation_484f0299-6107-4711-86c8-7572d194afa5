import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { 
  XAPIStatement, 
  StatementResult, 
  StatementQuery,
  Actor 
} from './interfaces/xapi.interface';

/**
 * xAPI客户端配置接口
 */
export interface XAPIClientConfig {
  endpoint: string;              // LRS端点URL
  username: string;              // 基本认证用户名
  password: string;              // 基本认证密码
  version: string;               // xAPI版本
  timeout?: number;              // 请求超时时间
}

/**
 * xAPI客户端服务
 * 负责与Learning Record Store (LRS) 通信
 */
@Injectable()
export class XAPIClientService {
  private readonly logger = new Logger(XAPIClientService.name);
  private readonly config: XAPIClientConfig;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.config = {
      endpoint: this.configService.get<string>('XAPI_ENDPOINT'),
      username: this.configService.get<string>('XAPI_USERNAME'),
      password: this.configService.get<string>('XAPI_PASSWORD'),
      version: this.configService.get<string>('XAPI_VERSION', '2.0.0'),
      timeout: this.configService.get<number>('XAPI_TIMEOUT', 30000),
    };

    // 配置HTTP客户端
    this.httpService.axiosRef.defaults.baseURL = this.config.endpoint;
    this.httpService.axiosRef.defaults.timeout = this.config.timeout;
    this.httpService.axiosRef.defaults.headers.common['Authorization'] = 
      `Basic ${Buffer.from(`${this.config.username}:${this.config.password}`).toString('base64')}`;
    this.httpService.axiosRef.defaults.headers.common['X-Experience-API-Version'] = this.config.version;
    this.httpService.axiosRef.defaults.headers.common['Content-Type'] = 'application/json';
  }

  /**
   * 发送单个xAPI语句
   * @param statement xAPI语句
   * @returns 语句ID
   */
  async sendStatement(statement: XAPIStatement): Promise<string> {
    try {
      // 确保语句有ID
      if (!statement.id) {
        statement.id = this.generateUUID();
      }

      // 确保有时间戳
      if (!statement.timestamp) {
        statement.timestamp = new Date().toISOString();
      }

      // 验证语句
      this.validateStatement(statement);

      const response = await firstValueFrom(
        this.httpService.post('/statements', statement)
      );

      this.logger.log(`成功发送xAPI语句: ${statement.id}`);
      return statement.id;
    } catch (error) {
      this.logger.error(`发送xAPI语句失败: ${error.message}`, error.stack);
      throw new Error(`发送xAPI语句失败: ${error.message}`);
    }
  }

  /**
   * 批量发送xAPI语句
   * @param statements xAPI语句数组
   * @returns 语句ID数组
   */
  async sendStatements(statements: XAPIStatement[]): Promise<string[]> {
    try {
      if (!statements || statements.length === 0) {
        return [];
      }

      // 为每个语句生成ID和时间戳
      const processedStatements = statements.map(statement => {
        if (!statement.id) {
          statement.id = this.generateUUID();
        }
        if (!statement.timestamp) {
          statement.timestamp = new Date().toISOString();
        }
        
        // 验证语句
        this.validateStatement(statement);
        
        return statement;
      });

      const response = await firstValueFrom(
        this.httpService.post('/statements', processedStatements)
      );

      const statementIds = processedStatements.map(s => s.id);
      this.logger.log(`成功批量发送 ${statementIds.length} 条xAPI语句`);
      return statementIds;
    } catch (error) {
      this.logger.error(`批量发送xAPI语句失败: ${error.message}`, error.stack);
      throw new Error(`批量发送xAPI语句失败: ${error.message}`);
    }
  }

  /**
   * 查询xAPI语句
   * @param query 查询参数
   * @returns 语句结果
   */
  async getStatements(query: StatementQuery = {}): Promise<StatementResult> {
    try {
      const params = this.buildQueryParams(query);
      
      const response = await firstValueFrom(
        this.httpService.get('/statements', { params })
      );

      this.logger.log(`查询到 ${response.data.statements?.length || 0} 条xAPI语句`);
      return response.data;
    } catch (error) {
      this.logger.error(`查询xAPI语句失败: ${error.message}`, error.stack);
      throw new Error(`查询xAPI语句失败: ${error.message}`);
    }
  }

  /**
   * 获取单个语句
   * @param statementId 语句ID
   * @returns xAPI语句
   */
  async getStatement(statementId: string): Promise<XAPIStatement> {
    try {
      const response = await firstValueFrom(
        this.httpService.get('/statements', { 
          params: { statementId } 
        })
      );

      if (!response.data.statements || response.data.statements.length === 0) {
        throw new Error(`未找到语句: ${statementId}`);
      }

      return response.data.statements[0];
    } catch (error) {
      this.logger.error(`获取xAPI语句失败: ${error.message}`, error.stack);
      throw new Error(`获取xAPI语句失败: ${error.message}`);
    }
  }

  /**
   * 撤销语句
   * @param statementId 要撤销的语句ID
   * @param actor 撤销者
   * @returns 撤销语句ID
   */
  async voidStatement(statementId: string, actor: Actor): Promise<string> {
    try {
      const voidStatement: XAPIStatement = {
        id: this.generateUUID(),
        actor,
        verb: {
          id: 'http://adlnet.gov/expapi/verbs/voided',
          display: { 'zh-CN': '撤销', 'en-US': 'voided' }
        },
        object: {
          objectType: 'StatementRef',
          id: statementId
        },
        timestamp: new Date().toISOString()
      };

      return await this.sendStatement(voidStatement);
    } catch (error) {
      this.logger.error(`撤销xAPI语句失败: ${error.message}`, error.stack);
      throw new Error(`撤销xAPI语句失败: ${error.message}`);
    }
  }

  /**
   * 验证xAPI语句
   * @param statement xAPI语句
   */
  private validateStatement(statement: XAPIStatement): void {
    if (!statement.actor) {
      throw new Error('xAPI语句缺少actor字段');
    }

    if (!statement.verb || !statement.verb.id) {
      throw new Error('xAPI语句缺少verb字段或verb.id');
    }

    if (!statement.object) {
      throw new Error('xAPI语句缺少object字段');
    }

    // 验证actor
    this.validateActor(statement.actor);

    // 验证时间戳格式
    if (statement.timestamp && !this.isValidISO8601(statement.timestamp)) {
      throw new Error('时间戳格式无效，必须是ISO 8601格式');
    }
  }

  /**
   * 验证actor
   * @param actor 行为者
   */
  private validateActor(actor: Actor): void {
    if (!actor.name && !actor.mbox && !actor.mbox_sha1sum && !actor.openid && !actor.account) {
      throw new Error('Actor必须包含至少一个标识符 (name, mbox, mbox_sha1sum, openid, 或 account)');
    }

    if (actor.mbox && !actor.mbox.startsWith('mailto:')) {
      throw new Error('mbox必须以"mailto:"开头');
    }
  }

  /**
   * 构建查询参数
   * @param query 查询对象
   * @returns 查询参数
   */
  private buildQueryParams(query: StatementQuery): any {
    const params: any = {};

    Object.keys(query).forEach(key => {
      const value = query[key];
      if (value !== undefined && value !== null) {
        if (typeof value === 'object') {
          params[key] = JSON.stringify(value);
        } else {
          params[key] = value;
        }
      }
    });

    return params;
  }

  /**
   * 生成UUID
   * @returns UUID字符串
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * 验证ISO 8601时间戳格式
   * @param timestamp 时间戳字符串
   * @returns 是否有效
   */
  private isValidISO8601(timestamp: string): boolean {
    const iso8601Regex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;
    return iso8601Regex.test(timestamp) && !isNaN(Date.parse(timestamp));
  }

  /**
   * 获取客户端配置信息
   * @returns 配置信息（隐藏敏感信息）
   */
  getConfig(): Partial<XAPIClientConfig> {
    return {
      endpoint: this.config.endpoint,
      version: this.config.version,
      timeout: this.config.timeout
    };
  }

  /**
   * 测试连接
   * @returns 连接状态
   */
  async testConnection(): Promise<boolean> {
    try {
      await firstValueFrom(
        this.httpService.get('/about')
      );
      return true;
    } catch (error) {
      this.logger.error(`xAPI连接测试失败: ${error.message}`);
      return false;
    }
  }
}
