/**
 * 区块链管理器 - DL引擎区块链功能的核心管理类
 */

import { EventEmitter } from '../../utils/EventEmitter';
import { WalletManager } from './WalletManager';
import { ContractManager } from './ContractManager';
import { 
  BlockchainConfig, 
  BlockchainNetwork, 
  BlockchainState, 
  BlockchainEventType,
  NetworkStatus,
  Transaction,
  BlockchainResult
} from '../types/BlockchainTypes';

export class BlockchainManager extends EventEmitter {
  private walletManager: WalletManager;
  private contractManager: ContractManager;
  private config: BlockchainConfig;
  private state: BlockchainState;
  private networkStatus: NetworkStatus;
  private isInitialized: boolean = false;

  constructor(config: BlockchainConfig) {
    super();
    this.config = config;
    this.walletManager = new WalletManager(this);
    this.contractManager = new ContractManager(this);
    
    this.state = {
      isConnected: false,
      currentNetwork: null,
      currentAccount: null,
      balance: '0',
      transactions: [],
      contracts: {},
      tokens: []
    };

    this.networkStatus = {
      isOnline: false,
      latency: 0,
      blockHeight: 0,
      gasPrice: '0',
      congestionLevel: 'low'
    };
  }

  /**
   * 初始化区块链管理器
   */
  async initialize(): Promise<void> {
    try {
      console.log('初始化区块链管理器...');
      
      // 初始化钱包管理器
      await this.walletManager.initialize();
      
      // 初始化合约管理器
      await this.contractManager.initialize();
      
      // 设置默认网络
      const defaultNetwork = this.config.networks[this.config.defaultNetwork];
      if (defaultNetwork) {
        await this.switchNetwork(defaultNetwork);
      }
      
      // 开始监控网络状态
      this.startNetworkMonitoring();
      
      this.isInitialized = true;
      this.emit(BlockchainEventType.WALLET_CONNECTED, this.state);
      
      console.log('区块链管理器初始化完成');
    } catch (error) {
      console.error('区块链管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 连接钱包
   */
  async connectWallet(walletType?: string): Promise<BlockchainResult<string>> {
    try {
      const result = await this.walletManager.connect(walletType);
      
      if (result.success && result.data) {
        this.state.isConnected = true;
        this.state.currentAccount = result.data;
        
        // 获取账户余额
        await this.updateAccountBalance();
        
        this.emit(BlockchainEventType.WALLET_CONNECTED, {
          address: result.data,
          walletType: this.walletManager.getCurrentWalletType()
        });
      }
      
      return result;
    } catch (error) {
      console.error('连接钱包失败:', error);
      return {
        success: false,
        error: {
          code: -1,
          message: error instanceof Error ? error.message : '连接钱包失败'
        }
      };
    }
  }

  /**
   * 断开钱包连接
   */
  async disconnectWallet(): Promise<void> {
    try {
      await this.walletManager.disconnect();
      
      this.state.isConnected = false;
      this.state.currentAccount = null;
      this.state.balance = '0';
      
      this.emit(BlockchainEventType.WALLET_DISCONNECTED);
    } catch (error) {
      console.error('断开钱包连接失败:', error);
    }
  }

  /**
   * 切换网络
   */
  async switchNetwork(network: BlockchainNetwork): Promise<BlockchainResult<void>> {
    try {
      const result = await this.walletManager.switchNetwork(network);
      
      if (result.success) {
        this.state.currentNetwork = network;
        
        // 重新初始化合约
        await this.contractManager.initializeContracts(network.chainId);
        
        // 更新账户余额
        if (this.state.currentAccount) {
          await this.updateAccountBalance();
        }
        
        this.emit(BlockchainEventType.NETWORK_CHANGED, network);
      }
      
      return result;
    } catch (error) {
      console.error('切换网络失败:', error);
      return {
        success: false,
        error: {
          code: -1,
          message: error instanceof Error ? error.message : '切换网络失败'
        }
      };
    }
  }

  /**
   * 发送交易
   */
  async sendTransaction(transaction: Partial<Transaction>): Promise<BlockchainResult<string>> {
    try {
      if (!this.state.isConnected || !this.state.currentAccount) {
        throw new Error('钱包未连接');
      }

      const result = await this.walletManager.sendTransaction(transaction);
      
      if (result.success && result.transactionHash) {
        // 添加到交易历史
        const tx: Transaction = {
          hash: result.transactionHash,
          from: this.state.currentAccount,
          to: transaction.to || '',
          value: transaction.value || '0',
          gasPrice: transaction.gasPrice || '0',
          gasLimit: transaction.gasLimit || '0',
          status: 'pending'
        };
        
        this.state.transactions.unshift(tx);
        
        this.emit(BlockchainEventType.TRANSACTION_SENT, tx);
        
        // 监控交易状态
        this.monitorTransaction(result.transactionHash);
      }
      
      return result;
    } catch (error) {
      console.error('发送交易失败:', error);
      return {
        success: false,
        error: {
          code: -1,
          message: error instanceof Error ? error.message : '发送交易失败'
        }
      };
    }
  }

  /**
   * 获取当前状态
   */
  getState(): BlockchainState {
    return { ...this.state };
  }

  /**
   * 获取网络状态
   */
  getNetworkStatus(): NetworkStatus {
    return { ...this.networkStatus };
  }

  /**
   * 获取钱包管理器
   */
  getWalletManager(): WalletManager {
    return this.walletManager;
  }

  /**
   * 获取合约管理器
   */
  getContractManager(): ContractManager {
    return this.contractManager;
  }

  /**
   * 获取配置
   */
  getConfig(): BlockchainConfig {
    return this.config;
  }

  /**
   * 更新账户余额
   */
  private async updateAccountBalance(): Promise<void> {
    try {
      if (!this.state.currentAccount) return;
      
      const balance = await this.walletManager.getBalance(this.state.currentAccount);
      this.state.balance = balance;
    } catch (error) {
      console.error('更新账户余额失败:', error);
    }
  }

  /**
   * 监控交易状态
   */
  private async monitorTransaction(txHash: string): Promise<void> {
    try {
      // 这里应该实现交易状态监控逻辑
      // 可以使用Web3提供的方法来监控交易确认状态
      console.log(`开始监控交易: ${txHash}`);
      
      // 模拟交易确认过程
      setTimeout(() => {
        const txIndex = this.state.transactions.findIndex(tx => tx.hash === txHash);
        if (txIndex !== -1) {
          this.state.transactions[txIndex].status = 'confirmed';
          this.emit(BlockchainEventType.TRANSACTION_CONFIRMED, this.state.transactions[txIndex]);
        }
      }, 30000); // 30秒后标记为确认（实际应该监控真实状态）
      
    } catch (error) {
      console.error('监控交易失败:', error);
      
      const txIndex = this.state.transactions.findIndex(tx => tx.hash === txHash);
      if (txIndex !== -1) {
        this.state.transactions[txIndex].status = 'failed';
        this.emit(BlockchainEventType.TRANSACTION_FAILED, this.state.transactions[txIndex]);
      }
    }
  }

  /**
   * 开始网络监控
   */
  private startNetworkMonitoring(): void {
    // 定期检查网络状态
    setInterval(async () => {
      try {
        await this.updateNetworkStatus();
      } catch (error) {
        console.error('更新网络状态失败:', error);
      }
    }, 30000); // 每30秒检查一次
  }

  /**
   * 更新网络状态
   */
  private async updateNetworkStatus(): Promise<void> {
    try {
      // 这里应该实现网络状态检查逻辑
      // 包括延迟测试、区块高度获取、Gas价格获取等
      
      this.networkStatus.isOnline = true;
      this.networkStatus.latency = Math.random() * 1000; // 模拟延迟
      this.networkStatus.blockHeight += 1; // 模拟区块高度增长
      
    } catch (error) {
      this.networkStatus.isOnline = false;
      console.error('网络状态检查失败:', error);
    }
  }

  /**
   * 销毁管理器
   */
  async destroy(): Promise<void> {
    try {
      await this.disconnectWallet();
      this.removeAllListeners();
      this.isInitialized = false;
    } catch (error) {
      console.error('销毁区块链管理器失败:', error);
    }
  }
}
