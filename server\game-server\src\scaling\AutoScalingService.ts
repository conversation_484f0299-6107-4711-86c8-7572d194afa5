/**
 * 自动扩缩容服务
 * 基于负载和性能指标的智能扩缩容系统
 */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { InstanceService } from '../instance/instance.service';
import { EnhancedMonitoringService } from '../monitoring/EnhancedMonitoringService';

// 扩缩容动作类型
enum ScalingAction {
  SCALE_UP = 'scale_up',
  SCALE_DOWN = 'scale_down',
  SCALE_OUT = 'scale_out', // 水平扩展
  SCALE_IN = 'scale_in',   // 水平收缩
}

// 扩缩容策略
interface ScalingPolicy {
  id: string;
  name: string;
  enabled: boolean;
  
  // 触发条件
  triggers: {
    metric: string;
    operator: 'gt' | 'lt' | 'gte' | 'lte';
    threshold: number;
    duration: number; // 持续时间（秒）
  }[];
  
  // 扩缩容动作
  action: ScalingAction;
  
  // 扩缩容参数
  parameters: {
    minInstances: number;
    maxInstances: number;
    scaleStep: number; // 每次扩缩容的实例数
    cooldownPeriod: number; // 冷却期（秒）
  };
  
  // 优先级
  priority: number;
}

// 扩缩容事件
interface ScalingEvent {
  id: string;
  policyId: string;
  action: ScalingAction;
  reason: string;
  timestamp: number;
  beforeInstances: number;
  afterInstances: number;
  success: boolean;
  error?: string;
}

// 扩缩容状态
interface ScalingState {
  currentInstances: number;
  targetInstances: number;
  lastScalingTime: number;
  isScaling: boolean;
  pendingActions: ScalingAction[];
}

// 预测模型数据
interface PredictionData {
  timestamp: number;
  userCount: number;
  cpuUsage: number;
  memoryUsage: number;
  responseTime: number;
}

/**
 * 自动扩缩容服务类
 */
@Injectable()
export class AutoScalingService {
  private readonly logger = new Logger(AutoScalingService.name);
  
  // 扩缩容策略
  private scalingPolicies: Map<string, ScalingPolicy> = new Map();
  
  // 扩缩容状态
  private scalingState: ScalingState = {
    currentInstances: 0,
    targetInstances: 0,
    lastScalingTime: 0,
    isScaling: false,
    pendingActions: [],
  };
  
  // 扩缩容历史
  private scalingHistory: ScalingEvent[] = [];
  
  // 指标历史（用于预测）
  private metricsHistory: PredictionData[] = [];
  
  // 配置
  private readonly enableAutoScaling: boolean;
  private readonly enablePredictiveScaling: boolean;
  private readonly maxScalingHistory: number;
  private readonly evaluationInterval: number;
  
  // 定时器
  private evaluationTimer?: NodeJS.Timeout;
  private predictionTimer?: NodeJS.Timeout;
  
  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly instanceService: InstanceService,
    private readonly monitoringService: EnhancedMonitoringService,
  ) {
    // 读取配置
    this.enableAutoScaling = this.configService.get<boolean>('ENABLE_AUTO_SCALING', true);
    this.enablePredictiveScaling = this.configService.get<boolean>('ENABLE_PREDICTIVE_SCALING', false);
    this.maxScalingHistory = this.configService.get<number>('MAX_SCALING_HISTORY', 1000);
    this.evaluationInterval = this.configService.get<number>('SCALING_EVALUATION_INTERVAL', 30000);
    
    this.initialize();
  }
  
  /**
   * 初始化自动扩缩容服务
   */
  private async initialize(): Promise<void> {
    try {
      // 初始化默认策略
      this.initializeDefaultPolicies();
      
      // 获取当前实例数
      await this.updateCurrentInstanceCount();
      
      // 启动评估循环
      if (this.enableAutoScaling) {
        this.startEvaluationLoop();
      }
      
      // 启动预测模型
      if (this.enablePredictiveScaling) {
        this.startPredictiveScaling();
      }
      
      this.logger.log('自动扩缩容服务初始化完成');
      
    } catch (error) {
      this.logger.error('自动扩缩容服务初始化失败:', error);
      throw error;
    }
  }
  
  /**
   * 初始化默认扩缩容策略
   */
  private initializeDefaultPolicies(): void {
    const defaultPolicies: ScalingPolicy[] = [
      {
        id: 'cpu_scale_up',
        name: 'CPU高负载扩容',
        enabled: true,
        triggers: [
          {
            metric: 'system.cpu.usage',
            operator: 'gt',
            threshold: 70,
            duration: 300, // 5分钟
          },
        ],
        action: ScalingAction.SCALE_OUT,
        parameters: {
          minInstances: 1,
          maxInstances: 10,
          scaleStep: 1,
          cooldownPeriod: 600, // 10分钟
        },
        priority: 1,
      },
      {
        id: 'cpu_scale_down',
        name: 'CPU低负载缩容',
        enabled: true,
        triggers: [
          {
            metric: 'system.cpu.usage',
            operator: 'lt',
            threshold: 30,
            duration: 900, // 15分钟
          },
        ],
        action: ScalingAction.SCALE_IN,
        parameters: {
          minInstances: 1,
          maxInstances: 10,
          scaleStep: 1,
          cooldownPeriod: 600,
        },
        priority: 2,
      },
      {
        id: 'memory_scale_up',
        name: '内存高负载扩容',
        enabled: true,
        triggers: [
          {
            metric: 'system.memory.usage',
            operator: 'gt',
            threshold: 80,
            duration: 180, // 3分钟
          },
        ],
        action: ScalingAction.SCALE_OUT,
        parameters: {
          minInstances: 1,
          maxInstances: 10,
          scaleStep: 1,
          cooldownPeriod: 600,
        },
        priority: 1,
      },
      {
        id: 'user_count_scale_up',
        name: '用户数量扩容',
        enabled: true,
        triggers: [
          {
            metric: 'application.users.total',
            operator: 'gt',
            threshold: 80, // 80个用户
            duration: 60, // 1分钟
          },
        ],
        action: ScalingAction.SCALE_OUT,
        parameters: {
          minInstances: 1,
          maxInstances: 10,
          scaleStep: 1,
          cooldownPeriod: 300, // 5分钟
        },
        priority: 0, // 最高优先级
      },
      {
        id: 'response_time_scale_up',
        name: '响应时间扩容',
        enabled: true,
        triggers: [
          {
            metric: 'application.requests.averageResponseTime',
            operator: 'gt',
            threshold: 2000, // 2秒
            duration: 120, // 2分钟
          },
        ],
        action: ScalingAction.SCALE_OUT,
        parameters: {
          minInstances: 1,
          maxInstances: 10,
          scaleStep: 1,
          cooldownPeriod: 600,
        },
        priority: 1,
      },
    ];
    
    for (const policy of defaultPolicies) {
      this.scalingPolicies.set(policy.id, policy);
    }
  }
  
  /**
   * 更新当前实例数
   */
  private async updateCurrentInstanceCount(): Promise<void> {
    try {
      const instances = this.instanceService.getAllInstances();
      this.scalingState.currentInstances = instances.length;
      
      if (this.scalingState.targetInstances === 0) {
        this.scalingState.targetInstances = this.scalingState.currentInstances;
      }
      
    } catch (error) {
      this.logger.error('更新实例数失败:', error);
    }
  }
  
  /**
   * 启动评估循环
   */
  private startEvaluationLoop(): void {
    this.evaluationTimer = setInterval(async () => {
      await this.evaluateScalingPolicies();
    }, this.evaluationInterval);
  }
  
  /**
   * 启动预测扩缩容
   */
  private startPredictiveScaling(): void {
    this.predictionTimer = setInterval(async () => {
      await this.performPredictiveScaling();
    }, 60000); // 每分钟执行一次预测
  }
  
  /**
   * 评估扩缩容策略
   */
  private async evaluateScalingPolicies(): Promise<void> {
    if (this.scalingState.isScaling) {
      this.logger.debug('正在执行扩缩容操作，跳过评估');
      return;
    }
    
    try {
      // 更新当前实例数
      await this.updateCurrentInstanceCount();
      
      // 获取当前指标
      const metrics = this.monitoringService.getCurrentMetrics();
      if (!metrics.system || !metrics.application) {
        return;
      }
      
      // 按优先级排序策略
      const sortedPolicies = Array.from(this.scalingPolicies.values())
        .filter(policy => policy.enabled)
        .sort((a, b) => a.priority - b.priority);
      
      // 评估每个策略
      for (const policy of sortedPolicies) {
        const shouldScale = await this.evaluatePolicy(policy, metrics);
        
        if (shouldScale) {
          await this.executeScalingAction(policy);
          break; // 只执行第一个匹配的策略
        }
      }
      
    } catch (error) {
      this.logger.error('评估扩缩容策略失败:', error);
    }
  }
  
  /**
   * 评估单个策略
   */
  private async evaluatePolicy(
    policy: ScalingPolicy,
    metrics: { system: any; application: any }
  ): Promise<boolean> {
    // 检查冷却期
    if (Date.now() - this.scalingState.lastScalingTime < policy.parameters.cooldownPeriod * 1000) {
      return false;
    }
    
    // 检查实例数限制
    if (policy.action === ScalingAction.SCALE_OUT || policy.action === ScalingAction.SCALE_UP) {
      if (this.scalingState.currentInstances >= policy.parameters.maxInstances) {
        return false;
      }
    } else if (policy.action === ScalingAction.SCALE_IN || policy.action === ScalingAction.SCALE_DOWN) {
      if (this.scalingState.currentInstances <= policy.parameters.minInstances) {
        return false;
      }
    }
    
    // 评估所有触发条件
    for (const trigger of policy.triggers) {
      const value = this.getMetricValue(trigger.metric, metrics);
      if (value === null) continue;
      
      const conditionMet = this.evaluateCondition(value, trigger.operator, trigger.threshold);
      
      if (!conditionMet) {
        return false; // 所有条件都必须满足
      }
      
      // 检查持续时间（简化实现）
      // 实际应该跟踪指标历史来验证持续时间
    }
    
    return true;
  }
  
  /**
   * 执行扩缩容动作
   */
  private async executeScalingAction(policy: ScalingPolicy): Promise<void> {
    this.scalingState.isScaling = true;
    
    const eventId = `scaling_${Date.now()}`;
    const beforeInstances = this.scalingState.currentInstances;
    let afterInstances = beforeInstances;
    let success = false;
    let error: string | undefined;
    
    try {
      this.logger.log(`执行扩缩容策略: ${policy.name}, 动作: ${policy.action}`);
      
      switch (policy.action) {
        case ScalingAction.SCALE_OUT:
          afterInstances = Math.min(
            beforeInstances + policy.parameters.scaleStep,
            policy.parameters.maxInstances
          );
          await this.scaleOut(afterInstances - beforeInstances);
          break;
          
        case ScalingAction.SCALE_IN:
          afterInstances = Math.max(
            beforeInstances - policy.parameters.scaleStep,
            policy.parameters.minInstances
          );
          await this.scaleIn(beforeInstances - afterInstances);
          break;
          
        case ScalingAction.SCALE_UP:
          // 垂直扩展（增加资源）
          await this.scaleUp();
          break;
          
        case ScalingAction.SCALE_DOWN:
          // 垂直收缩（减少资源）
          await this.scaleDown();
          break;
      }
      
      success = true;
      this.scalingState.currentInstances = afterInstances;
      this.scalingState.targetInstances = afterInstances;
      this.scalingState.lastScalingTime = Date.now();
      
    } catch (err) {
      error = (err as Error).message;
      this.logger.error(`扩缩容执行失败: ${error}`);
    } finally {
      this.scalingState.isScaling = false;
    }
    
    // 记录扩缩容事件
    const scalingEvent: ScalingEvent = {
      id: eventId,
      policyId: policy.id,
      action: policy.action,
      reason: policy.name,
      timestamp: Date.now(),
      beforeInstances,
      afterInstances,
      success,
      error,
    };
    
    this.scalingHistory.push(scalingEvent);
    
    // 限制历史记录大小
    if (this.scalingHistory.length > this.maxScalingHistory) {
      this.scalingHistory.shift();
    }
    
    // 发出扩缩容事件
    this.eventEmitter.emit('scaling.executed', scalingEvent);
  }
  
  /**
   * 水平扩展（增加实例）
   */
  private async scaleOut(count: number): Promise<void> {
    for (let i = 0; i < count; i++) {
      await this.instanceService.createInstance({
        sceneId: 'default',
        isMediaInstance: false,
      });
    }
    
    this.logger.log(`水平扩展完成，增加了 ${count} 个实例`);
  }
  
  /**
   * 水平收缩（减少实例）
   */
  private async scaleIn(count: number): Promise<void> {
    const instances = this.instanceService.getAllInstances();
    const instancesToRemove = instances
      .filter(instance => instance.currentUsers === 0)
      .slice(0, count);
    
    for (const instance of instancesToRemove) {
      await this.instanceService.destroyInstance(instance.id);
    }
    
    this.logger.log(`水平收缩完成，移除了 ${instancesToRemove.length} 个实例`);
  }
  
  /**
   * 垂直扩展（增加资源）
   */
  private async scaleUp(): Promise<void> {
    // 实现垂直扩展逻辑
    // 例如：增加CPU、内存配额
    this.logger.log('垂直扩展完成');
  }
  
  /**
   * 垂直收缩（减少资源）
   */
  private async scaleDown(): Promise<void> {
    // 实现垂直收缩逻辑
    // 例如：减少CPU、内存配额
    this.logger.log('垂直收缩完成');
  }
  
  /**
   * 执行预测扩缩容
   */
  private async performPredictiveScaling(): Promise<void> {
    try {
      // 收集当前指标用于预测
      const metrics = this.monitoringService.getCurrentMetrics();
      if (metrics.system && metrics.application) {
        const predictionData: PredictionData = {
          timestamp: Date.now(),
          userCount: metrics.application.users.total,
          cpuUsage: metrics.system.cpu.usage,
          memoryUsage: metrics.system.memory.usage,
          responseTime: metrics.application.requests.averageResponseTime,
        };
        
        this.metricsHistory.push(predictionData);
        
        // 限制历史数据大小
        if (this.metricsHistory.length > 1440) { // 24小时数据
          this.metricsHistory.shift();
        }
      }
      
      // 执行预测（简化实现）
      const prediction = this.predictFutureLoad();
      
      if (prediction.shouldScale) {
        this.logger.log(`预测扩缩容建议: ${prediction.action}, 原因: ${prediction.reason}`);
        
        // 可以在这里触发预测性扩缩容
        // 但需要更谨慎的策略来避免过度扩缩容
      }
      
    } catch (error) {
      this.logger.error('预测扩缩容失败:', error);
    }
  }
  
  /**
   * 预测未来负载
   */
  private predictFutureLoad(): {
    shouldScale: boolean;
    action: ScalingAction | null;
    reason: string;
    confidence: number;
  } {
    if (this.metricsHistory.length < 10) {
      return {
        shouldScale: false,
        action: null,
        reason: '数据不足',
        confidence: 0,
      };
    }
    
    // 简单的趋势分析
    const recent = this.metricsHistory.slice(-10);
    const userTrend = this.calculateTrend(recent.map(d => d.userCount));
    const cpuTrend = this.calculateTrend(recent.map(d => d.cpuUsage));
    
    // 如果用户数和CPU使用率都在上升
    if (userTrend > 0.1 && cpuTrend > 0.1) {
      return {
        shouldScale: true,
        action: ScalingAction.SCALE_OUT,
        reason: '用户数和CPU使用率呈上升趋势',
        confidence: 0.7,
      };
    }
    
    // 如果用户数和CPU使用率都在下降
    if (userTrend < -0.1 && cpuTrend < -0.1) {
      return {
        shouldScale: true,
        action: ScalingAction.SCALE_IN,
        reason: '用户数和CPU使用率呈下降趋势',
        confidence: 0.6,
      };
    }
    
    return {
      shouldScale: false,
      action: null,
      reason: '无明显趋势',
      confidence: 0.5,
    };
  }
  
  /**
   * 计算趋势
   */
  private calculateTrend(values: number[]): number {
    if (values.length < 2) return 0;
    
    const first = values[0];
    const last = values[values.length - 1];
    
    return (last - first) / first;
  }
  
  /**
   * 获取指标值
   */
  private getMetricValue(metric: string, metrics: any): number | null {
    const parts = metric.split('.');
    let obj = metrics;
    
    for (const part of parts) {
      if (obj && typeof obj === 'object' && part in obj) {
        obj = obj[part];
      } else {
        return null;
      }
    }
    
    return typeof obj === 'number' ? obj : null;
  }
  
  /**
   * 评估条件
   */
  private evaluateCondition(value: number, operator: string, threshold: number): boolean {
    switch (operator) {
      case 'gt': return value > threshold;
      case 'lt': return value < threshold;
      case 'gte': return value >= threshold;
      case 'lte': return value <= threshold;
      default: return false;
    }
  }
  
  /**
   * 监听告警事件
   */
  @OnEvent('autoscaling.check')
  async handleAutoscalingCheck(data: any): Promise<void> {
    this.logger.log('收到自动扩缩容检查请求:', data);
    
    // 立即执行一次策略评估
    await this.evaluateScalingPolicies();
  }
  
  /**
   * 获取扩缩容状态
   */
  public getScalingState(): ScalingState {
    return { ...this.scalingState };
  }
  
  /**
   * 获取扩缩容历史
   */
  public getScalingHistory(limit: number = 100): ScalingEvent[] {
    return this.scalingHistory.slice(-limit);
  }
  
  /**
   * 获取扩缩容策略
   */
  public getScalingPolicies(): ScalingPolicy[] {
    return Array.from(this.scalingPolicies.values());
  }
  
  /**
   * 添加扩缩容策略
   */
  public addScalingPolicy(policy: ScalingPolicy): void {
    this.scalingPolicies.set(policy.id, policy);
  }
  
  /**
   * 移除扩缩容策略
   */
  public removeScalingPolicy(policyId: string): void {
    this.scalingPolicies.delete(policyId);
  }
  
  /**
   * 启用/禁用策略
   */
  public togglePolicy(policyId: string, enabled: boolean): void {
    const policy = this.scalingPolicies.get(policyId);
    if (policy) {
      policy.enabled = enabled;
    }
  }
  
  /**
   * 手动触发扩缩容
   */
  public async manualScale(action: ScalingAction, count: number = 1): Promise<void> {
    if (this.scalingState.isScaling) {
      throw new Error('正在执行扩缩容操作，请稍后再试');
    }
    
    this.scalingState.isScaling = true;
    
    try {
      switch (action) {
        case ScalingAction.SCALE_OUT:
          await this.scaleOut(count);
          break;
        case ScalingAction.SCALE_IN:
          await this.scaleIn(count);
          break;
        case ScalingAction.SCALE_UP:
          await this.scaleUp();
          break;
        case ScalingAction.SCALE_DOWN:
          await this.scaleDown();
          break;
      }
      
      await this.updateCurrentInstanceCount();
      this.scalingState.lastScalingTime = Date.now();
      
    } finally {
      this.scalingState.isScaling = false;
    }
  }
}
