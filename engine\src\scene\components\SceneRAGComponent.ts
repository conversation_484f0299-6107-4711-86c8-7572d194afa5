/**
 * 场景RAG应用组件
 * 管理场景中的RAG应用配置和运行状态
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EventEmitter } from '../../utils/EventEmitter';
import type { SceneAvatarConfig, AvatarInstance } from '../SceneAvatarManager';

/**
 * RAG应用配置
 */
export interface RAGApplicationConfig {
  /** 应用ID */
  id: string;
  /** 应用名称 */
  name: string;
  /** 应用描述 */
  description: string;
  /** 知识库ID */
  knowledgeBaseId: string;
  /** 数字人配置列表 */
  avatars: SceneAvatarConfig[];
  /** 是否自动启动 */
  autoStart: boolean;
  /** 最大并发会话数 */
  maxConcurrentSessions: number;
  /** 会话超时时间（秒） */
  sessionTimeout: number;
  /** 欢迎消息 */
  welcomeMessage?: string;
  /** 告别消息 */
  farewellMessage?: string;
}

/**
 * RAG应用状态
 */
export interface RAGApplicationState {
  /** 应用状态 */
  status: 'stopped' | 'starting' | 'running' | 'stopping' | 'error';
  /** 活跃会话数 */
  activeSessions: number;
  /** 总消息数 */
  totalMessages: number;
  /** 启动时间 */
  startTime?: number;
  /** 错误信息 */
  error?: string;
}

/**
 * 会话信息
 */
export interface SessionInfo {
  /** 会话ID */
  id: string;
  /** 用户ID */
  userId?: string;
  /** 关联的数字人ID */
  avatarId: string;
  /** 开始时间 */
  startTime: number;
  /** 最后活动时间 */
  lastActivity: number;
  /** 消息数量 */
  messageCount: number;
  /** 会话状态 */
  status: 'active' | 'idle' | 'ended';
}

/**
 * 场景RAG应用组件
 */
export class SceneRAGComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE = 'SceneRAGComponent';

  /** RAG应用配置 */
  private ragConfig: RAGApplicationConfig;

  /** 应用状态 */
  private ragState: RAGApplicationState;
  
  /** 活跃会话映射 */
  private sessions: Map<string, SessionInfo> = new Map();
  
  /** 数字人实例映射 */
  private avatarInstances: Map<string, AvatarInstance> = new Map();
  
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  
  /** 会话超时检查间隔 */
  private sessionCheckInterval: number = 30000; // 30秒
  
  /** 上次会话检查时间 */
  private lastSessionCheck: number = 0;

  /**
   * 构造函数
   */
  constructor(entity: Entity, config: RAGApplicationConfig) {
    super(SceneRAGComponent.TYPE);

    // 设置实体引用
    this.setEntity(entity);

    this.ragConfig = config;
    this.ragState = {
      status: 'stopped',
      activeSessions: 0,
      totalMessages: 0,
    };
  }

  /**
   * 初始化组件
   */
  public async initialize(): Promise<void> {
    try {
      // 获取场景和数字人管理器
      const world = this.entity?.getWorld();
      if (!world) {
        throw new Error('实体未关联到世界');
      }

      const scene = world.getActiveScene();
      if (!scene) {
        throw new Error('世界中没有活跃场景');
      }

      const avatarManager = scene.getAvatarManager();
      
      // 添加数字人到场景
      for (const avatarConfig of this.ragConfig.avatars) {
        const instance = await avatarManager.addAvatar(avatarConfig);
        this.avatarInstances.set(avatarConfig.avatarId, instance);
        
        // 监听数字人事件
        avatarManager.on('conversationStarted', this.onConversationStarted.bind(this));
        avatarManager.on('conversationEnded', this.onConversationEnded.bind(this));
        avatarManager.on('messageProcessed', this.onMessageProcessed.bind(this));
      }

      // 如果配置了自动启动，则启动应用
      if (this.ragConfig.autoStart) {
        await this.start();
      }

      console.log(`RAG应用 ${this.ragConfig.name} 初始化完成`);
    } catch (error) {
      console.error('RAG应用初始化失败:', error);
      this.ragState.status = 'error';
      this.ragState.error = error.message;
      throw error;
    }
  }

  /**
   * 启动RAG应用
   */
  public async start(): Promise<void> {
    if (this.ragState.status === 'running') {
      return;
    }

    try {
      this.ragState.status = 'starting';
      this.eventEmitter.emit('statusChanged', this.ragState);

      // 激活所有数字人
      const world = this.entity?.getWorld();
      const scene = world?.getActiveScene();
      const avatarManager = scene?.getAvatarManager();

      if (avatarManager) {
        for (const avatarConfig of this.ragConfig.avatars) {
          await avatarManager.activateAvatar(avatarConfig.avatarId);
        }
      }

      this.ragState.status = 'running';
      this.ragState.startTime = Date.now();
      this.eventEmitter.emit('statusChanged', this.ragState);
      this.eventEmitter.emit('started');

      console.log(`RAG应用 ${this.ragConfig.name} 已启动`);
    } catch (error) {
      this.ragState.status = 'error';
      this.ragState.error = error.message;
      this.eventEmitter.emit('statusChanged', this.ragState);
      throw error;
    }
  }

  /**
   * 停止RAG应用
   */
  public async stop(): Promise<void> {
    if (this.ragState.status === 'stopped') {
      return;
    }

    try {
      this.ragState.status = 'stopping';
      this.eventEmitter.emit('statusChanged', this.ragState);

      // 结束所有活跃会话
      for (const session of this.sessions.values()) {
        if (session.status === 'active') {
          await this.endSession(session.id);
        }
      }

      // 停用所有数字人
      const world = this.entity?.getWorld();
      const scene = world?.getActiveScene();
      const avatarManager = scene?.getAvatarManager();

      if (avatarManager) {
        for (const avatarConfig of this.ragConfig.avatars) {
          await avatarManager.deactivateAvatar(avatarConfig.avatarId);
        }
      }

      this.ragState.status = 'stopped';
      this.ragState.startTime = undefined;
      this.eventEmitter.emit('statusChanged', this.ragState);
      this.eventEmitter.emit('stopped');

      console.log(`RAG应用 ${this.ragConfig.name} 已停止`);
    } catch (error) {
      this.ragState.status = 'error';
      this.ragState.error = error.message;
      this.eventEmitter.emit('statusChanged', this.ragState);
      throw error;
    }
  }

  /**
   * 开始会话
   */
  public async startSession(avatarId: string, userId?: string): Promise<string> {
    if (this.ragState.status !== 'running') {
      throw new Error('RAG应用未运行');
    }

    if (this.ragState.activeSessions >= this.ragConfig.maxConcurrentSessions) {
      throw new Error('已达到最大并发会话数');
    }

    const world = this.entity?.getWorld();
    const scene = world?.getActiveScene();
    const avatarManager = scene?.getAvatarManager();

    if (!avatarManager) {
      throw new Error('数字人管理器不可用');
    }

    // 开始数字人对话
    const sessionId = await avatarManager.startConversation(avatarId, userId);

    // 创建会话信息
    const session: SessionInfo = {
      id: sessionId,
      userId,
      avatarId,
      startTime: Date.now(),
      lastActivity: Date.now(),
      messageCount: 0,
      status: 'active',
    };

    this.sessions.set(sessionId, session);
    this.ragState.activeSessions++;
    
    this.eventEmitter.emit('sessionStarted', session);
    return sessionId;
  }

  /**
   * 结束会话
   */
  public async endSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`会话 ${sessionId} 不存在`);
    }

    const world = this.entity?.getWorld();
    const scene = world?.getActiveScene();
    const avatarManager = scene?.getAvatarManager();

    if (avatarManager) {
      await avatarManager.endConversation(session.avatarId);
    }

    session.status = 'ended';
    this.sessions.delete(sessionId);
    this.ragState.activeSessions--;
    
    this.eventEmitter.emit('sessionEnded', session);
  }

  /**
   * 发送消息
   */
  public async sendMessage(sessionId: string, message: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`会话 ${sessionId} 不存在`);
    }

    if (session.status !== 'active') {
      throw new Error(`会话 ${sessionId} 不活跃`);
    }

    const world = this.entity?.getWorld();
    const scene = world?.getActiveScene();
    const avatarManager = scene?.getAvatarManager();

    if (avatarManager) {
      await avatarManager.sendMessage(session.avatarId, message, session.userId);
    }

    // 更新会话信息
    session.lastActivity = Date.now();
    session.messageCount++;
    this.ragState.totalMessages++;
  }

  /**
   * 更新组件
   */
  public update(_deltaTime: number): void {
    if (this.ragState.status !== 'running') {
      return;
    }

    // 检查会话超时
    const now = Date.now();
    if (now - this.lastSessionCheck > this.sessionCheckInterval) {
      this.checkSessionTimeouts();
      this.lastSessionCheck = now;
    }
  }

  /**
   * 检查会话超时
   */
  private checkSessionTimeouts(): void {
    const now = Date.now();
    const timeoutMs = this.ragConfig.sessionTimeout * 1000;

    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.status === 'active' && now - session.lastActivity > timeoutMs) {
        session.status = 'idle';
        this.eventEmitter.emit('sessionTimeout', session);
        
        // 自动结束超时会话
        this.endSession(sessionId).catch(console.error);
      }
    }
  }

  /**
   * 对话开始事件处理
   */
  private onConversationStarted(event: any): void {
    // 更新会话状态
    const session = this.sessions.get(event.sessionId);
    if (session) {
      session.status = 'active';
      session.lastActivity = Date.now();
    }
  }

  /**
   * 对话结束事件处理
   */
  private onConversationEnded(event: any): void {
    // 清理会话
    const session = this.sessions.get(event.sessionId);
    if (session) {
      this.sessions.delete(event.sessionId);
      this.ragState.activeSessions--;
    }
  }

  /**
   * 消息处理事件处理
   */
  private onMessageProcessed(event: any): void {
    // 更新统计信息
    this.ragState.totalMessages++;
    
    // 更新会话活动时间
    const session = this.sessions.get(event.sessionId);
    if (session) {
      session.lastActivity = Date.now();
      session.messageCount++;
    }
  }

  /**
   * 获取配置
   */
  public getConfig(): RAGApplicationConfig {
    return { ...this.ragConfig };
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<RAGApplicationConfig>): void {
    this.ragConfig = { ...this.ragConfig, ...config };
    this.eventEmitter.emit('configUpdated', this.ragConfig);
  }

  /**
   * 获取RAG状态
   */
  public getRagState(): RAGApplicationState {
    return { ...this.ragState };
  }

  /**
   * 获取会话列表
   */
  public getSessions(): SessionInfo[] {
    return Array.from(this.sessions.values());
  }

  /**
   * 获取活跃会话
   */
  public getActiveSessions(): SessionInfo[] {
    return Array.from(this.sessions.values()).filter(s => s.status === 'active');
  }

  /**
   * 监听事件
   */
  public addEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听
   */
  public removeEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    // 创建新的实体用于新组件实例
    const world = this.entity?.getWorld();
    if (!world) {
      throw new Error('无法获取世界实例');
    }

    const newEntity = world.createEntity();
    return new SceneRAGComponent(newEntity, this.ragConfig);
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    // 停止应用
    this.stop().catch(console.error);

    // 清理资源
    this.sessions.clear();
    this.avatarInstances.clear();
    this.eventEmitter.removeAllListeners();

    super.dispose();
  }
}
