/**
 * 情感服务控制器
 */

import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  Param,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam } from '@nestjs/swagger';
import { EmotionDataService } from '../emotion-data/emotion-data.service';
import { EmotionAnalysisService } from './emotion-analysis.service';

/**
 * 情感分析请求DTO
 */
export class EmotionAnalysisDto {
  text: string;
  userId?: string;
  context?: any;
  source?: string;
}

/**
 * 情感记录查询DTO
 */
export class EmotionQueryDto {
  userId?: string;
  emotionType?: string;
  startTime?: string;
  endTime?: string;
  minIntensity?: number;
  maxIntensity?: number;
  limit?: number;
  offset?: number;
}

/**
 * 批量情感记录DTO
 */
export class BatchEmotionRecordDto {
  records: Array<{
    userId: string;
    emotionType: string;
    intensity: number;
    confidence: number;
    source: string;
    context?: any;
    timestamp?: string;
  }>;
}

@ApiTags('情感服务')
@Controller('emotion')
export class EmotionController {
  private readonly logger = new Logger(EmotionController.name);

  constructor(
    private readonly emotionDataService: EmotionDataService,
    private readonly emotionAnalysisService: EmotionAnalysisService,
  ) {}

  @Post('analyze')
  @ApiOperation({ summary: '分析文本情感' })
  @ApiResponse({ status: 200, description: '情感分析成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async analyzeEmotion(@Body() dto: EmotionAnalysisDto) {
    try {
      if (!dto.text || dto.text.trim().length === 0) {
        throw new HttpException('文本内容不能为空', HttpStatus.BAD_REQUEST);
      }

      // 执行情感分析
      const analysisResult = await this.emotionAnalysisService.analyzeText(dto.text);

      // 如果提供了用户ID，保存分析结果
      if (dto.userId) {
        await this.emotionDataService.saveEmotionRecord({
          userId: dto.userId,
          emotionType: analysisResult.type,
          intensity: analysisResult.intensity,
          confidence: analysisResult.confidence,
          source: dto.source || 'text_analysis',
          context: {
            originalText: dto.text,
            details: analysisResult.details,
            ...dto.context,
          },
        });
      }

      this.logger.log(`情感分析完成: ${analysisResult.type} (${analysisResult.intensity})`);

      return {
        success: true,
        data: analysisResult,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('情感分析失败:', error);
      throw new HttpException(
        error.message || '情感分析失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('records')
  @ApiOperation({ summary: '保存情感记录' })
  @ApiResponse({ status: 201, description: '情感记录保存成功' })
  async saveEmotionRecord(@Body() recordData: {
    userId: string;
    emotionType: string;
    intensity: number;
    confidence: number;
    source: string;
    context?: any;
    timestamp?: string;
  }) {
    try {
      const record = await this.emotionDataService.saveEmotionRecord({
        ...recordData,
        timestamp: recordData.timestamp ? new Date(recordData.timestamp) : undefined,
      });

      this.logger.log(`保存情感记录: ${record.id}`);

      return {
        success: true,
        data: record,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('保存情感记录失败:', error);
      throw new HttpException(
        error.message || '保存情感记录失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('records/batch')
  @ApiOperation({ summary: '批量保存情感记录' })
  @ApiResponse({ status: 201, description: '批量保存成功' })
  async batchSaveEmotionRecords(@Body() dto: BatchEmotionRecordDto) {
    try {
      if (!dto.records || dto.records.length === 0) {
        throw new HttpException('记录列表不能为空', HttpStatus.BAD_REQUEST);
      }

      const processedRecords = dto.records.map(record => ({
        ...record,
        timestamp: record.timestamp ? new Date(record.timestamp) : undefined,
      }));

      const savedRecords = await this.emotionDataService.batchSaveEmotionRecords(processedRecords);

      this.logger.log(`批量保存情感记录: ${savedRecords.length} 条`);

      return {
        success: true,
        data: savedRecords,
        count: savedRecords.length,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('批量保存情感记录失败:', error);
      throw new HttpException(
        error.message || '批量保存情感记录失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('records')
  @ApiOperation({ summary: '查询情感记录' })
  @ApiQuery({ name: 'userId', required: false, description: '用户ID' })
  @ApiQuery({ name: 'emotionType', required: false, description: '情感类型' })
  @ApiQuery({ name: 'startTime', required: false, description: '开始时间' })
  @ApiQuery({ name: 'endTime', required: false, description: '结束时间' })
  @ApiQuery({ name: 'limit', required: false, description: '限制数量' })
  @ApiQuery({ name: 'offset', required: false, description: '偏移量' })
  async queryEmotionRecords(@Query() query: EmotionQueryDto) {
    try {
      const options = {
        ...query,
        startTime: query.startTime ? new Date(query.startTime) : undefined,
        endTime: query.endTime ? new Date(query.endTime) : undefined,
      };

      const records = await this.emotionDataService.queryEmotionRecords(options);

      this.logger.log(`查询情感记录: ${records.length} 条`);

      return {
        success: true,
        data: records,
        count: records.length,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('查询情感记录失败:', error);
      throw new HttpException(
        error.message || '查询情感记录失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('statistics/:userId')
  @ApiOperation({ summary: '获取用户情感统计' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiQuery({ name: 'startTime', required: false, description: '开始时间' })
  @ApiQuery({ name: 'endTime', required: false, description: '结束时间' })
  async getEmotionStatistics(
    @Param('userId') userId: string,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string,
  ) {
    try {
      const timeRange = {
        start: startTime ? new Date(startTime) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 默认7天前
        end: endTime ? new Date(endTime) : new Date(), // 默认现在
      };

      const statistics = await this.emotionDataService.getEmotionStatistics(userId, timeRange);

      this.logger.log(`获取情感统计: 用户 ${userId}`);

      return {
        success: true,
        data: statistics,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('获取情感统计失败:', error);
      throw new HttpException(
        error.message || '获取情感统计失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('patterns/:userId')
  @ApiOperation({ summary: '获取用户情感模式' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  async getUserEmotionPatterns(@Param('userId') userId: string) {
    try {
      const patterns = await this.emotionDataService.getUserEmotionPatterns(userId);

      this.logger.log(`获取用户情感模式: 用户 ${userId}, ${patterns.length} 个模式`);

      return {
        success: true,
        data: patterns,
        count: patterns.length,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('获取用户情感模式失败:', error);
      throw new HttpException(
        error.message || '获取用户情感模式失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('patterns/:userId/analysis')
  @ApiOperation({ summary: '分析用户情感模式' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiQuery({ name: 'startTime', required: false, description: '开始时间' })
  @ApiQuery({ name: 'endTime', required: false, description: '结束时间' })
  async analyzeEmotionPatterns(
    @Param('userId') userId: string,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string,
  ) {
    try {
      const timeRange = {
        start: startTime ? new Date(startTime) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 默认30天前
        end: endTime ? new Date(endTime) : new Date(), // 默认现在
      };

      const analysis = await this.emotionDataService.analyzeEmotionPatterns(userId, timeRange);

      this.logger.log(`分析用户情感模式: 用户 ${userId}`);

      return {
        success: true,
        data: analysis,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('分析用户情感模式失败:', error);
      throw new HttpException(
        error.message || '分析用户情感模式失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('profile/:userId')
  @ApiOperation({ summary: '获取用户情感档案' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  async getUserEmotionProfile(@Param('userId') userId: string) {
    try {
      const profile = await this.emotionDataService.getUserEmotionProfile(userId);

      if (!profile) {
        throw new HttpException('用户情感档案不存在', HttpStatus.NOT_FOUND);
      }

      this.logger.log(`获取用户情感档案: 用户 ${userId}`);

      return {
        success: true,
        data: profile,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('获取用户情感档案失败:', error);
      throw new HttpException(
        error.message || '获取用户情感档案失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('cleanup')
  @ApiOperation({ summary: '清理过期数据' })
  @ApiQuery({ name: 'retentionDays', required: false, description: '保留天数，默认30天' })
  async cleanupExpiredData(@Query('retentionDays') retentionDays?: number) {
    try {
      const days = retentionDays || 30;
      const deletedCount = await this.emotionDataService.cleanupExpiredData(days);

      this.logger.log(`清理过期数据: 删除 ${deletedCount} 条记录`);

      return {
        success: true,
        data: {
          deletedCount,
          retentionDays: days,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('清理过期数据失败:', error);
      throw new HttpException(
        error.message || '清理过期数据失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
