/**
 * 增强的连接池管理器
 * 专为支持100+并发用户优化的连接管理系统
 */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { WebSocket } from 'ws';

// 连接状态枚举
enum ConnectionState {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTING = 'disconnecting',
  DISCONNECTED = 'disconnected',
  ERROR = 'error',
}

// 连接信息接口
interface ConnectionInfo {
  id: string;
  socket: WebSocket;
  userId: string;
  state: ConnectionState;
  createdAt: Date;
  lastActivity: Date;
  messageCount: number;
  bytesReceived: number;
  bytesSent: number;
  latency: number;
  errorCount: number;
}

// 用户连接池接口
interface UserConnectionPool {
  userId: string;
  connections: Map<string, ConnectionInfo>;
  maxConnections: number;
  totalMessageCount: number;
  totalBytesTransferred: number;
  lastActivity: Date;
}

// 连接池统计信息
interface ConnectionPoolStats {
  totalConnections: number;
  activeConnections: number;
  totalUsers: number;
  averageConnectionsPerUser: number;
  totalMessageCount: number;
  totalBytesTransferred: number;
  averageLatency: number;
  errorRate: number;
}

/**
 * 增强的连接池管理器类
 */
@Injectable()
export class EnhancedConnectionPool {
  private readonly logger = new Logger(EnhancedConnectionPool.name);
  
  // 用户连接池
  private userPools: Map<string, UserConnectionPool> = new Map();
  
  // 全局连接映射
  private connections: Map<string, ConnectionInfo> = new Map();
  
  // 配置参数
  private readonly maxConnectionsPerUser: number;
  private readonly maxTotalConnections: number;
  private readonly connectionTimeout: number;
  private readonly heartbeatInterval: number;
  private readonly cleanupInterval: number;
  private readonly enableCompression: boolean;
  private readonly enableBatching: boolean;
  
  // 统计信息
  private stats: ConnectionPoolStats = {
    totalConnections: 0,
    activeConnections: 0,
    totalUsers: 0,
    averageConnectionsPerUser: 0,
    totalMessageCount: 0,
    totalBytesTransferred: 0,
    averageLatency: 0,
    errorRate: 0,
  };
  
  // 定时器
  private heartbeatTimer?: NodeJS.Timeout;
  private cleanupTimer?: NodeJS.Timeout;
  private statsTimer?: NodeJS.Timeout;
  
  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    // 从配置中读取参数
    this.maxConnectionsPerUser = this.configService.get<number>('MAX_CONNECTIONS_PER_USER', 10);
    this.maxTotalConnections = this.configService.get<number>('MAX_TOTAL_CONNECTIONS', 10000);
    this.connectionTimeout = this.configService.get<number>('CONNECTION_TIMEOUT', 30000);
    this.heartbeatInterval = this.configService.get<number>('HEARTBEAT_INTERVAL', 30000);
    this.cleanupInterval = this.configService.get<number>('CLEANUP_INTERVAL', 60000);
    this.enableCompression = this.configService.get<boolean>('ENABLE_COMPRESSION', true);
    this.enableBatching = this.configService.get<boolean>('ENABLE_BATCHING', true);
    
    this.initialize();
  }
  
  /**
   * 初始化连接池
   */
  private initialize(): void {
    // 启动心跳检测
    this.startHeartbeat();
    
    // 启动清理任务
    this.startCleanup();
    
    // 启动统计更新
    this.startStatsUpdate();
    
    this.logger.log('增强连接池管理器已初始化');
  }
  
  /**
   * 添加连接
   */
  public addConnection(userId: string, socket: WebSocket): string {
    // 检查总连接数限制
    if (this.connections.size >= this.maxTotalConnections) {
      throw new Error('已达到最大连接数限制');
    }
    
    // 获取或创建用户连接池
    let userPool = this.userPools.get(userId);
    if (!userPool) {
      userPool = {
        userId,
        connections: new Map(),
        maxConnections: this.maxConnectionsPerUser,
        totalMessageCount: 0,
        totalBytesTransferred: 0,
        lastActivity: new Date(),
      };
      this.userPools.set(userId, userPool);
    }
    
    // 检查用户连接数限制
    if (userPool.connections.size >= userPool.maxConnections) {
      // 移除最旧的连接
      const oldestConnection = this.findOldestConnection(userPool);
      if (oldestConnection) {
        this.removeConnection(oldestConnection.id);
      }
    }
    
    // 创建连接信息
    const connectionId = this.generateConnectionId();
    const connectionInfo: ConnectionInfo = {
      id: connectionId,
      socket,
      userId,
      state: ConnectionState.CONNECTING,
      createdAt: new Date(),
      lastActivity: new Date(),
      messageCount: 0,
      bytesReceived: 0,
      bytesSent: 0,
      latency: 0,
      errorCount: 0,
    };
    
    // 设置WebSocket事件监听器
    this.setupSocketListeners(connectionInfo);
    
    // 添加到连接池
    this.connections.set(connectionId, connectionInfo);
    userPool.connections.set(connectionId, connectionInfo);
    
    // 更新统计信息
    this.updateStats();
    
    // 发出连接添加事件
    this.eventEmitter.emit('connection.added', {
      connectionId,
      userId,
      totalConnections: this.connections.size,
    });
    
    this.logger.debug(`用户 ${userId} 添加连接 ${connectionId}，当前连接数: ${userPool.connections.size}`);
    
    return connectionId;
  }
  
  /**
   * 移除连接
   */
  public removeConnection(connectionId: string): void {
    const connectionInfo = this.connections.get(connectionId);
    if (!connectionInfo) {
      return;
    }
    
    const { userId, socket } = connectionInfo;
    
    // 关闭WebSocket连接
    if (socket.readyState === WebSocket.OPEN) {
      socket.close(1000, 'Connection removed');
    }
    
    // 从连接池中移除
    this.connections.delete(connectionId);
    
    const userPool = this.userPools.get(userId);
    if (userPool) {
      userPool.connections.delete(connectionId);
      
      // 如果用户没有连接了，移除用户池
      if (userPool.connections.size === 0) {
        this.userPools.delete(userId);
      }
    }
    
    // 更新统计信息
    this.updateStats();
    
    // 发出连接移除事件
    this.eventEmitter.emit('connection.removed', {
      connectionId,
      userId,
      totalConnections: this.connections.size,
    });
    
    this.logger.debug(`移除连接 ${connectionId}，用户: ${userId}`);
  }
  
  /**
   * 获取用户连接
   */
  public getUserConnections(userId: string): ConnectionInfo[] {
    const userPool = this.userPools.get(userId);
    return userPool ? Array.from(userPool.connections.values()) : [];
  }
  
  /**
   * 获取连接信息
   */
  public getConnection(connectionId: string): ConnectionInfo | undefined {
    return this.connections.get(connectionId);
  }
  
  /**
   * 广播消息给用户的所有连接
   */
  public broadcastToUser(userId: string, message: any): void {
    const userPool = this.userPools.get(userId);
    if (!userPool) {
      return;
    }
    
    const messageStr = JSON.stringify(message);
    const messageBytes = Buffer.byteLength(messageStr, 'utf8');
    
    for (const connectionInfo of userPool.connections.values()) {
      if (connectionInfo.socket.readyState === WebSocket.OPEN) {
        try {
          connectionInfo.socket.send(messageStr);
          connectionInfo.bytesSent += messageBytes;
          connectionInfo.messageCount++;
          connectionInfo.lastActivity = new Date();
        } catch (error) {
          this.logger.error(`发送消息失败: ${error.message}`);
          connectionInfo.errorCount++;
        }
      }
    }
    
    userPool.totalMessageCount++;
    userPool.totalBytesTransferred += messageBytes;
    userPool.lastActivity = new Date();
  }
  
  /**
   * 广播消息给所有连接
   */
  public broadcastToAll(message: any, excludeUserId?: string): void {
    const messageStr = JSON.stringify(message);
    const messageBytes = Buffer.byteLength(messageStr, 'utf8');
    
    for (const [userId, userPool] of this.userPools) {
      if (excludeUserId && userId === excludeUserId) {
        continue;
      }
      
      for (const connectionInfo of userPool.connections.values()) {
        if (connectionInfo.socket.readyState === WebSocket.OPEN) {
          try {
            connectionInfo.socket.send(messageStr);
            connectionInfo.bytesSent += messageBytes;
            connectionInfo.messageCount++;
            connectionInfo.lastActivity = new Date();
          } catch (error) {
            this.logger.error(`广播消息失败: ${error.message}`);
            connectionInfo.errorCount++;
          }
        }
      }
    }
  }
  
  /**
   * 设置WebSocket事件监听器
   */
  private setupSocketListeners(connectionInfo: ConnectionInfo): void {
    const { socket } = connectionInfo;
    
    socket.on('open', () => {
      connectionInfo.state = ConnectionState.CONNECTED;
      this.logger.debug(`连接 ${connectionInfo.id} 已建立`);
    });
    
    socket.on('message', (data: Buffer) => {
      connectionInfo.bytesReceived += data.length;
      connectionInfo.messageCount++;
      connectionInfo.lastActivity = new Date();
      
      // 发出消息接收事件
      this.eventEmitter.emit('connection.message', {
        connectionId: connectionInfo.id,
        userId: connectionInfo.userId,
        data: data.toString(),
      });
    });
    
    socket.on('close', (code: number, reason: string) => {
      connectionInfo.state = ConnectionState.DISCONNECTED;
      this.logger.debug(`连接 ${connectionInfo.id} 已关闭: ${code} ${reason}`);
      
      // 从连接池中移除
      this.removeConnection(connectionInfo.id);
    });
    
    socket.on('error', (error: Error) => {
      connectionInfo.state = ConnectionState.ERROR;
      connectionInfo.errorCount++;
      this.logger.error(`连接 ${connectionInfo.id} 错误: ${error.message}`);
      
      // 发出连接错误事件
      this.eventEmitter.emit('connection.error', {
        connectionId: connectionInfo.id,
        userId: connectionInfo.userId,
        error: error.message,
      });
    });
    
    socket.on('pong', () => {
      // 计算延迟
      const now = Date.now();
      const pingTime = (socket as any).pingTime;
      if (pingTime) {
        connectionInfo.latency = now - pingTime;
      }
    });
  }
  
  /**
   * 查找最旧的连接
   */
  private findOldestConnection(userPool: UserConnectionPool): ConnectionInfo | null {
    let oldestConnection: ConnectionInfo | null = null;
    let oldestTime = Date.now();
    
    for (const connection of userPool.connections.values()) {
      if (connection.createdAt.getTime() < oldestTime) {
        oldestTime = connection.createdAt.getTime();
        oldestConnection = connection;
      }
    }
    
    return oldestConnection;
  }
  
  /**
   * 启动心跳检测
   */
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      this.performHeartbeat();
    }, this.heartbeatInterval);
  }
  
  /**
   * 执行心跳检测
   */
  private performHeartbeat(): void {
    const now = Date.now();
    
    for (const connectionInfo of this.connections.values()) {
      if (connectionInfo.socket.readyState === WebSocket.OPEN) {
        try {
          (connectionInfo.socket as any).pingTime = now;
          connectionInfo.socket.ping();
        } catch (error) {
          this.logger.error(`心跳检测失败: ${error.message}`);
          connectionInfo.errorCount++;
        }
      }
    }
  }
  
  /**
   * 启动清理任务
   */
  private startCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.performCleanup();
    }, this.cleanupInterval);
  }
  
  /**
   * 执行清理任务
   */
  private performCleanup(): void {
    const now = Date.now();
    const timeoutThreshold = now - this.connectionTimeout;
    const connectionsToRemove: string[] = [];
    
    for (const [connectionId, connectionInfo] of this.connections) {
      // 检查连接是否超时
      if (connectionInfo.lastActivity.getTime() < timeoutThreshold) {
        connectionsToRemove.push(connectionId);
      }
      
      // 检查连接状态
      if (connectionInfo.socket.readyState === WebSocket.CLOSED ||
          connectionInfo.socket.readyState === WebSocket.CLOSING) {
        connectionsToRemove.push(connectionId);
      }
    }
    
    // 移除超时或无效的连接
    for (const connectionId of connectionsToRemove) {
      this.removeConnection(connectionId);
    }
    
    if (connectionsToRemove.length > 0) {
      this.logger.debug(`清理了 ${connectionsToRemove.length} 个无效连接`);
    }
  }
  
  /**
   * 启动统计更新
   */
  private startStatsUpdate(): void {
    this.statsTimer = setInterval(() => {
      this.updateStats();
    }, 5000); // 每5秒更新一次统计信息
  }
  
  /**
   * 更新统计信息
   */
  private updateStats(): void {
    const activeConnections = Array.from(this.connections.values())
      .filter(conn => conn.socket.readyState === WebSocket.OPEN).length;
    
    let totalMessageCount = 0;
    let totalBytesTransferred = 0;
    let totalLatency = 0;
    let totalErrors = 0;
    let latencyCount = 0;
    
    for (const connectionInfo of this.connections.values()) {
      totalMessageCount += connectionInfo.messageCount;
      totalBytesTransferred += connectionInfo.bytesReceived + connectionInfo.bytesSent;
      totalErrors += connectionInfo.errorCount;
      
      if (connectionInfo.latency > 0) {
        totalLatency += connectionInfo.latency;
        latencyCount++;
      }
    }
    
    this.stats = {
      totalConnections: this.connections.size,
      activeConnections,
      totalUsers: this.userPools.size,
      averageConnectionsPerUser: this.userPools.size > 0 ? this.connections.size / this.userPools.size : 0,
      totalMessageCount,
      totalBytesTransferred,
      averageLatency: latencyCount > 0 ? totalLatency / latencyCount : 0,
      errorRate: totalMessageCount > 0 ? totalErrors / totalMessageCount : 0,
    };
    
    // 发出统计更新事件
    this.eventEmitter.emit('connectionPool.stats', this.stats);
  }
  
  /**
   * 生成连接ID
   */
  private generateConnectionId(): string {
    return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * 获取统计信息
   */
  public getStats(): ConnectionPoolStats {
    return { ...this.stats };
  }
  
  /**
   * 获取用户数量
   */
  public getUserCount(): number {
    return this.userPools.size;
  }
  
  /**
   * 获取连接数量
   */
  public getConnectionCount(): number {
    return this.connections.size;
  }
  
  /**
   * 销毁连接池
   */
  public destroy(): void {
    // 清除定时器
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
    }
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    if (this.statsTimer) {
      clearInterval(this.statsTimer);
    }
    
    // 关闭所有连接
    for (const connectionInfo of this.connections.values()) {
      if (connectionInfo.socket.readyState === WebSocket.OPEN) {
        connectionInfo.socket.close(1000, 'Server shutdown');
      }
    }
    
    // 清空连接池
    this.connections.clear();
    this.userPools.clear();
    
    this.logger.log('连接池已销毁');
  }
}
