/**
 * 性能优化器
 * 提供内存管理、处理性能优化和错误恢复机制
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';

/**
 * 性能指标
 */
export interface PerformanceMetrics {
  /** 帧率 */
  fps: number;
  /** 平均处理时间 */
  averageProcessingTime: number;
  /** 内存使用量 */
  memoryUsage: number;
  /** CPU使用率 */
  cpuUsage: number;
  /** 处理队列长度 */
  queueLength: number;
  /** 错误率 */
  errorRate: number;
  /** 丢帧率 */
  dropFrameRate: number;
}

/**
 * 优化策略
 */
export enum OptimizationStrategy {
  QUALITY_FIRST = 'quality_first',
  PERFORMANCE_FIRST = 'performance_first',
  BALANCED = 'balanced',
  ADAPTIVE = 'adaptive'
}

/**
 * 性能优化配置
 */
export interface PerformanceOptimizerConfig {
  /** 优化策略 */
  strategy: OptimizationStrategy;
  /** 目标FPS */
  targetFPS: number;
  /** 最大内存使用量（MB） */
  maxMemoryUsage: number;
  /** 最大处理时间（ms） */
  maxProcessingTime: number;
  /** 是否启用自适应优化 */
  enableAdaptiveOptimization: boolean;
  /** 是否启用内存管理 */
  enableMemoryManagement: boolean;
  /** 是否启用错误恢复 */
  enableErrorRecovery: boolean;
  /** 性能监控间隔 */
  monitoringInterval: number;
  /** 优化调整间隔 */
  optimizationInterval: number;
}

/**
 * 处理任务
 */
interface ProcessingTask {
  id: string;
  type: string;
  data: any;
  priority: number;
  timestamp: number;
  timeout: number;
  retryCount: number;
  maxRetries: number;
}

/**
 * 性能优化器
 */
export class PerformanceOptimizer extends EventEmitter {
  private config: PerformanceOptimizerConfig;
  private metrics: PerformanceMetrics;
  private processingQueue: ProcessingTask[] = [];
  private activeProcessors: Map<string, Worker | any> = new Map();
  private memoryManager: MemoryManager;
  private errorRecovery: ErrorRecoveryManager;
  private performanceMonitor: PerformanceMonitor;
  
  private isOptimizing = false;
  private lastOptimizationTime = 0;
  private frameTimeHistory: number[] = [];
  private processingTimeHistory: number[] = [];

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: PerformanceOptimizerConfig = {
    strategy: OptimizationStrategy.BALANCED,
    targetFPS: 30,
    maxMemoryUsage: 512, // 512MB
    maxProcessingTime: 33, // 33ms for 30fps
    enableAdaptiveOptimization: true,
    enableMemoryManagement: true,
    enableErrorRecovery: true,
    monitoringInterval: 1000,
    optimizationInterval: 5000
  };

  constructor(config: Partial<PerformanceOptimizerConfig> = {}) {
    super();
    this.config = { ...PerformanceOptimizer.DEFAULT_CONFIG, ...config };
    
    this.metrics = {
      fps: 0,
      averageProcessingTime: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      queueLength: 0,
      errorRate: 0,
      dropFrameRate: 0
    };

    this.memoryManager = new MemoryManager(this.config);
    this.errorRecovery = new ErrorRecoveryManager(this.config);
    this.performanceMonitor = new PerformanceMonitor(this.config);

    this.startMonitoring();
  }

  /**
   * 开始性能监控
   */
  private startMonitoring(): void {
    setInterval(() => {
      this.updateMetrics();
      this.checkOptimizationNeeded();
    }, this.config.monitoringInterval);
  }

  /**
   * 更新性能指标
   */
  private updateMetrics(): void {
    // 更新FPS
    this.metrics.fps = this.calculateFPS();
    
    // 更新平均处理时间
    this.metrics.averageProcessingTime = this.calculateAverageProcessingTime();
    
    // 更新内存使用量
    this.metrics.memoryUsage = this.memoryManager.getCurrentUsage();
    
    // 更新CPU使用率
    this.metrics.cpuUsage = this.performanceMonitor.getCPUUsage();
    
    // 更新队列长度
    this.metrics.queueLength = this.processingQueue.length;
    
    // 更新错误率
    this.metrics.errorRate = this.errorRecovery.getErrorRate();
    
    // 更新丢帧率
    this.metrics.dropFrameRate = this.calculateDropFrameRate();

    this.emit('metricsUpdated', this.metrics);
  }

  /**
   * 计算FPS
   */
  private calculateFPS(): number {
    if (this.frameTimeHistory.length < 2) return 0;
    
    const recentFrames = this.frameTimeHistory.slice(-30); // 最近30帧
    const totalTime = recentFrames[recentFrames.length - 1] - recentFrames[0];
    
    return recentFrames.length > 1 ? (recentFrames.length - 1) / (totalTime / 1000) : 0;
  }

  /**
   * 计算平均处理时间
   */
  private calculateAverageProcessingTime(): number {
    if (this.processingTimeHistory.length === 0) return 0;
    
    const recentTimes = this.processingTimeHistory.slice(-30);
    return recentTimes.reduce((sum, time) => sum + time, 0) / recentTimes.length;
  }

  /**
   * 计算丢帧率
   */
  private calculateDropFrameRate(): number {
    const targetFrameTime = 1000 / this.config.targetFPS;
    const recentTimes = this.frameTimeHistory.slice(-30);
    
    if (recentTimes.length < 2) return 0;
    
    let droppedFrames = 0;
    for (let i = 1; i < recentTimes.length; i++) {
      const frameTime = recentTimes[i] - recentTimes[i - 1];
      if (frameTime > targetFrameTime * 1.5) { // 超过目标时间50%认为丢帧
        droppedFrames++;
      }
    }
    
    return droppedFrames / (recentTimes.length - 1);
  }

  /**
   * 检查是否需要优化
   */
  private checkOptimizationNeeded(): void {
    const now = Date.now();
    
    if (now - this.lastOptimizationTime < this.config.optimizationInterval) {
      return;
    }

    if (this.shouldOptimize()) {
      this.performOptimization();
      this.lastOptimizationTime = now;
    }
  }

  /**
   * 判断是否应该优化
   */
  private shouldOptimize(): boolean {
    // FPS低于目标
    if (this.metrics.fps < this.config.targetFPS * 0.8) {
      return true;
    }
    
    // 处理时间过长
    if (this.metrics.averageProcessingTime > this.config.maxProcessingTime) {
      return true;
    }
    
    // 内存使用过高
    if (this.metrics.memoryUsage > this.config.maxMemoryUsage * 0.9) {
      return true;
    }
    
    // 错误率过高
    if (this.metrics.errorRate > 0.1) {
      return true;
    }
    
    // 丢帧率过高
    if (this.metrics.dropFrameRate > 0.2) {
      return true;
    }

    return false;
  }

  /**
   * 执行性能优化
   */
  private performOptimization(): void {
    if (this.isOptimizing) return;
    
    this.isOptimizing = true;
    
    try {
      Debug.log('PerformanceOptimizer', '开始性能优化', this.metrics);
      
      // 根据策略执行不同的优化
      switch (this.config.strategy) {
        case OptimizationStrategy.QUALITY_FIRST:
          this.optimizeForQuality();
          break;
        case OptimizationStrategy.PERFORMANCE_FIRST:
          this.optimizeForPerformance();
          break;
        case OptimizationStrategy.BALANCED:
          this.optimizeBalanced();
          break;
        case OptimizationStrategy.ADAPTIVE:
          this.optimizeAdaptive();
          break;
      }
      
      // 内存管理
      if (this.config.enableMemoryManagement) {
        this.memoryManager.performCleanup();
      }
      
      // 错误恢复
      if (this.config.enableErrorRecovery) {
        this.errorRecovery.performRecovery();
      }
      
      this.emit('optimizationPerformed', {
        strategy: this.config.strategy,
        metrics: this.metrics,
        timestamp: Date.now()
      });
      
    } catch (error) {
      Debug.error('PerformanceOptimizer', '性能优化失败', error);
    } finally {
      this.isOptimizing = false;
    }
  }

  /**
   * 质量优先优化
   */
  private optimizeForQuality(): void {
    // 保持高质量，适度降低性能要求
    this.adjustProcessingQuality(1.0);
    this.adjustProcessingFrequency(0.9);
  }

  /**
   * 性能优先优化
   */
  private optimizeForPerformance(): void {
    // 降低质量，提高性能
    this.adjustProcessingQuality(0.7);
    this.adjustProcessingFrequency(1.2);
    this.enablePerformanceMode();
  }

  /**
   * 平衡优化
   */
  private optimizeBalanced(): void {
    // 平衡质量和性能
    if (this.metrics.fps < this.config.targetFPS * 0.9) {
      this.adjustProcessingQuality(0.8);
      this.adjustProcessingFrequency(1.1);
    } else if (this.metrics.averageProcessingTime > this.config.maxProcessingTime * 0.8) {
      this.adjustProcessingQuality(0.9);
      this.adjustProcessingFrequency(1.0);
    }
  }

  /**
   * 自适应优化
   */
  private optimizeAdaptive(): void {
    // 根据当前性能动态调整
    const performanceScore = this.calculatePerformanceScore();
    
    if (performanceScore < 0.5) {
      // 性能很差，大幅优化
      this.adjustProcessingQuality(0.6);
      this.adjustProcessingFrequency(1.3);
      this.enablePerformanceMode();
    } else if (performanceScore < 0.7) {
      // 性能一般，适度优化
      this.adjustProcessingQuality(0.8);
      this.adjustProcessingFrequency(1.1);
    } else if (performanceScore > 0.9) {
      // 性能很好，可以提高质量
      this.adjustProcessingQuality(1.0);
      this.adjustProcessingFrequency(0.9);
    }
  }

  /**
   * 计算性能得分
   */
  private calculatePerformanceScore(): number {
    const fpsScore = Math.min(1, this.metrics.fps / this.config.targetFPS);
    const timeScore = Math.max(0, 1 - this.metrics.averageProcessingTime / this.config.maxProcessingTime);
    const memoryScore = Math.max(0, 1 - this.metrics.memoryUsage / this.config.maxMemoryUsage);
    const errorScore = Math.max(0, 1 - this.metrics.errorRate);
    
    return (fpsScore + timeScore + memoryScore + errorScore) / 4;
  }

  /**
   * 调整处理质量
   */
  private adjustProcessingQuality(factor: number): void {
    this.emit('qualityAdjustment', { factor });
  }

  /**
   * 调整处理频率
   */
  private adjustProcessingFrequency(factor: number): void {
    this.emit('frequencyAdjustment', { factor });
  }

  /**
   * 启用性能模式
   */
  private enablePerformanceMode(): void {
    this.emit('performanceModeEnabled');
  }

  /**
   * 添加处理任务
   */
  public addTask(task: Omit<ProcessingTask, 'timestamp' | 'retryCount'>): void {
    const fullTask: ProcessingTask = {
      ...task,
      timestamp: Date.now(),
      retryCount: 0
    };
    
    // 按优先级插入队列
    const insertIndex = this.processingQueue.findIndex(t => t.priority < fullTask.priority);
    if (insertIndex === -1) {
      this.processingQueue.push(fullTask);
    } else {
      this.processingQueue.splice(insertIndex, 0, fullTask);
    }
    
    this.processQueue();
  }

  /**
   * 处理任务队列
   */
  private processQueue(): void {
    while (this.processingQueue.length > 0 && this.activeProcessors.size < 4) {
      const task = this.processingQueue.shift()!;
      this.processTask(task);
    }
  }

  /**
   * 处理单个任务
   */
  private async processTask(task: ProcessingTask): Promise<void> {
    const startTime = Date.now();
    
    try {
      this.activeProcessors.set(task.id, task);
      
      // 模拟任务处理
      await this.executeTask(task);
      
      const processingTime = Date.now() - startTime;
      this.processingTimeHistory.push(processingTime);
      
      // 限制历史大小
      if (this.processingTimeHistory.length > 100) {
        this.processingTimeHistory.shift();
      }
      
      this.emit('taskCompleted', { task, processingTime });
      
    } catch (error) {
      this.errorRecovery.handleError(error, task);
      
      // 重试逻辑
      if (task.retryCount < task.maxRetries) {
        task.retryCount++;
        this.processingQueue.unshift(task); // 重新加入队列头部
      } else {
        this.emit('taskFailed', { task, error });
      }
      
    } finally {
      this.activeProcessors.delete(task.id);
      this.processQueue(); // 继续处理队列
    }
  }

  /**
   * 执行任务
   */
  private async executeTask(task: ProcessingTask): Promise<any> {
    // 这里应该根据任务类型执行相应的处理
    return new Promise((resolve) => {
      setTimeout(resolve, Math.random() * 10); // 模拟处理时间
    });
  }

  /**
   * 记录帧时间
   */
  public recordFrameTime(): void {
    const now = Date.now();
    this.frameTimeHistory.push(now);
    
    // 限制历史大小
    if (this.frameTimeHistory.length > 100) {
      this.frameTimeHistory.shift();
    }
  }

  /**
   * 获取性能指标
   */
  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<PerformanceOptimizerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    this.memoryManager.updateConfig(this.config);
    this.errorRecovery.updateConfig(this.config);
    this.performanceMonitor.updateConfig(this.config);
    
    this.emit('configUpdated', this.config);
  }

  /**
   * 获取配置
   */
  public getConfig(): PerformanceOptimizerConfig {
    return { ...this.config };
  }

  /**
   * 重置优化器
   */
  public reset(): void {
    this.processingQueue = [];
    this.activeProcessors.clear();
    this.frameTimeHistory = [];
    this.processingTimeHistory = [];
    this.isOptimizing = false;
    this.lastOptimizationTime = 0;
    
    this.memoryManager.reset();
    this.errorRecovery.reset();
    this.performanceMonitor.reset();
    
    this.emit('reset');
  }

  /**
   * 销毁优化器
   */
  public destroy(): void {
    this.reset();
    this.removeAllListeners();
  }
}

/**
 * 内存管理器
 */
class MemoryManager {
  private config: PerformanceOptimizerConfig;
  private memoryCache: Map<string, any> = new Map();
  private cacheAccessTimes: Map<string, number> = new Map();
  private maxCacheSize = 100;

  constructor(config: PerformanceOptimizerConfig) {
    this.config = config;
  }

  /**
   * 获取当前内存使用量
   */
  public getCurrentUsage(): number {
    // 简化实现，实际应该使用performance.memory或其他API
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      return (performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
    }

    // 估算内存使用量
    return this.memoryCache.size * 0.1; // 简化估算
  }

  /**
   * 执行内存清理
   */
  public performCleanup(): void {
    // 清理过期缓存
    this.cleanupExpiredCache();

    // 如果内存使用过高，强制清理
    if (this.getCurrentUsage() > this.config.maxMemoryUsage * 0.8) {
      this.forceCleanup();
    }
  }

  /**
   * 清理过期缓存
   */
  private cleanupExpiredCache(): void {
    const now = Date.now();
    const expireTime = 5 * 60 * 1000; // 5分钟过期

    for (const [key, accessTime] of this.cacheAccessTimes.entries()) {
      if (now - accessTime > expireTime) {
        this.memoryCache.delete(key);
        this.cacheAccessTimes.delete(key);
      }
    }
  }

  /**
   * 强制清理
   */
  private forceCleanup(): void {
    // 清理最少使用的缓存项
    const sortedEntries = Array.from(this.cacheAccessTimes.entries())
      .sort((a, b) => a[1] - b[1]);

    const itemsToRemove = Math.floor(this.memoryCache.size * 0.3);

    for (let i = 0; i < itemsToRemove && i < sortedEntries.length; i++) {
      const key = sortedEntries[i][0];
      this.memoryCache.delete(key);
      this.cacheAccessTimes.delete(key);
    }
  }

  /**
   * 缓存数据
   */
  public cache(key: string, data: any): void {
    if (this.memoryCache.size >= this.maxCacheSize) {
      this.cleanupExpiredCache();
    }

    this.memoryCache.set(key, data);
    this.cacheAccessTimes.set(key, Date.now());
  }

  /**
   * 获取缓存数据
   */
  public getCache(key: string): any {
    if (this.memoryCache.has(key)) {
      this.cacheAccessTimes.set(key, Date.now());
      return this.memoryCache.get(key);
    }
    return null;
  }

  /**
   * 更新配置
   */
  public updateConfig(config: PerformanceOptimizerConfig): void {
    this.config = config;
  }

  /**
   * 重置
   */
  public reset(): void {
    this.memoryCache.clear();
    this.cacheAccessTimes.clear();
  }
}

/**
 * 错误恢复管理器
 */
class ErrorRecoveryManager {
  private config: PerformanceOptimizerConfig;
  private errorHistory: Array<{ error: Error; timestamp: number; task?: any }> = [];
  private recoveryStrategies: Map<string, RecoveryStrategy> = new Map();

  constructor(config: PerformanceOptimizerConfig) {
    this.config = config;
    this.initializeRecoveryStrategies();
  }

  /**
   * 初始化恢复策略
   */
  private initializeRecoveryStrategies(): void {
    this.recoveryStrategies.set('MediaPipeError', {
      maxRetries: 3,
      retryDelay: 1000,
      fallbackAction: 'reinitialize'
    });

    this.recoveryStrategies.set('CameraError', {
      maxRetries: 2,
      retryDelay: 2000,
      fallbackAction: 'switchCamera'
    });

    this.recoveryStrategies.set('ProcessingError', {
      maxRetries: 5,
      retryDelay: 500,
      fallbackAction: 'skipFrame'
    });
  }

  /**
   * 处理错误
   */
  public handleError(error: Error, task?: any): void {
    this.errorHistory.push({
      error,
      timestamp: Date.now(),
      task
    });

    // 限制错误历史大小
    if (this.errorHistory.length > 100) {
      this.errorHistory.shift();
    }

    // 根据错误类型选择恢复策略
    const strategy = this.selectRecoveryStrategy(error);
    if (strategy) {
      this.executeRecoveryStrategy(strategy, error, task);
    }
  }

  /**
   * 选择恢复策略
   */
  private selectRecoveryStrategy(error: Error): RecoveryStrategy | null {
    const errorType = error.constructor.name;
    return this.recoveryStrategies.get(errorType) || this.recoveryStrategies.get('ProcessingError') || null;
  }

  /**
   * 执行恢复策略
   */
  private executeRecoveryStrategy(strategy: RecoveryStrategy, error: Error, task?: any): void {
    setTimeout(() => {
      switch (strategy.fallbackAction) {
        case 'reinitialize':
          this.reinitializeSystem();
          break;
        case 'switchCamera':
          this.switchCamera();
          break;
        case 'skipFrame':
          this.skipFrame();
          break;
      }
    }, strategy.retryDelay);
  }

  /**
   * 重新初始化系统
   */
  private reinitializeSystem(): void {
    // 触发系统重新初始化
    Debug.log('ErrorRecoveryManager', '执行系统重新初始化');
  }

  /**
   * 切换摄像头
   */
  private switchCamera(): void {
    // 触发摄像头切换
    Debug.log('ErrorRecoveryManager', '执行摄像头切换');
  }

  /**
   * 跳过帧
   */
  private skipFrame(): void {
    // 跳过当前帧处理
    Debug.log('ErrorRecoveryManager', '跳过当前帧');
  }

  /**
   * 执行恢复
   */
  public performRecovery(): void {
    // 检查是否需要执行恢复操作
    const recentErrors = this.getRecentErrors(5000); // 最近5秒的错误

    if (recentErrors.length > 10) {
      // 错误过多，执行全面恢复
      this.performFullRecovery();
    }
  }

  /**
   * 获取最近的错误
   */
  private getRecentErrors(timeWindow: number): Array<{ error: Error; timestamp: number; task?: any }> {
    const now = Date.now();
    return this.errorHistory.filter(entry => now - entry.timestamp <= timeWindow);
  }

  /**
   * 执行全面恢复
   */
  private performFullRecovery(): void {
    Debug.log('ErrorRecoveryManager', '执行全面系统恢复');
    // 清理错误历史
    this.errorHistory = [];
    // 触发系统重置
  }

  /**
   * 获取错误率
   */
  public getErrorRate(): number {
    const recentErrors = this.getRecentErrors(60000); // 最近1分钟
    const totalOperations = 1800; // 假设每分钟1800次操作（30fps * 60s）

    return recentErrors.length / totalOperations;
  }

  /**
   * 更新配置
   */
  public updateConfig(config: PerformanceOptimizerConfig): void {
    this.config = config;
  }

  /**
   * 重置
   */
  public reset(): void {
    this.errorHistory = [];
  }
}

/**
 * 性能监控器
 */
class PerformanceMonitor {
  private config: PerformanceOptimizerConfig;
  private cpuUsageHistory: number[] = [];
  private lastCpuMeasurement = 0;

  constructor(config: PerformanceOptimizerConfig) {
    this.config = config;
  }

  /**
   * 获取CPU使用率
   */
  public getCPUUsage(): number {
    // 简化的CPU使用率计算
    const now = Date.now();

    if (now - this.lastCpuMeasurement > 1000) {
      const usage = this.measureCPUUsage();
      this.cpuUsageHistory.push(usage);

      if (this.cpuUsageHistory.length > 60) {
        this.cpuUsageHistory.shift();
      }

      this.lastCpuMeasurement = now;
    }

    return this.cpuUsageHistory.length > 0 ?
      this.cpuUsageHistory[this.cpuUsageHistory.length - 1] : 0;
  }

  /**
   * 测量CPU使用率
   */
  private measureCPUUsage(): number {
    // 简化实现，实际应该使用更精确的方法
    const start = performance.now();

    // 执行一些计算来测量CPU性能
    let sum = 0;
    for (let i = 0; i < 100000; i++) {
      sum += Math.random();
    }

    const end = performance.now();
    const executionTime = end - start;

    // 将执行时间转换为CPU使用率估算
    return Math.min(1, executionTime / 10); // 简化计算
  }

  /**
   * 更新配置
   */
  public updateConfig(config: PerformanceOptimizerConfig): void {
    this.config = config;
  }

  /**
   * 重置
   */
  public reset(): void {
    this.cpuUsageHistory = [];
    this.lastCpuMeasurement = 0;
  }
}

/**
 * 恢复策略
 */
interface RecoveryStrategy {
  maxRetries: number;
  retryDelay: number;
  fallbackAction: string;
}
