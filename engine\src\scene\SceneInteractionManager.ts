/**
 * 场景交互管理器
 * 处理用户与场景中数字人的交互
 */
import { EventEmitter } from '../utils/EventEmitter';
import type { Scene } from './Scene';
import type { Entity } from '../core/Entity';
import type { AvatarInstance } from './SceneAvatarManager';
import { SceneRAGComponent } from './components/SceneRAGComponent';

/**
 * 交互类型
 */
export enum InteractionType {
  CLICK = 'click',
  HOVER = 'hover',
  PROXIMITY = 'proximity',
  VOICE = 'voice',
  GESTURE = 'gesture',
}

/**
 * 交互事件
 */
export interface InteractionEvent {
  /** 事件类型 */
  type: InteractionType;
  /** 目标实体 */
  target: Entity;
  /** 数字人ID */
  avatarId?: string;
  /** 用户位置 */
  userPosition?: { x: number; y: number; z: number };
  /** 交互位置 */
  position?: { x: number; y: number; z: number };
  /** 距离 */
  distance?: number;
  /** 时间戳 */
  timestamp: number;
  /** 额外数据 */
  data?: any;
}

/**
 * 交互区域配置
 */
export interface InteractionZone {
  /** 区域ID */
  id: string;
  /** 关联的数字人ID */
  avatarId: string;
  /** 中心位置 */
  center: { x: number; y: number; z: number };
  /** 半径 */
  radius: number;
  /** 交互类型 */
  types: InteractionType[];
  /** 是否启用 */
  enabled: boolean;
  /** 优先级 */
  priority: number;
}

/**
 * 用户状态
 */
export interface UserState {
  /** 用户ID */
  id: string;
  /** 位置 */
  position: { x: number; y: number; z: number };
  /** 朝向 */
  rotation: { x: number; y: number; z: number };
  /** 是否在交互中 */
  isInteracting: boolean;
  /** 当前交互的数字人ID */
  currentAvatarId?: string;
  /** 当前会话ID */
  currentSessionId?: string;
  /** 最后活动时间 */
  lastActivity: number;
}

/**
 * 场景交互管理器
 */
export class SceneInteractionManager extends EventEmitter {
  /** 关联的场景 */
  private scene: Scene;
  
  /** 交互区域映射 */
  private interactionZones: Map<string, InteractionZone> = new Map();
  
  /** 用户状态映射 */
  private userStates: Map<string, UserState> = new Map();
  
  /** 是否已初始化 */
  private initialized: boolean = false;
  
  /** 交互检测间隔 */
  private interactionCheckInterval: number = 100; // 100ms
  
  /** 上次检测时间 */
  private lastInteractionCheck: number = 0;
  
  /** 默认用户ID */
  private defaultUserId: string = 'default_user';

  /**
   * 构造函数
   */
  constructor(scene: Scene) {
    super();
    this.scene = scene;
  }

  /**
   * 初始化管理器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 监听场景事件
    this.scene.on('entityAdded', this.onEntityAdded.bind(this));
    this.scene.on('entityRemoved', this.onEntityRemoved.bind(this));

    // 监听数字人管理器事件
    const avatarManager = this.scene.getAvatarManager();
    avatarManager.on('avatarAdded', this.onAvatarAdded.bind(this));
    avatarManager.on('avatarRemoved', this.onAvatarRemoved.bind(this));

    // 创建默认用户状态
    this.userStates.set(this.defaultUserId, {
      id: this.defaultUserId,
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      isInteracting: false,
      lastActivity: Date.now(),
    });

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 添加交互区域
   */
  public addInteractionZone(zone: InteractionZone): void {
    this.interactionZones.set(zone.id, zone);
    this.emit('zoneAdded', zone);
  }

  /**
   * 移除交互区域
   */
  public removeInteractionZone(zoneId: string): void {
    const zone = this.interactionZones.get(zoneId);
    if (zone) {
      this.interactionZones.delete(zoneId);
      this.emit('zoneRemoved', zone);
    }
  }

  /**
   * 更新用户位置
   */
  public updateUserPosition(
    userId: string,
    position: { x: number; y: number; z: number },
    rotation?: { x: number; y: number; z: number }
  ): void {
    let userState = this.userStates.get(userId);
    if (!userState) {
      userState = {
        id: userId,
        position,
        rotation: rotation || { x: 0, y: 0, z: 0 },
        isInteracting: false,
        lastActivity: Date.now(),
      };
      this.userStates.set(userId, userState);
    } else {
      userState.position = position;
      if (rotation) {
        userState.rotation = rotation;
      }
      userState.lastActivity = Date.now();
    }

    this.emit('userPositionUpdated', userState);
  }

  /**
   * 处理点击交互
   */
  public async handleClickInteraction(
    position: { x: number; y: number; z: number },
    userId: string = this.defaultUserId
  ): Promise<void> {
    const nearestAvatar = this.findNearestAvatar(position);
    if (!nearestAvatar) {
      return;
    }

    const event: InteractionEvent = {
      type: InteractionType.CLICK,
      target: nearestAvatar.entity,
      avatarId: nearestAvatar.id,
      userPosition: this.getUserPosition(userId),
      position,
      distance: this.calculateDistance(position, nearestAvatar.config.position),
      timestamp: Date.now(),
    };

    await this.processInteraction(event, userId);
  }

  /**
   * 处理语音交互
   */
  public async handleVoiceInteraction(
    message: string,
    userId: string = this.defaultUserId
  ): Promise<void> {
    const userState = this.userStates.get(userId);
    if (!userState) {
      return;
    }

    // 如果用户正在与数字人交互，直接发送消息
    if (userState.isInteracting && userState.currentAvatarId && userState.currentSessionId) {
      const ragComponent = this.findRAGComponent(userState.currentAvatarId);
      if (ragComponent) {
        await ragComponent.sendMessage(userState.currentSessionId, message);
        return;
      }
    }

    // 否则找到最近的数字人开始交互
    const nearestAvatar = this.findNearestAvatar(userState.position);
    if (nearestAvatar) {
      const event: InteractionEvent = {
        type: InteractionType.VOICE,
        target: nearestAvatar.entity,
        avatarId: nearestAvatar.id,
        userPosition: userState.position,
        timestamp: Date.now(),
        data: { message },
      };

      await this.processInteraction(event, userId);
    }
  }

  /**
   * 处理交互事件
   */
  private async processInteraction(event: InteractionEvent, userId: string): Promise<void> {
    if (!event.avatarId) {
      return;
    }

    const userState = this.userStates.get(userId);
    if (!userState) {
      return;
    }

    try {
      // 如果用户已在与其他数字人交互，先结束当前交互
      if (userState.isInteracting && userState.currentAvatarId !== event.avatarId) {
        await this.endInteraction(userId);
      }

      // 开始新的交互
      if (!userState.isInteracting) {
        await this.startInteraction(event.avatarId, userId);
      }

      // 处理具体的交互类型
      switch (event.type) {
        case InteractionType.VOICE:
          if (event.data?.message && userState.currentSessionId) {
            const ragComponent = this.findRAGComponent(event.avatarId);
            if (ragComponent) {
              await ragComponent.sendMessage(userState.currentSessionId, event.data.message);
            }
          }
          break;
        
        case InteractionType.CLICK:
          // 点击交互可以触发默认问候
          if (userState.currentSessionId) {
            const ragComponent = this.findRAGComponent(event.avatarId);
            if (ragComponent) {
              await ragComponent.sendMessage(userState.currentSessionId, '你好');
            }
          }
          break;
      }

      this.emit('interactionProcessed', event);
    } catch (error) {
      console.error('处理交互失败:', error);
      this.emit('interactionError', { event, error });
    }
  }

  /**
   * 开始交互
   */
  private async startInteraction(avatarId: string, userId: string): Promise<void> {
    const userState = this.userStates.get(userId);
    if (!userState) {
      return;
    }

    const ragComponent = this.findRAGComponent(avatarId);
    if (!ragComponent) {
      throw new Error(`未找到数字人 ${avatarId} 的RAG组件`);
    }

    // 开始会话
    const sessionId = await ragComponent.startSession(avatarId, userId);

    // 更新用户状态
    userState.isInteracting = true;
    userState.currentAvatarId = avatarId;
    userState.currentSessionId = sessionId;
    userState.lastActivity = Date.now();

    this.emit('interactionStarted', { userId, avatarId, sessionId });
  }

  /**
   * 结束交互
   */
  public async endInteraction(userId: string): Promise<void> {
    const userState = this.userStates.get(userId);
    if (!userState || !userState.isInteracting) {
      return;
    }

    if (userState.currentSessionId && userState.currentAvatarId) {
      const ragComponent = this.findRAGComponent(userState.currentAvatarId);
      if (ragComponent) {
        await ragComponent.endSession(userState.currentSessionId);
      }
    }

    // 清理用户状态
    const avatarId = userState.currentAvatarId;
    const sessionId = userState.currentSessionId;
    
    userState.isInteracting = false;
    userState.currentAvatarId = undefined;
    userState.currentSessionId = undefined;

    this.emit('interactionEnded', { userId, avatarId, sessionId });
  }

  /**
   * 更新管理器
   */
  public update(deltaTime: number): void {
    if (!this.initialized) {
      return;
    }

    // 检查近距离交互
    const now = Date.now();
    if (now - this.lastInteractionCheck > this.interactionCheckInterval) {
      this.checkProximityInteractions();
      this.lastInteractionCheck = now;
    }
  }

  /**
   * 检查近距离交互
   */
  private checkProximityInteractions(): void {
    for (const userState of this.userStates.values()) {
      if (userState.isInteracting) {
        continue; // 跳过已在交互中的用户
      }

      // 检查是否进入交互区域
      for (const zone of this.interactionZones.values()) {
        if (!zone.enabled || !zone.types.includes(InteractionType.PROXIMITY)) {
          continue;
        }

        const distance = this.calculateDistance(userState.position, zone.center);
        if (distance <= zone.radius) {
          // 触发近距离交互
          const event: InteractionEvent = {
            type: InteractionType.PROXIMITY,
            target: this.findAvatarEntity(zone.avatarId)!,
            avatarId: zone.avatarId,
            userPosition: userState.position,
            position: zone.center,
            distance,
            timestamp: Date.now(),
          };

          this.processInteraction(event, userState.id).catch(console.error);
          break; // 只处理第一个匹配的区域
        }
      }
    }
  }

  /**
   * 查找最近的数字人
   */
  private findNearestAvatar(position: { x: number; y: number; z: number }): AvatarInstance | null {
    const avatarManager = this.scene.getAvatarManager();
    const avatars = avatarManager.getActiveAvatars();
    
    let nearest: AvatarInstance | null = null;
    let minDistance = Infinity;

    for (const avatar of avatars) {
      const distance = this.calculateDistance(position, avatar.config.position);
      if (distance < minDistance) {
        minDistance = distance;
        nearest = avatar;
      }
    }

    return nearest;
  }

  /**
   * 查找RAG组件
   */
  private findRAGComponent(avatarId: string): SceneRAGComponent | null {
    const entities = this.scene.getEntities();
    for (const entity of entities) {
      const ragComponent = entity.getComponent<SceneRAGComponent>('SceneRAGComponent');
      if (ragComponent) {
        const config = ragComponent.getConfig();
        if (config.avatars.some(avatar => avatar.avatarId === avatarId)) {
          return ragComponent;
        }
      }
    }
    return null;
  }

  /**
   * 查找数字人实体
   */
  private findAvatarEntity(avatarId: string): Entity | null {
    const avatarManager = this.scene.getAvatarManager();
    const avatar = avatarManager.getAvatar(avatarId);
    return avatar ? avatar.entity : null;
  }

  /**
   * 计算距离
   */
  private calculateDistance(
    pos1: { x: number; y: number; z: number },
    pos2: { x: number; y: number; z: number }
  ): number {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    const dz = pos1.z - pos2.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }

  /**
   * 获取用户位置
   */
  private getUserPosition(userId: string): { x: number; y: number; z: number } {
    const userState = this.userStates.get(userId);
    return userState ? userState.position : { x: 0, y: 0, z: 0 };
  }

  /**
   * 实体添加事件处理
   */
  private onEntityAdded(entity: Entity): void {
    // 检查是否为RAG组件实体
    const ragComponent = entity.getComponent<SceneRAGComponent>('SceneRAGComponent');
    if (ragComponent) {
      // 为每个数字人创建交互区域
      const config = ragComponent.getConfig();
      for (const avatarConfig of config.avatars) {
        const zone: InteractionZone = {
          id: `zone_${avatarConfig.avatarId}`,
          avatarId: avatarConfig.avatarId,
          center: avatarConfig.position,
          radius: avatarConfig.interactionRadius || 2.0,
          types: [InteractionType.PROXIMITY, InteractionType.CLICK, InteractionType.VOICE],
          enabled: true,
          priority: 1,
        };
        this.addInteractionZone(zone);
      }
    }
  }

  /**
   * 实体移除事件处理
   */
  private onEntityRemoved(entity: Entity): void {
    // 清理相关的交互区域
    const ragComponent = entity.getComponent<SceneRAGComponent>('SceneRAGComponent');
    if (ragComponent) {
      const config = ragComponent.getConfig();
      for (const avatarConfig of config.avatars) {
        this.removeInteractionZone(`zone_${avatarConfig.avatarId}`);
      }
    }
  }

  /**
   * 数字人添加事件处理
   */
  private onAvatarAdded(avatar: AvatarInstance): void {
    // 自动创建交互区域
    const zone: InteractionZone = {
      id: `zone_${avatar.id}`,
      avatarId: avatar.id,
      center: avatar.config.position,
      radius: avatar.config.interactionRadius || 2.0,
      types: [InteractionType.PROXIMITY, InteractionType.CLICK, InteractionType.VOICE],
      enabled: true,
      priority: 1,
    };
    this.addInteractionZone(zone);
  }

  /**
   * 数字人移除事件处理
   */
  private onAvatarRemoved(avatarId: string): void {
    this.removeInteractionZone(`zone_${avatarId}`);
  }

  /**
   * 获取交互区域
   */
  public getInteractionZones(): InteractionZone[] {
    return Array.from(this.interactionZones.values());
  }

  /**
   * 获取用户状态
   */
  public getUserStates(): UserState[] {
    return Array.from(this.userStates.values());
  }

  /**
   * 销毁管理器
   */
  public dispose(): void {
    // 结束所有交互
    for (const userState of this.userStates.values()) {
      if (userState.isInteracting) {
        this.endInteraction(userState.id).catch(console.error);
      }
    }

    // 清理资源
    this.interactionZones.clear();
    this.userStates.clear();

    // 移除事件监听
    this.scene.off('entityAdded', this.onEntityAdded.bind(this));
    this.scene.off('entityRemoved', this.onEntityRemoved.bind(this));

    const avatarManager = this.scene.getAvatarManager();
    avatarManager.off('avatarAdded', this.onAvatarAdded.bind(this));
    avatarManager.off('avatarRemoved', this.onAvatarRemoved.bind(this));

    this.initialized = false;
    this.emit('disposed');
  }
}
