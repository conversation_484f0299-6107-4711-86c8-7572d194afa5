import { 
  Entity, 
  PrimaryGeneratedColumn, 
  Column, 
  CreateDateColumn, 
  UpdateDateColumn,
  Index 
} from 'typeorm';

/**
 * 学习记录实体
 * 存储xAPI学习记录语句的本地副本
 */
@Entity('learning_records')
@Index(['userId', 'timestamp'])
@Index(['verb'])
@Index(['syncStatus'])
export class LearningRecord {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'statement_id', unique: true })
  @Index()
  statementId: string;

  @Column({ name: 'user_id' })
  @Index()
  userId: string;

  @Column()
  verb: string;

  @Column({ name: 'object_id' })
  objectId: string;

  @Column({ type: 'text', nullable: true })
  result: string;

  @Column({ type: 'text', nullable: true })
  context: string;

  @Column({ type: 'datetime' })
  @Index()
  timestamp: Date;

  @Column({ name: 'raw_statement', type: 'text' })
  rawStatement: string;

  @Column({ 
    name: 'sync_status', 
    type: 'enum', 
    enum: ['pending', 'syncing', 'synced', 'failed', 'retry'],
    default: 'pending'
  })
  @Index()
  syncStatus: string;

  @Column({ name: 'retry_count', default: 0 })
  retryCount: number;

  @Column({ name: 'last_error', type: 'text', nullable: true })
  lastError: string;

  @Column({ name: 'last_sync_attempt', type: 'datetime', nullable: true })
  lastSyncAttempt: Date;

  @Column({ name: 'synced_at', type: 'datetime', nullable: true })
  syncedAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
