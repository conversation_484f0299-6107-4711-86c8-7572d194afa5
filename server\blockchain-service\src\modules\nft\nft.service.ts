/**
 * NFT服务 - 处理NFT相关的业务逻辑
 */

import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, Like, In } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { ConfigService } from '@nestjs/config';

import { NFTToken } from '../../entities/nft-token.entity';
import { User } from '../../entities/user.entity';
import { BlockchainService } from '../blockchain/blockchain.service';
import { IPFSService } from '../ipfs/ipfs.service';
import { TransactionService } from '../transaction/transaction.service';

import { CreateNFTDto } from './dto/create-nft.dto';
import { UpdateNFTDto } from './dto/update-nft.dto';
import { NFTSearchDto } from './dto/nft-search.dto';
import { TransferNFTDto } from './dto/transfer-nft.dto';
import { SetNFTPriceDto } from './dto/set-nft-price.dto';

@Injectable()
export class NFTService {
  private readonly logger = new Logger(NFTService.name);

  constructor(
    @InjectRepository(NFTToken)
    private readonly nftRepository: Repository<NFTToken>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectQueue('nft-processing')
    private readonly nftQueue: Queue,
    private readonly blockchainService: BlockchainService,
    private readonly ipfsService: IPFSService,
    private readonly transactionService: TransactionService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 铸造NFT
   */
  async mintNFT(createNFTDto: CreateNFTDto, creatorAddress: string): Promise<NFTToken> {
    this.logger.log(`开始铸造NFT: ${createNFTDto.name}`);

    try {
      // 验证创建者
      const creator = await this.userRepository.findOne({
        where: { walletAddress: creatorAddress },
      });

      if (!creator) {
        throw new BadRequestException('创建者不存在');
      }

      // 上传元数据到IPFS
      const metadata = {
        name: createNFTDto.name,
        description: createNFTDto.description,
        image: createNFTDto.image,
        external_url: createNFTDto.externalUrl,
        attributes: createNFTDto.attributes || [],
        animation_url: createNFTDto.animationUrl,
        dl_engine_data: {
          scene_id: createNFTDto.sceneId,
          asset_type: createNFTDto.assetType,
          engine_version: createNFTDto.engineVersion || '1.0.0',
          creation_timestamp: Date.now(),
          creator_address: creatorAddress,
          license_type: createNFTDto.licenseType,
          educational_metadata: createNFTDto.educationalMetadata,
          technical_metadata: createNFTDto.technicalMetadata,
          interaction_metadata: createNFTDto.interactionMetadata,
        },
      };

      const ipfsHash = await this.ipfsService.uploadMetadata(metadata);
      const tokenURI = `ipfs://${ipfsHash}`;

      // 调用智能合约铸造NFT
      const mintResult = await this.blockchainService.mintNFT({
        to: creatorAddress,
        tokenURI,
        royaltyRecipient: createNFTDto.royaltyRecipient || creatorAddress,
        royaltyPercentage: createNFTDto.royaltyPercentage || 0,
      });

      // 创建NFT记录
      const nft = this.nftRepository.create({
        tokenId: mintResult.tokenId,
        contractAddress: mintResult.contractAddress,
        chainId: mintResult.chainId,
        ownerAddress: creatorAddress,
        creatorAddress: creatorAddress,
        tokenURI,
        metadata,
        ipfsHash,
        mintTransactionHash: mintResult.transactionHash,
        mintBlockNumber: mintResult.blockNumber,
        royaltyRecipient: createNFTDto.royaltyRecipient || creatorAddress,
        royaltyPercentage: createNFTDto.royaltyPercentage || 0,
      });

      const savedNFT = await this.nftRepository.save(nft);

      // 记录交易
      await this.transactionService.recordTransaction({
        hash: mintResult.transactionHash,
        type: 'nft_mint',
        from: '0x0000000000000000000000000000000000000000',
        to: creatorAddress,
        value: '0',
        gasUsed: mintResult.gasUsed,
        gasPrice: mintResult.gasPrice,
        blockNumber: mintResult.blockNumber,
        status: 'confirmed',
        metadata: {
          tokenId: mintResult.tokenId,
          contractAddress: mintResult.contractAddress,
          nftId: savedNFT.id,
        },
      });

      // 添加到处理队列（用于后续处理，如通知、索引等）
      await this.nftQueue.add('nft-minted', {
        nftId: savedNFT.id,
        creatorAddress,
        transactionHash: mintResult.transactionHash,
      });

      this.logger.log(`NFT铸造成功: ${savedNFT.id}`);
      return savedNFT;
    } catch (error) {
      this.logger.error(`NFT铸造失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 转移NFT
   */
  async transferNFT(transferDto: TransferNFTDto, fromAddress: string): Promise<NFTToken> {
    this.logger.log(`转移NFT: ${transferDto.tokenId} -> ${transferDto.toAddress}`);

    try {
      // 查找NFT
      const nft = await this.findByTokenId(transferDto.tokenId, transferDto.contractAddress);
      
      if (nft.ownerAddress !== fromAddress) {
        throw new BadRequestException('您不是此NFT的拥有者');
      }

      if (nft.status !== 'active') {
        throw new BadRequestException('此NFT无法转移');
      }

      // 验证接收者地址
      if (!this.blockchainService.isValidAddress(transferDto.toAddress)) {
        throw new BadRequestException('无效的接收者地址');
      }

      // 调用智能合约转移NFT
      const transferResult = await this.blockchainService.transferNFT({
        contractAddress: transferDto.contractAddress,
        tokenId: transferDto.tokenId,
        from: fromAddress,
        to: transferDto.toAddress,
      });

      // 更新NFT记录
      nft.ownerAddress = transferDto.toAddress;
      nft.recordTransfer();
      
      if (nft.isForSale) {
        nft.removeFromSale();
      }

      const updatedNFT = await this.nftRepository.save(nft);

      // 记录交易
      await this.transactionService.recordTransaction({
        hash: transferResult.transactionHash,
        type: 'nft_transfer',
        from: fromAddress,
        to: transferDto.toAddress,
        value: '0',
        gasUsed: transferResult.gasUsed,
        gasPrice: transferResult.gasPrice,
        blockNumber: transferResult.blockNumber,
        status: 'confirmed',
        metadata: {
          tokenId: transferDto.tokenId,
          contractAddress: transferDto.contractAddress,
          nftId: nft.id,
        },
      });

      // 添加到处理队列
      await this.nftQueue.add('nft-transferred', {
        nftId: nft.id,
        fromAddress,
        toAddress: transferDto.toAddress,
        transactionHash: transferResult.transactionHash,
      });

      this.logger.log(`NFT转移成功: ${nft.id}`);
      return updatedNFT;
    } catch (error) {
      this.logger.error(`NFT转移失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 设置NFT价格
   */
  async setNFTPrice(setPriceDto: SetNFTPriceDto, ownerAddress: string): Promise<NFTToken> {
    const nft = await this.findByTokenId(setPriceDto.tokenId, setPriceDto.contractAddress);
    
    if (nft.ownerAddress !== ownerAddress) {
      throw new BadRequestException('您不是此NFT的拥有者');
    }

    if (setPriceDto.price && parseFloat(setPriceDto.price) > 0) {
      nft.setForSale(setPriceDto.price, setPriceDto.currency || 'ETH');
    } else {
      nft.removeFromSale();
    }

    return await this.nftRepository.save(nft);
  }

  /**
   * 搜索NFT
   */
  async searchNFTs(searchDto: NFTSearchDto): Promise<{ nfts: NFTToken[]; total: number }> {
    const queryBuilder = this.nftRepository.createQueryBuilder('nft')
      .leftJoinAndSelect('nft.owner', 'owner')
      .leftJoinAndSelect('nft.creator', 'creator');

    // 基础过滤条件
    queryBuilder.where('nft.status = :status', { status: 'active' });

    // 搜索关键词
    if (searchDto.keyword) {
      queryBuilder.andWhere(
        '(nft.metadata->"$.name" LIKE :keyword OR nft.metadata->"$.description" LIKE :keyword)',
        { keyword: `%${searchDto.keyword}%` }
      );
    }

    // 资产类型过滤
    if (searchDto.assetType) {
      queryBuilder.andWhere('nft.metadata->"$.dl_engine_data.asset_type" = :assetType', {
        assetType: searchDto.assetType,
      });
    }

    // 许可证类型过滤
    if (searchDto.licenseType) {
      queryBuilder.andWhere('nft.metadata->"$.dl_engine_data.license_type" = :licenseType', {
        licenseType: searchDto.licenseType,
      });
    }

    // 创建者过滤
    if (searchDto.creator) {
      queryBuilder.andWhere('nft.creatorAddress = :creator', { creator: searchDto.creator });
    }

    // 拥有者过滤
    if (searchDto.owner) {
      queryBuilder.andWhere('nft.ownerAddress = :owner', { owner: searchDto.owner });
    }

    // 价格范围过滤
    if (searchDto.minPrice) {
      queryBuilder.andWhere('nft.price >= :minPrice', { minPrice: searchDto.minPrice });
    }
    if (searchDto.maxPrice) {
      queryBuilder.andWhere('nft.price <= :maxPrice', { maxPrice: searchDto.maxPrice });
    }

    // 是否在售过滤
    if (searchDto.forSale !== undefined) {
      queryBuilder.andWhere('nft.isForSale = :forSale', { forSale: searchDto.forSale });
    }

    // 是否已验证过滤
    if (searchDto.verified !== undefined) {
      queryBuilder.andWhere('nft.isVerified = :verified', { verified: searchDto.verified });
    }

    // 排序
    const sortField = searchDto.sortBy || 'createdAt';
    const sortOrder = searchDto.sortOrder || 'DESC';
    
    switch (sortField) {
      case 'price':
        queryBuilder.orderBy('nft.price', sortOrder);
        break;
      case 'viewCount':
        queryBuilder.orderBy('nft.viewCount', sortOrder);
        break;
      case 'likeCount':
        queryBuilder.orderBy('nft.likeCount', sortOrder);
        break;
      case 'transferCount':
        queryBuilder.orderBy('nft.transferCount', sortOrder);
        break;
      default:
        queryBuilder.orderBy('nft.createdAt', sortOrder);
    }

    // 分页
    const page = searchDto.page || 1;
    const limit = Math.min(searchDto.limit || 20, 100); // 最大100条
    const offset = (page - 1) * limit;

    queryBuilder.skip(offset).take(limit);

    const [nfts, total] = await queryBuilder.getManyAndCount();

    return { nfts, total };
  }

  /**
   * 根据Token ID查找NFT
   */
  async findByTokenId(tokenId: string, contractAddress: string): Promise<NFTToken> {
    const nft = await this.nftRepository.findOne({
      where: { tokenId, contractAddress },
      relations: ['owner', 'creator'],
    });

    if (!nft) {
      throw new NotFoundException('NFT不存在');
    }

    return nft;
  }

  /**
   * 获取用户的NFT
   */
  async getUserNFTs(userAddress: string, page = 1, limit = 20): Promise<{ nfts: NFTToken[]; total: number }> {
    const [nfts, total] = await this.nftRepository.findAndCount({
      where: { ownerAddress: userAddress, status: 'active' },
      relations: ['creator'],
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return { nfts, total };
  }

  /**
   * 获取用户创建的NFT
   */
  async getUserCreatedNFTs(userAddress: string, page = 1, limit = 20): Promise<{ nfts: NFTToken[]; total: number }> {
    const [nfts, total] = await this.nftRepository.findAndCount({
      where: { creatorAddress: userAddress, status: 'active' },
      relations: ['owner'],
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return { nfts, total };
  }

  /**
   * 增加NFT查看次数
   */
  async incrementViewCount(tokenId: string, contractAddress: string): Promise<void> {
    await this.nftRepository.increment(
      { tokenId, contractAddress },
      'viewCount',
      1
    );
  }

  /**
   * 点赞NFT
   */
  async likeNFT(tokenId: string, contractAddress: string): Promise<NFTToken> {
    const nft = await this.findByTokenId(tokenId, contractAddress);
    nft.incrementLikeCount();
    return await this.nftRepository.save(nft);
  }

  /**
   * 取消点赞NFT
   */
  async unlikeNFT(tokenId: string, contractAddress: string): Promise<NFTToken> {
    const nft = await this.findByTokenId(tokenId, contractAddress);
    nft.decrementLikeCount();
    return await this.nftRepository.save(nft);
  }

  /**
   * 验证NFT
   */
  async verifyNFT(tokenId: string, contractAddress: string): Promise<NFTToken> {
    const nft = await this.findByTokenId(tokenId, contractAddress);
    nft.verify();
    return await this.nftRepository.save(nft);
  }

  /**
   * 取消验证NFT
   */
  async unverifyNFT(tokenId: string, contractAddress: string): Promise<NFTToken> {
    const nft = await this.findByTokenId(tokenId, contractAddress);
    nft.unverify();
    return await this.nftRepository.save(nft);
  }

  /**
   * 推荐NFT
   */
  async featureNFT(tokenId: string, contractAddress: string): Promise<NFTToken> {
    const nft = await this.findByTokenId(tokenId, contractAddress);
    nft.feature();
    return await this.nftRepository.save(nft);
  }

  /**
   * 取消推荐NFT
   */
  async unfeatureNFT(tokenId: string, contractAddress: string): Promise<NFTToken> {
    const nft = await this.findByTokenId(tokenId, contractAddress);
    nft.unfeature();
    return await this.nftRepository.save(nft);
  }

  /**
   * 获取推荐NFT
   */
  async getFeaturedNFTs(limit = 10): Promise<NFTToken[]> {
    return await this.nftRepository.find({
      where: { isFeatured: true, status: 'active' },
      relations: ['owner', 'creator'],
      order: { updatedAt: 'DESC' },
      take: limit,
    });
  }

  /**
   * 获取热门NFT
   */
  async getPopularNFTs(limit = 10): Promise<NFTToken[]> {
    return await this.nftRepository.find({
      where: { status: 'active' },
      relations: ['owner', 'creator'],
      order: { viewCount: 'DESC', likeCount: 'DESC' },
      take: limit,
    });
  }

  /**
   * 获取最新NFT
   */
  async getLatestNFTs(limit = 10): Promise<NFTToken[]> {
    return await this.nftRepository.find({
      where: { status: 'active' },
      relations: ['owner', 'creator'],
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  /**
   * 更新NFT元数据
   */
  async updateNFTMetadata(tokenId: string, contractAddress: string, updateDto: UpdateNFTDto, ownerAddress: string): Promise<NFTToken> {
    const nft = await this.findByTokenId(tokenId, contractAddress);
    
    if (nft.ownerAddress !== ownerAddress) {
      throw new BadRequestException('您不是此NFT的拥有者');
    }

    // 更新元数据
    const updatedMetadata = {
      ...nft.metadata,
      ...updateDto,
      dl_engine_data: {
        ...nft.metadata.dl_engine_data,
        ...updateDto.dlEngineData,
      },
    };

    nft.updateMetadata(updatedMetadata);
    
    // 如果有新的元数据，上传到IPFS
    if (Object.keys(updateDto).length > 0) {
      const ipfsHash = await this.ipfsService.uploadMetadata(updatedMetadata);
      nft.ipfsHash = ipfsHash;
      nft.tokenURI = `ipfs://${ipfsHash}`;
    }

    return await this.nftRepository.save(nft);
  }
}
