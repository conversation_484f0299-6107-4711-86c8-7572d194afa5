import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as jieba from 'jieba';

/**
 * 意图类型
 */
export enum IntentType {
  GREETING = 'greeting',           // 问候
  QUESTION = 'question',           // 询问
  COMPLAINT = 'complaint',         // 投诉
  PRAISE = 'praise',              // 表扬
  REQUEST = 'request',            // 请求
  GOODBYE = 'goodbye',            // 告别
  CHITCHAT = 'chitchat',          // 闲聊
  UNKNOWN = 'unknown',            // 未知
}

/**
 * 实体类型
 */
export interface Entity {
  type: string;
  value: string;
  start: number;
  end: number;
  confidence: number;
}

/**
 * 意图分析结果
 */
export interface IntentResult {
  type: IntentType;
  confidence: number;
  entities: Entity[];
  keywords: string[];
}

/**
 * 意图模式
 */
interface IntentPattern {
  type: IntentType;
  keywords: string[];
  patterns: RegExp[];
  weight: number;
}

@Injectable()
export class IntentService {
  private intentPatterns: IntentPattern[] = [];

  constructor(private configService: ConfigService) {
    this.initializeIntentPatterns();
  }

  /**
   * 初始化意图模式
   */
  private initializeIntentPatterns(): void {
    this.intentPatterns = [
      // 问候意图
      {
        type: IntentType.GREETING,
        keywords: ['你好', '您好', '早上好', '下午好', '晚上好', '嗨', 'hi', 'hello'],
        patterns: [
          /^(你好|您好|嗨|hi|hello)/i,
          /(早上好|下午好|晚上好)/i,
        ],
        weight: 1.0,
      },

      // 告别意图
      {
        type: IntentType.GOODBYE,
        keywords: ['再见', '拜拜', '回见', '下次见', 'bye', 'goodbye'],
        patterns: [
          /^(再见|拜拜|回见|下次见|bye|goodbye)/i,
          /(谢谢.*再见|感谢.*再见)/i,
        ],
        weight: 1.0,
      },

      // 询问意图
      {
        type: IntentType.QUESTION,
        keywords: ['什么', '怎么', '如何', '为什么', '哪里', '谁', '多少', '几', '吗', '呢'],
        patterns: [
          /^(什么|怎么|如何|为什么|哪里|谁|多少|几)/,
          /.*[？?]$/,
          /.*(是什么|怎么办|如何做|为什么会|在哪里)/,
        ],
        weight: 0.8,
      },

      // 投诉意图
      {
        type: IntentType.COMPLAINT,
        keywords: ['投诉', '抱怨', '不满', '问题', '错误', '故障', '坏了', '不好', '差'],
        patterns: [
          /.*(投诉|抱怨|不满|有问题)/,
          /.*(坏了|故障|错误|不好用)/,
          /.*(太差|很差|不行)/,
        ],
        weight: 0.9,
      },

      // 表扬意图
      {
        type: IntentType.PRAISE,
        keywords: ['好', '棒', '优秀', '不错', '满意', '喜欢', '赞', '厉害'],
        patterns: [
          /.*(很好|真好|太好了|不错|满意)/,
          /.*(喜欢|赞|厉害|优秀|棒)/,
          /.*(感谢|谢谢)/,
        ],
        weight: 0.8,
      },

      // 请求意图
      {
        type: IntentType.REQUEST,
        keywords: ['请', '帮', '能否', '可以', '希望', '想要', '需要'],
        patterns: [
          /^(请|帮|能否|可以)/,
          /.*(帮我|帮忙|请帮|能帮)/,
          /.*(想要|需要|希望)/,
        ],
        weight: 0.7,
      },

      // 闲聊意图
      {
        type: IntentType.CHITCHAT,
        keywords: ['天气', '今天', '心情', '吃饭', '睡觉', '工作', '学习'],
        patterns: [
          /.*(天气|今天|心情)/,
          /.*(吃饭|睡觉|工作|学习)/,
          /.*(怎么样|如何|还好吗)/,
        ],
        weight: 0.5,
      },
    ];
  }

  /**
   * 分析意图
   */
  async analyzeIntent(text: string): Promise<IntentResult> {
    if (!text || text.trim().length === 0) {
      return {
        type: IntentType.UNKNOWN,
        confidence: 0,
        entities: [],
        keywords: [],
      };
    }

    // 预处理文本
    const processedText = this.preprocessText(text);

    // 分词
    const tokens = this.tokenize(processedText);

    // 提取关键词
    const keywords = this.extractKeywords(tokens);

    // 匹配意图模式
    const intentScores = this.matchIntentPatterns(processedText, keywords);

    // 获取最佳意图
    const bestIntent = this.getBestIntent(intentScores);

    // 提取实体
    const entities = this.extractEntities(processedText, tokens);

    return {
      type: bestIntent.type,
      confidence: bestIntent.confidence,
      entities,
      keywords,
    };
  }

  /**
   * 预处理文本
   */
  private preprocessText(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\u4e00-\u9fff\w\s？?！!。.，,]/g, '') // 保留中文、英文、数字和基本标点
      .trim();
  }

  /**
   * 分词
   */
  private tokenize(text: string): string[] {
    // 使用jieba进行中文分词
    return jieba.cut(text, true);
  }

  /**
   * 提取关键词
   */
  private extractKeywords(tokens: string[]): string[] {
    // 停用词列表
    const stopWords = new Set([
      '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
      '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
      '自己', '这', '那', '它', '他', '她', '我们', '你们', '他们', '这个', '那个',
    ]);

    return tokens.filter(token => 
      token.length > 1 && 
      !stopWords.has(token) && 
      !/^\d+$/.test(token) // 过滤纯数字
    );
  }

  /**
   * 匹配意图模式
   */
  private matchIntentPatterns(text: string, keywords: string[]): Map<IntentType, number> {
    const scores = new Map<IntentType, number>();

    for (const pattern of this.intentPatterns) {
      let score = 0;

      // 关键词匹配
      const keywordMatches = keywords.filter(keyword => 
        pattern.keywords.some(patternKeyword => 
          keyword.includes(patternKeyword) || patternKeyword.includes(keyword)
        )
      );
      
      if (keywordMatches.length > 0) {
        score += (keywordMatches.length / keywords.length) * 0.6;
      }

      // 正则模式匹配
      for (const regex of pattern.patterns) {
        if (regex.test(text)) {
          score += 0.4;
          break;
        }
      }

      // 应用权重
      score *= pattern.weight;

      if (score > 0) {
        scores.set(pattern.type, Math.max(scores.get(pattern.type) || 0, score));
      }
    }

    return scores;
  }

  /**
   * 获取最佳意图
   */
  private getBestIntent(scores: Map<IntentType, number>): { type: IntentType; confidence: number } {
    let bestType = IntentType.UNKNOWN;
    let bestScore = 0;

    for (const [type, score] of scores.entries()) {
      if (score > bestScore) {
        bestType = type;
        bestScore = score;
      }
    }

    // 如果分数太低，认为是未知意图
    if (bestScore < 0.3) {
      bestType = IntentType.UNKNOWN;
      bestScore = 0.1;
    }

    return {
      type: bestType,
      confidence: Math.min(1.0, bestScore),
    };
  }

  /**
   * 提取实体
   */
  private extractEntities(text: string, tokens: string[]): Entity[] {
    const entities: Entity[] = [];

    // 提取数字实体
    const numberRegex = /\d+/g;
    let match;
    while ((match = numberRegex.exec(text)) !== null) {
      entities.push({
        type: 'number',
        value: match[0],
        start: match.index,
        end: match.index + match[0].length,
        confidence: 0.9,
      });
    }

    // 提取时间实体
    const timePatterns = [
      /今天|明天|昨天|后天|前天/g,
      /\d+点|\d+时/g,
      /上午|下午|晚上|早上|中午/g,
    ];

    for (const pattern of timePatterns) {
      pattern.lastIndex = 0; // 重置正则表达式
      while ((match = pattern.exec(text)) !== null) {
        entities.push({
          type: 'time',
          value: match[0],
          start: match.index,
          end: match.index + match[0].length,
          confidence: 0.8,
        });
      }
    }

    // 提取地点实体（简单实现）
    const locationKeywords = ['医院', '学校', '公司', '家', '商店', '银行', '餐厅'];
    for (const keyword of locationKeywords) {
      const index = text.indexOf(keyword);
      if (index !== -1) {
        entities.push({
          type: 'location',
          value: keyword,
          start: index,
          end: index + keyword.length,
          confidence: 0.7,
        });
      }
    }

    return entities;
  }

  /**
   * 批量分析意图
   */
  async batchAnalyzeIntent(texts: string[]): Promise<IntentResult[]> {
    return Promise.all(texts.map(text => this.analyzeIntent(text)));
  }

  /**
   * 获取意图统计
   */
  getIntentStatistics(): any {
    return {
      supportedIntents: Object.values(IntentType),
      patternCount: this.intentPatterns.length,
      totalKeywords: this.intentPatterns.reduce((sum, pattern) => sum + pattern.keywords.length, 0),
    };
  }

  /**
   * 添加自定义意图模式
   */
  addCustomIntentPattern(pattern: IntentPattern): void {
    this.intentPatterns.push(pattern);
  }

  /**
   * 更新意图模式权重
   */
  updateIntentWeight(intentType: IntentType, weight: number): void {
    const pattern = this.intentPatterns.find(p => p.type === intentType);
    if (pattern) {
      pattern.weight = weight;
    }
  }
}
