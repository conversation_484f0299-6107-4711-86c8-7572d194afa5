/**
 * UIEventComponent.ts
 *
 * UI事件组件，用于处理UI元素的事件
 */

import { Component } from '../../core/Component';
import { Vector2, Vector3, Raycaster } from 'three';
import type { Camera } from 'three';
import { IUIEvent, UIEventType } from '../interfaces/IUIElement';
import { UIComponent } from './UIComponent';
import { UI3DComponent } from './UI3DComponent';

/**
 * UI事件数据
 */
export interface UIEventData {
  type: UIEventType;
  target: UIComponent;
  originalEvent?: any;
  position?: Vector2 | Vector3;
  delta?: Vector2;
  button?: number;
  key?: string;
  keyCode?: number;
  altKey?: boolean;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  value?: any;
  stopPropagation: boolean;
  preventDefault: boolean;
  timestamp: number;
}

/**
 * UI事件
 * 实现IUIEvent接口
 */
export class UIEvent implements IUIEvent {
  type: UIEventType;
  target: UIComponent;
  data: UIEventData;

  /**
   * 构造函数
   * @param type 事件类型
   * @param target 目标UI元素
   * @param data 事件数据
   */
  constructor(type: UIEventType, target: UIComponent, data: Partial<UIEventData> = {}) {
    this.type = type;
    this.target = target;
    this.data = {
      type,
      target,
      stopPropagation: false,
      preventDefault: false,
      timestamp: Date.now(),
      ...data
    };
  }

  /**
   * 阻止事件冒泡
   */
  stopPropagation(): void {
    this.data.stopPropagation = true;
  }

  /**
   * 阻止事件默认行为
   */
  preventDefault(): void {
    this.data.preventDefault = true;
  }
}

/**
 * 事件监听器类型
 */
export type EventListener = (event: UIEvent) => void;

/**
 * UI事件组件
 * 用于处理实体的UI事件
 */
export class UIEventComponent extends Component {
  // 事件监听器映射
  private eventListeners: Map<UIEventType, EventListener[]> = new Map();

  // 当前悬停的UI元素
  private hoveredElement?: UIComponent;

  // 当前聚焦的UI元素
  private focusedElement?: UIComponent;

  // 当前按下的UI元素
  private activeElement?: UIComponent;

  // 拖拽状态
  private isDragging: boolean = false;
  private dragStartPosition?: Vector2;
  private dragCurrentPosition?: Vector2;

  // 射线投射器（用于3D UI元素）
  private raycaster: Raycaster = new Raycaster();

  /**
   * 构造函数
   */
  constructor() {
    // 调用基类构造函数，传入组件类型名称
    super('UIEvent');

    // 初始化事件监听器映射
    Object.values(UIEventType).forEach(type => {
      this.eventListeners.set(type as UIEventType, []);
    });
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器函数
   */
  addEventListener(type: UIEventType, listener: EventListener): void {
    const listeners = this.eventListeners.get(type) || [];
    if (!listeners.includes(listener)) {
      listeners.push(listener);
      this.eventListeners.set(type, listeners);
    }
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器函数
   */
  removeEventListener(type: UIEventType, listener: EventListener): void {
    const listeners = this.eventListeners.get(type) || [];
    const index = listeners.indexOf(listener);
    if (index !== -1) {
      listeners.splice(index, 1);
    }
  }

  /**
   * 分发事件
   * @param event UI事件
   */
  dispatchEvent(event: UIEvent): void {
    // 获取事件类型的监听器
    const listeners = this.eventListeners.get(event.type) || [];

    // 调用所有监听器
    for (const listener of listeners) {
      listener(event);

      // 如果事件被阻止冒泡，则停止
      if (event.data.stopPropagation) {
        break;
      }
    }

    // 如果没有阻止冒泡，且目标有父元素，则向上冒泡
    if (!event.data.stopPropagation && event.target.parent) {
      const parentEvent = new UIEvent(event.type, event.target.parent as UIComponent, event.data);
      this.dispatchEvent(parentEvent);
    }
  }

  /**
   * 创建并分发事件
   * @param type 事件类型
   * @param target 目标UI元素
   * @param data 事件数据
   */
  createAndDispatchEvent(type: UIEventType, target: UIComponent, data: Partial<UIEventData> = {}): void {
    const event = new UIEvent(type, target, data);
    this.dispatchEvent(event);
  }

  /**
   * 处理鼠标移动
   * @param x 鼠标X坐标
   * @param y 鼠标Y坐标
   * @param uiElements UI元素列表
   * @param camera 相机（用于3D UI元素）
   */
  handleMouseMove(x: number, y: number, uiElements: UIComponent[], camera?: Camera): void {
    // 更新拖拽状态
    if (this.isDragging && this.activeElement && this.dragStartPosition) {
      this.dragCurrentPosition = new Vector2(x, y);
      const delta = new Vector2().subVectors(this.dragCurrentPosition, this.dragStartPosition);

      // 分发拖拽事件
      this.createAndDispatchEvent(UIEventType.DRAG, this.activeElement, {
        position: new Vector2(x, y),
        delta,
        originalEvent: { type: 'mousemove', clientX: x, clientY: y }
      });
    }

    // 查找当前悬停的元素
    const element = this.findElementAtPosition(x, y, uiElements, camera);

    // 如果悬停元素改变
    if (element !== this.hoveredElement) {
      // 如果之前有悬停元素，分发鼠标离开事件
      if (this.hoveredElement) {
        this.createAndDispatchEvent(UIEventType.HOVER, this.hoveredElement, {
          position: new Vector2(x, y),
          originalEvent: { type: 'mouseleave', clientX: x, clientY: y }
        });

        // 如果是3D UI元素，更新悬停状态
        if (this.hoveredElement instanceof UI3DComponent) {
          this.hoveredElement.setHovered(false);
        }
      }

      // 更新悬停元素
      this.hoveredElement = element;

      // 如果有新的悬停元素，分发鼠标进入事件
      if (this.hoveredElement) {
        this.createAndDispatchEvent(UIEventType.HOVER, this.hoveredElement, {
          position: new Vector2(x, y),
          originalEvent: { type: 'mouseenter', clientX: x, clientY: y }
        });

        // 如果是3D UI元素，更新悬停状态
        if (this.hoveredElement instanceof UI3DComponent) {
          this.hoveredElement.setHovered(true);
        }
      }
    }
  }

  /**
   * 处理鼠标按下
   * @param x 鼠标X坐标
   * @param y 鼠标Y坐标
   * @param button 按下的按钮
   * @param uiElements UI元素列表
   * @param camera 相机（用于3D UI元素）
   */
  handleMouseDown(x: number, y: number, button: number, uiElements: UIComponent[], camera?: Camera): void {
    // 查找当前点击的元素
    const element = this.findElementAtPosition(x, y, uiElements, camera);

    // 如果找到元素
    if (element && element.interactive) {
      // 更新活动元素
      this.activeElement = element;

      // 开始拖拽状态
      this.isDragging = true;
      this.dragStartPosition = new Vector2(x, y);
      this.dragCurrentPosition = new Vector2(x, y);

      // 分发鼠标按下事件
      this.createAndDispatchEvent(UIEventType.CLICK, element, {
        position: new Vector2(x, y),
        button,
        originalEvent: { type: 'mousedown', clientX: x, clientY: y, button }
      });

      // 如果是3D UI元素，更新激活状态
      if (element instanceof UI3DComponent) {
        element.setActive(true);
      }

      // 更新焦点元素
      if (this.focusedElement !== element) {
        // 如果之前有焦点元素，分发失焦事件
        if (this.focusedElement) {
          this.createAndDispatchEvent(UIEventType.BLUR, this.focusedElement, {
            originalEvent: { type: 'blur' }
          });
        }

        // 更新焦点元素
        this.focusedElement = element;

        // 分发获焦事件
        this.createAndDispatchEvent(UIEventType.FOCUS, element, {
          originalEvent: { type: 'focus' }
        });
      }
    } else {
      // 如果点击空白处，清除焦点
      if (this.focusedElement) {
        this.createAndDispatchEvent(UIEventType.BLUR, this.focusedElement, {
          originalEvent: { type: 'blur' }
        });
        this.focusedElement = undefined;
      }
    }
  }

  /**
   * 处理鼠标释放
   * @param x 鼠标X坐标
   * @param y 鼠标Y坐标
   * @param button 释放的按钮
   * @param _uiElements UI元素列表 - 未使用，保留以保持API一致性
   * @param _camera 相机（用于3D UI元素） - 未使用，保留以保持API一致性
   */
  handleMouseUp(x: number, y: number, button: number, _uiElements: UIComponent[], _camera?: Camera): void {
    // 如果有活动元素
    if (this.activeElement) {
      // 如果正在拖拽，分发拖拽结束事件
      if (this.isDragging && this.dragStartPosition && this.dragCurrentPosition) {
        const delta = new Vector2().subVectors(this.dragCurrentPosition, this.dragStartPosition);

        // 如果拖拽距离足够小，视为点击
        if (delta.length() < 5) {
          // 分发点击事件
          this.createAndDispatchEvent(UIEventType.CLICK, this.activeElement, {
            position: new Vector2(x, y),
            button,
            originalEvent: { type: 'click', clientX: x, clientY: y, button }
          });
        }

        // 分发拖拽结束事件
        this.createAndDispatchEvent(UIEventType.DRAG_END, this.activeElement, {
          position: new Vector2(x, y),
          delta,
          originalEvent: { type: 'mouseup', clientX: x, clientY: y, button }
        });
      }

      // 如果是3D UI元素，更新激活状态
      if (this.activeElement instanceof UI3DComponent) {
        this.activeElement.setActive(false);
      }

      // 重置拖拽状态
      this.isDragging = false;
      this.dragStartPosition = undefined;
      this.dragCurrentPosition = undefined;
      this.activeElement = undefined;
    }
  }

  /**
   * 处理键盘按下
   * @param key 按下的键
   * @param keyCode 键码
   * @param modifiers 修饰键状态
   */
  handleKeyDown(key: string, keyCode: number, modifiers: { altKey: boolean, ctrlKey: boolean, shiftKey: boolean, metaKey: boolean }): void {
    // 如果有焦点元素，分发键盘按下事件
    if (this.focusedElement) {
      this.createAndDispatchEvent(UIEventType.KEY_DOWN, this.focusedElement, {
        key,
        keyCode,
        ...modifiers,
        originalEvent: { type: 'keydown', key, keyCode, ...modifiers }
      });
    }
  }

  /**
   * 处理键盘释放
   * @param key 释放的键
   * @param keyCode 键码
   * @param modifiers 修饰键状态
   */
  handleKeyUp(key: string, keyCode: number, modifiers: { altKey: boolean, ctrlKey: boolean, shiftKey: boolean, metaKey: boolean }): void {
    // 如果有焦点元素，分发键盘释放事件
    if (this.focusedElement) {
      this.createAndDispatchEvent(UIEventType.KEY_UP, this.focusedElement, {
        key,
        keyCode,
        ...modifiers,
        originalEvent: { type: 'keyup', key, keyCode, ...modifiers }
      });
    }
  }

  /**
   * 在指定位置查找UI元素
   * @param x X坐标
   * @param y Y坐标
   * @param uiElements UI元素列表
   * @param camera 相机（用于3D UI元素）
   * @returns 找到的UI元素，如果没有找到则返回undefined
   */
  private findElementAtPosition(x: number, y: number, uiElements: UIComponent[], camera?: Camera): UIComponent | undefined {
    // 按z-index排序元素（从高到低）
    const sortedElements = [...uiElements].sort((a, b) => b.zIndex - a.zIndex);

    // 首先检查2D UI元素
    for (const element of sortedElements) {
      if (!element.visible || !element.interactive) continue;

      if (!element.is3D) {
        // 2D元素使用屏幕坐标检测
        const position = element.position instanceof Vector2 ? element.position : new Vector2((element.position as Vector3).x, (element.position as Vector3).y);
        const halfSize = element.size.clone().multiplyScalar(0.5);

        // 检查点是否在元素范围内
        if (x >= position.x - halfSize.x && x <= position.x + halfSize.x &&
            y >= position.y - halfSize.y && y <= position.y + halfSize.y) {
          return element;
        }
      }
    }

    // 然后检查3D UI元素（如果有相机）
    if (camera) {
      // 将屏幕坐标转换为归一化设备坐标（NDC）
      const ndcX = (x / window.innerWidth) * 2 - 1;
      const ndcY = -(y / window.innerHeight) * 2 + 1;

      // 设置射线投射器
      this.raycaster.setFromCamera(new Vector2(ndcX, ndcY), camera);

      // 收集所有3D UI元素的网格
      const meshes = [];
      const meshToElement = new Map();

      for (const element of sortedElements) {
        if (!element.visible || !element.interactive || !element.is3D) continue;

        const ui3d = element as UI3DComponent;
        if (ui3d.mesh) {
          meshes.push(ui3d.mesh);
          meshToElement.set(ui3d.mesh, element);
        }
      }

      // 执行射线投射
      const intersects = this.raycaster.intersectObjects(meshes, false);

      // 返回第一个相交的元素
      if (intersects.length > 0) {
        return meshToElement.get(intersects[0].object);
      }
    }

    return undefined;
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    const newComponent = new UIEventComponent();
    // 复制事件监听器
    for (const [eventType, listeners] of this.eventListeners) {
      for (const listener of listeners) {
        newComponent.addEventListener(eventType, listener);
      }
    }
    return newComponent;
  }
}
