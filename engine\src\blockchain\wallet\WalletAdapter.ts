/**
 * 钱包适配器基类 - 定义所有钱包适配器的通用接口
 */

import { EventEmitter } from '../../utils/EventEmitter';
import { BlockchainNetwork, Transaction, WalletType } from '../types/BlockchainTypes';

export abstract class WalletAdapter extends EventEmitter {
  protected walletType: WalletType;
  protected isInitialized: boolean = false;

  constructor(walletType: WalletType) {
    super();
    this.walletType = walletType;
  }

  /**
   * 获取钱包类型
   */
  getType(): WalletType {
    return this.walletType;
  }

  /**
   * 检查钱包是否可用
   */
  abstract isAvailable(): Promise<boolean>;

  /**
   * 检查钱包是否已连接
   */
  abstract isConnected(): Promise<boolean>;

  /**
   * 连接钱包
   */
  abstract connect(): Promise<string>;

  /**
   * 断开钱包连接
   */
  abstract disconnect(): Promise<void>;

  /**
   * 获取当前账户地址
   */
  abstract getAddress(): Promise<string>;

  /**
   * 获取账户余额
   */
  abstract getBalance(address?: string): Promise<string>;

  /**
   * 获取当前链ID
   */
  abstract getChainId(): Promise<number>;

  /**
   * 切换网络
   */
  abstract switchNetwork(network: BlockchainNetwork): Promise<void>;

  /**
   * 发送交易
   */
  abstract sendTransaction(transaction: Partial<Transaction>): Promise<string>;

  /**
   * 签名消息
   */
  abstract signMessage(message: string): Promise<string>;

  /**
   * 签名类型化数据
   */
  abstract signTypedData(domain: any, types: any, value: any): Promise<string>;

  /**
   * 添加代币到钱包
   */
  abstract addToken(tokenAddress: string, tokenSymbol: string, tokenDecimals: number, tokenImage?: string): Promise<boolean>;

  /**
   * 监听账户变化
   */
  protected abstract setupAccountListener(): void;

  /**
   * 监听网络变化
   */
  protected abstract setupNetworkListener(): void;

  /**
   * 监听连接状态变化
   */
  protected abstract setupConnectionListener(): void;

  /**
   * 初始化钱包适配器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // 设置事件监听器
      this.setupAccountListener();
      this.setupNetworkListener();
      this.setupConnectionListener();

      this.isInitialized = true;
      console.log(`${this.walletType} 适配器初始化完成`);
    } catch (error) {
      console.error(`${this.walletType} 适配器初始化失败:`, error);
      throw error;
    }
  }

  /**
   * 销毁钱包适配器
   */
  async destroy(): Promise<void> {
    try {
      await this.disconnect();
      this.removeAllListeners();
      this.isInitialized = false;
      console.log(`${this.walletType} 适配器已销毁`);
    } catch (error) {
      console.error(`${this.walletType} 适配器销毁失败:`, error);
    }
  }

  /**
   * 格式化地址
   */
  protected formatAddress(address: string): string {
    if (!address) return '';
    return address.toLowerCase();
  }

  /**
   * 验证地址格式
   */
  protected isValidAddress(address: string): boolean {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  }

  /**
   * 将Wei转换为Ether
   */
  protected weiToEther(wei: string): string {
    try {
      const weiValue = BigInt(wei);
      const etherValue = Number(weiValue) / Math.pow(10, 18);
      return etherValue.toString();
    } catch (error) {
      console.error('Wei转换Ether失败:', error);
      return '0';
    }
  }

  /**
   * 将Ether转换为Wei
   */
  protected etherToWei(ether: string): string {
    try {
      const etherValue = parseFloat(ether);
      const weiValue = BigInt(Math.floor(etherValue * Math.pow(10, 18)));
      return weiValue.toString();
    } catch (error) {
      console.error('Ether转换Wei失败:', error);
      return '0';
    }
  }

  /**
   * 处理钱包错误
   */
  protected handleWalletError(error: any): Error {
    let message = '钱包操作失败';
    
    if (error?.code) {
      switch (error.code) {
        case 4001:
          message = '用户拒绝了请求';
          break;
        case 4100:
          message = '请求的方法不被支持';
          break;
        case 4200:
          message = '钱包不支持请求的方法';
          break;
        case 4900:
          message = '钱包已断开连接';
          break;
        case 4901:
          message = '钱包未连接到请求的链';
          break;
        default:
          message = error.message || message;
      }
    } else if (error?.message) {
      message = error.message;
    }

    return new Error(message);
  }

  /**
   * 等待交易确认
   */
  protected async waitForTransaction(txHash: string, confirmations: number = 1): Promise<any> {
    // 这里应该实现等待交易确认的逻辑
    // 由于需要Web3实例，这里只是一个占位符
    console.log(`等待交易确认: ${txHash}, 确认数: ${confirmations}`);
    
    return new Promise((resolve) => {
      // 模拟等待过程
      setTimeout(() => {
        resolve({
          transactionHash: txHash,
          blockNumber: Math.floor(Math.random() * 1000000),
          status: 1
        });
      }, 30000); // 30秒后返回确认结果
    });
  }

  /**
   * 估算Gas费用
   */
  protected async estimateGas(transaction: Partial<Transaction>): Promise<string> {
    // 这里应该实现Gas估算逻辑
    // 由于需要Web3实例，这里返回一个默认值
    return '21000'; // 标准转账的Gas限制
  }

  /**
   * 获取当前Gas价格
   */
  protected async getGasPrice(): Promise<string> {
    // 这里应该实现获取Gas价格的逻辑
    // 由于需要Web3实例，这里返回一个默认值
    return '20000000000'; // 20 Gwei
  }

  /**
   * 验证网络
   */
  protected async validateNetwork(chainId: number): Promise<boolean> {
    try {
      const currentChainId = await this.getChainId();
      return currentChainId === chainId;
    } catch (error) {
      console.error('验证网络失败:', error);
      return false;
    }
  }

  /**
   * 格式化交易参数
   */
  protected formatTransactionParams(transaction: Partial<Transaction>): any {
    const params: any = {};

    if (transaction.to) {
      params.to = transaction.to;
    }

    if (transaction.value) {
      params.value = '0x' + BigInt(transaction.value).toString(16);
    }

    if (transaction.gasLimit) {
      params.gas = '0x' + BigInt(transaction.gasLimit).toString(16);
    }

    if (transaction.gasPrice) {
      params.gasPrice = '0x' + BigInt(transaction.gasPrice).toString(16);
    }

    return params;
  }
}
