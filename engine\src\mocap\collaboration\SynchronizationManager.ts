/**
 * 同步管理器
 * 管理多用户数据同步和延迟补偿
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';

/**
 * 同步数据包
 */
export interface SyncPacket {
  id: string;
  userId: string;
  type: string;
  data: any;
  timestamp: number;
  sequence: number;
  priority: number;
}

/**
 * 网络延迟信息
 */
export interface LatencyInfo {
  userId: string;
  rtt: number; // 往返时间
  jitter: number; // 抖动
  packetLoss: number; // 丢包率
  lastUpdate: number;
}

/**
 * 同步状态
 */
export interface SyncState {
  lastSyncTime: number;
  pendingPackets: number;
  syncRate: number;
  averageLatency: number;
  totalPacketsSent: number;
  totalPacketsReceived: number;
}

/**
 * 同步管理器
 */
export class SynchronizationManager extends EventEmitter {
  private config: any;
  private syncState: SyncState;
  private latencyMap: Map<string, LatencyInfo> = new Map();
  private pendingPackets: Map<string, SyncPacket> = new Map();
  private sequenceNumbers: Map<string, number> = new Map();
  private syncBuffer: SyncPacket[] = [];
  private syncTimer: NodeJS.Timeout | null = null;
  private predictionBuffer: Map<string, any[]> = new Map();

  constructor(config: any) {
    super();
    this.config = config;
    
    this.syncState = {
      lastSyncTime: 0,
      pendingPackets: 0,
      syncRate: config.syncFrequency || 30,
      averageLatency: 0,
      totalPacketsSent: 0,
      totalPacketsReceived: 0
    };

    this.startSyncLoop();
  }

  /**
   * 开始同步循环
   */
  private startSyncLoop(): void {
    const syncInterval = 1000 / this.syncState.syncRate;
    
    this.syncTimer = setInterval(() => {
      this.processSyncBuffer();
      this.updateLatencyStats();
      this.cleanupExpiredPackets();
    }, syncInterval);
  }

  /**
   * 广播更新
   */
  public broadcastUpdate(data: any): void {
    const packet: SyncPacket = {
      id: this.generatePacketId(),
      userId: data.userId,
      type: data.type,
      data: data.data,
      timestamp: Date.now(),
      sequence: this.getNextSequence(data.userId),
      priority: this.calculatePriority(data.type)
    };

    this.addToSyncBuffer(packet);
    this.syncState.totalPacketsSent++;
  }

  /**
   * 添加到同步缓冲区
   */
  private addToSyncBuffer(packet: SyncPacket): void {
    // 按优先级插入
    const insertIndex = this.syncBuffer.findIndex(p => p.priority < packet.priority);
    
    if (insertIndex === -1) {
      this.syncBuffer.push(packet);
    } else {
      this.syncBuffer.splice(insertIndex, 0, packet);
    }

    // 限制缓冲区大小
    if (this.syncBuffer.length > 100) {
      this.syncBuffer.shift(); // 移除最旧的包
    }
  }

  /**
   * 处理同步缓冲区
   */
  private processSyncBuffer(): void {
    if (this.syncBuffer.length === 0) return;

    const now = Date.now();
    const packetsToSend: SyncPacket[] = [];

    // 选择要发送的包
    while (this.syncBuffer.length > 0 && packetsToSend.length < 10) {
      const packet = this.syncBuffer.shift()!;
      
      // 检查包是否过期
      if (now - packet.timestamp > 1000) { // 1秒过期
        continue;
      }

      packetsToSend.push(packet);
    }

    // 发送包
    for (const packet of packetsToSend) {
      this.sendPacket(packet);
    }

    this.syncState.lastSyncTime = now;
  }

  /**
   * 发送数据包
   */
  private sendPacket(packet: SyncPacket): void {
    // 应用数据压缩
    if (this.config.enableDataCompression) {
      packet.data = this.compressData(packet.data);
    }

    // 添加到待确认列表
    this.pendingPackets.set(packet.id, packet);

    // 触发发送事件
    this.emit('packetSend', packet);

    Debug.log('SynchronizationManager', `发送同步包: ${packet.type} (${packet.id})`);
  }

  /**
   * 接收数据包
   */
  public receivePacket(packet: SyncPacket): void {
    this.syncState.totalPacketsReceived++;

    // 检查包的有效性
    if (!this.validatePacket(packet)) {
      Debug.warn('SynchronizationManager', '收到无效数据包', packet.id);
      return;
    }

    // 解压数据
    if (this.config.enableDataCompression) {
      packet.data = this.decompressData(packet.data);
    }

    // 应用延迟补偿
    if (this.config.enableLatencyCompensation) {
      packet.data = this.compensateLatency(packet.data, packet.userId);
    }

    // 处理包
    this.processReceivedPacket(packet);

    // 发送确认
    this.sendAcknowledgment(packet.id, packet.userId);
  }

  /**
   * 验证数据包
   */
  private validatePacket(packet: SyncPacket): boolean {
    // 检查必要字段
    if (!packet.id || !packet.userId || !packet.type) {
      return false;
    }

    // 检查时间戳
    const now = Date.now();
    if (Math.abs(now - packet.timestamp) > 5000) { // 5秒容差
      return false;
    }

    // 检查序列号
    const lastSequence = this.sequenceNumbers.get(packet.userId) || 0;
    if (packet.sequence <= lastSequence) {
      return false; // 重复或过期的包
    }

    return true;
  }

  /**
   * 处理接收到的包
   */
  private processReceivedPacket(packet: SyncPacket): void {
    // 更新序列号
    this.sequenceNumbers.set(packet.userId, packet.sequence);

    // 触发处理事件
    this.emit('packetReceived', packet);

    Debug.log('SynchronizationManager', `处理同步包: ${packet.type} (${packet.id})`);
  }

  /**
   * 延迟补偿
   */
  public compensateLatency(data: any, userId?: string): any {
    if (!userId) return data;

    const latencyInfo = this.latencyMap.get(userId);
    if (!latencyInfo) return data;

    // 简化的延迟补偿
    const compensatedData = { ...data };
    
    if (data.poseResults && data.poseResults.landmarks) {
      // 基于延迟预测位置
      const latencySeconds = latencyInfo.rtt / 2000; // 单向延迟
      
      // 这里应该实现更复杂的预测算法
      // 简化实现：轻微调整位置
      compensatedData.poseResults = {
        ...data.poseResults,
        landmarks: data.poseResults.landmarks.map((landmark: any) => ({
          ...landmark,
          x: landmark.x + (Math.random() - 0.5) * 0.001 * latencySeconds,
          y: landmark.y + (Math.random() - 0.5) * 0.001 * latencySeconds
        }))
      };
    }

    return compensatedData;
  }

  /**
   * 发送确认
   */
  private sendAcknowledgment(packetId: string, userId: string): void {
    const ackPacket = {
      type: 'acknowledgment',
      packetId,
      userId,
      timestamp: Date.now()
    };

    this.emit('sendAcknowledgment', ackPacket);
  }

  /**
   * 接收确认
   */
  public receiveAcknowledgment(ackData: any): void {
    const packet = this.pendingPackets.get(ackData.packetId);
    if (packet) {
      // 计算RTT
      const rtt = Date.now() - packet.timestamp;
      this.updateLatency(packet.userId, rtt);
      
      // 移除待确认包
      this.pendingPackets.delete(ackData.packetId);
    }
  }

  /**
   * 更新延迟信息
   */
  private updateLatency(userId: string, rtt: number): void {
    let latencyInfo = this.latencyMap.get(userId);
    
    if (!latencyInfo) {
      latencyInfo = {
        userId,
        rtt,
        jitter: 0,
        packetLoss: 0,
        lastUpdate: Date.now()
      };
    } else {
      // 计算抖动
      const jitter = Math.abs(rtt - latencyInfo.rtt);
      latencyInfo.jitter = latencyInfo.jitter * 0.9 + jitter * 0.1;
      
      // 更新RTT（平滑）
      latencyInfo.rtt = latencyInfo.rtt * 0.8 + rtt * 0.2;
      latencyInfo.lastUpdate = Date.now();
    }
    
    this.latencyMap.set(userId, latencyInfo);
  }

  /**
   * 更新延迟统计
   */
  private updateLatencyStats(): void {
    const latencies = Array.from(this.latencyMap.values()).map(info => info.rtt);
    
    if (latencies.length > 0) {
      this.syncState.averageLatency = latencies.reduce((sum, rtt) => sum + rtt, 0) / latencies.length;
    }
  }

  /**
   * 清理过期包
   */
  private cleanupExpiredPackets(): void {
    const now = Date.now();
    const expireTime = 5000; // 5秒过期

    // 清理待确认包
    for (const [packetId, packet] of this.pendingPackets.entries()) {
      if (now - packet.timestamp > expireTime) {
        this.pendingPackets.delete(packetId);
        
        // 记录丢包
        const latencyInfo = this.latencyMap.get(packet.userId);
        if (latencyInfo) {
          latencyInfo.packetLoss = latencyInfo.packetLoss * 0.9 + 0.1;
        }
      }
    }

    // 清理预测缓冲区
    for (const [userId, predictions] of this.predictionBuffer.entries()) {
      const validPredictions = predictions.filter(p => now - p.timestamp < expireTime);
      this.predictionBuffer.set(userId, validPredictions);
    }
  }

  /**
   * 计算优先级
   */
  private calculatePriority(dataType: string): number {
    const priorities: { [key: string]: number } = {
      'pose_update': 3,
      'hands_update': 3,
      'interaction_event': 5,
      'user_state_change': 2,
      'system_message': 1
    };

    return priorities[dataType] || 1;
  }

  /**
   * 压缩数据
   */
  private compressData(data: any): any {
    // 简化的数据压缩
    // 实际应该使用更高效的压缩算法
    return JSON.stringify(data);
  }

  /**
   * 解压数据
   */
  private decompressData(compressedData: any): any {
    // 简化的数据解压
    try {
      return JSON.parse(compressedData);
    } catch (error) {
      Debug.error('SynchronizationManager', '数据解压失败', error);
      return compressedData;
    }
  }

  /**
   * 生成包ID
   */
  private generatePacketId(): string {
    return `packet_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 获取下一个序列号
   */
  private getNextSequence(userId: string): number {
    const current = this.sequenceNumbers.get(userId) || 0;
    const next = current + 1;
    this.sequenceNumbers.set(userId, next);
    return next;
  }

  /**
   * 执行同步
   */
  public performSync(data: any): void {
    // 实现具体的同步逻辑
    this.emit('syncPerformed', data);
  }

  /**
   * 调整同步频率
   */
  public adjustSyncRate(newRate: number): void {
    this.syncState.syncRate = Math.max(1, Math.min(60, newRate));
    
    // 重启同步循环
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }
    this.startSyncLoop();
    
    Debug.log('SynchronizationManager', `同步频率调整为: ${this.syncState.syncRate}Hz`);
  }

  /**
   * 获取延迟信息
   */
  public getLatencyInfo(userId: string): LatencyInfo | null {
    return this.latencyMap.get(userId) || null;
  }

  /**
   * 获取同步统计
   */
  public getStats(): any {
    return {
      ...this.syncState,
      pendingPackets: this.pendingPackets.size,
      connectedUsers: this.latencyMap.size,
      bufferSize: this.syncBuffer.length,
      latencyStats: this.getLatencyStats()
    };
  }

  /**
   * 获取延迟统计
   */
  private getLatencyStats(): any {
    const latencies = Array.from(this.latencyMap.values());
    
    if (latencies.length === 0) {
      return { min: 0, max: 0, avg: 0, jitter: 0 };
    }

    const rtts = latencies.map(info => info.rtt);
    const jitters = latencies.map(info => info.jitter);

    return {
      min: Math.min(...rtts),
      max: Math.max(...rtts),
      avg: rtts.reduce((sum, rtt) => sum + rtt, 0) / rtts.length,
      jitter: jitters.reduce((sum, jitter) => sum + jitter, 0) / jitters.length
    };
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }

    this.latencyMap.clear();
    this.pendingPackets.clear();
    this.sequenceNumbers.clear();
    this.syncBuffer = [];
    this.predictionBuffer.clear();
    
    this.removeAllListeners();
  }
}
