/**
 * 场景数字人管理器
 * 管理场景中的数字人实体和RAG应用关联
 */
import { EventEmitter } from '../utils/EventEmitter';
import type { Scene } from './Scene';
import type { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { EnhancedAvatarComponent, EnhancedAvatarConfig } from '../avatar/components/EnhancedAvatarComponent';
import { VoiceInteractionComponent, VoiceConfig } from '../voice/VoiceInteractionComponent';

/**
 * 场景数字人配置
 */
export interface SceneAvatarConfig {
  /** 数字人ID */
  avatarId: string;
  /** 数字人名称 */
  name: string;
  /** 关联的知识库ID */
  knowledgeBaseId?: string;
  /** RAG应用ID */
  ragApplicationId?: string;
  /** 位置 */
  position: { x: number; y: number; z: number };
  /** 旋转 */
  rotation: { x: number; y: number; z: number };
  /** 缩放 */
  scale: { x: number; y: number; z: number };
  /** 是否自动启动 */
  autoStart?: boolean;
  /** 交互区域半径 */
  interactionRadius?: number;
  /** 语音配置 */
  voiceConfig?: VoiceConfig;
  /** 数字人配置 */
  avatarConfig?: EnhancedAvatarConfig;
}

/**
 * 数字人实例信息
 */
export interface AvatarInstance {
  /** 实例ID */
  id: string;
  /** 实体引用 */
  entity: Entity;
  /** 数字人组件 */
  avatarComponent: EnhancedAvatarComponent;
  /** 语音交互组件 */
  voiceComponent?: VoiceInteractionComponent;
  /** 配置 */
  config: SceneAvatarConfig;
  /** 是否活跃 */
  isActive: boolean;
  /** 是否正在对话 */
  isInConversation: boolean;
  /** 当前会话ID */
  currentSessionId?: string;
}

/**
 * 交互事件
 */
export interface InteractionEvent {
  /** 事件类型 */
  type: 'start' | 'end' | 'message' | 'error';
  /** 数字人ID */
  avatarId: string;
  /** 用户ID */
  userId?: string;
  /** 消息内容 */
  message?: string;
  /** 会话ID */
  sessionId?: string;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 场景数字人管理器
 */
export class SceneAvatarManager extends EventEmitter {
  /** 关联的场景 */
  private scene: Scene;
  
  /** 数字人实例映射 */
  private avatarInstances: Map<string, AvatarInstance> = new Map();
  
  /** 数字人配置映射 */
  private avatarConfigs: Map<string, SceneAvatarConfig> = new Map();
  
  /** 是否已初始化 */
  private initialized: boolean = false;
  
  /** 交互检测间隔 */
  private interactionCheckInterval: number = 100; // 100ms
  
  /** 上次检测时间 */
  private lastInteractionCheck: number = 0;

  /**
   * 构造函数
   */
  constructor(scene: Scene) {
    super();
    this.scene = scene;
  }

  /**
   * 初始化管理器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 监听场景事件
    this.scene.on('entityAdded', this.onEntityAdded.bind(this));
    this.scene.on('entityRemoved', this.onEntityRemoved.bind(this));

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 添加数字人到场景
   */
  public async addAvatar(config: SceneAvatarConfig): Promise<AvatarInstance> {
    // 检查是否已存在
    if (this.avatarInstances.has(config.avatarId)) {
      throw new Error(`数字人 ${config.avatarId} 已存在于场景中`);
    }

    // 创建实体
    const entity = this.scene.getWorld().createEntity(`Avatar_${config.name}`);
    
    // 设置变换
    const transform = entity.getTransform();
    transform.setPosition(config.position.x, config.position.y, config.position.z);
    transform.setRotation(config.rotation.x, config.rotation.y, config.rotation.z);
    transform.setScale(config.scale.x, config.scale.y, config.scale.z);

    // 创建增强数字人组件
    const avatarComponent = new EnhancedAvatarComponent(entity, {
      ...config.avatarConfig,
      name: config.name,
      knowledgeBase: {
        knowledgeBaseId: config.knowledgeBaseId,
      },
    });
    entity.addComponent(avatarComponent);

    // 创建语音交互组件（如果配置了语音）
    let voiceComponent: VoiceInteractionComponent | undefined;
    if (config.voiceConfig) {
      voiceComponent = new VoiceInteractionComponent(entity, config.voiceConfig);
      entity.addComponent(voiceComponent);
      
      // 监听语音交互事件
      voiceComponent.on('recognitionResult', (result) => {
        this.handleVoiceInput(config.avatarId, result);
      });
      
      voiceComponent.on('synthesisCompleted', (result) => {
        this.handleVoiceOutput(config.avatarId, result);
      });
    }

    // 添加到场景
    this.scene.addEntity(entity);

    // 创建实例信息
    const instance: AvatarInstance = {
      id: config.avatarId,
      entity,
      avatarComponent,
      voiceComponent,
      config,
      isActive: config.autoStart || false,
      isInConversation: false,
    };

    // 保存实例和配置
    this.avatarInstances.set(config.avatarId, instance);
    this.avatarConfigs.set(config.avatarId, config);

    // 如果自动启动，则激活数字人
    if (config.autoStart) {
      await this.activateAvatar(config.avatarId);
    }

    this.emit('avatarAdded', instance);
    return instance;
  }

  /**
   * 移除数字人
   */
  public async removeAvatar(avatarId: string): Promise<void> {
    const instance = this.avatarInstances.get(avatarId);
    if (!instance) {
      throw new Error(`数字人 ${avatarId} 不存在`);
    }

    // 停止对话
    if (instance.isInConversation) {
      await this.endConversation(avatarId);
    }

    // 停用数字人
    if (instance.isActive) {
      await this.deactivateAvatar(avatarId);
    }

    // 从场景中移除实体
    this.scene.removeEntity(instance.entity);

    // 清理资源
    instance.avatarComponent.dispose();
    if (instance.voiceComponent) {
      instance.voiceComponent.dispose();
    }

    // 移除实例和配置
    this.avatarInstances.delete(avatarId);
    this.avatarConfigs.delete(avatarId);

    this.emit('avatarRemoved', avatarId);
  }

  /**
   * 激活数字人
   */
  public async activateAvatar(avatarId: string): Promise<void> {
    const instance = this.avatarInstances.get(avatarId);
    if (!instance) {
      throw new Error(`数字人 ${avatarId} 不存在`);
    }

    if (instance.isActive) {
      return;
    }

    // 初始化语音组件
    if (instance.voiceComponent) {
      await instance.voiceComponent.initialize();
    }

    // 初始化数字人组件
    await instance.avatarComponent.initialize();

    instance.isActive = true;
    this.emit('avatarActivated', avatarId);
  }

  /**
   * 停用数字人
   */
  public async deactivateAvatar(avatarId: string): Promise<void> {
    const instance = this.avatarInstances.get(avatarId);
    if (!instance) {
      throw new Error(`数字人 ${avatarId} 不存在`);
    }

    if (!instance.isActive) {
      return;
    }

    // 结束当前对话
    if (instance.isInConversation) {
      await this.endConversation(avatarId);
    }

    instance.isActive = false;
    this.emit('avatarDeactivated', avatarId);
  }

  /**
   * 开始对话
   */
  public async startConversation(avatarId: string, userId?: string): Promise<string> {
    const instance = this.avatarInstances.get(avatarId);
    if (!instance) {
      throw new Error(`数字人 ${avatarId} 不存在`);
    }

    if (!instance.isActive) {
      throw new Error(`数字人 ${avatarId} 未激活`);
    }

    if (instance.isInConversation) {
      throw new Error(`数字人 ${avatarId} 已在对话中`);
    }

    // 生成会话ID
    const sessionId = `session_${avatarId}_${Date.now()}`;

    // 开始对话
    instance.isInConversation = true;
    instance.currentSessionId = sessionId;

    // 发送问候语
    if (instance.config.avatarConfig?.dialogue?.personality) {
      await this.sendMessage(avatarId, '您好，我是您的智能助手，有什么可以帮助您的吗？');
    }

    const event: InteractionEvent = {
      type: 'start',
      avatarId,
      userId,
      sessionId,
      timestamp: Date.now(),
    };

    this.emit('conversationStarted', event);
    return sessionId;
  }

  /**
   * 结束对话
   */
  public async endConversation(avatarId: string): Promise<void> {
    const instance = this.avatarInstances.get(avatarId);
    if (!instance) {
      throw new Error(`数字人 ${avatarId} 不存在`);
    }

    if (!instance.isInConversation) {
      return;
    }

    const sessionId = instance.currentSessionId;

    // 发送告别语
    if (instance.config.avatarConfig?.dialogue?.personality) {
      await this.sendMessage(avatarId, '感谢您的咨询，祝您生活愉快！');
    }

    // 结束对话
    instance.isInConversation = false;
    instance.currentSessionId = undefined;

    const event: InteractionEvent = {
      type: 'end',
      avatarId,
      sessionId,
      timestamp: Date.now(),
    };

    this.emit('conversationEnded', event);
  }

  /**
   * 发送消息给数字人
   */
  public async sendMessage(avatarId: string, message: string, userId?: string): Promise<void> {
    const instance = this.avatarInstances.get(avatarId);
    if (!instance) {
      throw new Error(`数字人 ${avatarId} 不存在`);
    }

    if (!instance.isActive) {
      throw new Error(`数字人 ${avatarId} 未激活`);
    }

    // 处理RAG对话
    const response = await instance.avatarComponent.processMessage(message);

    // 语音合成
    if (instance.voiceComponent && response) {
      await instance.voiceComponent.synthesizeSpeech(response, {
        generateLipSync: true,
      });
    }

    const event: InteractionEvent = {
      type: 'message',
      avatarId,
      userId,
      message: response,
      sessionId: instance.currentSessionId,
      timestamp: Date.now(),
    };

    this.emit('messageProcessed', event);
  }

  /**
   * 更新管理器
   */
  public update(deltaTime: number): void {
    if (!this.initialized) {
      return;
    }

    // 检查交互
    const now = Date.now();
    if (now - this.lastInteractionCheck > this.interactionCheckInterval) {
      this.checkInteractions();
      this.lastInteractionCheck = now;
    }

    // 更新数字人实例
    for (const instance of this.avatarInstances.values()) {
      if (instance.isActive) {
        instance.avatarComponent.update(deltaTime);
        if (instance.voiceComponent) {
          instance.voiceComponent.update?.(deltaTime);
        }
      }
    }
  }

  /**
   * 检查交互
   */
  private checkInteractions(): void {
    // 这里可以实现基于距离的自动交互检测
    // 例如：当用户靠近数字人时自动开始对话
  }

  /**
   * 处理语音输入
   */
  private async handleVoiceInput(avatarId: string, result: any): Promise<void> {
    if (result.text && result.isFinal) {
      await this.sendMessage(avatarId, result.text);
    }
  }

  /**
   * 处理语音输出
   */
  private handleVoiceOutput(avatarId: string, result: any): void {
    // 处理语音合成完成事件
    this.emit('voiceOutputCompleted', { avatarId, result });
  }

  /**
   * 实体添加事件处理
   */
  private onEntityAdded(entity: Entity): void {
    // 检查是否为数字人实体
    const avatarComponent = entity.getComponent<EnhancedAvatarComponent>('EnhancedAvatarComponent');
    if (avatarComponent) {
      // 自动注册数字人
      // 这里可以添加自动发现逻辑
    }
  }

  /**
   * 实体移除事件处理
   */
  private onEntityRemoved(entity: Entity): void {
    // 查找并清理对应的数字人实例
    for (const [avatarId, instance] of this.avatarInstances.entries()) {
      if (instance.entity === entity) {
        this.avatarInstances.delete(avatarId);
        this.avatarConfigs.delete(avatarId);
        this.emit('avatarRemoved', avatarId);
        break;
      }
    }
  }

  /**
   * 获取数字人实例
   */
  public getAvatar(avatarId: string): AvatarInstance | undefined {
    return this.avatarInstances.get(avatarId);
  }

  /**
   * 获取所有数字人实例
   */
  public getAllAvatars(): AvatarInstance[] {
    return Array.from(this.avatarInstances.values());
  }

  /**
   * 获取活跃的数字人
   */
  public getActiveAvatars(): AvatarInstance[] {
    return Array.from(this.avatarInstances.values()).filter(instance => instance.isActive);
  }

  /**
   * 获取正在对话的数字人
   */
  public getConversationAvatars(): AvatarInstance[] {
    return Array.from(this.avatarInstances.values()).filter(instance => instance.isInConversation);
  }

  /**
   * 销毁管理器
   */
  public dispose(): void {
    // 停用所有数字人
    for (const avatarId of this.avatarInstances.keys()) {
      this.deactivateAvatar(avatarId).catch(console.error);
    }

    // 清理资源
    this.avatarInstances.clear();
    this.avatarConfigs.clear();

    // 移除事件监听
    this.scene.off('entityAdded', this.onEntityAdded.bind(this));
    this.scene.off('entityRemoved', this.onEntityRemoved.bind(this));

    this.initialized = false;
    this.emit('disposed');
  }
}
