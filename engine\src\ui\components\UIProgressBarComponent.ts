/**
 * UIProgressBarComponent.ts
 *
 * 进度条UI组件，用于显示任务进度、加载状态等
 */

import { Vector2, Vector3 } from 'three';
import { UIComponent, UIComponentProps, UIComponentType } from './UIComponent';
import { Component } from '../../core/Component';

/**
 * 进度条方向枚举
 */
export enum ProgressBarDirection {
  HORIZONTAL = 'horizontal',
  VERTICAL = 'vertical'
}

/**
 * 进度条样式枚举
 */
export enum ProgressBarStyle {
  SOLID = 'solid',
  STRIPED = 'striped',
  ANIMATED = 'animated'
}

/**
 * 进度条属性
 */
export interface UIProgressBarComponentProps extends UIComponentProps {
  // 进度值（0-100）
  value?: number;
  
  // 最小值
  min?: number;
  
  // 最大值
  max?: number;
  
  // 方向
  direction?: ProgressBarDirection;
  
  // 样式
  style?: ProgressBarStyle;
  
  // 显示文本
  showText?: boolean;
  
  // 自定义文本
  text?: string;
  
  // 文本格式化函数
  textFormatter?: (value: number, min: number, max: number) => string;
  
  // 进度条颜色
  progressColor?: string;
  
  // 背景颜色
  trackColor?: string;
  
  // 文本颜色
  textColor?: string;
  
  // 动画持续时间（毫秒）
  animationDuration?: number;
  
  // 是否启用动画
  animated?: boolean;
  
  // 事件回调
  onValueChange?: (value: number) => void;
  onComplete?: () => void;
}

/**
 * 进度条UI组件
 */
export class UIProgressBarComponent extends UIComponent {
  // 进度值
  private _value: number = 0;
  private _min: number = 0;
  private _max: number = 100;
  
  // 样式属性
  direction: ProgressBarDirection = ProgressBarDirection.HORIZONTAL;
  style: ProgressBarStyle = ProgressBarStyle.SOLID;
  showText: boolean = true;
  text?: string;
  textFormatter?: (value: number, min: number, max: number) => string;
  progressColor: string = '#1890ff';
  trackColor: string = '#f0f0f0';
  textColor: string = '#000000';
  animationDuration: number = 300;
  animated: boolean = true;
  
  // 事件回调
  onValueChange?: (value: number) => void;
  onComplete?: () => void;
  
  // 内部元素
  private trackElement?: HTMLElement;
  private progressElement?: HTMLElement;
  private textElement?: HTMLElement;
  
  // 动画相关
  private animationStartTime: number = 0;
  private animationStartValue: number = 0;
  private animationTargetValue: number = 0;
  private isAnimating: boolean = false;

  // 静态样式注入标记
  private static stylesInjected: boolean = false;

  /**
   * 构造函数
   */
  constructor(props: UIProgressBarComponentProps = {}) {
    super({ ...props, type: UIComponentType.PROGRESS_BAR });

    // 设置属性
    this._value = props.value ?? 0;
    this._min = props.min ?? 0;
    this._max = props.max ?? 100;
    this.direction = props.direction ?? ProgressBarDirection.HORIZONTAL;
    this.style = props.style ?? ProgressBarStyle.SOLID;
    this.showText = props.showText ?? true;
    this.text = props.text;
    this.textFormatter = props.textFormatter;
    this.progressColor = props.progressColor ?? '#1890ff';
    this.trackColor = props.trackColor ?? '#f0f0f0';
    this.textColor = props.textColor ?? '#000000';
    this.animationDuration = props.animationDuration ?? 300;
    this.animated = props.animated ?? true;
    this.onValueChange = props.onValueChange;
    this.onComplete = props.onComplete;

    // 注入CSS样式
    this.injectProgressBarStyles();

    // 创建HTML结构
    this.createProgressBarElements();

    // 初始化显示
    this.updateDisplay();
  }

  /**
   * 注入进度条CSS样式
   */
  private injectProgressBarStyles(): void {
    if (UIProgressBarComponent.stylesInjected) return;

    const style = document.createElement('style');
    style.textContent = `
      @keyframes progress-bar-stripes {
        0% {
          background-position: 1rem 0;
        }
        100% {
          background-position: 0 0;
        }
      }

      .ui-progress-track {
        position: relative;
        overflow: hidden;
      }

      .ui-progress-bar {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        transition: width 0.3s ease;
      }

      .ui-progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 12px;
        font-weight: bold;
        pointer-events: none;
        z-index: 1;
      }
    `;

    document.head.appendChild(style);
    UIProgressBarComponent.stylesInjected = true;
  }

  /**
   * 创建进度条HTML元素
   */
  private createProgressBarElements(): void {
    if (!this.htmlElement) return;
    
    // 清空现有内容
    this.htmlElement.innerHTML = '';
    
    // 创建轨道元素
    this.trackElement = document.createElement('div');
    this.trackElement.className = 'ui-progress-track';
    this.htmlElement.appendChild(this.trackElement);
    
    // 创建进度元素
    this.progressElement = document.createElement('div');
    this.progressElement.className = 'ui-progress-bar';
    this.trackElement.appendChild(this.progressElement);
    
    // 创建文本元素
    if (this.showText) {
      this.textElement = document.createElement('div');
      this.textElement.className = 'ui-progress-text';
      this.htmlElement.appendChild(this.textElement);
    }
    
    // 应用样式
    this.applyProgressBarStyles();
  }

  /**
   * 应用进度条样式
   */
  private applyProgressBarStyles(): void {
    if (!this.trackElement || !this.progressElement) return;
    
    // 容器样式
    const containerStyle = this.htmlElement!.style;
    containerStyle.position = 'relative';
    containerStyle.display = 'flex';
    containerStyle.alignItems = 'center';
    
    // 轨道样式
    const trackStyle = this.trackElement.style;
    trackStyle.position = 'relative';
    trackStyle.backgroundColor = this.trackColor;
    trackStyle.borderRadius = '4px';
    trackStyle.overflow = 'hidden';
    
    if (this.direction === ProgressBarDirection.HORIZONTAL) {
      trackStyle.width = '100%';
      trackStyle.height = '8px';
    } else {
      trackStyle.width = '8px';
      trackStyle.height = '100%';
    }
    
    // 进度条样式
    const progressStyle = this.progressElement.style;
    progressStyle.backgroundColor = this.progressColor;
    progressStyle.borderRadius = '4px';
    progressStyle.transition = this.animated ? `all ${this.animationDuration}ms ease` : 'none';
    
    // 根据样式类型设置特殊效果
    if (this.style === ProgressBarStyle.STRIPED) {
      progressStyle.backgroundImage = 'linear-gradient(45deg, rgba(255,255,255,.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,.15) 50%, rgba(255,255,255,.15) 75%, transparent 75%, transparent)';
      progressStyle.backgroundSize = '1rem 1rem';
    }
    
    if (this.style === ProgressBarStyle.ANIMATED) {
      progressStyle.backgroundImage = 'linear-gradient(45deg, rgba(255,255,255,.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,.15) 50%, rgba(255,255,255,.15) 75%, transparent 75%, transparent)';
      progressStyle.backgroundSize = '1rem 1rem';
      progressStyle.animation = 'progress-bar-stripes 1s linear infinite';
    }
    
    // 文本样式
    if (this.textElement) {
      const textStyle = this.textElement.style;
      textStyle.position = 'absolute';
      textStyle.top = '50%';
      textStyle.left = '50%';
      textStyle.transform = 'translate(-50%, -50%)';
      textStyle.color = this.textColor;
      textStyle.fontSize = '12px';
      textStyle.fontWeight = 'bold';
      textStyle.pointerEvents = 'none';
      textStyle.zIndex = '1';
    }
  }

  /**
   * 更新显示
   */
  private updateDisplay(): void {
    if (!this.progressElement) return;
    
    const percentage = this.getPercentage();
    
    // 更新进度条尺寸
    if (this.direction === ProgressBarDirection.HORIZONTAL) {
      this.progressElement.style.width = `${percentage}%`;
      this.progressElement.style.height = '100%';
    } else {
      this.progressElement.style.width = '100%';
      this.progressElement.style.height = `${percentage}%`;
    }
    
    // 更新文本
    if (this.textElement && this.showText) {
      let displayText = this.text;
      
      if (!displayText) {
        if (this.textFormatter) {
          displayText = this.textFormatter(this._value, this._min, this._max);
        } else {
          displayText = `${Math.round(percentage)}%`;
        }
      }
      
      this.textElement.textContent = displayText;
    }
  }

  /**
   * 获取百分比
   */
  private getPercentage(): number {
    const range = this._max - this._min;
    if (range === 0) return 0;
    return Math.max(0, Math.min(100, ((this._value - this._min) / range) * 100));
  }

  /**
   * 设置进度值
   */
  setValue(value: number, animated: boolean = this.animated): void {
    const clampedValue = Math.max(this._min, Math.min(this._max, value));

    if (clampedValue === this._value) return;

    if (animated && this.animationDuration > 0) {
      this.animateToValue(clampedValue);
    } else {
      this._value = clampedValue;
      this.updateDisplay();

      // 触发事件
      if (this.onValueChange) {
        this.onValueChange(this._value);
      }

      if (this._value === this._max && this.onComplete) {
        this.onComplete();
      }
    }
  }

  /**
   * 动画到目标值
   */
  private animateToValue(targetValue: number): void {
    this.animationStartTime = Date.now();
    this.animationStartValue = this._value;
    this.animationTargetValue = targetValue;
    this.isAnimating = true;
  }

  /**
   * 获取进度值
   */
  getValue(): number {
    return this._value;
  }

  /**
   * 设置最小值
   */
  setMin(min: number): void {
    this._min = min;
    if (this._value < min) {
      this.setValue(min);
    } else {
      this.updateDisplay();
    }
  }

  /**
   * 获取最小值
   */
  getMin(): number {
    return this._min;
  }

  /**
   * 设置最大值
   */
  setMax(max: number): void {
    this._max = max;
    if (this._value > max) {
      this.setValue(max);
    } else {
      this.updateDisplay();
    }
  }

  /**
   * 获取最大值
   */
  getMax(): number {
    return this._max;
  }

  /**
   * 更新组件
   */
  override update(deltaTime: number): void {
    super.update(deltaTime);
    
    // 处理动画
    if (this.isAnimating) {
      const elapsed = Date.now() - this.animationStartTime;
      const progress = Math.min(1, elapsed / this.animationDuration);
      
      // 使用缓动函数
      const easedProgress = this.easeInOutCubic(progress);
      
      this._value = this.animationStartValue + 
        (this.animationTargetValue - this.animationStartValue) * easedProgress;
      
      this.updateDisplay();
      
      if (progress >= 1) {
        this.isAnimating = false;
        this._value = this.animationTargetValue;
        
        // 触发事件
        if (this.onValueChange) {
          this.onValueChange(this._value);
        }
        
        if (this._value === this._max && this.onComplete) {
          this.onComplete();
        }
      }
    }
  }

  /**
   * 缓动函数
   */
  private easeInOutCubic(t: number): number {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new UIProgressBarComponent({
      id: this.id,
      type: this.uiType,
      visible: this.visible,
      interactive: this.interactive,
      position: this.position instanceof Vector3 ? this.position.clone() : new Vector2(this.position.x, this.position.y),
      size: new Vector2(this.size.x, this.size.y),
      opacity: this.opacity,
      zIndex: this.zIndex,
      layoutType: this.layoutType,
      layoutParams: { ...this.layoutParams },
      backgroundColor: this.backgroundColor,
      borderColor: this.borderColor,
      borderWidth: this.borderWidth,
      borderRadius: this.borderRadius,
      padding: { ...this.padding },
      margin: { ...this.margin },
      data: { ...this.data },
      tags: [...this.tags],
      is3D: this.is3D,
      value: this._value,
      min: this._min,
      max: this._max,
      direction: this.direction,
      style: this.style,
      progressColor: this.progressColor,
      trackColor: this.trackColor,
      textColor: this.textColor,
      showText: this.showText,
      text: this.text,
      textFormatter: this.textFormatter,
      animated: this.animated,
      animationDuration: this.animationDuration,
      onValueChange: this.onValueChange,
      onComplete: this.onComplete
    });
  }

  /**
   * 销毁组件
   */
  override dispose(): void {
    this.trackElement = undefined;
    this.progressElement = undefined;
    this.textElement = undefined;
    this.onValueChange = undefined;
    this.onComplete = undefined;

    super.dispose();
  }
}
