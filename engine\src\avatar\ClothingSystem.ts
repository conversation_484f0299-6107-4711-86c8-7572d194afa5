/**
 * 服装系统
 * 负责虚拟化身的服装适配和渲染
 */
import * as THREE from 'three';
import { EventEmitter } from '../utils/EventEmitter';
import type { BodyData, ClothingData, ClothingItem } from './AvatarCustomizationSystem';
import { ClothingType } from './AvatarCustomizationSystem';

/**
 * 服装系统配置接口
 */
export interface ClothingSystemConfig {
  /** 调试模式 */
  debug?: boolean;
  /** 服装资源路径 */
  clothingAssetsPath?: string;
  /** 是否启用物理模拟 */
  enablePhysics?: boolean;
  /** 质量级别 */
  qualityLevel?: 'low' | 'medium' | 'high';
}

/**
 * 服装模板接口
 */
export interface ClothingTemplate {
  /** 模板ID */
  id: string;
  /** 服装类型 */
  type: ClothingType;
  /** 名称 */
  name: string;
  /** 基础几何 */
  baseGeometry: THREE.BufferGeometry;
  /** 材质配置 */
  materialConfig: ClothingMaterialConfig;
  /** 适配点 */
  attachmentPoints: AttachmentPoint[];
  /** 尺寸变体 */
  sizeVariants: SizeVariant[];
}

/**
 * 服装材质配置接口
 */
export interface ClothingMaterialConfig {
  /** 基础颜色 */
  baseColor: string;
  /** 材质类型 */
  materialType: 'fabric' | 'leather' | 'metal' | 'plastic';
  /** 纹理路径 */
  texturePaths: {
    diffuse?: string;
    normal?: string;
    roughness?: string;
    metallic?: string;
  };
  /** 物理属性 */
  physicalProperties: {
    stiffness: number;
    damping: number;
    friction: number;
  };
}

/**
 * 附着点接口
 */
export interface AttachmentPoint {
  /** 附着点名称 */
  name: string;
  /** 身体部位 */
  bodyPart: string;
  /** 相对位置 */
  position: THREE.Vector3;
  /** 权重 */
  weight: number;
}

/**
 * 尺寸变体接口
 */
export interface SizeVariant {
  /** 尺寸名称 */
  size: string;
  /** 缩放因子 */
  scaleFactor: THREE.Vector3;
  /** 偏移量 */
  offset: THREE.Vector3;
}

/**
 * 服装适配结果接口
 */
export interface ClothingFitResult {
  /** 适配后的几何 */
  geometry: THREE.BufferGeometry;
  /** 材质 */
  material: THREE.Material;
  /** 适配质量评分 */
  fitQuality: number;
  /** 碰撞检测结果 */
  collisions: CollisionInfo[];
}

/**
 * 碰撞信息接口
 */
export interface CollisionInfo {
  /** 碰撞点 */
  point: THREE.Vector3;
  /** 碰撞深度 */
  depth: number;
  /** 碰撞法线 */
  normal: THREE.Vector3;
}

/**
 * 服装系统
 */
export class ClothingSystem extends EventEmitter {
  /** 系统配置 */
  private config: ClothingSystemConfig;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 服装模板库 */
  private clothingTemplates: Map<string, ClothingTemplate> = new Map();

  /** 材质缓存 */
  private materialCache: Map<string, THREE.Material> = new Map();

  /** 物理模拟器 */
  private physicsSimulator: any = null;

  /**
   * 构造函数
   */
  constructor(config: ClothingSystemConfig = {}) {
    super();
    
    this.config = {
      debug: false,
      clothingAssetsPath: '/assets/clothing/',
      enablePhysics: true,
      qualityLevel: 'medium',
      ...config
    };
  }

  /**
   * 初始化系统
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // 加载服装模板
      await this.loadClothingTemplates();

      // 初始化物理模拟器
      if (this.config.enablePhysics) {
        this.initializePhysicsSimulator();
      }

      this.initialized = true;

      if (this.config.debug) {
        console.log('服装系统已初始化');
      }

      this.emit('initialized');
    } catch (error) {
      console.error('服装系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 应用服装到身体
   */
  public async applyClothing(bodyData: BodyData, clothingItems: ClothingItem[]): Promise<ClothingData> {
    if (!this.initialized) {
      throw new Error('服装系统未初始化');
    }

    try {
      const fittedItems: ClothingItem[] = [];

      for (const item of clothingItems) {
        const fittedItem = await this.fitClothingToBody(bodyData, item);
        fittedItems.push(fittedItem);
      }

      // 检测服装间碰撞
      const resolvedItems = await this.resolveClothingCollisions(fittedItems);

      const clothingData: ClothingData = {
        items: resolvedItems
      };

      this.emit('clothingApplied', clothingData);

      if (this.config.debug) {
        console.log('服装应用完成', { itemCount: resolvedItems.length });
      }

      return clothingData;
    } catch (error) {
      console.error('服装应用失败:', error);
      throw error;
    }
  }

  /**
   * 适配服装到身体
   */
  private async fitClothingToBody(bodyData: BodyData, clothingItem: ClothingItem): Promise<ClothingItem> {
    // 获取服装模板
    const template = this.clothingTemplates.get(clothingItem.id);
    if (!template) {
      throw new Error(`服装模板 ${clothingItem.id} 不存在`);
    }

    // 分析身体尺寸
    const bodyMeasurements = this.analyzeBodyMeasurements(bodyData);

    // 选择合适的尺寸变体
    const sizeVariant = this.selectSizeVariant(template, bodyMeasurements);

    // 调整服装几何
    const adjustedGeometry = this.adjustClothingGeometry(
      template.baseGeometry,
      sizeVariant,
      bodyMeasurements
    );

    // 执行物理适配
    let fittedGeometry = adjustedGeometry;
    if (this.config.enablePhysics) {
      fittedGeometry = await this.simulateClothingFit(adjustedGeometry, bodyData.geometry);
    }

    // 创建材质
    const material = this.createClothingMaterial(template.materialConfig, clothingItem.color);

    return {
      ...clothingItem,
      geometry: fittedGeometry,
      material
    };
  }

  /**
   * 分析身体尺寸
   */
  private analyzeBodyMeasurements(bodyData: BodyData): any {
    const geometry = bodyData.geometry;
    const positions = geometry.attributes.position.array as Float32Array;

    // 计算边界框
    let minX = Infinity, maxX = -Infinity;
    let minY = Infinity, maxY = -Infinity;
    let minZ = Infinity, maxZ = -Infinity;

    for (let i = 0; i < positions.length; i += 3) {
      minX = Math.min(minX, positions[i]);
      maxX = Math.max(maxX, positions[i]);
      minY = Math.min(minY, positions[i + 1]);
      maxY = Math.max(maxY, positions[i + 1]);
      minZ = Math.min(minZ, positions[i + 2]);
      maxZ = Math.max(maxZ, positions[i + 2]);
    }

    return {
      width: maxX - minX,
      height: maxY - minY,
      depth: maxZ - minZ,
      chest: (maxX - minX) * 0.8, // 估算胸围
      waist: (maxX - minX) * 0.7, // 估算腰围
      hips: (maxX - minX) * 0.9   // 估算臀围
    };
  }

  /**
   * 选择尺寸变体
   */
  private selectSizeVariant(template: ClothingTemplate, bodyMeasurements: any): SizeVariant {
    // 简化的尺寸选择逻辑
    const sizes = ['XS', 'S', 'M', 'L', 'XL'];
    const chestSizes = [80, 85, 90, 95, 100];

    let bestSize = 'M';
    let minDiff = Infinity;

    for (let i = 0; i < sizes.length; i++) {
      const diff = Math.abs(bodyMeasurements.chest - chestSizes[i]);
      if (diff < minDiff) {
        minDiff = diff;
        bestSize = sizes[i];
      }
    }

    // 查找对应的尺寸变体
    const variant = template.sizeVariants.find(v => v.size === bestSize);
    
    return variant || {
      size: 'M',
      scaleFactor: new THREE.Vector3(1, 1, 1),
      offset: new THREE.Vector3(0, 0, 0)
    };
  }

  /**
   * 调整服装几何
   */
  private adjustClothingGeometry(
    baseGeometry: THREE.BufferGeometry,
    sizeVariant: SizeVariant,
    _bodyMeasurements: any
  ): THREE.BufferGeometry {
    const geometry = baseGeometry.clone();
    const positions = geometry.attributes.position.array as Float32Array;

    // 应用尺寸缩放
    for (let i = 0; i < positions.length; i += 3) {
      positions[i] *= sizeVariant.scaleFactor.x;
      positions[i + 1] *= sizeVariant.scaleFactor.y;
      positions[i + 2] *= sizeVariant.scaleFactor.z;

      // 应用偏移
      positions[i] += sizeVariant.offset.x;
      positions[i + 1] += sizeVariant.offset.y;
      positions[i + 2] += sizeVariant.offset.z;
    }

    geometry.attributes.position.needsUpdate = true;
    geometry.computeVertexNormals();

    return geometry;
  }

  /**
   * 模拟服装适配
   */
  private async simulateClothingFit(
    clothingGeometry: THREE.BufferGeometry,
    bodyGeometry: THREE.BufferGeometry
  ): Promise<THREE.BufferGeometry> {
    // 简化的物理模拟
    // 实际实现需要使用专业的布料模拟库

    const fittedGeometry = clothingGeometry.clone();
    const clothingPositions = fittedGeometry.attributes.position.array as Float32Array;
    const bodyPositions = bodyGeometry.attributes.position.array as Float32Array;

    // 简单的碰撞检测和位置调整
    for (let i = 0; i < clothingPositions.length; i += 3) {
      const clothingPoint = new THREE.Vector3(
        clothingPositions[i],
        clothingPositions[i + 1],
        clothingPositions[i + 2]
      );

      // 查找最近的身体顶点
      let minDistance = Infinity;
      let closestBodyPoint = new THREE.Vector3();

      for (let j = 0; j < bodyPositions.length; j += 3) {
        const bodyPoint = new THREE.Vector3(
          bodyPositions[j],
          bodyPositions[j + 1],
          bodyPositions[j + 2]
        );

        const distance = clothingPoint.distanceTo(bodyPoint);
        if (distance < minDistance) {
          minDistance = distance;
          closestBodyPoint = bodyPoint;
        }
      }

      // 如果服装顶点太接近身体，向外推
      const minGap = 0.01; // 最小间隙
      if (minDistance < minGap) {
        const direction = clothingPoint.clone().sub(closestBodyPoint).normalize();
        const adjustedPoint = closestBodyPoint.clone().add(direction.multiplyScalar(minGap));
        
        clothingPositions[i] = adjustedPoint.x;
        clothingPositions[i + 1] = adjustedPoint.y;
        clothingPositions[i + 2] = adjustedPoint.z;
      }
    }

    fittedGeometry.attributes.position.needsUpdate = true;
    fittedGeometry.computeVertexNormals();

    return fittedGeometry;
  }

  /**
   * 解决服装间碰撞
   */
  private async resolveClothingCollisions(clothingItems: ClothingItem[]): Promise<ClothingItem[]> {
    // 简化的碰撞解决
    // 实际实现需要更复杂的碰撞检测和解决算法
    
    return clothingItems; // 暂时直接返回
  }

  /**
   * 创建服装材质
   */
  private createClothingMaterial(materialConfig: ClothingMaterialConfig, color: string): THREE.Material {
    const cacheKey = `${materialConfig.materialType}_${color}`;
    
    if (this.materialCache.has(cacheKey)) {
      return this.materialCache.get(cacheKey)!;
    }

    let material: THREE.Material;

    switch (materialConfig.materialType) {
      case 'fabric':
        material = new THREE.MeshStandardMaterial({
          color: color,
          roughness: 0.8,
          metalness: 0.0
        });
        break;
      
      case 'leather':
        material = new THREE.MeshStandardMaterial({
          color: color,
          roughness: 0.6,
          metalness: 0.1
        });
        break;
      
      case 'metal':
        material = new THREE.MeshStandardMaterial({
          color: color,
          roughness: 0.2,
          metalness: 0.9
        });
        break;
      
      case 'plastic':
        material = new THREE.MeshStandardMaterial({
          color: color,
          roughness: 0.3,
          metalness: 0.0
        });
        break;
      
      default:
        material = new THREE.MeshStandardMaterial({
          color: color
        });
    }

    this.materialCache.set(cacheKey, material);
    return material;
  }

  /**
   * 加载服装模板
   */
  private async loadClothingTemplates(): Promise<void> {
    // 模拟加载服装模板
    await new Promise(resolve => setTimeout(resolve, 100));

    // 创建一些基础服装模板
    this.createBasicClothingTemplates();
  }

  /**
   * 创建基础服装模板
   */
  private createBasicClothingTemplates(): void {
    // T恤模板
    const tshirtTemplate: ClothingTemplate = {
      id: 'basic_tshirt',
      type: ClothingType.SHIRT,
      name: '基础T恤',
      baseGeometry: this.createTshirtGeometry(),
      materialConfig: {
        baseColor: '#FFFFFF',
        materialType: 'fabric',
        texturePaths: {},
        physicalProperties: {
          stiffness: 0.5,
          damping: 0.3,
          friction: 0.7
        }
      },
      attachmentPoints: [],
      sizeVariants: this.createSizeVariants()
    };

    this.clothingTemplates.set(tshirtTemplate.id, tshirtTemplate);

    // 裤子模板
    const pantsTemplate: ClothingTemplate = {
      id: 'basic_pants',
      type: ClothingType.PANTS,
      name: '基础裤子',
      baseGeometry: this.createPantsGeometry(),
      materialConfig: {
        baseColor: '#000080',
        materialType: 'fabric',
        texturePaths: {},
        physicalProperties: {
          stiffness: 0.7,
          damping: 0.4,
          friction: 0.8
        }
      },
      attachmentPoints: [],
      sizeVariants: this.createSizeVariants()
    };

    this.clothingTemplates.set(pantsTemplate.id, pantsTemplate);
  }

  /**
   * 创建T恤几何
   */
  private createTshirtGeometry(): THREE.BufferGeometry {
    // 简化的T恤几何
    const geometry = new THREE.BoxGeometry(1, 1.2, 0.2);
    return geometry;
  }

  /**
   * 创建裤子几何
   */
  private createPantsGeometry(): THREE.BufferGeometry {
    // 简化的裤子几何
    const geometry = new THREE.CylinderGeometry(0.3, 0.4, 1.5, 8);
    return geometry;
  }

  /**
   * 创建尺寸变体
   */
  private createSizeVariants(): SizeVariant[] {
    return [
      {
        size: 'XS',
        scaleFactor: new THREE.Vector3(0.8, 0.9, 0.8),
        offset: new THREE.Vector3(0, 0, 0)
      },
      {
        size: 'S',
        scaleFactor: new THREE.Vector3(0.9, 0.95, 0.9),
        offset: new THREE.Vector3(0, 0, 0)
      },
      {
        size: 'M',
        scaleFactor: new THREE.Vector3(1.0, 1.0, 1.0),
        offset: new THREE.Vector3(0, 0, 0)
      },
      {
        size: 'L',
        scaleFactor: new THREE.Vector3(1.1, 1.05, 1.1),
        offset: new THREE.Vector3(0, 0, 0)
      },
      {
        size: 'XL',
        scaleFactor: new THREE.Vector3(1.2, 1.1, 1.2),
        offset: new THREE.Vector3(0, 0, 0)
      }
    ];
  }

  /**
   * 初始化物理模拟器
   */
  private initializePhysicsSimulator(): void {
    this.physicsSimulator = { initialized: true };
  }

  /**
   * 更新系统
   */
  public update(_deltaTime: number): void {
    // 系统更新逻辑
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    this.clothingTemplates.clear();
    this.materialCache.clear();
    this.initialized = false;
    this.removeAllListeners();
  }
}
