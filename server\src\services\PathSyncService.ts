/**
 * 路径同步服务
 * 支持多用户协作编辑路径
 */
import { Server as SocketIOServer, Socket } from 'socket.io';
import { Logger } from '../utils/Logger';
import { avatarPathService } from './AvatarPathService';
import { EventEmitter } from 'events';
import { RedisClient } from '../utils/Redis';

/**
 * 路径编辑会话接口
 */
interface PathEditSession {
  pathId: string;
  userId: string;
  userName: string;
  socketId: string;
  joinedAt: Date;
  lastActivity: Date;
  cursor?: {
    x: number;
    y: number;
    z: number;
  };
  selection?: {
    pointIndex: number;
  };
}

/**
 * 路径操作接口
 */
interface PathOperation {
  id: string;
  type: 'addPoint' | 'removePoint' | 'updatePoint' | 'updatePath';
  pathId: string;
  userId: string;
  timestamp: Date;
  data: any;
  applied: boolean;
}

/**
 * 路径同步事件接口
 */
interface PathSyncEvent {
  type: string;
  pathId: string;
  userId: string;
  data: any;
  timestamp: Date;
}

/**
 * 路径同步服务类
 */
export class PathSyncService extends EventEmitter {
  private logger = new Logger('PathSyncService');
  private io: SocketIOServer;
  private redis: RedisClient;
  
  // 活动会话管理
  private activeSessions = new Map<string, PathEditSession[]>(); // pathId -> sessions
  private userSessions = new Map<string, PathEditSession>(); // socketId -> session
  
  // 操作队列管理
  private operationQueues = new Map<string, PathOperation[]>(); // pathId -> operations
  private operationHistory = new Map<string, PathOperation[]>(); // pathId -> history
  
  // 冲突解决
  private conflictResolver = new Map<string, NodeJS.Timeout>(); // pathId -> timeout

  /**
   * 构造函数
   * @param io Socket.IO服务器实例
   * @param redis Redis客户端
   */
  constructor(io: SocketIOServer, redis: RedisClient) {
    super();
    this.io = io;
    this.redis = redis;
    
    this.setupSocketHandlers();
    this.setupCleanupTimer();
    
    this.logger.info('路径同步服务初始化完成');
  }

  /**
   * 设置Socket处理器
   */
  private setupSocketHandlers(): void {
    this.io.on('connection', (socket: Socket) => {
      this.logger.debug('用户连接', { socketId: socket.id });

      // 加入路径编辑
      socket.on('joinPathEdit', async (data: { pathId: string; userId: string; userName: string }) => {
        await this.handleJoinPathEdit(socket, data);
      });

      // 离开路径编辑
      socket.on('leavePathEdit', async (data: { pathId: string }) => {
        await this.handleLeavePathEdit(socket, data);
      });

      // 路径操作
      socket.on('pathOperation', async (operation: Omit<PathOperation, 'id' | 'timestamp' | 'applied'>) => {
        await this.handlePathOperation(socket, operation);
      });

      // 光标移动
      socket.on('cursorMove', async (data: { pathId: string; cursor: { x: number; y: number; z: number } }) => {
        await this.handleCursorMove(socket, data);
      });

      // 选择变化
      socket.on('selectionChange', async (data: { pathId: string; selection: { pointIndex: number } }) => {
        await this.handleSelectionChange(socket, data);
      });

      // 请求同步
      socket.on('requestSync', async (data: { pathId: string }) => {
        await this.handleRequestSync(socket, data);
      });

      // 断开连接
      socket.on('disconnect', () => {
        this.handleDisconnect(socket);
      });
    });
  }

  /**
   * 处理加入路径编辑
   */
  private async handleJoinPathEdit(
    socket: Socket, 
    data: { pathId: string; userId: string; userName: string }
  ): Promise<void> {
    try {
      const { pathId, userId, userName } = data;
      
      this.logger.info('用户加入路径编辑', { pathId, userId, socketId: socket.id });

      // 验证路径是否存在
      const path = await avatarPathService.getPath(pathId);
      if (!path) {
        socket.emit('error', { message: '路径不存在' });
        return;
      }

      // 创建编辑会话
      const session: PathEditSession = {
        pathId,
        userId,
        userName,
        socketId: socket.id,
        joinedAt: new Date(),
        lastActivity: new Date()
      };

      // 加入房间
      socket.join(`path:${pathId}`);

      // 添加到活动会话
      if (!this.activeSessions.has(pathId)) {
        this.activeSessions.set(pathId, []);
      }
      this.activeSessions.get(pathId)!.push(session);
      this.userSessions.set(socket.id, session);

      // 通知其他用户
      socket.to(`path:${pathId}`).emit('userJoined', {
        userId,
        userName,
        joinedAt: session.joinedAt
      });

      // 发送当前状态
      const currentSessions = this.activeSessions.get(pathId) || [];
      socket.emit('pathEditJoined', {
        pathId,
        path: path.toJSON(),
        activeSessions: currentSessions.map(s => ({
          userId: s.userId,
          userName: s.userName,
          joinedAt: s.joinedAt,
          cursor: s.cursor,
          selection: s.selection
        })),
        operationHistory: this.operationHistory.get(pathId) || []
      });

      // 保存到Redis
      await this.saveSessionToRedis(session);

    } catch (error) {
      this.logger.error('加入路径编辑失败', error);
      socket.emit('error', { message: '加入编辑失败' });
    }
  }

  /**
   * 处理离开路径编辑
   */
  private async handleLeavePathEdit(socket: Socket, data: { pathId: string }): Promise<void> {
    try {
      const session = this.userSessions.get(socket.id);
      if (!session || session.pathId !== data.pathId) {
        return;
      }

      await this.removeUserSession(socket, session);

    } catch (error) {
      this.logger.error('离开路径编辑失败', error);
    }
  }

  /**
   * 处理路径操作
   */
  private async handlePathOperation(
    socket: Socket,
    operationData: Omit<PathOperation, 'id' | 'timestamp' | 'applied'>
  ): Promise<void> {
    try {
      const session = this.userSessions.get(socket.id);
      if (!session) {
        socket.emit('error', { message: '未加入编辑会话' });
        return;
      }

      // 创建操作对象
      const operation: PathOperation = {
        id: `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(),
        applied: false,
        ...operationData,
        userId: session.userId
      };

      this.logger.debug('收到路径操作', { operation });

      // 添加到操作队列
      if (!this.operationQueues.has(operation.pathId)) {
        this.operationQueues.set(operation.pathId, []);
      }
      this.operationQueues.get(operation.pathId)!.push(operation);

      // 广播操作到其他用户
      socket.to(`path:${operation.pathId}`).emit('pathOperation', operation);

      // 处理操作队列
      await this.processOperationQueue(operation.pathId);

      // 更新用户活动时间
      session.lastActivity = new Date();

    } catch (error) {
      this.logger.error('处理路径操作失败', error);
      socket.emit('error', { message: '操作失败' });
    }
  }

  /**
   * 处理光标移动
   */
  private async handleCursorMove(
    socket: Socket,
    data: { pathId: string; cursor: { x: number; y: number; z: number } }
  ): Promise<void> {
    try {
      const session = this.userSessions.get(socket.id);
      if (!session || session.pathId !== data.pathId) {
        return;
      }

      // 更新光标位置
      session.cursor = data.cursor;
      session.lastActivity = new Date();

      // 广播光标位置
      socket.to(`path:${data.pathId}`).emit('cursorMove', {
        userId: session.userId,
        cursor: data.cursor
      });

    } catch (error) {
      this.logger.error('处理光标移动失败', error);
    }
  }

  /**
   * 处理选择变化
   */
  private async handleSelectionChange(
    socket: Socket,
    data: { pathId: string; selection: { pointIndex: number } }
  ): Promise<void> {
    try {
      const session = this.userSessions.get(socket.id);
      if (!session || session.pathId !== data.pathId) {
        return;
      }

      // 更新选择
      session.selection = data.selection;
      session.lastActivity = new Date();

      // 广播选择变化
      socket.to(`path:${data.pathId}`).emit('selectionChange', {
        userId: session.userId,
        selection: data.selection
      });

    } catch (error) {
      this.logger.error('处理选择变化失败', error);
    }
  }

  /**
   * 处理同步请求
   */
  private async handleRequestSync(socket: Socket, data: { pathId: string }): Promise<void> {
    try {
      const session = this.userSessions.get(socket.id);
      if (!session || session.pathId !== data.pathId) {
        return;
      }

      // 获取最新路径数据
      const path = await avatarPathService.getPath(data.pathId);
      if (!path) {
        socket.emit('error', { message: '路径不存在' });
        return;
      }

      // 发送同步数据
      socket.emit('pathSync', {
        pathId: data.pathId,
        path: path.toJSON(),
        operationHistory: this.operationHistory.get(data.pathId) || []
      });

    } catch (error) {
      this.logger.error('处理同步请求失败', error);
    }
  }

  /**
   * 处理断开连接
   */
  private handleDisconnect(socket: Socket): void {
    const session = this.userSessions.get(socket.id);
    if (session) {
      this.removeUserSession(socket, session);
    }
  }

  /**
   * 移除用户会话
   */
  private async removeUserSession(socket: Socket, session: PathEditSession): Promise<void> {
    try {
      const { pathId, userId } = session;

      this.logger.info('用户离开路径编辑', { pathId, userId, socketId: socket.id });

      // 离开房间
      socket.leave(`path:${pathId}`);

      // 从活动会话中移除
      const sessions = this.activeSessions.get(pathId);
      if (sessions) {
        const index = sessions.findIndex(s => s.socketId === socket.id);
        if (index >= 0) {
          sessions.splice(index, 1);
        }
        
        if (sessions.length === 0) {
          this.activeSessions.delete(pathId);
          // 清理操作队列
          this.operationQueues.delete(pathId);
        }
      }

      // 从用户会话中移除
      this.userSessions.delete(socket.id);

      // 通知其他用户
      socket.to(`path:${pathId}`).emit('userLeft', { userId });

      // 从Redis中移除
      await this.removeSessionFromRedis(session);

    } catch (error) {
      this.logger.error('移除用户会话失败', error);
    }
  }

  /**
   * 处理操作队列
   */
  private async processOperationQueue(pathId: string): Promise<void> {
    try {
      const queue = this.operationQueues.get(pathId);
      if (!queue || queue.length === 0) {
        return;
      }

      // 按时间戳排序
      queue.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

      // 处理未应用的操作
      const unprocessedOps = queue.filter(op => !op.applied);
      if (unprocessedOps.length === 0) {
        return;
      }

      // 获取当前路径
      const path = await avatarPathService.getPath(pathId);
      if (!path) {
        return;
      }

      // 应用操作
      let hasChanges = false;
      for (const operation of unprocessedOps) {
        try {
          await this.applyOperation(path, operation);
          operation.applied = true;
          hasChanges = true;

          // 添加到历史记录
          if (!this.operationHistory.has(pathId)) {
            this.operationHistory.set(pathId, []);
          }
          this.operationHistory.get(pathId)!.push(operation);

        } catch (error) {
          this.logger.error('应用操作失败', { operation, error });
        }
      }

      // 如果有变化，保存路径
      if (hasChanges) {
        await path.save();
        
        // 广播路径更新
        this.io.to(`path:${pathId}`).emit('pathUpdated', {
          pathId,
          path: path.toJSON(),
          operations: unprocessedOps
        });
      }

      // 清理已应用的操作
      this.operationQueues.set(pathId, queue.filter(op => !op.applied));

    } catch (error) {
      this.logger.error('处理操作队列失败', error);
    }
  }

  /**
   * 应用操作到路径
   */
  private async applyOperation(path: any, operation: PathOperation): Promise<void> {
    switch (operation.type) {
      case 'addPoint':
        path.points.push(operation.data.point);
        break;

      case 'removePoint':
        const removeIndex = operation.data.index;
        if (removeIndex >= 0 && removeIndex < path.points.length) {
          path.points.splice(removeIndex, 1);
        }
        break;

      case 'updatePoint':
        const updateIndex = operation.data.index;
        if (updateIndex >= 0 && updateIndex < path.points.length) {
          Object.assign(path.points[updateIndex], operation.data.point);
        }
        break;

      case 'updatePath':
        Object.assign(path, operation.data);
        break;

      default:
        throw new Error(`未知操作类型: ${operation.type}`);
    }
  }

  /**
   * 保存会话到Redis
   */
  private async saveSessionToRedis(session: PathEditSession): Promise<void> {
    try {
      const key = `path_session:${session.pathId}:${session.socketId}`;
      await this.redis.setex(key, 3600, JSON.stringify(session)); // 1小时过期
    } catch (error) {
      this.logger.error('保存会话到Redis失败', error);
    }
  }

  /**
   * 从Redis移除会话
   */
  private async removeSessionFromRedis(session: PathEditSession): Promise<void> {
    try {
      const key = `path_session:${session.pathId}:${session.socketId}`;
      await this.redis.del(key);
    } catch (error) {
      this.logger.error('从Redis移除会话失败', error);
    }
  }

  /**
   * 设置清理定时器
   */
  private setupCleanupTimer(): void {
    // 每5分钟清理一次非活动会话
    setInterval(() => {
      this.cleanupInactiveSessions();
    }, 5 * 60 * 1000);

    // 每小时清理一次操作历史
    setInterval(() => {
      this.cleanupOperationHistory();
    }, 60 * 60 * 1000);
  }

  /**
   * 清理非活动会话
   */
  private cleanupInactiveSessions(): void {
    const now = new Date();
    const timeout = 30 * 60 * 1000; // 30分钟超时

    for (const [pathId, sessions] of this.activeSessions.entries()) {
      const activeSessions = sessions.filter(session => {
        const inactive = now.getTime() - session.lastActivity.getTime() > timeout;
        if (inactive) {
          this.logger.info('清理非活动会话', { pathId, userId: session.userId });
          this.userSessions.delete(session.socketId);
        }
        return !inactive;
      });

      if (activeSessions.length === 0) {
        this.activeSessions.delete(pathId);
        this.operationQueues.delete(pathId);
      } else {
        this.activeSessions.set(pathId, activeSessions);
      }
    }
  }

  /**
   * 清理操作历史
   */
  private cleanupOperationHistory(): void {
    const maxHistorySize = 1000;
    const maxAge = 24 * 60 * 60 * 1000; // 24小时
    const now = new Date();

    for (const [pathId, history] of this.operationHistory.entries()) {
      // 按时间过滤
      const recentHistory = history.filter(op => 
        now.getTime() - op.timestamp.getTime() < maxAge
      );

      // 按数量限制
      if (recentHistory.length > maxHistorySize) {
        recentHistory.splice(0, recentHistory.length - maxHistorySize);
      }

      if (recentHistory.length === 0) {
        this.operationHistory.delete(pathId);
      } else {
        this.operationHistory.set(pathId, recentHistory);
      }
    }
  }

  /**
   * 获取路径的活动会话
   */
  public getActiveSessionsForPath(pathId: string): PathEditSession[] {
    return this.activeSessions.get(pathId) || [];
  }

  /**
   * 获取路径的操作历史
   */
  public getOperationHistoryForPath(pathId: string): PathOperation[] {
    return this.operationHistory.get(pathId) || [];
  }
}

// 导出单例实例（需要在应用启动时初始化）
export let pathSyncService: PathSyncService;
