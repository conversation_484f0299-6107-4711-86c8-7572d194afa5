import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Document } from '../../documents/entities/document.entity';

export enum KnowledgeBaseStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PROCESSING = 'processing',
}

@Entity('knowledge_bases')
@Index(['sceneId'])
@Index(['ownerId'])
export class KnowledgeBase {
  @ApiProperty({ description: '知识库ID' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '知识库名称' })
  @Column({ length: 255 })
  name: string;

  @ApiProperty({ description: '知识库描述' })
  @Column({ type: 'text', nullable: true })
  description: string;

  @ApiProperty({ description: '关联场景ID' })
  @Column({ name: 'scene_id', length: 36, nullable: true })
  sceneId: string;

  @ApiProperty({ description: '所有者ID' })
  @Column({ name: 'owner_id', length: 36 })
  ownerId: string;

  @ApiProperty({ description: '知识库状态', enum: KnowledgeBaseStatus })
  @Column({
    type: 'enum',
    enum: KnowledgeBaseStatus,
    default: KnowledgeBaseStatus.ACTIVE,
  })
  status: KnowledgeBaseStatus;

  @ApiProperty({ description: '配置信息' })
  @Column({ type: 'json', nullable: true })
  config: {
    chunkSize?: number;
    chunkOverlap?: number;
    embeddingModel?: string;
    searchThreshold?: number;
    maxResults?: number;
  };

  @ApiProperty({ description: '统计信息' })
  @Column({ type: 'json', nullable: true })
  statistics: {
    documentCount?: number;
    totalSize?: number;
    lastUpdated?: string;
    vectorCount?: number;
  };

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ApiProperty({ description: '关联文档', type: () => [Document] })
  @OneToMany(() => Document, (document) => document.knowledgeBase)
  documents: Document[];
}
