/**
 * 智能决策系统
 * 
 * 提供基于AI的智能决策功能，支持多种决策算法和策略。
 * 包括目标导向规划、情境感知决策和学习优化。
 */

import { EventEmitter } from 'events';
import { Blackboard } from './BehaviorTreeEngine';

/**
 * 决策类型
 */
export enum DecisionType {
  IMMEDIATE = 'immediate',     // 即时决策
  PLANNED = 'planned',         // 计划决策
  REACTIVE = 'reactive',       // 反应决策
  STRATEGIC = 'strategic'      // 战略决策
}

/**
 * 决策优先级
 */
export enum DecisionPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  CRITICAL = 4
}

/**
 * 决策上下文
 */
export interface DecisionContext {
  entityId: string;
  currentGoals: Goal[];
  environmentState: EnvironmentState;
  socialContext: SocialContext;
  emotionalState: EmotionalState;
  memoryContext: MemoryContext;
  constraints: Constraint[];
  timestamp: number;
}

/**
 * 目标接口
 */
export interface Goal {
  id: string;
  name: string;
  type: string;
  priority: number;
  deadline?: number;
  progress: number;
  isCompleted: boolean;
  subGoals: Goal[];
  requirements: string[];
  rewards: number;
}

/**
 * 环境状态
 */
export interface EnvironmentState {
  location: { x: number; y: number; z: number };
  weather: string;
  timeOfDay: number;
  temperature: number;
  visibility: number;
  obstacles: any[];
  resources: any[];
  threats: any[];
}

/**
 * 社交上下文
 */
export interface SocialContext {
  nearbyEntities: string[];
  relationships: Map<string, number>;
  groupMembership: string[];
  socialRoles: string[];
  communicationHistory: any[];
}

/**
 * 情感状态
 */
export interface EmotionalState {
  primaryEmotion: string;
  intensity: number;
  mood: string;
  stress: number;
  confidence: number;
  motivation: number;
}

/**
 * 记忆上下文
 */
export interface MemoryContext {
  shortTermMemory: any[];
  longTermMemory: any[];
  episodicMemory: any[];
  proceduralMemory: any[];
  workingMemory: any[];
}

/**
 * 约束条件
 */
export interface Constraint {
  type: string;
  description: string;
  severity: number;
  isHard: boolean;
}

/**
 * 决策选项
 */
export interface DecisionOption {
  id: string;
  name: string;
  description: string;
  type: string;
  cost: number;
  benefit: number;
  risk: number;
  duration: number;
  requirements: string[];
  consequences: any[];
  confidence: number;
}

/**
 * 决策结果
 */
export interface DecisionResult {
  selectedOption: DecisionOption;
  reasoning: string;
  confidence: number;
  alternatives: DecisionOption[];
  executionPlan: ExecutionStep[];
  timestamp: number;
}

/**
 * 执行步骤
 */
export interface ExecutionStep {
  id: string;
  action: string;
  parameters: any;
  duration: number;
  dependencies: string[];
  priority: number;
}

/**
 * 决策策略接口
 */
export interface DecisionStrategy {
  name: string;
  evaluate(context: DecisionContext, options: DecisionOption[]): DecisionResult;
}

/**
 * 效用函数决策策略
 */
export class UtilityBasedStrategy implements DecisionStrategy {
  public name = 'utility_based';

  public evaluate(context: DecisionContext, options: DecisionOption[]): DecisionResult {
    let bestOption = options[0];
    let bestUtility = -Infinity;
    const evaluatedOptions: DecisionOption[] = [];

    for (const option of options) {
      const utility = this.calculateUtility(option, context);
      evaluatedOptions.push({ ...option, confidence: utility });

      if (utility > bestUtility) {
        bestUtility = utility;
        bestOption = option;
      }
    }

    return {
      selectedOption: bestOption,
      reasoning: `选择了效用值最高的选项 (${bestUtility.toFixed(2)})`,
      confidence: bestUtility,
      alternatives: evaluatedOptions.filter(o => o.id !== bestOption.id),
      executionPlan: this.generateExecutionPlan(bestOption),
      timestamp: Date.now()
    };
  }

  private calculateUtility(option: DecisionOption, context: DecisionContext): number {
    // 基础效用计算
    let utility = option.benefit - option.cost;

    // 风险调整
    utility -= option.risk * 0.5;

    // 目标相关性调整
    const goalRelevance = this.calculateGoalRelevance(option, context.currentGoals);
    utility *= (1 + goalRelevance);

    // 情感状态调整
    const emotionalModifier = this.calculateEmotionalModifier(option, context.emotionalState);
    utility *= emotionalModifier;

    // 时间压力调整
    const timeModifier = this.calculateTimeModifier(option, context);
    utility *= timeModifier;

    return utility;
  }

  private calculateGoalRelevance(option: DecisionOption, goals: Goal[]): number {
    let relevance = 0;
    for (const goal of goals) {
      if (option.requirements.some(req => goal.requirements.includes(req))) {
        relevance += goal.priority * 0.1;
      }
    }
    return Math.min(relevance, 1.0);
  }

  private calculateEmotionalModifier(option: DecisionOption, emotion: EmotionalState): number {
    let modifier = 1.0;

    // 根据情感状态调整决策倾向
    switch (emotion.primaryEmotion) {
      case 'fear':
        modifier *= (1 - option.risk * 0.3);
        break;
      case 'anger':
        modifier *= (1 + option.risk * 0.2);
        break;
      case 'joy':
        modifier *= (1 + option.benefit * 0.1);
        break;
      case 'sadness':
        modifier *= (1 - option.cost * 0.1);
        break;
    }

    // 信心水平影响
    modifier *= (0.5 + emotion.confidence * 0.5);

    return Math.max(modifier, 0.1);
  }

  private calculateTimeModifier(option: DecisionOption, context: DecisionContext): number {
    // 根据时间压力调整决策
    const urgentGoals = context.currentGoals.filter(g => 
      g.deadline && g.deadline - context.timestamp < 300000 // 5分钟内
    );

    if (urgentGoals.length > 0) {
      return option.duration < 60000 ? 1.2 : 0.8; // 偏好快速行动
    }

    return 1.0;
  }

  private generateExecutionPlan(option: DecisionOption): ExecutionStep[] {
    // 简单的执行计划生成
    return [{
      id: `step_${option.id}`,
      action: option.name,
      parameters: {},
      duration: option.duration,
      dependencies: [],
      priority: 1
    }];
  }
}

/**
 * 基于规则的决策策略
 */
export class RuleBasedStrategy implements DecisionStrategy {
  public name = 'rule_based';
  private rules: DecisionRule[] = [];

  public addRule(rule: DecisionRule): void {
    this.rules.push(rule);
    this.rules.sort((a, b) => b.priority - a.priority);
  }

  public evaluate(context: DecisionContext, options: DecisionOption[]): DecisionResult {
    for (const rule of this.rules) {
      if (rule.condition(context)) {
        const selectedOption = rule.action(context, options);
        if (selectedOption) {
          return {
            selectedOption,
            reasoning: `应用规则: ${rule.name}`,
            confidence: rule.confidence,
            alternatives: options.filter(o => o.id !== selectedOption.id),
            executionPlan: this.generateExecutionPlan(selectedOption),
            timestamp: Date.now()
          };
        }
      }
    }

    // 如果没有规则匹配，返回第一个选项
    return {
      selectedOption: options[0],
      reasoning: '没有匹配的规则，使用默认选项',
      confidence: 0.5,
      alternatives: options.slice(1),
      executionPlan: this.generateExecutionPlan(options[0]),
      timestamp: Date.now()
    };
  }

  private generateExecutionPlan(option: DecisionOption): ExecutionStep[] {
    return [{
      id: `step_${option.id}`,
      action: option.name,
      parameters: {},
      duration: option.duration,
      dependencies: [],
      priority: 1
    }];
  }
}

/**
 * 决策规则接口
 */
export interface DecisionRule {
  name: string;
  priority: number;
  confidence: number;
  condition: (context: DecisionContext) => boolean;
  action: (context: DecisionContext, options: DecisionOption[]) => DecisionOption | null;
}

/**
 * 学习数据接口
 */
export interface LearningData {
  context: DecisionContext;
  selectedOption: DecisionOption;
  outcome: number; // 结果评分 -1 到 1
  timestamp: number;
}

/**
 * GOAP动作接口
 */
export interface GOAPAction {
  name: string;
  cost: number;
  preconditions: Map<string, any>;
  effects: Map<string, any>;
  execute: (context: DecisionContext) => Promise<boolean>;
}

/**
 * GOAP状态
 */
export interface GOAPState {
  [key: string]: any;
}

/**
 * 决策性能指标
 */
export interface DecisionMetrics {
  totalDecisions: number;
  averageConfidence: number;
  successRate: number;
  averageExecutionTime: number;
  strategyUsage: Map<string, number>;
  recentPerformance: number[];
}

/**
 * 强化学习决策策略
 */
export class ReinforcementLearningStrategy implements DecisionStrategy {
  public name = 'reinforcement_learning';
  private qTable: Map<string, Map<string, number>> = new Map();
  private learningRate = 0.1;
  private discountFactor = 0.9;
  private explorationRate = 0.1;
  private learningData: LearningData[] = [];

  public evaluate(context: DecisionContext, options: DecisionOption[]): DecisionResult {
    const stateKey = this.getStateKey(context);

    // 获取或初始化Q值
    if (!this.qTable.has(stateKey)) {
      this.qTable.set(stateKey, new Map());
    }
    const qValues = this.qTable.get(stateKey)!;

    // ε-贪婪策略选择动作
    let selectedOption: DecisionOption;
    let reasoning: string;

    if (Math.random() < this.explorationRate) {
      // 探索：随机选择
      selectedOption = options[Math.floor(Math.random() * options.length)];
      reasoning = '探索性决策：随机选择动作';
    } else {
      // 利用：选择Q值最高的动作
      let bestOption = options[0];
      let bestQValue = qValues.get(bestOption.id) || 0;

      for (const option of options) {
        const qValue = qValues.get(option.id) || 0;
        if (qValue > bestQValue) {
          bestQValue = qValue;
          bestOption = option;
        }
      }

      selectedOption = bestOption;
      reasoning = `利用性决策：选择Q值最高的动作 (${bestQValue.toFixed(3)})`;
    }

    return {
      selectedOption,
      reasoning,
      confidence: this.calculateConfidence(stateKey, selectedOption.id),
      alternatives: options.filter(o => o.id !== selectedOption.id),
      executionPlan: this.generateExecutionPlan(selectedOption),
      timestamp: Date.now()
    };
  }

  /**
   * 学习反馈
   */
  public learn(context: DecisionContext, selectedOption: DecisionOption, reward: number): void {
    const stateKey = this.getStateKey(context);
    const actionId = selectedOption.id;

    // 获取当前Q值
    const qValues = this.qTable.get(stateKey) || new Map();
    const currentQ = qValues.get(actionId) || 0;

    // Q学习更新公式：Q(s,a) = Q(s,a) + α[r + γ*max(Q(s',a')) - Q(s,a)]
    // 简化版本，假设没有下一状态
    const newQ = currentQ + this.learningRate * (reward - currentQ);

    qValues.set(actionId, newQ);
    this.qTable.set(stateKey, qValues);

    // 记录学习数据
    this.learningData.push({
      context,
      selectedOption,
      outcome: reward,
      timestamp: Date.now()
    });

    // 限制学习数据大小
    if (this.learningData.length > 10000) {
      this.learningData.shift();
    }
  }

  private getStateKey(context: DecisionContext): string {
    // 简化的状态表示
    return `${context.entityId}_${context.emotionalState.primaryEmotion}_${context.currentGoals.length}`;
  }

  private calculateConfidence(stateKey: string, actionId: string): number {
    const qValues = this.qTable.get(stateKey);
    if (!qValues) return 0.5;

    const qValue = qValues.get(actionId) || 0;
    return Math.max(0, Math.min(1, (qValue + 1) / 2)); // 归一化到0-1
  }

  private generateExecutionPlan(option: DecisionOption): ExecutionStep[] {
    return [{
      id: `step_${option.id}`,
      action: option.name,
      parameters: {},
      duration: option.duration,
      dependencies: [],
      priority: 1
    }];
  }

  /**
   * 获取学习统计
   */
  public getLearningStats(): any {
    return {
      totalExperiences: this.learningData.length,
      qTableSize: this.qTable.size,
      averageReward: this.learningData.length > 0
        ? this.learningData.reduce((sum, data) => sum + data.outcome, 0) / this.learningData.length
        : 0,
      explorationRate: this.explorationRate
    };
  }
}

/**
 * 目标导向行动规划（GOAP）策略
 */
export class GOAPStrategy implements DecisionStrategy {
  public name = 'goap';
  private actions: GOAPAction[] = [];

  public addAction(action: GOAPAction): void {
    this.actions.push(action);
  }

  public evaluate(context: DecisionContext, options: DecisionOption[]): DecisionResult {
    // 获取当前状态
    const currentState = this.getWorldState(context);

    // 选择最高优先级的目标
    const targetGoal = this.selectGoal(context.currentGoals);
    if (!targetGoal) {
      return this.createDefaultResult(options);
    }

    // 规划动作序列
    const plan = this.planActions(currentState, targetGoal);

    if (plan.length === 0) {
      return this.createDefaultResult(options);
    }

    // 将计划转换为决策选项
    const selectedOption = this.convertPlanToOption(plan, options);

    return {
      selectedOption,
      reasoning: `GOAP规划：为目标 ${targetGoal.name} 生成了 ${plan.length} 步计划`,
      confidence: 0.8,
      alternatives: options.filter(o => o.id !== selectedOption.id),
      executionPlan: this.convertPlanToSteps(plan),
      timestamp: Date.now()
    };
  }

  private getWorldState(context: DecisionContext): GOAPState {
    return {
      location: context.environmentState.location,
      hasResources: context.environmentState.resources.length > 0,
      threatsNearby: context.environmentState.threats.length > 0,
      emotionalState: context.emotionalState.primaryEmotion,
      socialContext: context.socialContext.nearbyEntities.length
    };
  }

  private selectGoal(goals: Goal[]): Goal | null {
    if (goals.length === 0) return null;

    // 选择优先级最高且未完成的目标
    return goals
      .filter(g => !g.isCompleted)
      .sort((a, b) => b.priority - a.priority)[0] || null;
  }

  private planActions(currentState: GOAPState, goal: Goal): GOAPAction[] {
    // 简化的A*规划算法
    const openList: { state: GOAPState; actions: GOAPAction[]; cost: number }[] = [];
    const closedList: Set<string> = new Set();

    openList.push({ state: currentState, actions: [], cost: 0 });

    while (openList.length > 0) {
      // 选择成本最低的节点
      openList.sort((a, b) => a.cost - b.cost);
      const current = openList.shift()!;

      const stateKey = JSON.stringify(current.state);
      if (closedList.has(stateKey)) continue;
      closedList.add(stateKey);

      // 检查是否达到目标
      if (this.isGoalSatisfied(current.state, goal)) {
        return current.actions;
      }

      // 扩展可能的动作
      for (const action of this.actions) {
        if (this.canExecuteAction(current.state, action)) {
          const newState = this.applyAction(current.state, action);
          const newActions = [...current.actions, action];
          const newCost = current.cost + action.cost;

          openList.push({ state: newState, actions: newActions, cost: newCost });
        }
      }

      // 防止无限循环
      if (closedList.size > 100) break;
    }

    return []; // 无法找到计划
  }

  private isGoalSatisfied(state: GOAPState, goal: Goal): boolean {
    // 简化的目标检查
    return goal.requirements.every(req => {
      switch (req) {
        case 'hasResources':
          return state.hasResources;
        case 'safeLocation':
          return !state.threatsNearby;
        default:
          return true;
      }
    });
  }

  private canExecuteAction(state: GOAPState, action: GOAPAction): boolean {
    for (const [key, value] of action.preconditions) {
      if (state[key] !== value) {
        return false;
      }
    }
    return true;
  }

  private applyAction(state: GOAPState, action: GOAPAction): GOAPState {
    const newState = { ...state };
    for (const [key, value] of action.effects) {
      newState[key] = value;
    }
    return newState;
  }

  private convertPlanToOption(plan: GOAPAction[], options: DecisionOption[]): DecisionOption {
    if (plan.length === 0) return options[0];

    const firstAction = plan[0];
    const matchingOption = options.find(o => o.name === firstAction.name);
    return matchingOption || options[0];
  }

  private convertPlanToSteps(plan: GOAPAction[]): ExecutionStep[] {
    return plan.map((action, index) => ({
      id: `goap_step_${index}`,
      action: action.name,
      parameters: {},
      duration: action.cost * 1000, // 转换为毫秒
      dependencies: index > 0 ? [`goap_step_${index - 1}`] : [],
      priority: plan.length - index
    }));
  }

  private createDefaultResult(options: DecisionOption[]): DecisionResult {
    return {
      selectedOption: options[0],
      reasoning: 'GOAP规划失败，使用默认选项',
      confidence: 0.3,
      alternatives: options.slice(1),
      executionPlan: [{
        id: `default_step`,
        action: options[0].name,
        parameters: {},
        duration: options[0].duration,
        dependencies: [],
        priority: 1
      }],
      timestamp: Date.now()
    };
  }
}

/**
 * 智能决策系统
 */
export class IntelligentDecisionSystem {
  private strategies: Map<string, DecisionStrategy> = new Map();
  private defaultStrategy = 'utility_based';
  private blackboard: Blackboard;
  private eventEmitter = new EventEmitter();
  private decisionHistory: DecisionResult[] = [];
  private maxHistorySize = 1000;

  // 性能监控
  private metrics: DecisionMetrics = {
    totalDecisions: 0,
    averageConfidence: 0,
    successRate: 0,
    averageExecutionTime: 0,
    strategyUsage: new Map(),
    recentPerformance: []
  };

  // 情境感知
  private contextPatterns: Map<string, DecisionResult[]> = new Map();
  private adaptiveThresholds: Map<string, number> = new Map();

  // 多智能体协作
  private collaborativeDecisions: Map<string, DecisionResult> = new Map();
  private entityRelationships: Map<string, Map<string, number>> = new Map();

  constructor(blackboard: Blackboard) {
    this.blackboard = blackboard;
    this.initializeDefaultStrategies();
    this.initializeAdaptiveSystem();
  }

  /**
   * 初始化默认策略
   */
  private initializeDefaultStrategies(): void {
    this.strategies.set('utility_based', new UtilityBasedStrategy());
    this.strategies.set('rule_based', new RuleBasedStrategy());
    this.strategies.set('reinforcement_learning', new ReinforcementLearningStrategy());
    this.strategies.set('goap', new GOAPStrategy());
  }

  /**
   * 初始化自适应系统
   */
  private initializeAdaptiveSystem(): void {
    // 初始化自适应阈值
    this.adaptiveThresholds.set('confidence_threshold', 0.7);
    this.adaptiveThresholds.set('risk_tolerance', 0.5);
    this.adaptiveThresholds.set('exploration_rate', 0.1);

    // 监听决策结果以进行自适应调整
    this.on('decisionMade', (data) => {
      this.updateAdaptiveThresholds(data.context, data.result);
    });
  }

  /**
   * 添加决策策略
   */
  public addStrategy(strategy: DecisionStrategy): void {
    this.strategies.set(strategy.name, strategy);
  }

  /**
   * 设置默认策略
   */
  public setDefaultStrategy(strategyName: string): void {
    if (this.strategies.has(strategyName)) {
      this.defaultStrategy = strategyName;
    }
  }

  /**
   * 做出决策（增强版）
   */
  public makeDecision(
    context: DecisionContext,
    options: DecisionOption[],
    strategyName?: string
  ): DecisionResult {
    const startTime = performance.now();

    // 情境感知决策策略选择
    const selectedStrategy = strategyName || this.selectBestStrategy(context);
    const strategy = this.strategies.get(selectedStrategy);

    if (!strategy) {
      throw new Error(`决策策略 ${selectedStrategy} 不存在`);
    }

    // 应用情境过滤和优化
    const optimizedOptions = this.optimizeOptions(context, options);

    // 执行决策
    const result = strategy.evaluate(context, optimizedOptions);

    // 计算执行时间
    const executionTime = performance.now() - startTime;

    // 更新性能指标
    this.updateMetrics(selectedStrategy, result, executionTime);

    // 记录决策历史和模式
    this.recordDecision(result);
    this.recordContextPattern(context, result);

    // 多智能体协作处理
    this.handleCollaborativeDecision(context, result);

    // 触发事件
    this.eventEmitter.emit('decisionMade', { context, result, strategy: selectedStrategy, executionTime });

    return result;
  }

  /**
   * 选择最佳策略
   */
  private selectBestStrategy(context: DecisionContext): string {
    // 基于情境特征选择策略
    const urgentGoals = context.currentGoals.filter(g =>
      g.deadline && g.deadline - context.timestamp < 300000
    );

    // 紧急情况使用规则策略
    if (urgentGoals.length > 0) {
      return 'rule_based';
    }

    // 复杂环境使用GOAP
    if (context.environmentState.obstacles.length > 5 || context.currentGoals.length > 3) {
      return 'goap';
    }

    // 学习环境使用强化学习
    if (this.hasLearningData(context.entityId)) {
      return 'reinforcement_learning';
    }

    // 默认使用效用策略
    return 'utility_based';
  }

  /**
   * 优化决策选项
   */
  private optimizeOptions(context: DecisionContext, options: DecisionOption[]): DecisionOption[] {
    // 基于历史模式过滤选项
    const contextKey = this.getContextKey(context);
    const patterns = this.contextPatterns.get(contextKey) || [];

    if (patterns.length > 0) {
      // 根据历史成功率调整选项权重
      return options.map(option => {
        const historicalSuccess = patterns.filter(p =>
          p.selectedOption.id === option.id && p.confidence > 0.7
        ).length;

        const adjustedOption = { ...option };
        if (historicalSuccess > 0) {
          adjustedOption.benefit *= 1.2; // 提升历史成功选项的收益
        }

        return adjustedOption;
      });
    }

    return options;
  }

  /**
   * 更新性能指标
   */
  private updateMetrics(strategyName: string, result: DecisionResult, executionTime: number): void {
    this.metrics.totalDecisions++;

    // 更新平均置信度
    const currentAvg = this.metrics.averageConfidence;
    this.metrics.averageConfidence = (currentAvg * (this.metrics.totalDecisions - 1) + result.confidence) / this.metrics.totalDecisions;

    // 更新平均执行时间
    const currentTimeAvg = this.metrics.averageExecutionTime;
    this.metrics.averageExecutionTime = (currentTimeAvg * (this.metrics.totalDecisions - 1) + executionTime) / this.metrics.totalDecisions;

    // 更新策略使用统计
    const currentUsage = this.metrics.strategyUsage.get(strategyName) || 0;
    this.metrics.strategyUsage.set(strategyName, currentUsage + 1);

    // 更新最近性能
    this.metrics.recentPerformance.push(result.confidence);
    if (this.metrics.recentPerformance.length > 100) {
      this.metrics.recentPerformance.shift();
    }

    // 计算成功率
    const recentSuccesses = this.metrics.recentPerformance.filter(p => p > 0.7).length;
    this.metrics.successRate = this.metrics.recentPerformance.length > 0
      ? recentSuccesses / this.metrics.recentPerformance.length
      : 0;
  }

  /**
   * 记录情境模式
   */
  private recordContextPattern(context: DecisionContext, result: DecisionResult): void {
    const contextKey = this.getContextKey(context);

    if (!this.contextPatterns.has(contextKey)) {
      this.contextPatterns.set(contextKey, []);
    }

    const patterns = this.contextPatterns.get(contextKey)!;
    patterns.push(result);

    // 限制模式记录大小
    if (patterns.length > 50) {
      patterns.shift();
    }
  }

  /**
   * 处理协作决策
   */
  private handleCollaborativeDecision(context: DecisionContext, result: DecisionResult): void {
    const entityId = context.entityId;

    // 记录当前决策
    this.collaborativeDecisions.set(entityId, result);

    // 更新实体关系
    for (const nearbyEntity of context.socialContext.nearbyEntities) {
      if (!this.entityRelationships.has(entityId)) {
        this.entityRelationships.set(entityId, new Map());
      }

      const relationships = this.entityRelationships.get(entityId)!;
      const currentRelation = relationships.get(nearbyEntity) || 0;

      // 基于决策类型调整关系
      if (result.selectedOption.type === 'cooperative') {
        relationships.set(nearbyEntity, Math.min(1, currentRelation + 0.1));
      } else if (result.selectedOption.type === 'competitive') {
        relationships.set(nearbyEntity, Math.max(-1, currentRelation - 0.1));
      }
    }
  }

  /**
   * 更新自适应阈值
   */
  private updateAdaptiveThresholds(context: DecisionContext, result: DecisionResult): void {
    // 基于决策结果调整阈值
    if (result.confidence < 0.5) {
      // 低置信度决策，降低置信度阈值
      const currentThreshold = this.adaptiveThresholds.get('confidence_threshold') || 0.7;
      this.adaptiveThresholds.set('confidence_threshold', Math.max(0.3, currentThreshold - 0.05));
    } else if (result.confidence > 0.9) {
      // 高置信度决策，提高置信度阈值
      const currentThreshold = this.adaptiveThresholds.get('confidence_threshold') || 0.7;
      this.adaptiveThresholds.set('confidence_threshold', Math.min(0.9, currentThreshold + 0.02));
    }

    // 基于情感状态调整风险容忍度
    if (context.emotionalState.stress > 0.8) {
      this.adaptiveThresholds.set('risk_tolerance', 0.3); // 高压力时降低风险容忍度
    } else if (context.emotionalState.confidence > 0.8) {
      this.adaptiveThresholds.set('risk_tolerance', 0.7); // 高信心时提高风险容忍度
    }
  }

  /**
   * 记录决策
   */
  private recordDecision(result: DecisionResult): void {
    this.decisionHistory.push(result);
    
    // 限制历史记录大小
    if (this.decisionHistory.length > this.maxHistorySize) {
      this.decisionHistory.shift();
    }
  }

  /**
   * 检查是否有学习数据
   */
  private hasLearningData(entityId: string): boolean {
    const rlStrategy = this.strategies.get('reinforcement_learning') as ReinforcementLearningStrategy;
    if (rlStrategy) {
      const stats = rlStrategy.getLearningStats();
      return stats.totalExperiences > 10; // 至少有10次经验
    }
    return false;
  }

  /**
   * 获取情境键
   */
  private getContextKey(context: DecisionContext): string {
    return `${context.entityId}_${context.emotionalState.primaryEmotion}_${context.environmentState.weather}_${context.currentGoals.length}`;
  }

  /**
   * 获取决策历史
   */
  public getDecisionHistory(limit?: number): DecisionResult[] {
    if (limit) {
      return this.decisionHistory.slice(-limit);
    }
    return [...this.decisionHistory];
  }

  /**
   * 获取性能指标
   */
  public getMetrics(): DecisionMetrics {
    return { ...this.metrics };
  }

  /**
   * 获取策略性能比较
   */
  public getStrategyPerformance(): Map<string, { usage: number; avgConfidence: number }> {
    const performance = new Map();

    for (const [strategy, usage] of this.metrics.strategyUsage) {
      // 计算该策略的平均置信度
      const strategyDecisions = this.decisionHistory.filter(d =>
        d.reasoning.includes(strategy) || d.reasoning.includes(this.getStrategyDisplayName(strategy))
      );

      const avgConfidence = strategyDecisions.length > 0
        ? strategyDecisions.reduce((sum, d) => sum + d.confidence, 0) / strategyDecisions.length
        : 0;

      performance.set(strategy, { usage, avgConfidence });
    }

    return performance;
  }

  /**
   * 获取策略显示名称
   */
  private getStrategyDisplayName(strategy: string): string {
    const displayNames: { [key: string]: string } = {
      'utility_based': '效用',
      'rule_based': '规则',
      'reinforcement_learning': '强化学习',
      'goap': 'GOAP'
    };
    return displayNames[strategy] || strategy;
  }

  /**
   * 协作决策
   */
  public makeCollaborativeDecision(
    contexts: DecisionContext[],
    options: DecisionOption[],
    collaborationType: 'consensus' | 'majority' | 'weighted'
  ): DecisionResult {
    const individualResults = contexts.map(context =>
      this.makeDecision(context, options)
    );

    let selectedOption: DecisionOption;
    let reasoning: string;
    let confidence: number;

    switch (collaborationType) {
      case 'consensus':
        // 寻找所有实体都同意的选项
        const consensusOption = this.findConsensusOption(individualResults);
        selectedOption = consensusOption || options[0];
        reasoning = consensusOption ? '达成共识的决策' : '无法达成共识，使用默认选项';
        confidence = consensusOption ? 0.9 : 0.3;
        break;

      case 'majority':
        // 多数投票
        const majorityResult = this.getMajorityDecision(individualResults);
        selectedOption = majorityResult.option;
        reasoning = `多数决策：${majorityResult.count}/${individualResults.length} 票`;
        confidence = majorityResult.count / individualResults.length;
        break;

      case 'weighted':
        // 基于实体关系权重的决策
        const weightedResult = this.getWeightedDecision(contexts, individualResults);
        selectedOption = weightedResult.option;
        reasoning = `加权决策：权重分数 ${weightedResult.score.toFixed(2)}`;
        confidence = Math.min(1, weightedResult.score);
        break;

      default:
        selectedOption = options[0];
        reasoning = '未知协作类型，使用默认选项';
        confidence = 0.3;
    }

    return {
      selectedOption,
      reasoning,
      confidence,
      alternatives: options.filter(o => o.id !== selectedOption.id),
      executionPlan: [{
        id: `collaborative_step`,
        action: selectedOption.name,
        parameters: { collaborationType, participantCount: contexts.length },
        duration: selectedOption.duration,
        dependencies: [],
        priority: 1
      }],
      timestamp: Date.now()
    };
  }

  /**
   * 寻找共识选项
   */
  private findConsensusOption(results: DecisionResult[]): DecisionOption | null {
    if (results.length === 0) return null;

    const firstOption = results[0].selectedOption;
    const allAgree = results.every(result => result.selectedOption.id === firstOption.id);

    return allAgree ? firstOption : null;
  }

  /**
   * 获取多数决策
   */
  private getMajorityDecision(results: DecisionResult[]): { option: DecisionOption; count: number } {
    const votes = new Map<string, { option: DecisionOption; count: number }>();

    for (const result of results) {
      const optionId = result.selectedOption.id;
      if (votes.has(optionId)) {
        votes.get(optionId)!.count++;
      } else {
        votes.set(optionId, { option: result.selectedOption, count: 1 });
      }
    }

    let maxVotes = 0;
    let winningOption = results[0].selectedOption;

    for (const vote of votes.values()) {
      if (vote.count > maxVotes) {
        maxVotes = vote.count;
        winningOption = vote.option;
      }
    }

    return { option: winningOption, count: maxVotes };
  }

  /**
   * 获取加权决策
   */
  private getWeightedDecision(
    contexts: DecisionContext[],
    results: DecisionResult[]
  ): { option: DecisionOption; score: number } {
    const scores = new Map<string, { option: DecisionOption; score: number }>();

    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      const context = contexts[i];
      const optionId = result.selectedOption.id;

      // 计算权重（基于置信度和社交影响力）
      const socialInfluence = context.socialContext.nearbyEntities.length * 0.1;
      const weight = result.confidence * (1 + socialInfluence);

      if (scores.has(optionId)) {
        scores.get(optionId)!.score += weight;
      } else {
        scores.set(optionId, { option: result.selectedOption, score: weight });
      }
    }

    let maxScore = 0;
    let winningOption = results[0].selectedOption;

    for (const score of scores.values()) {
      if (score.score > maxScore) {
        maxScore = score.score;
        winningOption = score.option;
      }
    }

    return { option: winningOption, score: maxScore };
  }

  /**
   * 监听事件
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 生成决策解释
   */
  public explainDecision(result: DecisionResult, context: DecisionContext): string {
    let explanation = `决策解释：\n`;
    explanation += `选择的行动：${result.selectedOption.name}\n`;
    explanation += `置信度：${(result.confidence * 100).toFixed(1)}%\n`;
    explanation += `推理过程：${result.reasoning}\n\n`;

    explanation += `决策因素分析：\n`;
    explanation += `- 收益：${result.selectedOption.benefit}\n`;
    explanation += `- 成本：${result.selectedOption.cost}\n`;
    explanation += `- 风险：${result.selectedOption.risk}\n`;
    explanation += `- 持续时间：${result.selectedOption.duration}ms\n\n`;

    explanation += `情境因素：\n`;
    explanation += `- 当前目标数量：${context.currentGoals.length}\n`;
    explanation += `- 主要情感：${context.emotionalState.primaryEmotion}\n`;
    explanation += `- 情感强度：${context.emotionalState.intensity}\n`;
    explanation += `- 环境威胁：${context.environmentState.threats.length}\n`;
    explanation += `- 社交环境：${context.socialContext.nearbyEntities.length} 个附近实体\n\n`;

    if (result.alternatives.length > 0) {
      explanation += `备选方案：\n`;
      result.alternatives.slice(0, 3).forEach((alt, index) => {
        explanation += `${index + 1}. ${alt.name} (收益:${alt.benefit}, 风险:${alt.risk})\n`;
      });
    }

    return explanation;
  }

  /**
   * 决策回滚
   */
  public rollbackDecision(decisionId: string): boolean {
    const decisionIndex = this.decisionHistory.findIndex(d =>
      d.timestamp.toString() === decisionId
    );

    if (decisionIndex === -1) {
      return false;
    }

    // 移除该决策及之后的所有决策
    const removedDecisions = this.decisionHistory.splice(decisionIndex);

    // 更新性能指标
    this.metrics.totalDecisions -= removedDecisions.length;

    // 触发回滚事件
    this.eventEmitter.emit('decisionRolledBack', {
      removedCount: removedDecisions.length,
      decisionId
    });

    return true;
  }

  /**
   * 预测决策结果
   */
  public predictDecisionOutcome(
    context: DecisionContext,
    option: DecisionOption
  ): { successProbability: number; expectedBenefit: number; riskAssessment: string } {
    // 基于历史数据预测
    const similarDecisions = this.decisionHistory.filter(d =>
      d.selectedOption.type === option.type &&
      Math.abs(d.selectedOption.cost - option.cost) < option.cost * 0.2
    );

    let successProbability = 0.5; // 默认概率
    if (similarDecisions.length > 0) {
      const successCount = similarDecisions.filter(d => d.confidence > 0.7).length;
      successProbability = successCount / similarDecisions.length;
    }

    // 计算期望收益
    const expectedBenefit = option.benefit * successProbability - option.cost * (1 - successProbability);

    // 风险评估
    let riskAssessment = '低风险';
    if (option.risk > 0.7) {
      riskAssessment = '高风险';
    } else if (option.risk > 0.4) {
      riskAssessment = '中等风险';
    }

    // 考虑情境因素
    if (context.emotionalState.stress > 0.8) {
      riskAssessment += ' (高压力状态下风险增加)';
    }

    return {
      successProbability,
      expectedBenefit,
      riskAssessment
    };
  }

  /**
   * 优化决策策略
   */
  public optimizeStrategies(): void {
    const performance = this.getStrategyPerformance();

    // 找到表现最好的策略
    let bestStrategy = this.defaultStrategy;
    let bestPerformance = 0;

    for (const [strategy, stats] of performance) {
      const score = stats.avgConfidence * (stats.usage / this.metrics.totalDecisions);
      if (score > bestPerformance) {
        bestPerformance = score;
        bestStrategy = strategy;
      }
    }

    // 更新默认策略
    if (bestStrategy !== this.defaultStrategy) {
      this.defaultStrategy = bestStrategy;
      this.eventEmitter.emit('strategyOptimized', {
        newDefault: bestStrategy,
        performance: bestPerformance
      });
    }

    // 调整强化学习参数
    const rlStrategy = this.strategies.get('reinforcement_learning') as ReinforcementLearningStrategy;
    if (rlStrategy) {
      const stats = rlStrategy.getLearningStats();
      if (stats.averageReward < 0.3) {
        // 增加探索率
        rlStrategy['explorationRate'] = Math.min(0.3, rlStrategy['explorationRate'] + 0.05);
      } else if (stats.averageReward > 0.7) {
        // 减少探索率
        rlStrategy['explorationRate'] = Math.max(0.05, rlStrategy['explorationRate'] - 0.02);
      }
    }
  }

  /**
   * 清除历史数据
   */
  public clearHistory(): void {
    this.decisionHistory = [];
    this.contextPatterns.clear();
    this.collaborativeDecisions.clear();

    // 重置性能指标
    this.metrics = {
      totalDecisions: 0,
      averageConfidence: 0,
      successRate: 0,
      averageExecutionTime: 0,
      strategyUsage: new Map(),
      recentPerformance: []
    };

    this.eventEmitter.emit('historyCleared');
  }

  /**
   * 导出决策数据
   */
  public exportDecisionData(): any {
    return {
      decisionHistory: this.decisionHistory,
      metrics: this.metrics,
      contextPatterns: Array.from(this.contextPatterns.entries()),
      adaptiveThresholds: Array.from(this.adaptiveThresholds.entries()),
      strategyPerformance: Array.from(this.getStrategyPerformance().entries()),
      timestamp: Date.now()
    };
  }

  /**
   * 导入决策数据
   */
  public importDecisionData(data: any): boolean {
    try {
      if (data.decisionHistory) {
        this.decisionHistory = data.decisionHistory;
      }

      if (data.metrics) {
        this.metrics = { ...this.metrics, ...data.metrics };
      }

      if (data.contextPatterns) {
        this.contextPatterns = new Map(data.contextPatterns);
      }

      if (data.adaptiveThresholds) {
        this.adaptiveThresholds = new Map(data.adaptiveThresholds);
      }

      this.eventEmitter.emit('dataImported', { success: true });
      return true;
    } catch (error) {
      console.error('导入决策数据失败:', error);
      this.eventEmitter.emit('dataImported', { success: false, error });
      return false;
    }
  }
}
