#!/bin/bash

# DL引擎RAG应用系统部署脚本
# 用于快速部署和管理RAG应用系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查环境变量文件
    if [ ! -f ".env" ]; then
        log_warning ".env 文件不存在，正在从 .env.example 复制..."
        cp .env.example .env
        log_warning "请编辑 .env 文件并设置正确的配置值"
    fi
    
    log_success "依赖检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p uploads/knowledge
    mkdir -p uploads/avatars
    mkdir -p uploads/voice
    mkdir -p temp/knowledge
    mkdir -p temp/voice
    mkdir -p assets/models
    mkdir -p assets/textures
    mkdir -p assets/animations
    mkdir -p config
    mkdir -p logs
    
    log_success "目录创建完成"
}

# 构建服务
build_services() {
    log_info "构建Docker镜像..."
    
    # 构建知识库服务
    if [ -d "server/knowledge-service" ]; then
        log_info "构建知识库服务..."
        docker-compose build knowledge-service
    fi
    
    # 构建RAG服务
    if [ -d "server/rag-service" ]; then
        log_info "构建RAG服务..."
        docker-compose build rag-service
    fi
    
    # 构建数字人服务
    if [ -d "server/avatar-service" ]; then
        log_info "构建数字人服务..."
        docker-compose build avatar-service
    fi
    
    # 构建语音服务
    if [ -d "server/voice-service" ]; then
        log_info "构建语音服务..."
        docker-compose build voice-service
    fi
    
    # 构建编辑器
    if [ -d "editor" ]; then
        log_info "构建编辑器..."
        docker-compose build editor
    fi
    
    log_success "Docker镜像构建完成"
}

# 启动基础服务
start_infrastructure() {
    log_info "启动基础设施服务..."
    
    # 启动数据库和缓存
    docker-compose up -d mysql redis elasticsearch
    
    # 等待服务启动
    log_info "等待基础服务启动..."
    sleep 30
    
    # 检查服务状态
    check_service_health "mysql" "3306"
    check_service_health "redis" "6379"
    check_service_health "elasticsearch" "9200"
    
    log_success "基础设施服务启动完成"
}

# 启动RAG服务
start_rag_services() {
    log_info "启动RAG应用服务..."
    
    # 按依赖顺序启动服务
    docker-compose up -d knowledge-service
    sleep 10
    check_service_health "knowledge-service" "4011"
    
    docker-compose up -d rag-service
    sleep 10
    check_service_health "rag-service" "4012"
    
    docker-compose up -d avatar-service
    sleep 10
    check_service_health "avatar-service" "4013"
    
    docker-compose up -d voice-service
    sleep 10
    check_service_health "voice-service" "4014"
    
    log_success "RAG应用服务启动完成"
}

# 启动前端服务
start_frontend() {
    log_info "启动前端服务..."
    
    # 启动API网关和编辑器
    docker-compose up -d api-gateway editor
    
    sleep 15
    
    check_service_health "api-gateway" "3000"
    check_service_health "editor" "80"
    
    log_success "前端服务启动完成"
}

# 检查服务健康状态
check_service_health() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    log_info "检查 $service_name 服务健康状态..."
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose ps $service_name | grep -q "Up"; then
            if nc -z localhost $port 2>/dev/null; then
                log_success "$service_name 服务健康检查通过"
                return 0
            fi
        fi
        
        log_info "等待 $service_name 服务启动... ($attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
    
    log_error "$service_name 服务启动失败或健康检查超时"
    return 1
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 等待MySQL启动
    sleep 10
    
    # 创建数据库
    docker-compose exec mysql mysql -uroot -p${MYSQL_ROOT_PASSWORD} -e "
        CREATE DATABASE IF NOT EXISTS ir_engine_knowledge;
        CREATE DATABASE IF NOT EXISTS ir_engine_rag;
        CREATE DATABASE IF NOT EXISTS ir_engine_avatars;
    " 2>/dev/null || log_warning "数据库可能已存在"
    
    log_success "数据库初始化完成"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 知识库服务迁移
    if docker-compose ps knowledge-service | grep -q "Up"; then
        docker-compose exec knowledge-service npm run migration:run || log_warning "知识库服务迁移失败"
    fi
    
    # RAG服务迁移
    if docker-compose ps rag-service | grep -q "Up"; then
        docker-compose exec rag-service npm run migration:run || log_warning "RAG服务迁移失败"
    fi
    
    # 数字人服务迁移
    if docker-compose ps avatar-service | grep -q "Up"; then
        docker-compose exec avatar-service npm run migration:run || log_warning "数字人服务迁移失败"
    fi
    
    log_success "数据库迁移完成"
}

# 显示服务状态
show_status() {
    log_info "服务状态："
    echo ""
    docker-compose ps
    echo ""
    
    log_info "服务访问地址："
    echo "编辑器: http://localhost"
    echo "API网关: http://localhost:3000"
    echo "知识库服务: http://localhost:4011"
    echo "RAG服务: http://localhost:4012"
    echo "数字人服务: http://localhost:4013"
    echo "语音服务: http://localhost:4014"
    echo "Elasticsearch: http://localhost:9200"
    echo ""
}

# 停止所有服务
stop_services() {
    log_info "停止所有服务..."
    docker-compose down
    log_success "所有服务已停止"
}

# 清理资源
cleanup() {
    log_info "清理Docker资源..."
    docker-compose down -v --remove-orphans
    docker system prune -f
    log_success "资源清理完成"
}

# 查看日志
view_logs() {
    local service=$1
    if [ -z "$service" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f $service
    fi
}

# 重启服务
restart_service() {
    local service=$1
    if [ -z "$service" ]; then
        log_error "请指定要重启的服务名称"
        exit 1
    fi
    
    log_info "重启服务: $service"
    docker-compose restart $service
    log_success "服务 $service 重启完成"
}

# 备份数据
backup_data() {
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p $backup_dir
    
    log_info "备份数据到: $backup_dir"
    
    # 备份MySQL数据
    docker-compose exec mysql mysqldump -uroot -p${MYSQL_ROOT_PASSWORD} --all-databases > $backup_dir/mysql_backup.sql
    
    # 备份上传文件
    cp -r uploads $backup_dir/
    
    # 备份配置文件
    cp .env $backup_dir/
    
    log_success "数据备份完成"
}

# 主函数
main() {
    case "$1" in
        "start")
            check_dependencies
            create_directories
            build_services
            start_infrastructure
            init_database
            start_rag_services
            run_migrations
            start_frontend
            show_status
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_service $2
            ;;
        "status")
            show_status
            ;;
        "logs")
            view_logs $2
            ;;
        "cleanup")
            cleanup
            ;;
        "backup")
            backup_data
            ;;
        "build")
            build_services
            ;;
        *)
            echo "用法: $0 {start|stop|restart|status|logs|cleanup|backup|build}"
            echo ""
            echo "命令说明："
            echo "  start   - 启动所有服务"
            echo "  stop    - 停止所有服务"
            echo "  restart - 重启指定服务"
            echo "  status  - 显示服务状态"
            echo "  logs    - 查看日志"
            echo "  cleanup - 清理Docker资源"
            echo "  backup  - 备份数据"
            echo "  build   - 构建Docker镜像"
            echo ""
            echo "示例："
            echo "  $0 start                    # 启动所有服务"
            echo "  $0 restart voice-service    # 重启语音服务"
            echo "  $0 logs rag-service         # 查看RAG服务日志"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
