/**
 * 数字人路径路由
 */
import { Router } from 'express';
import { avatarPathController, pathValidationRules } from '../controllers/AvatarPathController';
import { authMiddleware } from '../middleware/auth';
import { rateLimitMiddleware } from '../middleware/rateLimit';
import { cacheMiddleware } from '../middleware/cache';

const router = Router();

/**
 * 创建路径
 * POST /api/avatar-paths
 */
router.post(
  '/',
  authMiddleware,
  rateLimitMiddleware({ windowMs: 60000, max: 10 }), // 每分钟最多10次
  pathValidationRules.createPath,
  avatarPathController.createPath.bind(avatarPathController)
);

/**
 * 查询路径列表
 * GET /api/avatar-paths
 */
router.get(
  '/',
  authMiddleware,
  rateLimitMiddleware({ windowMs: 60000, max: 100 }), // 每分钟最多100次
  cacheMiddleware({ ttl: 300 }), // 缓存5分钟
  pathValidationRules.queryPaths,
  avatarPathController.queryPaths.bind(avatarPathController)
);

/**
 * 获取路径详情
 * GET /api/avatar-paths/:pathId
 */
router.get(
  '/:pathId',
  authMiddleware,
  rateLimitMiddleware({ windowMs: 60000, max: 100 }),
  cacheMiddleware({ ttl: 300 }),
  pathValidationRules.getPath,
  avatarPathController.getPath.bind(avatarPathController)
);

/**
 * 更新路径
 * PUT /api/avatar-paths/:pathId
 */
router.put(
  '/:pathId',
  authMiddleware,
  rateLimitMiddleware({ windowMs: 60000, max: 20 }), // 每分钟最多20次
  pathValidationRules.updatePath,
  avatarPathController.updatePath.bind(avatarPathController)
);

/**
 * 删除路径
 * DELETE /api/avatar-paths/:pathId
 */
router.delete(
  '/:pathId',
  authMiddleware,
  rateLimitMiddleware({ windowMs: 60000, max: 10 }), // 每分钟最多10次
  pathValidationRules.deletePath,
  avatarPathController.deletePath.bind(avatarPathController)
);

/**
 * 克隆路径
 * POST /api/avatar-paths/:pathId/clone
 */
router.post(
  '/:pathId/clone',
  authMiddleware,
  rateLimitMiddleware({ windowMs: 60000, max: 5 }), // 每分钟最多5次
  pathValidationRules.clonePath,
  avatarPathController.clonePath.bind(avatarPathController)
);

/**
 * 验证路径
 * POST /api/avatar-paths/:pathId/validate
 */
router.post(
  '/:pathId/validate',
  authMiddleware,
  rateLimitMiddleware({ windowMs: 60000, max: 20 }),
  pathValidationRules.getPath,
  avatarPathController.validatePath.bind(avatarPathController)
);

/**
 * 导出路径
 * GET /api/avatar-paths/:pathId/export
 */
router.get(
  '/:pathId/export',
  authMiddleware,
  rateLimitMiddleware({ windowMs: 60000, max: 10 }),
  pathValidationRules.getPath,
  avatarPathController.exportPath.bind(avatarPathController)
);

/**
 * 导入路径
 * POST /api/avatar-paths/import
 */
router.post(
  '/import',
  authMiddleware,
  rateLimitMiddleware({ windowMs: 60000, max: 5 }),
  avatarPathController.importPath.bind(avatarPathController)
);

/**
 * 批量操作路径
 * POST /api/avatar-paths/batch
 */
router.post(
  '/batch',
  authMiddleware,
  rateLimitMiddleware({ windowMs: 60000, max: 5 }),
  pathValidationRules.batchOperation,
  avatarPathController.batchOperation.bind(avatarPathController)
);

/**
 * 获取项目路径统计
 * GET /api/avatar-paths/projects/:projectId/statistics
 */
router.get(
  '/projects/:projectId/statistics',
  authMiddleware,
  rateLimitMiddleware({ windowMs: 60000, max: 50 }),
  cacheMiddleware({ ttl: 600 }), // 缓存10分钟
  pathValidationRules.getStatistics,
  avatarPathController.getPathStatistics.bind(avatarPathController)
);

export default router;
