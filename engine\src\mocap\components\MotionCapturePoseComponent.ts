/**
 * 动作捕捉姿势组件
 * 用于存储和管理动作捕捉姿势状态
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';

/**
 * 动作捕捉姿势类型
 */
export enum MotionCapturePoseType {
  /** 站立 */
  STANDING = 'standing',
  /** 坐姿 */
  SITTING = 'sitting',
  /** T姿势 */
  T_POSE = 'tpose',
  /** A姿势 */
  A_POSE = 'apose',
  /** 行走 */
  WALKING = 'walking',
  /** 跑步 */
  RUNNING = 'running',
  /** 跳跃 */
  JUMPING = 'jumping',
  /** 蹲下 */
  CROUCHING = 'crouching',
  /** 趴下 */
  PRONE = 'prone',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 动作捕捉姿势状态
 */
export interface MotionCapturePoseState {
  /** 是否开始 */
  begun: boolean;
  /** 持续时间（秒） */
  duration?: number;
  /** 强度（0-1） */
  intensity?: number;
  /** 置信度（0-1） */
  confidence?: number;
}

/**
 * 动作捕捉姿势组件配置
 */
export interface MotionCapturePoseComponentConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 姿势持续阈值（秒） */
  poseHoldThreshold?: number;
  /** 姿势角度阈值（弧度） */
  poseAngleThreshold?: number;
}

/**
 * 动作捕捉姿势组件
 * 存储和管理动作捕捉姿势状态
 */
export class MotionCapturePoseComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE = 'MotionCapturePoseComponent';

  // 移除重复的enabled声明，使用基类的enabled属性

  /** 姿势持续阈值（秒） */
  public poseHoldThreshold: number;

  /** 姿势角度阈值（弧度） */
  public poseAngleThreshold: number;

  /** 当前姿势类型 */
  public currentPose: MotionCapturePoseType = MotionCapturePoseType.STANDING;

  /** 站立姿势状态 */
  public standing?: MotionCapturePoseState;

  /** 坐姿状态 */
  public sitting?: MotionCapturePoseState;

  /** T姿势状态 */
  public tpose?: MotionCapturePoseState;

  /** A姿势状态 */
  public apose?: MotionCapturePoseState;

  /** 行走状态 */
  public walking?: MotionCapturePoseState;

  /** 跑步状态 */
  public running?: MotionCapturePoseState;

  /** 跳跃状态 */
  public jumping?: MotionCapturePoseState;

  /** 蹲下状态 */
  public crouching?: MotionCapturePoseState;

  /** 趴下状态 */
  public prone?: MotionCapturePoseState;

  /** 自定义姿势状态 */
  public custom?: MotionCapturePoseState;

  /** 姿势持续计时器 */
  private poseHoldTimer: number = 0;

  /** 上一帧姿势类型 */
  private previousPose: MotionCapturePoseType = MotionCapturePoseType.STANDING;

  /**
   * 构造函数
   * @param entity 实体
   * @param config 组件配置
   */
  constructor(entity: Entity, config: MotionCapturePoseComponentConfig = {}) {
    super(MotionCapturePoseComponent.TYPE);
    this.setEntity(entity);

    this.setEnabled(config.enabled !== undefined ? config.enabled : true);
    this.poseHoldThreshold = config.poseHoldThreshold || 0.25; // 默认0.25秒
    this.poseAngleThreshold = config.poseAngleThreshold || 1.25; // 默认1.25弧度

    // 初始化姿势状态
    this.initPoseStates();
  }

  /**
   * 初始化姿势状态
   */
  private initPoseStates(): void {
    this.standing = { begun: true, duration: 0, intensity: 1, confidence: 1 };
    this.sitting = { begun: false, duration: 0, intensity: 0, confidence: 0 };
    this.tpose = { begun: false, duration: 0, intensity: 0, confidence: 0 };
    this.apose = { begun: false, duration: 0, intensity: 0, confidence: 0 };
    this.walking = { begun: false, duration: 0, intensity: 0, confidence: 0 };
    this.running = { begun: false, duration: 0, intensity: 0, confidence: 0 };
    this.jumping = { begun: false, duration: 0, intensity: 0, confidence: 0 };
    this.crouching = { begun: false, duration: 0, intensity: 0, confidence: 0 };
    this.prone = { begun: false, duration: 0, intensity: 0, confidence: 0 };
    this.custom = { begun: false, duration: 0, intensity: 0, confidence: 0 };
  }

  /**
   * 设置姿势状态
   * @param poseType 姿势类型
   * @param state 姿势状态
   */
  public setPoseState(poseType: MotionCapturePoseType, state: Partial<MotionCapturePoseState>): void {
    // 获取当前姿势状态
    const currentState = this.getPoseState(poseType);
    if (!currentState) return;

    // 更新状态
    if (state.begun !== undefined) currentState.begun = state.begun;
    if (state.duration !== undefined) currentState.duration = state.duration;
    if (state.intensity !== undefined) currentState.intensity = state.intensity;
    if (state.confidence !== undefined) currentState.confidence = state.confidence;

    // 如果姿势开始，更新当前姿势类型
    if (state.begun && !this.getPoseState(poseType)?.begun) {
      this.previousPose = this.currentPose;
      this.currentPose = poseType;
    }
  }

  /**
   * 获取姿势状态
   * @param poseType 姿势类型
   * @returns 姿势状态
   */
  public getPoseState(poseType: MotionCapturePoseType): MotionCapturePoseState | undefined {
    switch (poseType) {
      case MotionCapturePoseType.STANDING:
        return this.standing;
      case MotionCapturePoseType.SITTING:
        return this.sitting;
      case MotionCapturePoseType.T_POSE:
        return this.tpose;
      case MotionCapturePoseType.A_POSE:
        return this.apose;
      case MotionCapturePoseType.WALKING:
        return this.walking;
      case MotionCapturePoseType.RUNNING:
        return this.running;
      case MotionCapturePoseType.JUMPING:
        return this.jumping;
      case MotionCapturePoseType.CROUCHING:
        return this.crouching;
      case MotionCapturePoseType.PRONE:
        return this.prone;
      case MotionCapturePoseType.CUSTOM:
        return this.custom;
      default:
        return undefined;
    }
  }

  /**
   * 更新姿势持续时间
   * @param deltaTime 时间增量（秒）
   */
  public updatePoseDurations(deltaTime: number): void {
    // 更新当前姿势的持续时间
    const currentState = this.getPoseState(this.currentPose);
    if (currentState) {
      currentState.duration = (currentState.duration || 0) + deltaTime;
    }

    // 如果当前姿势与上一帧不同，重置计时器
    if (this.currentPose !== this.previousPose) {
      this.poseHoldTimer = 0;
      this.previousPose = this.currentPose;
    } else {
      // 否则增加计时器
      this.poseHoldTimer += deltaTime;
    }
  }

  /**
   * 检查姿势是否持续足够长时间
   * @param poseType 姿势类型
   * @returns 是否持续足够长时间
   */
  public isPoseHeld(poseType: MotionCapturePoseType): boolean {
    // 获取姿势状态
    const state = this.getPoseState(poseType);
    if (!state || !state.begun) return false;

    // 检查持续时间是否超过阈值
    return (state.duration || 0) >= this.poseHoldThreshold;
  }

  /**
   * 重置所有姿势状态
   */
  public resetAllPoses(): void {
    // 重置所有姿势状态
    for (const poseType of Object.values(MotionCapturePoseType)) {
      const state = this.getPoseState(poseType as MotionCapturePoseType);
      if (state) {
        state.begun = false;
        state.duration = 0;
        state.intensity = 0;
        state.confidence = 0;
      }
    }

    // 重置为站立姿势
    this.currentPose = MotionCapturePoseType.STANDING;
    this.previousPose = MotionCapturePoseType.STANDING;
    this.standing = { begun: true, duration: 0, intensity: 1, confidence: 1 };
    this.poseHoldTimer = 0;
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new MotionCapturePoseComponent(this.getEntity()!, {
      enabled: this.isEnabled(),
      poseHoldThreshold: this.poseHoldThreshold,
      poseAngleThreshold: this.poseAngleThreshold
    });
  }

  /**
   * 克隆组件
   * @param entity 目标实体
   * @returns 克隆的组件
   */
  public clone(entity?: Entity): MotionCapturePoseComponent {
    const targetEntity = entity || this.getEntity()!;
    const component = new MotionCapturePoseComponent(targetEntity, {
      enabled: this.isEnabled(),
      poseHoldThreshold: this.poseHoldThreshold,
      poseAngleThreshold: this.poseAngleThreshold
    });

    // 复制姿势状态
    component.currentPose = this.currentPose;
    component.previousPose = this.previousPose;
    component.poseHoldTimer = this.poseHoldTimer;

    // 复制各种姿势状态
    if (this.standing) component.standing = { ...this.standing };
    if (this.sitting) component.sitting = { ...this.sitting };
    if (this.tpose) component.tpose = { ...this.tpose };
    if (this.apose) component.apose = { ...this.apose };
    if (this.walking) component.walking = { ...this.walking };
    if (this.running) component.running = { ...this.running };
    if (this.jumping) component.jumping = { ...this.jumping };
    if (this.crouching) component.crouching = { ...this.crouching };
    if (this.prone) component.prone = { ...this.prone };
    if (this.custom) component.custom = { ...this.custom };

    return component;
  }
}
