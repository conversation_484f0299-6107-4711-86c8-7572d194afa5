import { 
  Controller, 
  Post, 
  Get, 
  Put,
  Body, 
  Param, 
  Query, 
  HttpStatus,
  HttpException,
  Logger,
  UseGuards,
  ValidationPipe
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery,
  ApiBearerAuth 
} from '@nestjs/swagger';
import { XAPIStatement } from '../xapi/interfaces/xapi.interface';
import { LearningDataSyncService } from '../sync/learning-data-sync.service';
import { LearnerProfileAnalyzerService } from '../profile/learner-profile-analyzer.service';
import { PersonalizedRecommendationService } from '../recommendation/personalized-recommendation.service';
import { 
  RecommendationRequest, 
  RecommendationFeedback 
} from '../recommendation/recommendation.interface';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';

/**
 * 学习记录跟踪控制器
 * 提供学习数据采集、用户画像分析和个性化推荐的API接口
 */
@ApiTags('学习记录跟踪')
@Controller('api/learning-tracking')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class LearningTrackingController {
  private readonly logger = new Logger(LearningTrackingController.name);

  constructor(
    private readonly syncService: LearningDataSyncService,
    private readonly profileAnalyzer: LearnerProfileAnalyzerService,
    private readonly recommendationService: PersonalizedRecommendationService,
  ) {}

  /**
   * 接收单个xAPI语句
   */
  @Post('statements')
  @ApiOperation({ summary: '接收xAPI学习记录语句' })
  @ApiResponse({ status: 201, description: '语句接收成功' })
  @ApiResponse({ status: 400, description: '语句格式错误' })
  async receiveStatement(@Body() statement: XAPIStatement) {
    try {
      await this.syncService.receiveStatement(statement);
      
      this.logger.log(`接收xAPI语句成功: ${statement.id}`);
      
      return {
        success: true,
        message: '学习记录接收成功',
        statementId: statement.id
      };
    } catch (error) {
      this.logger.error(`接收xAPI语句失败: ${error.message}`, error.stack);
      throw new HttpException(
        `接收学习记录失败: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * 批量接收xAPI语句
   */
  @Post('statements/batch')
  @ApiOperation({ summary: '批量接收xAPI学习记录语句' })
  @ApiResponse({ status: 201, description: '批量语句接收成功' })
  @ApiResponse({ status: 400, description: '语句格式错误' })
  async receiveStatements(@Body() body: { statements: XAPIStatement[] }) {
    try {
      const { statements } = body;
      
      if (!statements || !Array.isArray(statements)) {
        throw new Error('statements字段必须是数组');
      }

      await this.syncService.receiveStatements(statements);
      
      this.logger.log(`批量接收xAPI语句成功: ${statements.length}条`);
      
      return {
        success: true,
        message: '批量学习记录接收成功',
        count: statements.length
      };
    } catch (error) {
      this.logger.error(`批量接收xAPI语句失败: ${error.message}`, error.stack);
      throw new HttpException(
        `批量接收学习记录失败: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * 获取用户学习画像
   */
  @Get('profile/:userId')
  @ApiOperation({ summary: '获取用户学习画像' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiQuery({ name: 'includeAnalysis', required: false, description: '是否包含分析结果' })
  @ApiResponse({ status: 200, description: '获取画像成功' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async getLearnerProfile(
    @Param('userId') userId: string,
    @Query('includeAnalysis') includeAnalysis?: boolean
  ) {
    try {
      const result = await this.profileAnalyzer.buildLearnerProfile(userId, {
        includeHistoricalData: true,
        minDataPoints: 5,
        analysisDepth: 'detailed',
        updateExisting: true
      });

      this.logger.log(`获取用户画像成功: ${userId}`);

      if (includeAnalysis === true) {
        return {
          success: true,
          data: result
        };
      } else {
        return {
          success: true,
          data: {
            profile: result.profile,
            confidence: result.confidence,
            analysisDate: result.analysisDate
          }
        };
      }
    } catch (error) {
      this.logger.error(`获取用户画像失败: ${userId}, ${error.message}`, error.stack);
      throw new HttpException(
        `获取用户画像失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取个性化推荐
   */
  @Get('recommendations/:userId')
  @ApiOperation({ summary: '获取个性化学习推荐' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiQuery({ name: 'limit', required: false, description: '推荐数量限制' })
  @ApiQuery({ name: 'types', required: false, description: '内容类型过滤' })
  @ApiQuery({ name: 'knowledgeAreas', required: false, description: '知识领域过滤' })
  @ApiQuery({ name: 'difficulty', required: false, description: '难度过滤' })
  @ApiResponse({ status: 200, description: '获取推荐成功' })
  async getRecommendations(
    @Param('userId') userId: string,
    @Query('limit') limit?: number,
    @Query('types') types?: string,
    @Query('knowledgeAreas') knowledgeAreas?: string,
    @Query('difficulty') difficulty?: string
  ) {
    try {
      const request: RecommendationRequest = {
        userId,
        limit: limit || 5,
        types: types ? types.split(',') as any : undefined,
        knowledgeAreas: knowledgeAreas ? knowledgeAreas.split(',') : undefined,
        difficulty: difficulty as any,
        excludeCompleted: true,
        includeReasons: true
      };

      const result = await this.recommendationService.generateRecommendations(request);
      
      this.logger.log(`获取推荐成功: ${userId}, 推荐数量: ${result.recommendations.length}`);
      
      return {
        success: true,
        data: result
      };
    } catch (error) {
      this.logger.error(`获取推荐失败: ${userId}, ${error.message}`, error.stack);
      throw new HttpException(
        `获取推荐失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 更新推荐反馈
   */
  @Put('recommendations/:recommendationId/feedback')
  @ApiOperation({ summary: '更新推荐反馈' })
  @ApiParam({ name: 'recommendationId', description: '推荐ID' })
  @ApiResponse({ status: 200, description: '反馈更新成功' })
  async updateRecommendationFeedback(
    @Param('recommendationId') recommendationId: string,
    @Body() feedback: {
      action: 'accepted' | 'rejected' | 'ignored' | 'completed';
      rating?: number;
      comment?: string;
      timeToDecision?: number;
    }
  ) {
    try {
      const feedbackData: RecommendationFeedback = {
        recommendationId,
        userId: '', // 从JWT token中获取
        action: feedback.action,
        rating: feedback.rating,
        comment: feedback.comment,
        timeToDecision: feedback.timeToDecision,
        timestamp: new Date()
      };

      await this.recommendationService.recordFeedback(feedbackData);
      
      this.logger.log(`推荐反馈更新成功: ${recommendationId}, 动作: ${feedback.action}`);
      
      return {
        success: true,
        message: '推荐反馈更新成功'
      };
    } catch (error) {
      this.logger.error(`推荐反馈更新失败: ${recommendationId}, ${error.message}`, error.stack);
      throw new HttpException(
        `推荐反馈更新失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取学习分析报告
   */
  @Get('analytics/:userId')
  @ApiOperation({ summary: '获取学习分析报告' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiQuery({ name: 'startDate', required: false, description: '开始日期' })
  @ApiQuery({ name: 'endDate', required: false, description: '结束日期' })
  @ApiResponse({ status: 200, description: '获取分析报告成功' })
  async getLearningAnalytics(
    @Param('userId') userId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    try {
      // 获取用户画像
      const profileResult = await this.profileAnalyzer.buildLearnerProfile(userId);
      
      // 获取推荐统计
      const timeRange = {
        start: startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        end: endDate ? new Date(endDate) : new Date()
      };
      
      const recommendationStats = await this.recommendationService.getRecommendationStats(
        userId, 
        timeRange
      );

      // 获取同步统计
      const syncStats = await this.syncService.getSyncStats();

      const analytics = {
        profile: profileResult.profile,
        progressSummary: this.generateProgressSummary(profileResult.profile),
        recommendations: profileResult.recommendations.slice(0, 3),
        insights: profileResult.insights,
        recommendationStats,
        syncStats: {
          totalRecords: syncStats.totalRecords,
          syncRate: syncStats.syncRate,
          lastSyncTime: syncStats.lastSyncTime
        },
        timeRange
      };

      this.logger.log(`获取学习分析报告成功: ${userId}`);
      
      return {
        success: true,
        data: analytics
      };
    } catch (error) {
      this.logger.error(`获取学习分析报告失败: ${userId}, ${error.message}`, error.stack);
      throw new HttpException(
        `获取学习分析报告失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取推荐历史
   */
  @Get('recommendations/:userId/history')
  @ApiOperation({ summary: '获取用户推荐历史' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiQuery({ name: 'limit', required: false, description: '限制数量' })
  @ApiResponse({ status: 200, description: '获取推荐历史成功' })
  async getRecommendationHistory(
    @Param('userId') userId: string,
    @Query('limit') limit?: number
  ) {
    try {
      const history = await this.recommendationService.getUserRecommendationHistory(
        userId, 
        limit || 20
      );
      
      this.logger.log(`获取推荐历史成功: ${userId}, 数量: ${history.length}`);
      
      return {
        success: true,
        data: {
          recommendations: history,
          count: history.length
        }
      };
    } catch (error) {
      this.logger.error(`获取推荐历史失败: ${userId}, ${error.message}`, error.stack);
      throw new HttpException(
        `获取推荐历史失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取同步状态
   */
  @Get('sync/status')
  @ApiOperation({ summary: '获取数据同步状态' })
  @ApiResponse({ status: 200, description: '获取同步状态成功' })
  async getSyncStatus() {
    try {
      const stats = await this.syncService.getSyncStats();
      
      return {
        success: true,
        data: {
          ...stats,
          status: stats.syncRate > 0.95 ? 'healthy' : 
                  stats.syncRate > 0.8 ? 'warning' : 'error'
        }
      };
    } catch (error) {
      this.logger.error(`获取同步状态失败: ${error.message}`, error.stack);
      throw new HttpException(
        `获取同步状态失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 手动触发同步
   */
  @Post('sync/trigger')
  @ApiOperation({ summary: '手动触发数据同步' })
  @ApiResponse({ status: 200, description: '同步触发成功' })
  async triggerSync() {
    try {
      await this.syncService.syncPendingRecords();
      
      this.logger.log('手动触发同步成功');
      
      return {
        success: true,
        message: '数据同步已触发'
      };
    } catch (error) {
      this.logger.error(`手动触发同步失败: ${error.message}`, error.stack);
      throw new HttpException(
        `触发同步失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 生成进度摘要
   * @param profile 用户画像
   * @returns 进度摘要
   */
  private generateProgressSummary(profile: any): any {
    const knowledgeAreas = Object.entries(profile.knowledgeAreas || {});
    const totalAreas = knowledgeAreas.length;
    
    if (totalAreas === 0) {
      return {
        totalKnowledgeAreas: 0,
        masteredAreas: 0,
        strugglingAreas: 0,
        overallProgress: 0,
        averageConfidence: 0
      };
    }

    const masteredAreas = knowledgeAreas.filter(([_, area]: [string, any]) => area.confidence > 0.8).length;
    const strugglingAreas = knowledgeAreas.filter(([_, area]: [string, any]) => area.confidence < 0.4).length;
    const averageConfidence = knowledgeAreas.reduce((sum, [_, area]: [string, any]) => sum + area.confidence, 0) / totalAreas;

    return {
      totalKnowledgeAreas: totalAreas,
      masteredAreas,
      strugglingAreas,
      overallProgress: masteredAreas / totalAreas,
      averageConfidence
    };
  }
}
