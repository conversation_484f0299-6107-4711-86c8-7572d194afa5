/**
 * MediaPipe姿态检测器
 * 集成Google MediaPipe进行实时姿态识别
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { LandmarkData, WorldLandmarkData } from '../types/LandmarkData';

/**
 * MediaPipe配置选项
 */
export interface MediaPipeConfig {
  /** 模型复杂度 (0-2) */
  modelComplexity: number;
  /** 是否启用平滑 */
  smoothLandmarks: boolean;
  /** 是否启用分割 */
  enableSegmentation: boolean;
  /** 最小检测置信度 */
  minDetectionConfidence: number;
  /** 最小跟踪置信度 */
  minTrackingConfidence: number;
  /** 是否启用世界坐标 */
  enableWorldLandmarks: boolean;
}

/**
 * 姿态检测结果
 */
export interface PoseResults {
  /** 2D关键点 */
  landmarks?: LandmarkData[];
  /** 3D世界坐标关键点 */
  worldLandmarks?: WorldLandmarkData[];
  /** 分割掩码 */
  segmentationMask?: ImageData;
  /** 检测置信度 */
  confidence: number;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 手部检测结果
 */
export interface HandResults {
  /** 左手关键点 */
  leftHand?: LandmarkData[];
  /** 右手关键点 */
  rightHand?: LandmarkData[];
  /** 手部分类 */
  handedness?: Array<{ label: string; score: number }>;
  /** 检测置信度 */
  confidence: number;
  /** 时间戳 */
  timestamp: number;
}

/**
 * MediaPipe姿态检测器
 */
export class MediaPipePoseDetector extends EventEmitter {
  private pose: any = null;
  private hands: any = null;
  private isInitialized = false;
  private config: MediaPipeConfig;
  private lastProcessTime = 0;
  private processingQueue: ImageData[] = [];
  private isProcessing = false;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: MediaPipeConfig = {
    modelComplexity: 1,
    smoothLandmarks: true,
    enableSegmentation: false,
    minDetectionConfidence: 0.5,
    minTrackingConfidence: 0.5,
    enableWorldLandmarks: true
  };

  constructor(config: Partial<MediaPipeConfig> = {}) {
    super();
    this.config = { ...MediaPipePoseDetector.DEFAULT_CONFIG, ...config };
  }

  /**
   * 初始化MediaPipe
   */
  public async initialize(): Promise<void> {
    try {
      // 动态导入MediaPipe
      const { Pose, Hands } = await this.loadMediaPipe();

      // 初始化姿态检测
      this.pose = new Pose({
        locateFile: (file: string) => {
          return `https://cdn.jsdelivr.net/npm/@mediapipe/pose/${file}`;
        }
      });

      this.pose.setOptions({
        modelComplexity: this.config.modelComplexity,
        smoothLandmarks: this.config.smoothLandmarks,
        enableSegmentation: this.config.enableSegmentation,
        smoothSegmentation: this.config.enableSegmentation,
        minDetectionConfidence: this.config.minDetectionConfidence,
        minTrackingConfidence: this.config.minTrackingConfidence
      });

      // 初始化手部检测
      this.hands = new Hands({
        locateFile: (file: string) => {
          return `https://cdn.jsdelivr.net/npm/@mediapipe/hands/${file}`;
        }
      });

      this.hands.setOptions({
        maxNumHands: 2,
        modelComplexity: this.config.modelComplexity,
        minDetectionConfidence: this.config.minDetectionConfidence,
        minTrackingConfidence: this.config.minTrackingConfidence
      });

      this.isInitialized = true;
      this.emit('initialized');
      
      Debug.log('MediaPipePoseDetector', 'MediaPipe初始化成功');
    } catch (error) {
      Debug.error('MediaPipePoseDetector', 'MediaPipe初始化失败', error);
      throw error;
    }
  }

  /**
   * 动态加载MediaPipe库
   */
  private async loadMediaPipe(): Promise<any> {
    // 检查是否已经加载
    if ((window as any).mediapipe) {
      return (window as any).mediapipe;
    }

    // 动态加载MediaPipe脚本
    await this.loadScript('https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils/camera_utils.js');
    await this.loadScript('https://cdn.jsdelivr.net/npm/@mediapipe/control_utils/control_utils.js');
    await this.loadScript('https://cdn.jsdelivr.net/npm/@mediapipe/drawing_utils/drawing_utils.js');
    await this.loadScript('https://cdn.jsdelivr.net/npm/@mediapipe/pose/pose.js');
    await this.loadScript('https://cdn.jsdelivr.net/npm/@mediapipe/hands/hands.js');

    return (window as any);
  }

  /**
   * 加载脚本
   */
  private loadScript(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
      document.head.appendChild(script);
    });
  }

  /**
   * 检测姿态
   */
  public async detectPose(imageData: ImageData): Promise<PoseResults> {
    if (!this.isInitialized || !this.pose) {
      throw new Error('MediaPipe未初始化');
    }

    // 防止处理队列过长
    if (this.processingQueue.length > 3) {
      this.processingQueue.shift(); // 移除最旧的帧
    }

    return new Promise((resolve, reject) => {
      const startTime = performance.now();

      this.pose.onResults((results: any) => {
        const processingTime = performance.now() - startTime;
        
        const poseResults: PoseResults = {
          landmarks: this.convertLandmarks(results.poseLandmarks),
          worldLandmarks: this.convertWorldLandmarks(results.poseWorldLandmarks),
          segmentationMask: results.segmentationMask,
          confidence: this.calculateConfidence(results.poseLandmarks),
          timestamp: Date.now()
        };

        this.emit('poseDetected', poseResults, processingTime);
        resolve(poseResults);
      });

      this.pose.onError((error: any) => {
        Debug.error('MediaPipePoseDetector', '姿态检测失败', error);
        reject(error);
      });

      // 发送图像数据
      this.pose.send({ image: imageData });
    });
  }

  /**
   * 检测手部
   */
  public async detectHands(imageData: ImageData): Promise<HandResults> {
    if (!this.isInitialized || !this.hands) {
      throw new Error('MediaPipe未初始化');
    }

    return new Promise((resolve, reject) => {
      const startTime = performance.now();

      this.hands.onResults((results: any) => {
        const processingTime = performance.now() - startTime;
        
        const handResults: HandResults = {
          leftHand: this.extractHandLandmarks(results, 'Left'),
          rightHand: this.extractHandLandmarks(results, 'Right'),
          handedness: results.multiHandedness,
          confidence: this.calculateHandConfidence(results),
          timestamp: Date.now()
        };

        this.emit('handsDetected', handResults, processingTime);
        resolve(handResults);
      });

      this.hands.onError((error: any) => {
        Debug.error('MediaPipePoseDetector', '手部检测失败', error);
        reject(error);
      });

      // 发送图像数据
      this.hands.send({ image: imageData });
    });
  }

  /**
   * 转换关键点格式
   */
  private convertLandmarks(landmarks: any[]): LandmarkData[] | undefined {
    if (!landmarks) return undefined;
    
    return landmarks.map((landmark, index) => ({
      x: landmark.x,
      y: landmark.y,
      z: landmark.z || 0,
      visibility: landmark.visibility || 1,
      index
    }));
  }

  /**
   * 转换世界坐标关键点格式
   */
  private convertWorldLandmarks(worldLandmarks: any[]): WorldLandmarkData[] | undefined {
    if (!worldLandmarks) return undefined;
    
    return worldLandmarks.map((landmark, index) => ({
      x: landmark.x,
      y: landmark.y,
      z: landmark.z,
      visibility: landmark.visibility || 1,
      index
    }));
  }

  /**
   * 提取手部关键点
   */
  private extractHandLandmarks(results: any, handType: 'Left' | 'Right'): LandmarkData[] | undefined {
    if (!results.multiHandLandmarks || !results.multiHandedness) {
      return undefined;
    }

    for (let i = 0; i < results.multiHandedness.length; i++) {
      const handedness = results.multiHandedness[i];
      if (handedness.label === handType) {
        const landmarks = results.multiHandLandmarks[i];
        return this.convertLandmarks(landmarks);
      }
    }

    return undefined;
  }

  /**
   * 计算姿态置信度
   */
  private calculateConfidence(landmarks: any[]): number {
    if (!landmarks || landmarks.length === 0) return 0;
    
    const visibilitySum = landmarks.reduce((sum, landmark) => {
      return sum + (landmark.visibility || 0);
    }, 0);
    
    return visibilitySum / landmarks.length;
  }

  /**
   * 计算手部置信度
   */
  private calculateHandConfidence(results: any): number {
    if (!results.multiHandedness || results.multiHandedness.length === 0) {
      return 0;
    }
    
    const scoreSum = results.multiHandedness.reduce((sum: number, hand: any) => {
      return sum + (hand.score || 0);
    }, 0);
    
    return scoreSum / results.multiHandedness.length;
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<MediaPipeConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (this.isInitialized) {
      if (this.pose) {
        this.pose.setOptions({
          modelComplexity: this.config.modelComplexity,
          smoothLandmarks: this.config.smoothLandmarks,
          enableSegmentation: this.config.enableSegmentation,
          minDetectionConfidence: this.config.minDetectionConfidence,
          minTrackingConfidence: this.config.minTrackingConfidence
        });
      }
      
      if (this.hands) {
        this.hands.setOptions({
          modelComplexity: this.config.modelComplexity,
          minDetectionConfidence: this.config.minDetectionConfidence,
          minTrackingConfidence: this.config.minTrackingConfidence
        });
      }
    }
  }

  /**
   * 获取当前配置
   */
  public getConfig(): MediaPipeConfig {
    return { ...this.config };
  }

  /**
   * 销毁检测器
   */
  public destroy(): void {
    if (this.pose) {
      this.pose.close();
      this.pose = null;
    }
    
    if (this.hands) {
      this.hands.close();
      this.hands = null;
    }
    
    this.isInitialized = false;
    this.processingQueue = [];
    this.removeAllListeners();
    
    Debug.log('MediaPipePoseDetector', 'MediaPipe检测器已销毁');
  }

  /**
   * 是否已初始化
   */
  public get initialized(): boolean {
    return this.isInitialized;
  }
}
