import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { DocumentsService } from './documents.service';
import { DocumentsController } from './documents.controller';
import { Document } from './entities/document.entity';
import { DocumentProcessorService } from './processors/document-processor.service';
import { DocumentProcessor } from './processors/document.processor';
import { VectorStoreModule } from '../vector-store/vector-store.module';
import { KnowledgeBaseModule } from '../knowledge-base/knowledge-base.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Document]),
    BullModule.registerQueue({
      name: 'document-processing',
    }),
    VectorStoreModule,
    // 避免循环依赖，使用forwardRef
  ],
  controllers: [DocumentsController],
  providers: [
    DocumentsService,
    DocumentProcessorService,
    DocumentProcessor,
  ],
  exports: [DocumentsService],
})
export class DocumentsModule {}
